﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Processing;
using Sleekflow.Apis.ShareHub.Api;
using Sleekflow.Apis.ShareHub.Model;
using Svg;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Services.Cache;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationServices;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.Externals;
using Travis_backend.SignalR;
using Travis_backend.SubscriptionPlanDomain.Enums;

namespace Travis_backend.CompanyDomain.Services
{
    public interface ICompanyService
    {
        /// <summary>
        /// Get Company by CompanyId.
        /// </summary>
        /// <param name="companyId">CompanyId.</param>
        /// <returns>Company.</returns>
        Task<Company?> GetCompany(string companyId);

        /// <summary>
        /// Get Company's Stripe Checkout CustomerId.
        /// </summary>
        /// <param name="companyId">CompanyId.</param>
        /// <returns>Stripe's Customer Id.</returns>
        Task<string?> GetStripeCheckoutCustomerIdAsync(string companyId);

        Task<CompanyCustomField> GetCustomFieldByFieldName(string companyId, string fieldName);

        Task CreateSandbox(string companyId, string sandboxIdentity);

        Task DeleteSandbox(string companyId);

        Task AddECommerceFields(string companyId);

        Task AddLeadSource(string companyId, string LeadSource);

        Task<bool> IsCustomUserProfileFieldExist(string companyId, string fieldname);

        Task AddCustomUserProfileFields(
            string companyId,
            string fieldName,
            FieldDataType fieldType,
            FieldsCategory fieldsCategory);

        Task<List<CompanyCustomUserProfileField>> UpdateCustomUserProfileFields(
            string companyId,
            List<CompanyCustomUserProfileField> customFields);

        Task<bool> IsCompanyStaffExist(string companyId, string staffId);

        Task ActivateQRCodeMapping(string companyId);

        Task<(string Url, string QrCodeUrl)> GetStaffQrcode(Staff companyUser);

        Task<(MemoryStream MemoryStream, string Url)> GetTeamQrcode(CompanyTeam companyTeam);

        Task<string> GetQRCodeIdentity(Staff staff);

        Task<string> GetTeamQRCodeContent(CompanyTeam team);

        Task<TargetedChannelModel> GetTargetedChannel(Staff companyUser);

        Task<TargetedChannelModel> GetTargetedChannelTeam(CompanyTeam companyTeam);

        Task<string> GetContactOwnerPhoneNumber(Staff companyUser);

        Task SendMfaEnrollmentEmailsByRole(StaffUserRole role, string companyId);

        Task<Staff> GetStaffAsync(string companyId, string staffId);

        Task<List<Staff>> GetAllStaffsAsync(string companyId);

        /// <summary>
        /// Get company's subscription tier info.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <returns>CompanySubscriptionTierInfo</returns>
        Task<CompanySubscriptionInfo> GetCompanySubscriptionInfo(string companyId);

        /// <summary>
        /// Update Company's Subscription Tier.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="tier">Subscription Country Tier.</param>
        /// <param name="currency">Currency.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<int> UpdateCompanySubscriptionTierAndCurrencyAsync(string companyId, string tier, string currency);

        /// <summary>
        /// Get whether country subscription tier and currency has set to company.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <returns>Boolean.</returns>
        Task<bool> HasSubscriptionCountryTierAndCurrencyAsync(string companyId);
    }

    public class CompanyService : ICompanyService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IConfiguration _configuration;
        private readonly ILogger _logger;
        private readonly ICompanyInfoCacheService _companyInfoCacheService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly IMfaService _mfaService;
        private readonly IQrCodesApi _qrCodesApi;
        private readonly IExternalFileService _externalFileService;
        private readonly IDbContextService _dbContextService;
        private readonly ICompanyUsageService _companyUsageService;
        private readonly ICompanyRepository _companyRepository;
        private readonly IContactCacheService _contactCacheService;
        private readonly ICompanyBillRecordRepository _companyBillRecordRepository;

        public CompanyService(
            ApplicationDbContext appDbContext,
            IConfiguration configuration,
            ILogger<CompanyService> logger,
            ICompanyInfoCacheService companyInfoCacheService,
            IAzureBlobStorageService azureBlobStorageService,
            IMfaService mfaService,
            IQrCodesApi qrCodesApi,
            IExternalFileService externalFileService,
            IDbContextService dbContextService,
            ICompanyRepository companyRepository,
            ICompanyUsageService companyUsageService,
            IContactCacheService contactCacheService,
            ICompanyBillRecordRepository companyBillRecordRepository)
        {
            _appDbContext = appDbContext;
            _configuration = configuration;
            _logger = logger;
            _companyInfoCacheService = companyInfoCacheService;
            _azureBlobStorageService = azureBlobStorageService;
            _mfaService = mfaService;
            _qrCodesApi = qrCodesApi;
            _externalFileService = externalFileService;
            _dbContextService = dbContextService;
            _companyRepository = companyRepository;
            _companyUsageService = companyUsageService;
            _contactCacheService = contactCacheService;
            _companyBillRecordRepository = companyBillRecordRepository;
        }

        /// <inheritdoc />
        public Task<Company?> GetCompany(string companyId)
        {
            return _companyRepository.GetCompany(companyId);
        }

        /// <inheritdoc />
        public async Task<string?> GetStripeCheckoutCustomerIdAsync(string companyId)
        {
            var company = await GetCompany(companyId);

            if (!string.IsNullOrWhiteSpace(company!.AffiliateCustomerId))
            {
                return company.AffiliateCustomerId;
            }

            var lastValidPaymentBillRecord = await _companyBillRecordRepository.GetLastValidPaymentBillRecord(companyId);

            return lastValidPaymentBillRecord?.customerId;
        }

        public Task<bool> IsCompanyStaffExist(string companyId, string staffId)
            => _appDbContext.UserRoleStaffs
                .AnyAsync(
                    x =>
                        x.IdentityId == staffId
                        && x.CompanyId == companyId);

        public async Task ActivateQRCodeMapping(string companyId)
        {
            // Generate default rules for staff and teams
            // Default message: Hello! I'd like to connect with {{teamname}}.
            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == companyId);

            if (!company.IsQRCodeMappingEnabled)
            {
                // Create QRCode automation
                var qrcodeMappingAutomation = new AssignmentRule
                {
                    CompanyId = companyId,
                    AssignmentRuleName = "QRCode Mapping",
                    AutomationType = AutomationType.QRCodeAssigneeMapping,
                    Conditions = new List<Condition>
                    {
                        new Condition
                        {
                            FieldName = "Message",
                            ConditionOperator = SupportedOperator.RegexMatched,
                            Values = new List<string>
                            {
                                "Hello! I'd like to connect with (.*)"
                            }
                        }
                    },
                    AutomationActions = new List<AutomationAction>
                    {
                        new AutomationAction
                        {
                            AutomatedTriggerType = AutomatedTriggerType.Assignment,
                            AssignmentType = AssignmentType.AssignWithRegExValue,
                        },
                        new AutomationAction
                        {
                            AutomatedTriggerType = AutomatedTriggerType.SendMessage,
                            MessageContent = "Thank you for your message.\nYou’re now connected to {0}!",
                            MessageParams = new List<string>
                            {
                                "@ContactOwner"
                            }
                        }
                    }
                };

                // Create QRCode automation for Team
                var qrcodeMappingTeamAutomation = new AssignmentRule
                {
                    CompanyId = companyId,
                    AssignmentRuleName = "QRCode Mapping For Teams",
                    AutomationType = AutomationType.QRCodeAssignTeamMapping,
                    Conditions = new List<Condition>
                    {
                        new Condition
                        {
                            FieldName = "Message",
                            ConditionOperator = SupportedOperator.RegexMatched,
                            Values = new List<string>
                            {
                                "Hello! I'd like to connect with (.*)"
                            }
                        }
                    },
                    AutomationActions = new List<AutomationAction>
                    {
                        new AutomationAction
                        {
                            AutomatedTriggerType = AutomatedTriggerType.SendMessage,
                            MessageContent = "Thank you for your message.\nYou’re now connected to {0}!",
                            MessageParams = new List<string>
                            {
                                "@assignedteam"
                            }
                        }
                    }
                };

                await _appDbContext.CompanyAssignmentRules.AddAsync(qrcodeMappingAutomation);
                await _appDbContext.CompanyAssignmentRules.AddAsync(qrcodeMappingTeamAutomation);
            }

            // Add conection message to existing teams
            var teams = await _appDbContext.CompanyStaffTeams
                .Where(x => x.CompanyId == companyId)
                .ToListAsync();

            foreach (var team in teams)
            {
                string assignmentMessage = await GetTeamQRCodeContent(team);
                team.QRCodeIdentity = assignmentMessage;

                await _appDbContext.SaveChangesAsync();
            }

            // Add QRcode identity to staff record
            var staffs = await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == companyId)
                .Include(x => x.Identity)
                .ToListAsync();

            foreach (var staff in staffs)
            {
                string qrcodeIdentity = await GetQRCodeIdentity(staff);
                staff.QRCodeIdentity = qrcodeIdentity;

                await _appDbContext.SaveChangesAsync();
            }

            company.IsQRCodeMappingEnabled = true;
            await _appDbContext.SaveChangesAsync();
        }

        public async Task<TargetedChannelModel> GetTargetedChannel(Staff companyUser)
        {
            try
            {
                var (
                    whatsappIds,
                    whatsapp360DialogIds,
                    cloudApiIds,
                    twilioSenderIds) = await GetStaffDefaultChannelIds(companyUser);

                return await GetDefaultChannelByChannels(
                    companyUser.CompanyId,
                    whatsappIds,
                    twilioSenderIds,
                    whatsapp360DialogIds,
                    cloudApiIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Unable to get staff's default whatsapp channel {SleekflowCompanyId}",
                    companyUser.CompanyId);
            }

            return null;
        }

        public async Task<TargetedChannelModel> GetTargetedChannelTeam(CompanyTeam companyTeam)
        {
            try
            {
                var (
                    whatsappIds,
                    whatsapp360DialogIds,
                    cloudApiIds,
                    twilioSenderIds) = await GetTeamDefaultChannelIds(companyTeam);

                return await GetDefaultChannelByChannels(
                    companyTeam.CompanyId,
                    whatsappIds,
                    twilioSenderIds,
                    whatsapp360DialogIds,
                    cloudApiIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Unable to get team's default whatsapp channel {SleekflowCompanyId}",
                    companyTeam.CompanyId);
            }

            return null;
        }

        public async Task<(string Url, string QrCodeUrl)> GetStaffQrcode(Staff companyUser)
        {
            try
            {
                var phoneNumber = await GetContactOwnerPhoneNumber(companyUser);

                var assigneeMapping = await _appDbContext.CompanyAssignmentRules
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.AutomationType == AutomationType.QRCodeAssigneeMapping
                            && x.Status == AutomationStatus.Live);

                var mappingMessager = $"Hello! I'd like to connect with {companyUser.Identity.UserName}";

                if (assigneeMapping != null)
                {
                    var messageContent = assigneeMapping.Conditions
                        .FirstOrDefault()?
                        .Values
                        .FirstOrDefault();

                    if (string.IsNullOrEmpty(companyUser.QRCodeIdentity))
                    {
                        companyUser.QRCodeIdentity = await GetQRCodeIdentity(companyUser);

                        await _appDbContext.SaveChangesAsync();
                    }

                    mappingMessager = messageContent
                        .Replace("(.*)", companyUser.QRCodeIdentity)
                        .Replace("\\", string.Empty);
                }

                var url = $"https://wa.me/{phoneNumber}/?text={System.Web.HttpUtility.UrlEncode(mappingMessager)}";
                var companyIcon = await _appDbContext.CompanyIconFiles
                    .FirstOrDefaultAsync(x => x.CompanyId == companyUser.CompanyId);

                var iconUrl = companyIcon is not null
                    ? _azureBlobStorageService.GetAzureBlobSasUri(
                        companyIcon.Filename,
                        companyIcon.BlobContainer)
                    : null;
                var getUrlQrCodeOutputOutput = await _qrCodesApi.QrCodesGetUrlQrCodePostAsync(
                    getUrlQrCodeInput: new GetUrlQrCodeInput(iconUrl, url, new QrCodeConfig(200)));

                return (url, getUrlQrCodeOutputOutput.Data.Url);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to get staff's QR code for company {CompanyId} staff {StaffId}",
                    companyUser.CompanyId,
                    companyUser.Id);

                return (string.Empty, string.Empty);
            }
        }

        public async Task<string> GetContactOwnerPhoneNumber(Staff companyUser)
        {
            var (
                whatsappIds,
                whatsapp360DialogIds,
                cloudApiIds,
                twilioSenderIds) = await GetStaffDefaultChannelIds(companyUser);

            var phoneNumber = await GetPhoneNumberByChannels(
                companyUser.CompanyId,
                whatsappIds,
                twilioSenderIds,
                whatsapp360DialogIds,
                cloudApiIds);

            return phoneNumber;
        }

        private async Task<(
            List<string> WhatsappIds,
            List<long> Whatsapp360DialogIds,
            List<string> CloudApiIds,
            List<string> TwilioSenderIds)> GetStaffDefaultChannelIds(Staff companyUser)
        {
            var whatsappIds = new List<string>();
            var whatsapp360DialogIds = new List<long>();
            var cloudApiIds = new List<string>();
            var twilioSenderIds = new List<string>();

            var dbContext = _dbContextService.GetDbContext();
            if (!string.IsNullOrEmpty(companyUser.QRCodeChannel?.channel))
            {
                if (companyUser.QRCodeChannel.channel == ChannelTypes.Whatsapp360Dialog)
                {
                    foreach (var channelId in companyUser.QRCodeChannel.ids)
                    {
                        var good = long.TryParse(channelId, out var id);

                        if (good)
                        {
                            whatsapp360DialogIds.Add(id);
                        }
                    }
                }

                // add cloud api support
                if (companyUser.QRCodeChannel.channel == ChannelTypes.WhatsappCloudApi)
                {
                    foreach (var channelId in companyUser.QRCodeChannel.ids)
                    {
                        cloudApiIds.Add(channelId);
                    }
                }

                foreach (var id in companyUser.QRCodeChannel.ids)
                {
                    var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                    whatsappIds.Add(twilioInstance[0]);

                    if (twilioInstance.Count() > 1)
                    {
                        twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                    }
                }
            }
            else
            {
                var associatedTeams = await dbContext.CompanyStaffTeams
                    .Where(x => x.Members.Any(y => y.StaffId == companyUser.Id))
                    .ToListAsync();

                foreach (var associatedTeam in associatedTeams)
                {
                    if (associatedTeam.DefaultChannels?.Count > 0)
                    {
                        foreach (var defaultChannel in associatedTeam.DefaultChannels
                                     .Where(
                                         x =>
                                             x.channel != ChannelTypes.Whatsapp360Dialog
                                             && x.channel != ChannelTypes.WhatsappCloudApi))
                        {
                            if (defaultChannel.ids != null)
                            {
                                foreach (var id in defaultChannel.ids)
                                {
                                    var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                    whatsappIds.Add(twilioInstance[0]);

                                    if (twilioInstance.Count() > 1)
                                    {
                                        twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                                    }
                                }
                            }
                        }

                        foreach (var defaultChannel in associatedTeam.DefaultChannels
                                     .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                        {
                            foreach (var channelId in defaultChannel.ids)
                            {
                                var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);

                                if (validLong)
                                {
                                    whatsapp360DialogIds.Add(whatsapp360dialogChannelId);
                                }
                            }
                        }

                        // add cloud api support
                        foreach (var defaultChannel in associatedTeam.DefaultChannels
                                     .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                        {
                            foreach (var channelId in defaultChannel.ids)
                            {
                                cloudApiIds.Add(channelId);
                            }
                        }
                    }
                }
            }

            return (
                whatsappIds,
                whatsapp360DialogIds,
                cloudApiIds,
                twilioSenderIds);
        }

        private async Task<(
            List<string> WhatsappIds,
            List<long> Whatsapp360DialogIds,
            List<string> CloudApiIds,
            List<string> TwilioSenderIds)> GetTeamDefaultChannelIds(CompanyTeam team)
        {
            var whatsappIds = new List<string>();
            var whatsapp360DialogIds = new List<long>();
            var cloudApiIds = new List<string>();
            var twilioSenderIds = new List<string>();

            if (!string.IsNullOrEmpty(team.QRCodeChannel?.channel))
            {
                if (team.QRCodeChannel.channel == ChannelTypes.Whatsapp360Dialog)
                {
                    foreach (var channelId in team.QRCodeChannel.ids)
                    {
                        var good = long.TryParse(channelId, out var id);

                        if (good)
                        {
                            whatsapp360DialogIds.Add(id);
                        }
                    }
                }

                // add cloud api support
                if (team.QRCodeChannel.channel == ChannelTypes.WhatsappCloudApi)
                {
                    foreach (var channelId in team.QRCodeChannel.ids)
                    {
                        cloudApiIds.Add(channelId);
                    }
                }

                foreach (var id in team.QRCodeChannel.ids)
                {
                    var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                    whatsappIds.Add(twilioInstance[0]);

                    if (twilioInstance.Count() > 1)
                    {
                        twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                    }
                }
            }
            else
            {
                var associatedTeams = await _appDbContext.CompanyStaffTeams
                    .Where(x => x.Members.Any(y => y.StaffId == team.Id))
                    .ToListAsync();

                foreach (var associatedTeam in associatedTeams)
                {
                    if (associatedTeam.DefaultChannels?.Count > 0)
                    {
                        foreach (var defaultChannel in associatedTeam.DefaultChannels
                                     .Where(
                                         x =>
                                             x.channel != ChannelTypes.Whatsapp360Dialog
                                             && x.channel != ChannelTypes.WhatsappCloudApi))
                        {
                            if (defaultChannel.ids != null)
                            {
                                foreach (var id in defaultChannel.ids)
                                {
                                    var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                    whatsappIds.Add(twilioInstance[0]);

                                    if (twilioInstance.Count() > 1)
                                    {
                                        twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                                    }
                                }
                            }
                        }

                        foreach (var defaultChannel in associatedTeam.DefaultChannels
                                     .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                        {
                            foreach (var channelId in defaultChannel.ids)
                            {
                                var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);

                                if (validLong)
                                {
                                    whatsapp360DialogIds.Add(whatsapp360dialogChannelId);
                                }
                            }
                        }

                        // add cloud api support
                        foreach (var defaultChannel in associatedTeam.DefaultChannels
                                     .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                        {
                            foreach (var channelId in defaultChannel.ids)
                            {
                                cloudApiIds.Add(channelId);
                            }
                        }
                    }
                }
            }

            return (
                whatsappIds,
                whatsapp360DialogIds,
                cloudApiIds,
                twilioSenderIds);
        }

        private async Task<string> GetTeamPhoneNumber(CompanyTeam companyTeam)
        {
            var whatsappIds = new List<string>();
            var whatsapp360DialogIds = new List<long>();
            var twilioSenderIds = new List<string>();
            var cloudApiIds = new List<string>();

            if (!string.IsNullOrEmpty(companyTeam.QRCodeChannel?.channel))
            {
                if (companyTeam.QRCodeChannel.channel == ChannelTypes.Whatsapp360Dialog)
                {
                    foreach (var channelId in companyTeam.QRCodeChannel.ids)
                    {
                        var good = long.TryParse(channelId, out var id);
                        if (good)
                        {
                            whatsapp360DialogIds.Add(id);
                        }
                    }
                }

                // add cloud api support
                if (companyTeam.QRCodeChannel.channel == ChannelTypes.WhatsappCloudApi)
                {
                    foreach (var channelId in companyTeam.QRCodeChannel.ids)
                    {
                        cloudApiIds.Add(channelId);
                    }
                }

                foreach (var id in companyTeam.QRCodeChannel.ids)
                {
                    var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                    whatsappIds.Add(twilioInstance[0]);

                    if (twilioInstance.Count() > 1)
                    {
                        twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                    }
                }
            }
            else
            {
                var associatedTeams = await _appDbContext.CompanyStaffTeams
                    .Where(x => x.Id == companyTeam.Id)
                    .ToListAsync();

                foreach (var associatedTeam in associatedTeams)
                {
                    if (associatedTeam.DefaultChannels?.Count > 0)
                    {
                        foreach (var defaultChannel in associatedTeam.DefaultChannels)
                        {
                            if (defaultChannel.ids != null)
                            {
                                foreach (var id in defaultChannel.ids)
                                {
                                    var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                    whatsappIds.Add(twilioInstance[0]);

                                    if (twilioInstance.Count() > 1)
                                    {
                                        twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                                    }
                                }
                            }
                        }

                        foreach (var defaultChannel in associatedTeam.DefaultChannels.Where(
                                     x => x.channel == ChannelTypes.Whatsapp360Dialog))
                        {
                            foreach (var channelId in defaultChannel.ids)
                            {
                                var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);
                                if (validLong)
                                {
                                    whatsapp360DialogIds.Add(whatsapp360dialogChannelId);
                                }
                            }
                        }

                        // add cloud api support
                        foreach (var defaultChannel in associatedTeam.DefaultChannels.Where(
                                     x => x.channel == ChannelTypes.WhatsappCloudApi))
                        {
                            foreach (var channelId in defaultChannel.ids)
                            {
                                cloudApiIds.Add(channelId);
                            }
                        }
                    }
                }
            }

            var phoneNumber = await GetPhoneNumberByChannels(
                companyTeam.CompanyId,
                whatsappIds,
                twilioSenderIds,
                whatsapp360DialogIds,
                cloudApiIds);

            return phoneNumber;
        }

        private async Task<TargetedChannelModel> GetDefaultChannelByChannels(
            string companyId,
            List<string> whatsappIds,
            List<string> twilioSenderIds,
            List<long> whatsapp360DialogIds,
            List<string> cloudApiIds)
        {
            var phoneNumber = string.Empty;

            if (whatsappIds.Count > 0)
            {
                var whatsappConfig = await _appDbContext.ConfigWhatsAppConfigs
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && whatsappIds.Contains(x.TwilioAccountId)
                            && twilioSenderIds.Contains(x.WhatsAppSender));

                if (whatsappConfig != null)
                {
                    return new TargetedChannelModel
                    {
                        channel = "twilio_whatsapp",
                        ids = new List<string>
                        {
                            $"{whatsappConfig.TwilioAccountId};{whatsappConfig.WhatsAppSender}"
                        }
                    };
                }
            }

            if (whatsapp360DialogIds.Count > 0)
            {
                var whatsapp360DialogConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && whatsapp360DialogIds.Contains(x.Id));

                if (whatsapp360DialogConfig != null)
                {
                    return new TargetedChannelModel
                    {
                        channel = ChannelTypes.Whatsapp360Dialog,
                        ids = new List<string>
                        {
                            $"{whatsapp360DialogConfig.Id}"
                        }
                    };
                }
            }

            // cloudApi support
            if (cloudApiIds.Count > 0)
            {
                var cloudApiConfig = await _appDbContext.ConfigWhatsappCloudApiConfigs
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && cloudApiIds.Contains(x.WhatsappPhoneNumber));

                if (cloudApiConfig != null)
                {
                    return new TargetedChannelModel
                    {
                        channel = ChannelTypes.WhatsappCloudApi,
                        ids = new List<string>
                        {
                            $"{cloudApiConfig.WhatsappPhoneNumber}"
                        }
                    };
                }
            }

            if (string.IsNullOrEmpty(phoneNumber))
            {
                var whatsappConfig = await _appDbContext.ConfigWhatsAppConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId);

                if (whatsappConfig != null)
                {
                    return new TargetedChannelModel
                    {
                        channel = "twilio_whatsapp",
                        ids = new List<string>
                        {
                            $"{whatsappConfig.TwilioAccountId};{whatsappConfig.WhatsAppSender}"
                        }
                    };
                }

                var whatsapp360DialogConfig = await _appDbContext.ConfigWhatsApp360DialogConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId);

                if (whatsapp360DialogConfig != null)
                {
                    return new TargetedChannelModel
                    {
                        channel = ChannelTypes.Whatsapp360Dialog,
                        ids = new List<string>
                        {
                            $"{whatsapp360DialogConfig.Id}"
                        }
                    };
                }

                // return if no default channel configured
                var cloudApiConfig = await _appDbContext.ConfigWhatsappCloudApiConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId);

                if (cloudApiConfig != null)
                {
                    return new TargetedChannelModel
                    {
                        channel = ChannelTypes.WhatsappCloudApi,
                        ids = new List<string>
                        {
                            $"{cloudApiConfig.WhatsappPhoneNumber}"
                        }
                    };
                }
            }

            return null;
        }

        private async Task<string> GetPhoneNumberByChannels(
            string companyId,
            List<string> whatsappIds,
            List<string> twilioSenderIds,
            List<long> whatsapp360DialogIds,
            List<string> cloudApiIds)
        {
            var phoneNumber = string.Empty;

            var dbContext = _dbContextService.GetDbContext();
            if (whatsappIds.Count > 0)
            {
                var whatsappConfig = await dbContext.ConfigWhatsAppConfigs
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && whatsappIds.Contains(x.TwilioAccountId)
                            && twilioSenderIds.Contains(x.WhatsAppSender));

                if (whatsappConfig != null)
                {
                    phoneNumber = whatsappConfig.WhatsAppSender.Replace("whatsapp:+", string.Empty);
                }
            }

            if (whatsapp360DialogIds.Count > 0)
            {
                var whatsapp360DialogConfig = await dbContext.ConfigWhatsApp360DialogConfigs
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && whatsapp360DialogIds.Contains(x.Id));

                if (whatsapp360DialogConfig != null)
                {
                    phoneNumber = whatsapp360DialogConfig.WhatsAppPhoneNumber;
                }
            }

            // cloudApi support
            if (cloudApiIds.Count > 0)
            {
                var cloudApiConfig = await dbContext.ConfigWhatsappCloudApiConfigs
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && cloudApiIds.Contains(x.WhatsappPhoneNumber));

                if (cloudApiConfig != null)
                {
                    phoneNumber = cloudApiConfig.WhatsappPhoneNumber;
                }
            }

            if (string.IsNullOrEmpty(phoneNumber))
            {
                var whatsappConfig = await dbContext.ConfigWhatsAppConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId);

                if (whatsappConfig != null)
                {
                    return whatsappConfig.WhatsAppSender.Replace("whatsapp:+", string.Empty);
                }

                var whatsapp360DialogConfig = await dbContext.ConfigWhatsApp360DialogConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId);

                if (whatsapp360DialogConfig != null)
                {
                    return whatsapp360DialogConfig.WhatsAppPhoneNumber;
                }

                // return if no default channel configured
                var cloudApiConfig = await dbContext.ConfigWhatsappCloudApiConfigs
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId);

                if (cloudApiConfig != null)
                {
                    return cloudApiConfig.WhatsappPhoneNumber;
                }
            }

            return phoneNumber;
        }

        public async Task<(MemoryStream MemoryStream, string Url)> GetTeamQrcode(CompanyTeam companyTeam)
        {
            var phoneNumber = await GetTeamPhoneNumber(companyTeam);

            var assigneeMapping = await _appDbContext.CompanyAssignmentRules
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyTeam.CompanyId
                        && x.AutomationType == AutomationType.QRCodeAssignTeamMapping
                        && x.Status == AutomationStatus.Live);

            var mappingMessager = $"Hello! I'd like to connect with {companyTeam.TeamName}";

            if (assigneeMapping != null)
            {
                var messageContent = assigneeMapping.Conditions
                    .FirstOrDefault()?
                    .Values
                    .FirstOrDefault();

                if (string.IsNullOrEmpty(companyTeam.QRCodeIdentity))
                {
                    companyTeam.QRCodeIdentity = await GetTeamQRCodeContent(companyTeam);
                    await _appDbContext.SaveChangesAsync();
                }

                mappingMessager = messageContent
                    .Replace("(.*)", companyTeam.QRCodeIdentity)
                    .Replace("\\", string.Empty);
            }

            var url = $"https://wa.me/{phoneNumber}/?text={System.Web.HttpUtility.UrlEncode(mappingMessager)}";
            var companyIcon = await _appDbContext.CompanyIconFiles
                .FirstOrDefaultAsync(x => x.CompanyId == companyTeam.CompanyId);

            var iconUrl = companyIcon is not null
                ? _azureBlobStorageService.GetAzureBlobSasUri(
                    companyIcon.Filename,
                    companyIcon.BlobContainer)
                : null;
            var getUrlQrCodeOutputOutput = await _qrCodesApi.QrCodesGetUrlQrCodePostAsync(
                getUrlQrCodeInput: new GetUrlQrCodeInput(iconUrl, url, new QrCodeConfig(200)));
            var qrCodeUrl = getUrlQrCodeOutputOutput.Data.Url;
            var context = await _externalFileService.DownloadFileFromUrlAsync(qrCodeUrl);

            return (new MemoryStream(context.FileContent), url);
        }

        public async Task<string> GetQRCodeIdentity(Staff staff)
        {
            var qrcodeIdentity = $"{staff.Identity.FirstName} {staff.Identity.LastName}";

            if (string.IsNullOrEmpty(qrcodeIdentity.Replace(" ", string.Empty)))
            {
                qrcodeIdentity = $"{staff.Identity.UserName}";
            }

            if (await _appDbContext.UserRoleStaffs
                    .AnyAsync(
                        x =>
                            x.CompanyId == staff.CompanyId
                            && x.QRCodeIdentity == qrcodeIdentity))
            {
                var i = 1;

                while (true)
                {
                    qrcodeIdentity = $"{qrcodeIdentity}{i}";

                    if (!await _appDbContext.UserRoleStaffs.AnyAsync(
                            x =>
                                x.CompanyId == staff.CompanyId
                                && x.QRCodeIdentity == qrcodeIdentity))
                    {
                        break;
                    }

                    i++;
                }
            }

            return qrcodeIdentity;
        }

        public async Task<string> GetTeamQRCodeContent(CompanyTeam team)
        {
            var qrcodeIdentity = $"{team.TeamName}";

            if (await _appDbContext.CompanyStaffTeams
                    .AnyAsync(
                        x =>
                            x.CompanyId == team.CompanyId
                            && x.QRCodeIdentity == qrcodeIdentity))
            {
                var i = 1;

                while (true)
                {
                    qrcodeIdentity = $"{team.TeamName}{i}";

                    if (!await _appDbContext.CompanyStaffTeams
                            .AnyAsync(
                                x =>
                                    x.CompanyId == team.CompanyId
                                    && x.QRCodeIdentity == qrcodeIdentity))
                    {
                        break;
                    }

                    i++;
                }
            }

            return qrcodeIdentity;
        }

        public Task<bool> IsCustomUserProfileFieldExist(string companyId, string fieldname)
            => _appDbContext.CompanyCustomUserProfileFields
                .AnyAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.FieldName.ToLower().Trim() == fieldname.ToLower().Trim());

        public async Task AddCustomUserProfileFields(
            string companyId,
            string fieldName,
            FieldDataType fieldType,
            FieldsCategory fieldsCategory)
        {
            var newCustomField = new CompanyCustomUserProfileField()
            {
                CompanyId = companyId,
                FieldName = fieldName,
                FieldsCategory = fieldsCategory,
                Type = fieldType,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual()
                    {
                        Language = "en", DisplayName = fieldName,
                    }
                }
            };

            if (!await _appDbContext.CompanyCustomUserProfileFields
                    .AnyAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.FieldName.ToLower().Replace(" ", string.Empty) ==
                            fieldName.ToLower().Replace(" ", string.Empty)))
            {
                _appDbContext.CompanyCustomUserProfileFields.Add(newCustomField);

                await _appDbContext.SaveChangesAsync();
            }
        }

        public async Task<List<CompanyCustomUserProfileField>> UpdateCustomUserProfileFields(
            string companyId,
            List<CompanyCustomUserProfileField> customFields)
        {
            var companyUserProfileCustomFields = _appDbContext.CompanyCustomUserProfileFields
                .Where(x => x.CompanyId == companyId)
                .Include(x => x.CustomUserProfileFieldLinguals)
                .Include(x => x.CustomUserProfileFieldOptions)
                .ThenInclude(x => x.CustomUserProfileFieldOptionLinguals);

            foreach (var customField in customFields)
            {
                if (!companyUserProfileCustomFields
                        .Any(
                            x =>
                                x.FieldName.ToLower().Replace(" ", string.Empty) ==
                                customField.FieldName.ToLower().Replace(" ", string.Empty) ||
                                x.Id == customField.Id))
                {
                    // Add new field if no exist
                    customField.IsDefault = false;

                    if (string.IsNullOrEmpty(customField.Id))
                    {
                        customField.Id = Guid.NewGuid().ToString();
                    }

                    customField.CompanyId = companyId;
                    customField.FieldName = customField.FieldName.Trim();
                    customField.FieldsCategory = FieldsCategory.Custom;

                    _appDbContext.CompanyCustomUserProfileFields.Add(customField);
                    // For adding new property we do save first
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    // Add, update, or remove the exist fields
                    CompanyCustomUserProfileField existing;
                    CompanyCustomUserProfileField existingById = null;

                    if (!string.IsNullOrEmpty(customField.Id))
                    {
                        existingById = companyUserProfileCustomFields
                            .FirstOrDefault(x => x.Id == customField.Id);
                    }

                    if (existingById != null)
                    {
                        existing = existingById;
                    }
                    else
                    {
                        existing = companyUserProfileCustomFields
                            .FirstOrDefault(
                                x =>
                                    x.FieldName.ToLower().Replace(" ", string.Empty) ==
                                    customField.FieldName.ToLower().Replace(" ", string.Empty));
                    }

                    if (existing == null)
                    {
                        continue;
                    }

                    if (existing.IsEditable.HasValue
                        && !existing.IsEditable.Value)
                    {
                        // Only can reorder the field
                        existing.Order = customField.Order;
                        existing.IsVisible = customField.IsVisible;

                        continue;
                    }

                    if (existing.CustomUserProfileFieldOptions.Any())
                    {
                        // Remove options
                        var removedOptions = existing.CustomUserProfileFieldOptions
                            .Where(
                                x => !customField.CustomUserProfileFieldOptions
                                    .Select(y => y.Value)
                                    .Contains(x.Value))
                            .ToList();

                        foreach (var removedOption in removedOptions)
                        {
                            existing.CustomUserProfileFieldOptions.Remove(removedOption);
                        }
                    }

                    if (existing.CustomUserProfileFieldOptions != null
                        && customField.CustomUserProfileFieldOptions != null
                        && (existing.CustomUserProfileFieldOptions
                                .Any(
                                    x => !customField.CustomUserProfileFieldOptions
                                        .Select(y => y.Value)
                                        .Contains(x.Value))
                            || existing.CustomUserProfileFieldOptions.Count !=
                            customField.CustomUserProfileFieldOptions.Count))
                    {
                        // Add options
                        if (!existing.CustomUserProfileFieldOptions
                                .Select(x => x.Value)
                                .SequenceEqual(
                                    customField.CustomUserProfileFieldOptions
                                        .Select(x => x.Value)))
                        {
                            var addedOptions = customField.CustomUserProfileFieldOptions
                                .Where(
                                    x => !existing.CustomUserProfileFieldOptions
                                        .Select(y => y.Value)
                                        .Contains(x.Value))
                                .ToList();

                            if (addedOptions.Any())
                            {
                                existing.CustomUserProfileFieldOptions.AddRange(addedOptions);
                            }
                        }
                    }

                    if (existing.CustomUserProfileFieldOptions != null)
                    {
                        // Update the order of Options
                        var newOptionsDict =
                            customField.CustomUserProfileFieldOptions.ToDictionary(x => x.Value, x => x.Order);

                        foreach (var existingOption in existing.CustomUserProfileFieldOptions.Where(
                                     existingOption => newOptionsDict.ContainsKey(existingOption.Value)))
                        {
                            existingOption.Order = newOptionsDict[existingOption.Value];
                        }
                    }

                    if (existing.CustomUserProfileFieldLinguals
                            .Any(
                                x =>
                                    !customField.CustomUserProfileFieldLinguals
                                        .Select(y => y.DisplayName)
                                        .Contains(x.DisplayName)) ||
                        existing.CustomUserProfileFieldLinguals.Count !=
                        customField.CustomUserProfileFieldLinguals.Count)
                    {
                        if (!existing.CustomUserProfileFieldLinguals
                                .Select(x => x.DisplayName)
                                .SequenceEqual(
                                    customField.CustomUserProfileFieldLinguals
                                        .Select(x => x.DisplayName)))
                        {
                            // Remove options
                            var removedLingual = existing.CustomUserProfileFieldLinguals
                                .Where(
                                    x => !customField.CustomUserProfileFieldLinguals
                                        .Select(y => y.DisplayName)
                                        .Contains(x.DisplayName))
                                .ToList();

                            foreach (var removeItem in removedLingual)
                            {
                                existing.CustomUserProfileFieldLinguals.Remove(removeItem);
                            }

                            var addedLingual = customField.CustomUserProfileFieldLinguals
                                .Where(
                                    x => !existing.CustomUserProfileFieldLinguals
                                        .Select(y => y.DisplayName)
                                        .Contains(x.DisplayName))
                                .ToList();

                            if (addedLingual.Any())
                            {
                                existing.CustomUserProfileFieldLinguals.AddRange(addedLingual);
                            }
                        }
                    }

                    if (existing.IsDefault != null && !existing.IsDefault.Value)
                    {
                        existing.FieldName = customField.FieldName;
                        existing.Type = customField.Type;
                    }

                    existing.Order = customField.Order;
                    existing.IsVisible = customField.IsVisible;
                    existing.IsEditable = customField.IsEditable;
                }
            }

            await _appDbContext.SaveChangesAsync();
            await _contactCacheService.InvalidateCompanyCustomUserProfileFieldsCacheAsync(companyId);

            BackgroundJob.Enqueue<ICompanyInfoCacheService>(
                x =>
                    x.RemoveCompanyInfoCache(companyId, "GetCompanyInfo"));

            return await companyUserProfileCustomFields.ToListAsync();
        }

        public async Task AddLeadSource(string companyId, string LeadSource)
        {
            try
            {
                var leadSourceCustomFields = await _appDbContext.CompanyCustomUserProfileFields
                    .Include(x => x.CustomUserProfileFieldOptions)
                    .ThenInclude(x => x.CustomUserProfileFieldOptionLinguals)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.FieldName == "LeadSource");

                if (leadSourceCustomFields != null)
                {
                    if (leadSourceCustomFields.CustomUserProfileFieldOptions
                        .All(x => x.Value != LeadSource))
                    {
                        leadSourceCustomFields.CustomUserProfileFieldOptions.Add(
                            new CustomUserProfileFieldOption
                            {
                                Value = LeadSource,
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = LeadSource, Language = "en"
                                    },
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = LeadSource, Language = "zh-HK"
                                    }
                                },
                                Order = 8
                            });
                    }

                    await _appDbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Company {CompanyId} error adding lead source {LeadSource}: {ExceptionMessage}",
                    nameof(AddLeadSource),
                    companyId,
                    LeadSource,
                    ex.Message);
            }
        }

        public async Task AddECommerceFields(string companyId)
        {
            try
            {
                var newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (x.FieldName == "Order Number"
                                || x.FieldName == "Last Order Number"))
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Number",
                        Type = FieldDataType.SingleLineText,
                        Order = 90,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Number", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂購號碼", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);

                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 90;
                    newField.FieldName = "Last Order Number";
                    newField.FieldsCategory = FieldsCategory.Shopify;

                    try
                    {
                        var matchedEnCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "en");

                        if (matchedEnCustomProfileLingual is not null)
                        {
                            matchedEnCustomProfileLingual.DisplayName = "Last Order Number";
                        }

                        var matchedZhCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "zh-hk");

                        if (matchedZhCustomProfileLingual is not null)
                        {
                            matchedZhCustomProfileLingual.DisplayName = "最近的訂購號碼";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "{MethodName} Last Order Number exception for company {CompanyId}",
                            nameof(AddECommerceFields),
                            companyId);
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (x.FieldName == "Ordered Items"
                                || x.FieldName == "Last Order Items"))
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Items",
                        Type = FieldDataType.MultiLineText,
                        Order = 91,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Items", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂購商品", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 91;
                    newField.FieldName = "Last Order Items";
                    newField.FieldsCategory = FieldsCategory.Shopify;

                    try
                    {
                        var matchedEnCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "en");

                        if (matchedEnCustomProfileLingual is not null)
                        {
                            matchedEnCustomProfileLingual.DisplayName = "Last Order Items";
                        }

                        var matchedZhCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "zh-hk");

                        if (matchedZhCustomProfileLingual is not null)
                        {
                            matchedZhCustomProfileLingual.DisplayName = "最近的訂購商品";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "{MethodName} Last Order Items exception for company {CompanyId}",
                            nameof(AddECommerceFields),
                            companyId);
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (x.FieldName == "Ordered Amount"
                                || x.FieldName == "Last Order Amount"))
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Amount",
                        Type = FieldDataType.SingleLineText,
                        Order = 92,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Amount", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂貨金額", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 92;
                    newField.FieldName = "Last Order Amount";
                    newField.FieldsCategory = FieldsCategory.Shopify;

                    try
                    {
                        var matchedEnCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "en");

                        if (matchedEnCustomProfileLingual is not null)
                        {
                            matchedEnCustomProfileLingual.DisplayName = "Last Order Amount";
                        }

                        var matchedZhCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "zh-hk");

                        if (matchedZhCustomProfileLingual is not null)
                        {
                            matchedZhCustomProfileLingual.DisplayName = "最近的訂貨金額";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "{MethodName} Last Order Amount exception for company {CompanyId}",
                            nameof(AddECommerceFields),
                            companyId);
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (x.FieldName == "Order Currency"
                                || x.FieldName == "Last Order Currency"))
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Currency",
                        Type = FieldDataType.SingleLineText,
                        Order = 93,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Currency", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂單貨幣", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 93;
                    newField.FieldName = "Last Order Currency";
                    newField.FieldsCategory = FieldsCategory.Shopify;

                    try
                    {
                        var matchedEnCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "en");

                        if (matchedEnCustomProfileLingual is not null)
                        {
                            matchedEnCustomProfileLingual.DisplayName = "Last Order Currency";
                        }

                        var matchedZhCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "zh-hk");

                        if (matchedZhCustomProfileLingual is not null)
                        {
                            matchedZhCustomProfileLingual.DisplayName = "最近的訂單貨幣";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "{MethodName} Last Order Currency exception for company {CompanyId}",
                            nameof(AddECommerceFields),
                            companyId);
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (x.FieldName == "Ordered Total Price"
                                || x.FieldName == "Last Order Total Price"))
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Total Price",
                        Type = FieldDataType.Number,
                        Order = 94,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Total Price", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂貨總額", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 94;
                    newField.FieldName = "Last Order Total Price";
                    newField.FieldsCategory = FieldsCategory.Shopify;

                    try
                    {
                        var matchedEnCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "en");

                        if (matchedEnCustomProfileLingual is not null)
                        {
                            matchedEnCustomProfileLingual.DisplayName = "Last Order Total Price";
                        }

                        var matchedZhCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "zh-hk");

                        if (matchedZhCustomProfileLingual is not null)
                        {
                            matchedZhCustomProfileLingual.DisplayName = "最近的訂貨總額";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "{MethodName} Last Order Total Price exception for company {CompanyId}",
                            nameof(AddECommerceFields),
                            companyId);
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (x.FieldName == "Ordered Date"
                                || x.FieldName == "Last Order Date"))
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Date",
                        Type = FieldDataType.DateTime,
                        Order = 95,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Date", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂購日期", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 95;
                    newField.FieldName = "Last Order Date";
                    newField.FieldsCategory = FieldsCategory.Shopify;

                    try
                    {
                        var matchedEnCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "en");

                        if (matchedEnCustomProfileLingual is not null)
                        {
                            matchedEnCustomProfileLingual.DisplayName = "Last Order Date";
                        }

                        var matchedZhCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "zh-hk");

                        if (matchedZhCustomProfileLingual is not null)
                        {
                            matchedZhCustomProfileLingual.DisplayName = "最近的訂購日期";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "{MethodName} Last Order Date exception for company {CompanyId}",
                            nameof(AddECommerceFields),
                            companyId);
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (x.FieldName == "Order Updated Date"
                                || x.FieldName == "Last Order Updated Date"))
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Updated Date",
                        Type = FieldDataType.DateTime,
                        Order = 96,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Updated Date", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂購更新日期", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 96;
                    newField.FieldName = "Last Order Updated Date";
                    newField.FieldsCategory = FieldsCategory.Shopify;

                    try
                    {
                        var matchedEnCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "en");

                        if (matchedEnCustomProfileLingual is not null)
                        {
                            matchedEnCustomProfileLingual.DisplayName = "Last Order Updated Date";
                        }

                        var matchedZhCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "zh-hk");

                        if (matchedZhCustomProfileLingual is not null)
                        {
                            matchedZhCustomProfileLingual.DisplayName = "最近的訂購更新日期";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "{MethodName} Last Order Updated Date exception for company {CompanyId}",
                            nameof(AddECommerceFields),
                            companyId);
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (x.FieldName == "Order Status"
                                || x.FieldName == "Last Order Status"))
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Status",
                        Type = FieldDataType.Options,
                        Order = 97,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Status", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂購狀態", Language = "zh-hk"
                            }
                        },
                        CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>
                        {
                            new CustomUserProfileFieldOption
                            {
                                Value = "open",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Open", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "closed",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Closed", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "cancelled",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Cancelled", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "temp",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Temp", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "pending",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Pending", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "removed",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Removed", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "confirmed",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Confirmed", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "completed",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Completed", Language = "en"
                                    }
                                }
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 97;
                    newField.FieldName = "Last Order Status";
                    newField.FieldsCategory = FieldsCategory.Shopify;

                    try
                    {
                        var matchedEnCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "en");

                        if (matchedEnCustomProfileLingual is not null)
                        {
                            matchedEnCustomProfileLingual.DisplayName = "Last Order Status";
                        }

                        var matchedZhCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "zh-hk");

                        if (matchedZhCustomProfileLingual is not null)
                        {
                            matchedZhCustomProfileLingual.DisplayName = "最近的訂購狀態";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "{MethodName} Last Order Status exception for company {CompanyId}",
                            nameof(AddECommerceFields),
                            companyId);
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (x.FieldName == "Order Status URL"
                                || x.FieldName == "Last Order Status URL"))
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Status URL",
                        Type = FieldDataType.SingleLineText,
                        Order = 98,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Status URL", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂購狀態連結", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 98;
                    newField.FieldName = "Last Order Status URL";
                    newField.FieldsCategory = FieldsCategory.Shopify;

                    try
                    {
                        var matchedEnCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "en");

                        if (matchedEnCustomProfileLingual is not null)
                        {
                            matchedEnCustomProfileLingual.DisplayName = "Last Order Status URL";
                        }

                        var matchedZhCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "zh-hk");

                        if (matchedZhCustomProfileLingual is not null)
                        {
                            matchedZhCustomProfileLingual.DisplayName = "最近的訂購狀態連結";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "{MethodName} Last Order Status URL exception for company {CompanyId}",
                            nameof(AddECommerceFields),
                            companyId);
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (x.FieldName == "Financial Status"
                                || x.FieldName == "Last Order Financial Status"))
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Financial Status",
                        Type = FieldDataType.Options,
                        Order = 99,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Financial Status", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的財務狀態", Language = "zh-hk"
                            }
                        },
                        CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>
                        {
                            new CustomUserProfileFieldOption
                            {
                                Value = "authorized",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Authorized", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "pending",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Pending", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "paid",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Paid", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "partially_paid",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Partially_paid", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "refunded",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Refunded", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "voided",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Voided", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "partially_refunded",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Partially_refunded", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "unpaid",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Unpaid", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "failed",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Failed", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "expired",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Expired", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "completed",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Completed", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "refunding",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Refunding", Language = "en"
                                    }
                                }
                            },
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 99;
                    newField.FieldName = "Last Order Financial Status";
                    newField.FieldsCategory = FieldsCategory.Shopify;

                    try
                    {
                        var matchedEnCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "en");

                        if (matchedEnCustomProfileLingual is not null)
                        {
                            matchedEnCustomProfileLingual.DisplayName = "Last Order Financial Status";
                        }

                        var matchedZhCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "zh-hk");

                        if (matchedZhCustomProfileLingual is not null)
                        {
                            matchedZhCustomProfileLingual.DisplayName = "最近的財務狀態";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "{MethodName} Last Order Financial Status exception for company {CompanyId}",
                            nameof(AddECommerceFields),
                            companyId);
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (x.FieldName == "Fulfillment Status"
                                || x.FieldName == "Last Order Fulfillment Status"))
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Fulfillment Status",
                        Type = FieldDataType.Options,
                        Order = 100,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Fulfillment Status", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的履行狀態", Language = "zh-hk"
                            }
                        },
                        CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>
                        {
                            new CustomUserProfileFieldOption
                            {
                                Value = "shipped",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Shipped", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "partial",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Partial", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "unshipped",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Unshipped", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "unfulfilled",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Unfulfilled", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "fulfilled",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Fulfilled", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "pending",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Pending", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "shipping",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Shipping", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "arrived",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Arrived", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "collected",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Collected", Language = "en"
                                    }
                                }
                            },
                            new CustomUserProfileFieldOption
                            {
                                Value = "returned",
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        DisplayName = "Returned", Language = "en"
                                    }
                                }
                            },
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 100;
                    newField.FieldName = "Last Order Fulfillment Status";
                    newField.FieldsCategory = FieldsCategory.Shopify;

                    try
                    {
                        var matchedEnCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "en");

                        if (matchedEnCustomProfileLingual is not null)
                        {
                            matchedEnCustomProfileLingual.DisplayName = "Last Order Fulfillment Status";
                        }

                        var matchedZhCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "zh-hk");

                        if (matchedZhCustomProfileLingual is not null)
                        {
                            matchedZhCustomProfileLingual.DisplayName = "最近的履行狀態";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "{MethodName} Last Order Fulfillment Status exception for company {CompanyId}",
                            nameof(AddECommerceFields),
                            companyId);
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                // newField = await _appDbContext.CompanyCustomUserProfileFields.Where(x => x.CompanyId == companyId && x.FieldName == "Customer Name").FirstOrDefaultAsync();
                // if (newField == null)
                // {
                //    var newTeamField = new CompanyCustomUserProfileField
                //    {
                //        CompanyId = companyId,
                //        FieldName = "Customer Name",
                //        Type = FieldDataType.SingleLineText,
                //        Order = 99,
                //        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>() {
                //                         new CustomUserProfileFieldLingual { DisplayName = "Customer Name", Language = "en" },
                //                         new CustomUserProfileFieldLingual { DisplayName = "顧客姓名", Language = "zh-hk" }
                //                },
                //        IsDeletable = false,
                //        IsEditable = true,
                //        IsVisible = true,
                //    };
                //    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                //    await _appDbContext.SaveChangesAsync();
                // }
                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && (x.FieldName == "Order Remarks"
                                || x.FieldName == "Last Order Remarks"))
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Remarks",
                        Type = FieldDataType.SingleLineText,
                        Order = 101,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Remarks", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂購備註", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 101;
                    newField.FieldsCategory = FieldsCategory.Shopify;
                    newField.FieldName = "Last Order Remarks";

                    try
                    {
                        var matchedEnCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "en");

                        if (matchedEnCustomProfileLingual is not null)
                        {
                            matchedEnCustomProfileLingual.DisplayName = "Last Order Remarks";
                        }

                        var matchedZhCustomProfileLingual = newField.CustomUserProfileFieldLinguals
                            .FirstOrDefault(x => x.Language == "zh-hk");

                        if (matchedZhCustomProfileLingual is not null)
                        {
                            matchedZhCustomProfileLingual.DisplayName = "最近的訂購備註";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "{MethodName} Last Order Remarks exception for company {CompanyId}",
                            nameof(AddECommerceFields),
                            companyId);
                    }

                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.FieldName == "Last Order Shipping Method")
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Shipping Method",
                        Type = FieldDataType.SingleLineText,
                        Order = 102,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Shipping Method", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂購運送方式", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 102;
                    newField.FieldsCategory = FieldsCategory.Shopify;
                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.FieldName == "Last Order Tracking Company")
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Tracking Company",
                        Type = FieldDataType.SingleLineText,
                        Order = 103,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Tracking Company", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂購追蹤公司", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 103;
                    newField.FieldsCategory = FieldsCategory.Shopify;
                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.FieldName == "Last Order Tracking URL")
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Tracking URL",
                        Type = FieldDataType.SingleLineText,
                        Order = 103,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Tracking URL", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂購追蹤連結", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 104;
                    newField.FieldsCategory = FieldsCategory.Shopify;
                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.FieldName == "Last Order Tracking Number")
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Last Order Tracking Number",
                        Type = FieldDataType.SingleLineText,
                        Order = 105,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Last Order Tracking Number", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "最近的訂購追蹤號碼", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 105;
                    newField.FieldsCategory = FieldsCategory.Shopify;
                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.FieldName == "Abandoned Cart Items")
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Abandoned Cart Items",
                        Type = FieldDataType.SingleLineText,
                        Order = 106,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Abandoned Cart Items", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "購物車物品", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 106;
                    newField.FieldsCategory = FieldsCategory.Shopify;
                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.FieldName == "Abandoned Cart Amount")
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Abandoned Cart Amount",
                        Type = FieldDataType.SingleLineText,
                        Order = 107,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Abandoned Cart Amount", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "購物車金額", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 107;
                    newField.FieldsCategory = FieldsCategory.Shopify;
                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.FieldName == "Abandoned Cart Currency")
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Abandoned Cart Currency",
                        Type = FieldDataType.SingleLineText,
                        Order = 108,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Abandoned Cart Currency", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "購物車貨幣", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 108;
                    newField.FieldsCategory = FieldsCategory.Shopify;
                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.FieldName == "Abandoned Cart Total Amount")
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Abandoned Cart Total Amount",
                        Type = FieldDataType.Number,
                        Order = 109,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Abandoned Cart Total Amount", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "購物車總額", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 109;
                    newField.FieldsCategory = FieldsCategory.Shopify;
                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.FieldName == "Abandoned Cart Date")
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Abandoned Cart Date",
                        Type = FieldDataType.DateTime,
                        Order = 110,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Abandoned Cart Date", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "購物車更新日期", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 110;
                    newField.FieldsCategory = FieldsCategory.Shopify;
                    await _appDbContext.SaveChangesAsync();
                }

                newField = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.FieldName == "Abandoned Cart URL")
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .FirstOrDefaultAsync();

                if (newField == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyId,
                        FieldName = "Abandoned Cart URL",
                        Type = FieldDataType.SingleLineText,
                        Order = 111,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Abandoned Cart URL", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "購物車連結", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = true,
                        IsVisible = true,
                        IsDefault = true,
                        FieldsCategory = FieldsCategory.Shopify
                    };

                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    newField.Order = 111;
                    newField.FieldsCategory = FieldsCategory.Shopify;
                    await _appDbContext.SaveChangesAsync();
                }

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Add ecommerce custom field error {SleekflowCompanyId}",
                    companyId);
            }
        }

        public async Task CreateSandbox(string companyId, string sandboxIdentity)
        {
            try
            {
                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == companyId);

                sandboxIdentity = sandboxIdentity.Replace(" ", "-");

                if (await _appDbContext.CompanySandboxes.AnyAsync(x => x.MappingIdentity == sandboxIdentity))
                {
                    Random rand = new Random();
                    var fortimerinterval1 = rand.Next(0, 2000);
                    await CreateSandbox(company.Id, $"{sandboxIdentity}-{fortimerinterval1}");

                    return;
                }

                _appDbContext.CompanySandboxes.Add(
                    new CompanySandbox
                    {
                        CompanyId = company.Id,
                        MappingIdentity = sandboxIdentity
                    });

                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Create sandbox error for company {CompanyId}, {SandboxIdentityId}: {ExceptionMessage}",
                    companyId,
                    sandboxIdentity,
                    ex.Message);
            }
        }

        public async Task DeleteSandbox(string companyId)
        {
            try
            {
                var company = await _appDbContext.CompanyCompanies
                    .FirstOrDefaultAsync(x => x.Id == companyId);

                var sandbox = await _appDbContext.CompanySandboxes
                    .Include(x => x.SandboxSenders)
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId);

                var sandboxUsers = await _appDbContext.UserProfiles
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.IsSandbox)
                    .ToListAsync();

                var sandboxConvos = await _appDbContext.Conversations
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.IsSandbox)
                    .ToListAsync();

                sandboxUsers.ForEach(x => x.IsSandbox = false);
                sandboxConvos.ForEach(x => x.IsSandbox = false);

                _appDbContext.CompanySandboxes.Remove(sandbox);
                await _appDbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Delete sandbox error for company {CompanyId}: {ExceptionMessage}",
                    companyId,
                    ex.Message);
            }
        }

        public Task<CompanyCustomField> GetCustomFieldByFieldName(string companyId, string fieldName)
            => _appDbContext.CompanyCompanyCustomFields
                .Include(x => x.CompanyCustomFieldFieldLinguals)
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.FieldName.ToLower() == fieldName.ToLower());

        public async Task SendMfaEnrollmentEmailsByRole(StaffUserRole role, string companyId)
        {
            var staffs = await _appDbContext.UserRoleStaffs
                .Where(
                    s =>
                        s.CompanyId == companyId
                        && s.RoleType == role)
                .Include(x => x.Identity)
                .ToListAsync();

            if (!staffs.Any())
            {
                _logger.LogWarning(
                    "[SendMfaEnrollmentEmailsByRole] No users have found in role {Role}",
                    role);

                return;
            }

            foreach (var staff in staffs)
            {
                try
                {
                    // Send Mfa enrollment email to user. (Override method from user manager)
                    await _mfaService.SendMfaEnrollmentEmailToUser(staff.Identity);

                    // Avoid the Global limit.
                    await Task.Delay(500);
                }
                catch (Exception err)
                {
                    // Log error and continue.
                    _logger.LogError(
                        err,
                        "[SendMfaEnrollmentEmailsByRole] Send Mfa enrollment email failed of user {UserEmail}, message:\n{ErrorMsg}\n{StackTrace}",
                        staff?.Identity?.Email,
                        err.Message,
                        err.StackTrace);
                }
            }

            _logger.LogInformation(
                "[SendMfaEnrollmentEmailsByRole] Sent 2FA enrollment to Role: {Role}, Users:\n{Users}",
                role,
                string.Join(", ", staffs.Select(s => s.Identity.Email)));
        }

        public async Task<Staff> GetStaffAsync(string companyId, string staffId)
        {
            Staff staff;

            if (string.IsNullOrWhiteSpace(companyId)
                || string.IsNullOrWhiteSpace(staffId))
            {
                return null;
            }

            if (int.TryParse(staffId, out var staffIdInt))
            {
                staff = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyId && x.Id != 1)
                    .Include(x => x.Identity)
                    .OrderBy(x => x.Order)
                    .ThenBy(x => x.Id)
                    .FirstOrDefaultAsync(x => x.Id == staffIdInt);
            }
            else
            {
                // StaffId is actually IdentityId
                staff = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyId && x.Id != 1)
                    .Include(x => x.Identity)
                    .OrderBy(x => x.Order)
                    .ThenBy(x => x.Id)
                    .FirstOrDefaultAsync(x => x.IdentityId == staffId);
            }

            return staff;
        }

        public Task<List<Staff>> GetAllStaffsAsync(string companyId)
            => _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == companyId && x.Id != 1)
                .Include(x => x.Identity)
                .ToListAsync();

        /// <inheritdoc />
        public async Task<CompanySubscriptionInfo> GetCompanySubscriptionInfo(string companyId)
        {
            var company = await _companyRepository.GetCompany(companyId);
            var billRecord = await _companyUsageService.GetCompanySubscriptionPlan(companyId);

            var basePlanId = billRecord.SubscriptionPlan.Id;
            var subscriptionTier = billRecord.SubscriptionPlan.SubscriptionTier;
            var countryTier = string.IsNullOrWhiteSpace(company.SubscriptionCountryTier) ? (SubscriptionCountryTier?) null : company.SubscriptionCountryTier.ToEnum<SubscriptionCountryTier>();
            var subscriptionInterval = billRecord.SubscriptionPlan.SubscriptionInterval;

            return new CompanySubscriptionInfo(basePlanId, subscriptionTier, countryTier, subscriptionInterval, company.SubscriptionCurrency);
        }

        /// <inheritdoc />
        public async Task<int> UpdateCompanySubscriptionTierAndCurrencyAsync(string companyId, string tier, string currency)
        {
            return await _companyRepository.UpdateCompanySubscriptionTierAndCurrencyAsync(companyId, tier, currency);
        }

        /// <inheritdoc />
        public async Task<bool> HasSubscriptionCountryTierAndCurrencyAsync(string companyId)
        {
            var isSubscriptionCountryTierHasValue = await _companyRepository.IsSubscriptionCountryTierHasValue(companyId);
            var isSubscriptionCurrencyHasValue = await _companyRepository.IsSubscriptionCurrencyHasValue(companyId);
            return isSubscriptionCountryTierHasValue && isSubscriptionCurrencyHasValue;
        }
    }
}