location_name: eastasia
sku_config:
  sleekflow_core:
    name: P3V3
    tier: PremiumV3
  sleekflow_core_db:
    name: HS_S_Gen5
    tier: Hyperscale
    family: Gen5
    capacity: 2
  sleekflow_analytic_db:
    name: HS_S_Gen5
    tier: Hyperscale
    family: Gen5
    capacity: 2
  sleekflow_powerflow:
    name: P1V3
    tier: PremiumV3
  sleekflow_sleek_pay:
    name: P0V3
    tier: PremiumV3
  redis:
    default:
      name: Standard
      family: C
      capacity: 1
    caching:
      name: Standard
      family: C
      capacity: 1
sql_db_config:
  administrator_login_random_secret: lmsoj6cjtBaQUrIk4ecUWg==BenSuWGN7TgRtYKk
  administrator_login_password_random_secret: zeYn9Y14kWEIL6Vwayerxw==RDFZQ7IWIP=id7hR
  whitelist_ip_ranges:
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: **************
      end_ip_address: **************
  is_read_scale_enable: "true"
  high_availability_replica_count: 1
vnet:
  default_address_space: **********/16
  default_subnet_address_prefix: **********/24
  sleekflow_core_db_address_prefix: **********/24
  sleekflow_core_address_prefix: **********/24
  sleekflow_powerflow_address_prefix: **********/24
  sleekflow_sleek_pay_address_prefix: **********/24
  sleekflow_core_worker_address_prefix: **********/24
auto_scale_config:
  sleekflow_core:
    capacity:
      default: "3"
      maximum: "10"
      minimum: "2"
    scale_out_instances: "1"
    scale_down_instances: "1"
sleekflow_core_config:
  aspnetcore_environment: development
  logger:
    gcp_is_enabled: "TRUE"
    gcp_project_id: cool-phalanx-404402
    gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  audit_hub:
    endpoint: https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io/v1/audit-hub
    key: DA+1m6JYB4n6MvuTPWKh+fiZEeoePLgYlYpa77H/vOF+pJcBJ4Kbwb4Lha7/mVCQIkICzfj3hZebYaKHvQdCKOnI/5WHP+jhk9YohtcsQSc=VQl5uyRAvLYVCdSc
  auth0:
    action_audience: https://api-dev.sleekflow.io/
    action_issuer: https://sso-dev.sleekflow.io/
    audience: https://api-dev.sleekflow.io
    client_id: A8CAuyJshC04w6FYYZZdxAtfoXSvKFnU
    client_secret: 04xRs0m4txIKznvm6L/kyBRHpmsT2DA94koI9PMCZsW7ZRBdbH7tJbKLRjE93hil80PWaKz1kDL3VqkzTAdNtUukMQQscPjRs1GpLn1jHz0=WLbFbrsAqDPYJMDR
    database_connection_name: Sleekflow-Username-Password-Authentication
    domain: sleekflow-dev.eu.auth0.com
    http_retries: 10
    issuers:
      - https://sso-dev.sf.chat/
      - https://sleekflow-dev.eu.auth0.com
    namespace: https://app.sleekflow.io/
    role_claim_type: roles
    user_email_claim_type: email
    user_id_claim_type: user_id
    username_claim_type: user_name
    tenant_hub_secret_key: CDNEsRP7qzddn/F/JWWeNHj7IqcBGD6dTsKFdNRuBcCfIJ+suNkEgI9jjjXKYVZcnbiGTRoCbsfVFScCVsidMxPlv/CXdNuN5g303bp9oMw=ef80cgmejwibLi2l
    health_check:
      is_enabled: "true"
      client_id: cMUb5OuJmGnWPhejGeqMzIexkBVX5XKE
      client_secret: dj2e99+7EZVYIiUzMUOxpj0KQFCD4jApF2hB2Pjhm9eHBOHrnv++T6v6c3t7qG/1OEWggAlafqI+6ggPtNdWyD4Pw4VrVlSjBxzyEjJPDL4=q6Xe5XHr863Z15iO
      username: <EMAIL>
      password: uhn7W+=LdXLY9^m
  azure:
    media_service:
      account_name: sfmediadev
      client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
      client_secret: s58NH69cxl0ltl0ipf3ei9HkAAeWM1IDeXiLsoZBNVvdb3fRkTpH335oPLaCW3epZGI=muNRJCswaSaR
      resource_group: sleekflow-resource-group-devf9af1d41
      subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
      tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
    text_analytics_credentials: 4a76771e5629485ba34bcdfca9ed3487
    text_analytics_url: https://sleekflowtextanalytics.cognitiveservices.azure.com/
  application_insights:
    is_telemetry_tracer_enabled: "true"
    is_sampling_disabled: "false"
  beamer:
    api_key: ""
    api_url: ""
    webhook_verify_key: ""
  chat_api:
    api_key: 0XW6FDDqVIVBQt3Am9SQkmb8O+R+tIasu9hJyUUaUVQ=Ps2RR3hVzVvIslCu
    api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
  commerce_hub:
    endpoint: https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io/v1/commerce-hub
    key: 5eA9/Qnzr2qBGOWwW+Kg1FemdGObglgi89bwoEjYASIsyk+37UOXoVmT4VZXGIsceHd0tsKaceNJcWIWnbPR8xyOARZ8zm/wwummLKDw0CyGySlyeVjrTGOxwT9B0OBuyqT5Drd0d0AaB3sTFU00mDu8wqx+f8znBgrf2wb9C+aur0aJ39j5c89xdtvYXX6bO6ZXkKN6R8Xv=E=n
  crm_hub:
    endpoint: https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io/v1/crm-hub
    key: HwxgKy8jP5SyHk2nm7WNw2IkB9a2GdOmr6fdVYszn1jlpg08PUVh7CkmLQ+BFydX7UOXSDW16ATcJOIR
  data_snapshot:
    is_enable: "false"
  development:
    redirect_fb_webhook: https://9633-42-200-140-118.ngrok.io/facebook/Webhook
  environment_features:
    is_recurring_job_enabled: "false"
  epplus:
    excel_package:
      license_context: NonCommercial
  facebook:
    client_id: 812364635796464
    client_secret: 26tXPo3PfIznNwYHlNvNIlUIRsejECoE90KLil1/+1sMmhP9rhKV/AOO6c5fFnOVSXwbSSMLledlCR9f
  ffmpeg:
    ffmpeg_exe_name: /usr/bin/ffmpeg
  flow_hub:
    endpoint: https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io/v1/flow-hub
    key: v/UnZMUH1em3VPT9INTcsWoH6+O1FRmb8wHk0EwpPUFxg7DjSGLjFbzqy7ChxIq98SWXyXOshaCpkJ0pAcB45+AHJd7bcIi9izkGAOvN+dQ=YOqpYpYMIsWkfSUH
  geo_sql_db:
    name: travis-crm-prod-db
  hub_spot:
    internal_hub_spot_api_key:
    is_enable: "false"
  instrumentation_engine_extension_version: disabled
  internal_google_cloud:
    credential:
  general_google_cloud:
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    project_id: cool-phalanx-404402
    server_location: asia-east1
    google_storage_bucket_name: sleekflow-transcoder-dev
  ip_look_up:
    key: 6d7GALrqX6vYjlt5DOlHqy9TTkPxv7bqIPrHx6SktRlmV99oW8V0vG411WNU+ftWGHQSluOJZCL0pjCw
  intelligent_hub:
    endpoint: https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io/v1/intelligent-hub
    key: keBxlRGsf53SSlyU3hUT3rflOeOBvrH+WEXMD8MobxllIQTwYPxX/GmmlnShSsFGDWFm8ZOPc4P/1n2r9yP9mh/c7KX1bt4a/KQpVVlV+ok=J2t7cOE4dS5qehDd
  webhook_hub:
    endpoint: https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io/v1/webhook-hub
    key: uizl7DQazdOoxXZH6uT/CMpVVzB+ueeJagF8B3YhndPMKqI441uAs2tZDCxqp6TkcqctCovUPM67Wr/W15j1uDIv3GEiKa+WxfnoQbf8iUU=EQ4YwkHHYzV7PJx=
    auth_secret_key: IO3SfpwkG7lPkvhMTsFipgsAe5Qo6hOQRNdV31lZCL/N4DXhqH457O/AU4e6SyzWeTVp/OUJ9buvM8SyJSMDpblANBk9KuTCZA4Y/1B05mI=52MUNrZzOeljmvUW
  message_hub:
    endpoint: https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io/v1/messaging-hub
    key: gx+goREkKuxDqlESaqVk5k7Pd8xh1SdGIFyGsdz2bvs+mMBMUBAivg7MOPjnq8cRDZMraaMVoRIcXhHd
  mixpanel:
    token: f1fdcc653c49d57f8417633000b36626
  mobile_app_management_extension_version: latest
  notification_hub:
    connection_string: Endpoint=sb://sleekflowtesting.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=l5XR33aDeefA79lau7IjY252oJRpdB/h4pP6v3F6hVY=
    hub_name: sleekflowTesting
  public_api_gateway:
    endpoint: https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io/v1/public-api-gateway
    key: 8nXQTNkhLqZo9tk7ZQKKVNc0SWr40QxdCA9XHC365ewflRgnykZHOcWas5D/+eafFPxsDWyzGwWPrFfN
  redis:
    connection_string: sleekflow-sp-uat.redis.cache.windows.net:6380,password=Ld6bAceqgYpA64Qu5B7QzFqx3GhRZolAKAzCaB5f6Fw=,ssl=True,abortConnect=False
  reseller:
    domain_name: https://partner-uat.sleekflow.io/
  rewardful:
    api_secret: q1v3iPx8M3vMkaivWYch7qjZsnm5naSzO13bbLbOvLjYsH6ji0hmIuGYjk261ugi=UVkHnSF3qCTlRPs
  salesforce:
    custom_active_web_app: https://dev-sfmc-custom-activity.vercel.app
  share_hub:
    endpoint: https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io/v1/share-hub
    key: MnzdkGQIeY+jFxvxFcmRYtg/UF80AHLxZMOA32dqvDX1N4UFDw4AHHHwnUH1QzZBDl3zYWx7GKDzSlo7
  shopify:
    shopify_api_key: 47s2p2WAmNEh+JjVfpLsrsed6sxD+KbD412iF4fi2eG5yu8TyQMdbJLOF1oTwtuWjqq0nabwoGwtlkFZ
    shopify_secret_key: pWZLFCZ8YF8yzObpKa5VaYuJt0sXoCT/hBc7IZ1cy9DCTkK0npSkb4qh1dA6FklY=mTGFoL33AE1fHTV
  sleek_pay:
    domain_name: https://pay-eas-dev.sleekflow.io
    shopify_graphql_admin_api_version: 2022-10
    stripe:
      public_keys:
        default: l1syDwRmQnD7q0edaoaYDygtzcxzynjgJy/eLRx8pHE7jo0m/X6PRcCdrRRUPB0IWQ0J/4+NQr6DO1ZFubOzZNZGDMeQEU9NzHEGxFhXjjo0g1NkbyrlTu2KV5sx090477BzYIKt9xS7AdiQB4tq5g==Jhtm2KZ35EbH1QSr
        gb: xz4MiGC97PXHAqZh4h5+BAZ6yJoB6ZFd4fHpcK9EuO42n8kSGVbIGEs265zhCEt+Rg88Tr6WWfE/bP9l6B1yKEXoJgxmAHy/EwEy3sYYG6rKSb5B7TQLp9Ep0SM+xgHKK058z2GI5jyTwVXFfluBLA==TJHk0cZWRxZi3hSS
        hk: vAJRHvhVU+C919JADELbjlURyqTIJRMoI86wIcQe0YK1Oaw7v1/ny27IwgUXcIxK8UOmlo8Zzj5WLV8pU0xxkHBus23BWRvI+SLJSGizU02fFPVj0m2pLDVihU1dtVHWeg4Rhqlmyf8JmiYvfIuWWQ==xVOBYcw0UEyPdRLV
        my: cGrY9M3BSLAPYDPMAPVahwLYBnATyeI/KWeQ7+EKPsMNp5o+pUqJGOKkWIC59TUvPLhsDAGzpDUXT6Y7kjsU2gxge+iKqRHiX/K14zrCmiXjNiUEz6AI5yDhFQCw9Bd9Ki2c4Y5AJrEb+xE7T8duCQ==0HZAupiSc2joqWpT
        sg: 1rfpsoC1Icn3Cghpz2xAOrsXUacZOWnPIYUJl08kgQbVUF0NeAiOdwHTHGx6sCVeQRVQO1MyqYaNnCCWYd54pDQ1UTfoOECbTJb+pLv0akqTuwaYPRol/Id28zGPAZhN54QXPfqFGvM4iLoqxRyLvg==1Rfr6gliynf5sJ53
      secret_keys:
        default: xYJyP3t+L4LEMj2XYqgGK73jNJruxbMN6enqKs+C6AVVI9k/7uSobUSErnYGl+A4b9nsRT3YL8w/U6Z3kEVkkB0rqv3GNdyANerpAYsmjHuYsGyvk1capcYF0JHKXHeCZnq2aXvMdIiG81dY6NqDMA==3jI33d2yyIeuPZEC
        gb: fDz78fGfMU+SMz4iNuYd8Bg9zDZRxGaYmc18x0YEQm9tVcKFjSxJQacJbik2uC8NOkpUmJl6lmr59HyALP1GRyOga1Hq0HJrL56tAw51UuMdV2dCXQ/M5zJMrgQ1aAKN7OE6uuJ4B6mV5JKD9CNp5A==kGhGgmEaWzwdJ=iQ
        hk: UAToXJZ085rZJlPqtNCJgpzIvDEFcWTvmBzsBOcbxg98se59wN2QG9z1EDEmhEr7/ht2ciMXTRq6ua1Unso5y9lE0mwcZAR8hfpdvpPGZJc8iNLuIJS+iPtNu50hGcME4JttmaMuY7PJlnRWh9o0KA==NM=tktLUjWGSXoy3
        my: EvQyz8e0xuIpINAEy3yoN7YVc86iSCnXgTWABJYkZpxIYqVoxSfUcOFBJn8oJnpMxI+ZYbmFO+GirifL0dozZARh6Z5jKFP69ie2QI4zIHWXzsETPyjcXiohsLZB5E1U0n0ILs8DLNMGhkB8jtc7Cw==bfnjEGGb8=lcpANX
        sg: MQ+gaEuZ/6ExGYQZJ6NvgUdt9WJxjibvEwKySdBVYhho/+CMmUHj/t4xlXPLJz1hDQEqjyKZuMEFP1mbGtro8GQqxqG5w/k+QKo7HU9q0nINnn6Mjbp6/YflhagNsEJp6gToSByf8LsJoWvcMrCkvA==359XPHWjB57elzJi
      connect_webhook_secrets:
        gb: IY1KayGvuDo8MaY+HDJQVH9YLDjpgL8Q8VJ7uxOFEFWG5JOt4vrIx8xvCvRdv05dp7FRhEcVvoxVnbmx
        hk: nCqYAS/wpYW/jkGKEpFhPjEIQG+fQfOF4+cAIpNka/9BLuxxVAhN49Y3A8wRm7bzk3V1kemBEYQIWoPL
        my: tasdBhu4hmEDvh6VQ3TYQEK1GJ29KJ92zBoFX3IqReMadYPWhlo4YqBS8pucmzs29GqXUWbH5LQXZ0=7
        sg: W8sVLhj8o94VIpP0OyNFWxziJyx+zbwRXOdV4XVy10eEySoeJmOkzpnVHEFd47ts2daQlyVOaj0JEzD=
      report_webhook_secrets:
        gb:
        hk:
        my:
        sg:
      webhook_secrets:
        default: czRXTdZc6lGymqU8FXj8Uw+dhh3Lbev6grDAXjw2AB0yglWxEasbzfjhEJ+wq3ADNDGMPwAeoWOw6Z24
        gb: f7HXjfx/ZnrY3V60RvCptuRU0k0hjG7ZjiPuuFU9cPGJMvR+eMbH4GyvLKIU95EF63ukiVzRCZHnlwma
        hk: LN4RREtN9OlxDcdMkwOf5CS4G2eYTjVvCIeawMHSiZBWhAYlkhNiLRtiZUcBhb8JWA3G176EpFAIQOlY
        my: L8XQnO6AFJvhU07YtKopiDW9lPfNr13czOLzYkk1MPYIzikwKWFBVqVGRM3w+n6AmXGhyPKhAtzwjNvS
        sg: xVKeqYR8JQmflaKvKPxNjFt2GnWRX/wIYpFZKCXFcjXkaZ7Syd3l7GTLCTmzajY2s1PT9uQSN7YTMd=m
  snapshot_debugger_extension_version: disabled
  sql_performance:
    from_raw_sql: "true"
    is_and_condition_enabled: "true"
    is_or_condition_enabled: "true"
    is_conversation_analytics_condition_enabled: "true"
    is_shopify_order_statistics_enabled: "true"
    is_sales_performance_enabled: "true"
    is_public_api_new_upsert_enabled: "false"
    is_zapier_new_upsert_enabled: "false"
  stripe:
    stripe_public_key: owAp7QQb4byH/XYp6yxkw3A4FEcuz2cEhRAr+Gb7VlnqIxpmsDOcdlvtU01SdVYbPgUKtGELiouC1qXr
    stripe_report_key: 8E257jSWfDU11S+OpaB/75jkeOOVG0MDR6KAyeNZ6nBaVmPt61c6UuxYeGWv/G9QE51P/vn09D61CNHKaq3B+9mh0ZoD/188EzXb9+iophDrFsyuu5+tW4rqmAEQ67ZLqnj6GVwrp5eXbCm5ohcfng==dMScVVgkKCX5WfYB
    stripe_secret_key: 2hQ6s4+xM5mOBoae6+K7UpEYx1hoYIKhw6NhIhtoTi4Pt0dCsWc9eTSMSYa71vaDvEKN3H9ZmBQBr=FW
    stripe_webhook_secret: Swue/GLmzijFtPmOwucveo0MPd05BaJgLP5vWpHAvZnka0pOed2DiEWOBb22XeP+uk78hgJyXRdNiUVs
  stripe_payment:
    stripe_payment_secret_key_gb: LDm1ll1ja935h2WHiKRjknRnR1traVMDuN9rzVgBJjpWLYEQuPIKavPJAeP+93Ffb78Kfg7L7afm/h6Ct9aNTt2GQzMbdPJ2+1lBnSz8520epYg7TCnYmK840bc/kq5yArYZb2AGCBbmWU81XDQh0g==ct8NNypMj0fx2Sp0
    stripe_payment_secret_key_hk: 7fzK4ufjYLHNfctpdb+XOWJ46tuSnKkjCKUI5QKEU9lrTPeOsnRLdR2MF9YoOHzfmtz+kBImGsAJzrkYXDR+9HGJlbB7cqrwYJR5yO5IdUSWWldxEZJSnyAjzm3Rt/h2aOkBCzWE6OR9DUxjYhvtgg==d1KYuoclOO4P9wea
    stripe_payment_secret_key_my: GDI4H0C1Cd9vPSgMHYLRrfcOwafmX4qD2W1gERpTjm8JhQfufHAgwMaKxWFpnvKjb7UhxYrefBWxg/aAwOun0M9kd99LQtc5vdIxjqpZTJ3wlnh68s2St12kQ0U3vAsW/W3wPFCvmayxcOratG23mw==AXLDy9QPEcEy9e28
    stripe_payment_secret_key_sg: 4j6AJ3RyjA0owtnUeYe0jN15L81yDmjMyyqXUd0OI4GB6o3hPETNXv0h4WLKY7TwrK6pYOk65wHDLmuN1DKZqeJXVpLPEIq497h3t8okgdBBxnzGKMIJ13MyyMFyd0orn/LMsdxidCZydD0DcG0ywg==NeK1Qp1hTt1pioer
  stripe_report:
    stripe_report_webhook_secret_gb:
    stripe_report_webhook_secret_hk:
    stripe_report_webhook_secret_my:
    stripe_report_webhook_secret_sg:
  tenant_hub:
    endpoint: https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io/v1/tenant-hub
    key: q0uE4nkAiseXFdnw11o8bTYqwHd9AVK6bm/qKYKbRFabZ3fb3T6F+LhJQ2+SSeoAONx4JvqmsJYaOoTeAB2DKQ87lWjsZK/n5AWUMeWBa2o=PnGPPDx27CgUBR3v
    is_enable_tenant_logic: "true"
  ticketing_hub:
    endpoint: https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io/v1/ticketing-hub
    key: ZSUGNZHM7BVj1sCkuOUvOTfr8bGuS07vPuucVlGqI9Ye5ZDZh/V5b5bdxKRSMdslEcfsPfgYHEKfoFiTiknH8CekPUrlBiT38H0+xlXrLx4=acW53D343opDuoIe
    is_enable_ticketing_logic: "false"
  test_swaping: 1
  token:
    audience: https://sleekflow-prod-api.azurewebsites.net
  tokens:
    audience: https://uat.sleekflow.io
    issuer: https://uat.sleekflow.io
    key: yfYzEkbjShUH6L5B61aVYOezBc05KtmxOpqnrI1f6yVrFw1HHFTaGdKzqZKUG2GxJJEmOSeGaTONAtfHpgZ0s9/QnuiV4cnjbN2gnPhageg=d=99eqpvrOoBdTp2
    lifetime: 365
  user_event_hub:
    endpoint: https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io/v1/user-event-hub
    key: M4VzL5FVoieL7otCxsIADTcEH5ihVi+xoAZhltWCOzM5PuD1yX/2zA0qDLJT9d3Mobg+kXwCZR+EwfQQfJznhNuzJxfRPx2q9ho3hnGim6U=FXLSCdmbEz2G0=9=
  values:
    app_domain_name: https://dev.sleekflow.io
    app_domain_name_v1: https://v1-dev.sleekflow.io
    app_domain_name_v2: https://dev.sleekflow.io
    domain_name: https://sleekflow-core-app-eas-dev.azurewebsites.net
    share_link_function: https://sleekflow-share-tracking-uat.azurewebsites.net
    sleekflow_api_gateway: https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io
    sleekflow_company_id: b6d7e442-38ae-4b9a-b100-2951729768bc
    sleekflow_public_api_url: ""
    sleekflow_public_api_key: ""
    sleekflow_company_should_use_public_api: "false"
  website_http_logging_retention_days: 2
  website_node_default_version: 6.9.1
  whatsapp_cloud_api_template:
    default_image_blob_id: sleekflow.png
  xdt_microsoft_application_insights_base_extensions: disabled
  xdt_microsoft_application_insight_mode: recommended
  global_pricing:
    is_feature_enabled: "true"
    plan_migration_incentives_start_date: "2024-10-20"
    plan_migration_incentives_end_date: "2025-02-20"
  feature_flags:
    - feature_name: FlowBuilderMonetisation
      is_enabled: "true"
    - feature_name: CancelledSubscriptionTermination
      is_enabled: "true"
    - feature_name: Rbac
      is_enabled: "true"
  contact_safe_deletion:
    is_feature_enabled: "true"
  partner_stack:
    public_key: twU1vXSLX09jl+q52Qp9ewe6HFTj9ZQS2hazTCw7pBG/2NDvfRLlj456OdrkhdT+N9Ocmyi8YRE0Hx1j
    secret_key: w7QlPpYf68gSCiTTbd/KT+PbrzQLii8//NBmXGsF+OZXCye8hssQESNVqZnKqL1nVtUFqZH6kd0=VBkc
  hangfire_worker:
    worker_count: 20
  integration_alert:
    endpoint: "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net/api/notifications/integration-disconnected"
    api_key: "o88200QCIcSqkI9U8S3Jhl54K1Oa1tyr3qQLAUVurOcwbkvIBTzRP/tvjK6F4TVxZVNATcEwLNTO7WeS"
    host_company_id: "b6d7e442-38ae-4b9a-b100-2951729768bc"
    from_phone_number: "18454069890"
    template_name: "integration_disconnect_noti"
    facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"
  internal_integration_hub:
    endpoint: "https://sleekflow-apigw-app.graydesert-391172fb.eastasia.azurecontainerapps.io/v1/internal-integration-hub"
    key: qmVkloEGT1ACxg8OYoJDZxT/W0JUv87c9JRHJ6d0/ljnVhxLkTVnW/LkkmoWsSEJWDWOu0DrX8gpl/7zDV/C+znpAuLWzvEXn+IuwiwcnJI=tE05kn4yfLHMYult
  hangfire_queues:
    disable_instances: ""
  rbac:
    is_middleware_verification_enabled: "true"
  flow_builder_flow_enrollments_incentives:
    period_start: "2025-01-24"
    period_end: "2025-04-24"
  hub_spot_smtp:
    username: "<EMAIL>"
    password: "CPPcMor4CQHcJ8nqBtw8cVwcBnvB04"
    send_execution_usage_reached_threshold_email:
      username: "<EMAIL>"
      password: "RugY2rVS7IeQbw91lAeUq7FUw9Pgqy"
  legacy_premium_opt_in_upgrade_incentives:
    period_start: "2025-02-10"
    period_end: "2025-06-10"
  live_chat:
    secret_key: "crG1BYC/fGQjp/FC1X3rmm8Xk9uVUsNRTHFPGadTME0nABk/gqvQC8BToKymtHdMJjOK2Cq1nupaXF47"
