using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using HtmlAgilityPack;
using Microsoft.Extensions.Logging;

namespace Travis_backend.Utils;

public class HtmlMetadataFetcher
{
    public class HtmlMetadata
    {
        public string Url { get; set; }

        public string Title { get; set; }

        public string Description { get; set; }

        public string Image { get; set; }
    }

    public static async Task<HtmlMetadata> GetPageMetadata(string url, ILogger logger)
    {
        var metadata = new HtmlMetadata { Url = url };
        var web = new HtmlWeb(); // HtmlAgilityPack's web loader

        try
        {
            // Load the HTML document from the URL asynchronously
            HtmlDocument doc = await web.LoadFromWebAsync(url);

            // 1. Get Page Title with priority: og:title > twitter:title > <title> tag
            var ogTitleNode = doc.DocumentNode.SelectSingleNode("//meta[@property='og:title']");
            if (ogTitleNode != null && !string.IsNullOrEmpty(ogTitleNode.GetAttributeValue("content", string.Empty)))
            {
                metadata.Title = ogTitleNode.GetAttributeValue("content", string.Empty).Trim();
            }
            else
            {
                var twitterTitleNode = doc.DocumentNode.SelectSingleNode("//meta[@name='twitter:title']");
                if (twitterTitleNode != null && !string.IsNullOrEmpty(twitterTitleNode.GetAttributeValue("content", string.Empty)))
                {
                    metadata.Title = twitterTitleNode.GetAttributeValue("content", string.Empty).Trim();
                }
                else
                {
                    var titleNode = doc.DocumentNode.SelectSingleNode("//title");
                    if (titleNode != null)
                    {
                        metadata.Title = titleNode.InnerText.Trim();
                    }
                }
            }

            // 2. Get Description with priority: og:description > twitter:description > meta description
            var ogDescriptionNode = doc.DocumentNode.SelectSingleNode("//meta[@property='og:description']");
            if (ogDescriptionNode != null && !string.IsNullOrEmpty(ogDescriptionNode.GetAttributeValue("content", string.Empty)))
            {
                metadata.Description = ogDescriptionNode.GetAttributeValue("content", string.Empty).Trim();
            }
            else
            {
                var twitterDescriptionNode = doc.DocumentNode.SelectSingleNode("//meta[@name='twitter:description']");
                if (twitterDescriptionNode != null && !string.IsNullOrEmpty(twitterDescriptionNode.GetAttributeValue("content", string.Empty)))
                {
                    metadata.Description = twitterDescriptionNode.GetAttributeValue("content", string.Empty).Trim();
                }
                else
                {
                    var descriptionNode = doc.DocumentNode.SelectSingleNode("//meta[@name='description']");
                    if (descriptionNode != null && !string.IsNullOrEmpty(descriptionNode.GetAttributeValue("content", string.Empty)))
                    {
                        metadata.Description = descriptionNode.GetAttributeValue("content", string.Empty).Trim();
                    }
                }
            }

            // 3. Get Image with priority: og:image > twitter:image > favicon/icon
            var ogImageNode = doc.DocumentNode.SelectSingleNode("//meta[@property='og:image']");
            if (ogImageNode != null && !string.IsNullOrEmpty(ogImageNode.GetAttributeValue("content", string.Empty)))
            {
                metadata.Image = ogImageNode.GetAttributeValue("content", string.Empty).Trim();
            }
            else
            {
                var twitterImageNode = doc.DocumentNode.SelectSingleNode("//meta[@name='twitter:image']");
                if (twitterImageNode != null && !string.IsNullOrEmpty(twitterImageNode.GetAttributeValue("content", string.Empty)))
                {
                    metadata.Image = twitterImageNode.GetAttributeValue("content", string.Empty).Trim();
                }
                else
                {
                    // Fallback: look for a rel="icon" or rel="shortcut icon" for a favicon
                    var iconNode = doc.DocumentNode.SelectSingleNode("//link[@rel='icon' or @rel='shortcut icon']");
                    if (iconNode != null && !string.IsNullOrEmpty(iconNode.GetAttributeValue("href", string.Empty)))
                    {
                        string iconUrl = iconNode.GetAttributeValue("href", string.Empty).Trim();
                        if (!Uri.IsWellFormedUriString(iconUrl, UriKind.Absolute))
                        {
                            // Make sure the icon URL is absolute
                            if (Uri.TryCreate(new Uri(url), iconUrl, out Uri absoluteUri))
                            {
                                iconUrl = absoluteUri.ToString();
                            }
                        }
                        metadata.Image = iconUrl;
                    }
                }
            }
        }
        catch (HttpRequestException ex)
        {
            logger.LogError($"HTTP Request Error fetching {url}: {ex.Message}");
            return null;
        }
        catch (Exception ex)
        {
            logger.LogError($"An error occurred fetching {url}: {ex.Message}");
            return null;
        }

        return metadata;
    }
}