using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ChannelDomain.Services.LiveChatV2;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.ChannelDomain.ViewModels.LiveChatV2;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.ViewModels;

namespace Travis_backend.Controllers.LiveChatControllers;

[Route("[controller]")]
[Authorize]
public class LiveChatV2Controller : Controller
{
    private readonly ILiveChatV2SenderService _liveChatV2SenderService;
    private readonly ILiveChatV2MessageService _liveChatV2MessageService;
    private readonly ILiveChatV2ConfigService _liveChatV2ConfigService;
    private readonly ILiveChatV2TrackingService _liveChatV2TrackingService;
    private readonly ILiveChatV2AuthManager _liveChatV2AuthManager;
    private readonly ICoreService _coreService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILogger<LiveChatV2Controller> _logger;

    private readonly IMapper _mapper;

    public LiveChatV2Controller(
        ILiveChatV2SenderService liveChatV2SenderService,
        ICoreService coreService,
        UserManager<ApplicationUser> userManager,
        ILogger<LiveChatV2Controller> logger,
        ILiveChatV2ConfigService liveChatV2ConfigService,
        ILiveChatV2MessageService liveChatV2MessageService,
        IMapper mapper,
        ILiveChatV2TrackingService liveChatV2TrackingService,
        ILiveChatV2AuthManager liveChatV2AuthManager)
    {
        _liveChatV2SenderService = liveChatV2SenderService;
        _coreService = coreService;
        _userManager = userManager;
        _logger = logger;
        _liveChatV2ConfigService = liveChatV2ConfigService;
        _liveChatV2MessageService = liveChatV2MessageService;
        _mapper = mapper;
        _liveChatV2TrackingService = liveChatV2TrackingService;
        _liveChatV2AuthManager = liveChatV2AuthManager;
    }

    #region Config
    [HttpPost]
    [Route("Configs")]
    public async Task<IActionResult> CreateLiveChatV2Config()
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser is null)
        {
            return Unauthorized();
        }

        var input = new CreateLiveChatV2ConfigInput(companyUser.Company);

        try
        {
            await _liveChatV2ConfigService.CreateConfigAsync(input);
            return Ok(new ResponseViewModel { message = "LiveChat V2 configuration created successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Failed to create LiveChat V2 config. CompanyId: {CompanyId}. Exception: {ExceptionMessage}",
                nameof(CreateLiveChatV2Config),
                companyUser.CompanyId,
                ex.Message);
            return BadRequest(new ResponseViewModel { message = ex.Message });
        }
    }

    [HttpPut]
    [Route("Configs/{Id}")]
    public async Task<IActionResult> UpdateLiveChatV2Config([FromRoute] string id, [FromForm] UpdateLiveChatV2ConfigInput input)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser is null)
        {
            return Unauthorized();
        }

        try
        {
            await _liveChatV2ConfigService.UpdateConfigAsync(id, input, companyUser.CompanyId);
            return Ok(new ResponseViewModel { message = "LiveChat V2 configuration updated successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Failed to update LiveChat V2 config. Id: {Id}, CompanyId: {CompanyId}. Exception: {ExceptionMessage}",
                nameof(UpdateLiveChatV2Config),
                id,
                companyUser.CompanyId,
                ex.Message);
            return BadRequest(new ResponseViewModel { message = ex.Message });
        }
    }

    #endregion
    [HttpGet]
    [Route("Public/Senders/{senderId}")]
    [AllowAnonymous]
    // [LiveChatV2SignatureValidate]
    public async Task<IActionResult> GetLiveChatV2PublicSender([FromRoute] string senderId)
    {
        try
        {
            var output = await _liveChatV2SenderService.GetPublicSenderAsync(senderId);
            if (!output.IsSuccess)
            {
                return BadRequest(new ResponseViewModel { message = output.Message });
            }

            return Ok(new GetLiveChatV2SenderResponse
            {
                Sender = output.Sender
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Failed to get LiveChat V2 public sender. SenderId: {SenderId}. Exception: {ExceptionMessage}",
                nameof(GetLiveChatV2PublicSender),
                senderId,
                ex.Message);
            return BadRequest(new ResponseViewModel { message = ex.Message });
        }
    }

    [HttpPost]
    [Route("Public/Senders")]
    [AllowAnonymous]
    public async Task<IActionResult> CreateLiveChatV2Sender([FromBody] CreateLiveChatV2SenderRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        // This will now correctly return the original client IP, even behind a proxy.
        var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
        var input = new CreateLiveChatV2SenderInput(request.LiveChatId, request.CompanyId, request.Metadata, ipAddress);

        try
        {
            var output = await _liveChatV2SenderService.CreateSenderAsync(input);
            if (!output.IsSuccess)
            {
                return BadRequest(new ResponseViewModel { message = output.Message });
            }

            var signature = _liveChatV2AuthManager.GenerateSignature(output.Id);
            return Ok(new CreateLiveChatV2SenderResponse(output.Id, output.LiveChatId, output.CompanyId, output.Metadata, signature));
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Failed to create LiveChat V2 sender. LiveChatId: {LiveChatId}, CompanyId: {CompanyId}. Exception: {ExceptionMessage}",
                nameof(CreateLiveChatV2Sender),
                input.LiveChatId,
                input.CompanyId,
                ex.Message);
            return BadRequest(new ResponseViewModel { message = ex.Message });
        }
    }


    [HttpPatch]
    [Route("Public/Senders/{senderId}")]
    // [LiveChatV2SignatureValidate]
    [AllowAnonymous]
    public async Task<IActionResult> UpdateLiveChatV2Sender([FromRoute] string senderId, [FromBody] UpdateLiveChatV2SenderRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var input = new UpdateLiveChatV2SenderInput(senderId, request.Metadata);

        try
        {
            var output = await _liveChatV2SenderService.UpdateSenderAsync(input);
            if (!output.IsSuccess)
            {
                return BadRequest(new ResponseViewModel { message = output.Message });
            }

            return Ok(new UpdateLiveChatV2SenderResponse(output.Id, output.LiveChatId, output.CompanyId, output.Metadata));
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Failed to update LiveChat V2 sender. SenderId: {SenderId}. Exception: {ExceptionMessage}",
                nameof(UpdateLiveChatV2Sender),
                senderId,
                ex.Message);
            return BadRequest(new ResponseViewModel { message = ex.Message });
        }
    }

    [HttpPost]
    [Route("Public/Senders/{senderId}/Channels/{channelIdentityId}/Messages")]
    [Consumes("multipart/form-data")]
    // [LiveChatV2SignatureValidate]
    [AllowAnonymous]
    public async Task<IActionResult> SendLiveChatV2Message(
        [FromRoute] string senderId,
        [FromRoute] string channelIdentityId,
        [FromForm] LiveChatV2ExtendedConversationMessageViewModel request)
    {
        var input = new SendLiveChatV2MessageInput(senderId, channelIdentityId, request);

        try
        {
            var output = await _liveChatV2MessageService.SendMessageAsync(input);
            if (!output.IsSuccess)
            {
                return BadRequest(new ResponseViewModel { message = output.Message });
            }

            // Can be refactored here to a new mapper class
            var messageViewModel = _mapper.Map<ConversationMessageResponseViewModel>(output.MessageDto);

            return Ok(messageViewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Failed to send LiveChat V2 message. SenderId: {SenderId}, ChannelIdentityId: {ChannelIdentityId}. Exception: {ExceptionMessage}",
                nameof(SendLiveChatV2Message),
                senderId,
                channelIdentityId,
                ex.Message);
            return BadRequest(new ResponseViewModel { message = ex.Message });
        }
    }

    [HttpGet]
    [Route("Public/Senders/{senderId}/Channels/{channelIdentityId}/Messages")]
    // [LiveChatV2SignatureValidate]
    [AllowAnonymous]
    public async Task<IActionResult> GetLiveChatV2Messages(
        [FromRoute] string senderId,
        [FromRoute] string channelIdentityId,
        [FromQuery(Name = "offset")]
        int offset = 0,
        [FromQuery(Name = "limit")]
        int limit = 10,
        [FromQuery(Name = "order")]
        string order = "asc")
    {
        var input = new GetLiveChatV2MessagesInput(senderId, channelIdentityId, offset, limit, order);

        try
        {
            var output = await _liveChatV2MessageService.GetMessagesAsync(input);
            if (!output.IsSuccess)
            {
                return BadRequest(new ResponseViewModel { message = output.Message });
            }

            // Can be refactored here to a new mapper class
            var messageViewModels = _mapper.Map<List<ConversationMessageResponseViewModel>>(output.MessageDto);

            messageViewModels.ForEach(
                message =>
                {
                    if (message.Sender != null)
                    {
                        message.Sender =
                            SensitiveInformationHelper.RemoveSensitiveInformationFromUserInfo(message.Sender);
                    }
                });

            return Ok(messageViewModels);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Failed to get LiveChat V2 message. SenderId: {SenderId}, ChannelIdentityId: {ChannelIdentityId}. Exception: {ExceptionMessage}",
                nameof(GetLiveChatV2Messages),
                senderId,
                channelIdentityId,
                ex.Message);
            return BadRequest(new ResponseViewModel { message = ex.Message });
        }
    }

    [HttpGet]
    [Route("Public/Configs")]
    [AllowAnonymous]
    public async Task<ActionResult<LiveChatV2ConfigDto>> GetLiveChatV2Config([FromQuery] string channelIdentityId)
    {
        if (string.IsNullOrEmpty(channelIdentityId))
        {
            return BadRequest(new ResponseViewModel { message = "ChannelIdentityId is required" });
        }

        try
        {
            var config = await _liveChatV2ConfigService.GetConfigByChannelIdentityIdAsync(channelIdentityId);
            return Ok(_mapper.Map<LiveChatV2ConfigDto>(config));
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Failed to get LiveChat V2 config. ChannelIdentityId: {ChannelIdentityId}. Exception: {ExceptionMessage}",
                nameof(GetLiveChatV2Config),
                channelIdentityId,
                ex.Message);
            return BadRequest(new ResponseViewModel { message = ex.Message });
        }
    }

    [HttpPost]
    [Route("Public/Senders/{senderId}/Tracking")]
    // [LiveChatV2SignatureValidate]
    [AllowAnonymous]
    public async Task<IActionResult> Tracking(
        [FromRoute]
        string senderId,
        [FromBody]
        CreateLiveChatV2TrackingEventRequest request)
    {
        var input = new CreateLiveChatV2TrackingEventInput(
            senderId,
            request.ChannelIdentityId,
            request.Name,
            request.StartedAt,
            request.EndedAt,
            request.Metadata);

        try
        {
            var output = await _liveChatV2TrackingService.CreateTrackingEventAsync(input);
            if (!output.IsSuccess)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = output.Message
                    });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = ex.Message
                });
        }
    }

    [HttpGet]
    [Route("Senders/{senderId}")]
    public async Task<ActionResult<GetLiveChatV2SenderResponse>> GetLiveChatV2Sender(
        [FromRoute]
        string senderId)
    {
        var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (companyUser is null)
        {
            return Unauthorized();
        }

        var input = new GetLiveChatV2SenderInput(
            senderId,
            companyUser.CompanyId);

        try
        {
            var output = await _liveChatV2SenderService.GetSenderAsync(input);
            if (!output.IsSuccess)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = output.Message
                    });
            }

            return Ok(
                new GetLiveChatV2SenderResponse
                {
                    Sender = output.Sender,
                    TrackingEvents = output.TrackingEvents
                });
        }
        catch (Exception ex)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = ex.Message
                });
        }
    }
}