using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq;
using Newtonsoft.Json;
using Sleekflow.Powerflow.Apis.Attributes;
using Sleekflow.Powerflow.Apis.ViewModels;
using Sleekflow.Powerflow.Apis.Services;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.CommonDomain.ViewModels;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;

namespace Sleekflow.Powerflow.Apis.Controllers;

[TenantHubAuthorization]
[Route("TenantHub/Internals")] // Align with other hubs: /{Hub}/Internals
public class TenantHubInternalCommandsController : ControllerBase
{
    private readonly ICompanyDetailsService _companyDetailsService;
    private readonly IWorkflowsApi _workflowsApi;

    public TenantHubInternalCommandsController(ICompanyDetailsService companyDetailsService, IWorkflowsApi workflowsApi)
    {
        _companyDetailsService = companyDetailsService;
        _workflowsApi = workflowsApi;
    }

    public record GetCompanyDetailsInput(string CompanyId);

    public class SimplifiedGetAllCompanyDetailsOutput
    {
        [JsonProperty("companyBillRecord")]
        public SimplifiedGetCmsCompanyBillRecordsResponse CompanyBillRecord { get; set; }

        [JsonProperty("workflowCounts")]
        public SimplifiedWorkflowCounts WorkflowCounts { get; set; }
    }

    public class SimplifiedWorkflowCounts
    {
        [JsonProperty("num_of_normal_workflows")]
        public int NumOfNormalWorkflows { get; set; }

        [JsonProperty("num_of_normal_active_workflows")]
        public int NumOfNormalActiveWorkflows { get; set; }

        [JsonProperty("num_of_ai_workflows")]
        public int NumOfAiWorkflows { get; set; }

        [JsonProperty("num_of_ai_active_workflows")]
        public int NumOfAiActiveWorkflows { get; set; }
    }

    public class SimplifiedGetCmsCompanyBillRecordsResponse
    {
        [JsonProperty("billRecords")]
        public List<SimplifiedCmsBillRecordDto> BillRecords { get; set; } = new();

        [JsonProperty("initialPaidDate")]
        public string InitialPaidDate { get; set; }
    }

    public class SimplifiedCmsBillRecordDto
    {
        [JsonProperty("id")]
        public long Id { get; set; }

        [JsonProperty("subscriptionPlanId")]
        public string SubscriptionPlanId { get; set; }

        [JsonProperty("subscriptionPlanName")]
        public string SubscriptionPlanName { get; set; }

        [JsonProperty("subscriptionPlan")]
        public SimplifiedSubscriptionPlan SubscriptionPlan { get; set; }

        [JsonProperty("periodStart")]
        public DateTime PeriodStart { get; set; }

        [JsonProperty("periodEnd")]
        public DateTime PeriodEnd { get; set; }

        [JsonProperty("payAmount")]
        public double PayAmount { get; set; }

        [JsonProperty("currency")]
        public string Currency { get; set; }

        [JsonProperty("quantity")]
        public long Quantity { get; set; }

        [JsonProperty("paymentBreakDowns")]
        public List<string> PaymentBreakDowns { get; set; }

        [JsonProperty("invoice_Id")]
        public string invoice_Id { get; set; }

        [JsonProperty("stripe_subscriptionId")]
        public string stripe_subscriptionId { get; set; }

        [JsonProperty("customerId")]
        public string customerId { get; set; }

        [JsonProperty("customer_email")]
        public string customer_email { get; set; }

        [JsonProperty("hosted_invoice_url")]
        public string hosted_invoice_url { get; set; }

        [JsonProperty("invoice_pdf")]
        public string invoice_pdf { get; set; }

        [JsonProperty("chargeId")]
        public string chargeId { get; set; }

        [JsonProperty("amount_due")]
        public long amount_due { get; set; }

        [JsonProperty("amount_paid")]
        public long amount_paid { get; set; }

        [JsonProperty("amount_remaining")]
        public long amount_remaining { get; set; }

        [JsonProperty("isFreeTrial")]
        public bool IsFreeTrial { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonProperty("paymentStatus")]
        public string PaymentStatus { get; set; }

        [JsonProperty("purchaseStaffId")]
        public string PurchaseStaffId { get; set; }

        [JsonProperty("purchaseStaffName")]
        public string PurchaseStaffName { get; set; }

        [JsonProperty("created")]
        public DateTime Created { get; set; }

        [JsonProperty("isDeletable")]
        public bool IsDeletable { get; set; }

        [JsonProperty("upgradeFromBillRecordId")]
        public long? UpgradeFromBillRecordId { get; set; }

        [JsonProperty("downgradeFromBillRecordId")]
        public long? DowngradeFromBillRecordId { get; set; }

        [JsonProperty("updatedAt")]
        public DateTime? UpdatedAt { get; set; }

        [JsonProperty("isIrregularPlan")]
        public bool IsIrregularPlan { get; set; }

        [JsonProperty("paymentIntervalType")]
        public string PaymentIntervalType { get; set; }

        // public List<PaymentSplit> PaymentSplits { get; set; }

        [JsonProperty("paymentTerms")]
        public int? PaymentTerms { get; set; }

        [JsonProperty("subscriptionInterval")]
        public int SubscriptionInterval { get; set; }

        [JsonProperty("noticePeriod")]
        public int? NoticePeriod { get; set; }

        // public List<CmsSalesPaymentRecordDto> CmsSalesPaymentRecords { get; set; } = new ();

        [JsonProperty("metadata")]
        public Dictionary<string, string> Metadata { get; set; }

        [JsonProperty("isAutoRenewalEnabled")]
        public bool IsAutoRenewalEnabled { get; set; } = true;
    }

    public class SimplifiedSubscriptionPlan
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("subscriptionName")]
        public string SubscriptionName { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("amount")]
        public double Amount { get; set; }

        [JsonProperty("maximumContact")]
        public int MaximumContact { get; set; }

        [JsonProperty("maximumMessageSent")]
        public int MaximumMessageSent { get; set; }

        [JsonProperty("includedAgents")]
        public int IncludedAgents { get; set; }

        [JsonProperty("maximumCampaignSent")]
        public int MaximumCampaignSent { get; set; }

        [JsonProperty("maximumChannel")]
        public bool MaximumChannel { get; set; }

        [JsonProperty("extraChatAgentPlan")]
        public string ExtraChatAgentPlan { get; set; }

        [JsonProperty("extraChatAgentPrice")]
        public int ExtraChatAgentPrice { get; set; }

        [JsonProperty("maximumAutomation")]
        public int MaximumAutomation { get; set; }

        [JsonProperty("maximumNumberOfChannel")]
        public int MaximumNumberOfChannel { get; set; }

        [JsonProperty("maximumAPICall")]
        public int MaximumAPICall { get; set; }

        [JsonProperty("currency")]
        public string Currency { get; set; }

        [JsonProperty("stripePlanId")]
        public string StripePlanId { get; set; }

        [JsonProperty("subscriptionTier")]
        public string SubscriptionTier { get; set; }

        [JsonProperty("version")]
        public int Version { get; set; }

        [JsonProperty("countryTier")]
        public string CountryTier { get; set; }

        [JsonProperty("maximumAgentsLimit")]
        public int? MaximumAgentsLimit { get; set; }

        // public List<SubscriptionSpecificFunction> AvailableFunctions { get; set; }

        // public SubscriptionPlanPricingMethod? PricingMethod { get; set; }

        // public ICollection<SubscriptionPlanTierPrice>? TieredPrices { get; set; }
    }

    [HttpPost("Commands/GetCompanyDetails")] // Align with Commands/* pattern
    public async Task<ActionResult<SimplifiedGetAllCompanyDetailsOutput>> GetCompanyDetails(
        [FromBody]
        GetCompanyDetailsInput input)
    {
        GetAllCompanyDetailsResponse result = await _companyDetailsService.GetAllCompanyDetailsAsync(input.CompanyId);
        if (result == null)
        {
            return BadRequest(new ResponseViewModel("Company Not Found"));
        }

        var normalWorkflowCounts = await _workflowsApi.WorkflowsCountWorkflowsPostAsync(
            countWorkflowsInput: new CountWorkflowsInput(input.CompanyId, "normal"));
        var aiWorkflowCounts = await _workflowsApi.WorkflowsCountWorkflowsPostAsync(
            countWorkflowsInput: new CountWorkflowsInput(input.CompanyId, "ai_workflow"));

        var simplified = new SimplifiedGetAllCompanyDetailsOutput
        {
            CompanyBillRecord = new SimplifiedGetCmsCompanyBillRecordsResponse
            {
                InitialPaidDate = result.CompanyBillRecord?.InitialPaidDate,
                BillRecords = (result.CompanyBillRecord?.BillRecords ?? new List<CmsBillRecordDto>())
                    .Select(br => new SimplifiedCmsBillRecordDto
                    {
                        Id = br.Id,
                        SubscriptionPlanId = br.SubscriptionPlanId,
                        SubscriptionPlanName = br.SubscriptionPlanName,
                        SubscriptionPlan = br.SubscriptionPlan == null
                            ? null
                            : new SimplifiedSubscriptionPlan
                            {
                                Id = br.SubscriptionPlan.Id,
                                SubscriptionName = br.SubscriptionPlan.SubscriptionName,
                                Description = br.SubscriptionPlan.Description,
                                Amount = br.SubscriptionPlan.Amount,
                                MaximumContact = br.SubscriptionPlan.MaximumContact,
                                MaximumMessageSent = br.SubscriptionPlan.MaximumMessageSent,
                                IncludedAgents = br.SubscriptionPlan.IncludedAgents,
                                MaximumCampaignSent = br.SubscriptionPlan.MaximumCampaignSent,
                                MaximumChannel = br.SubscriptionPlan.MaximumChannel,
                                ExtraChatAgentPlan = br.SubscriptionPlan.ExtraChatAgentPlan,
                                ExtraChatAgentPrice = br.SubscriptionPlan.ExtraChatAgentPrice,
                                MaximumAutomation = br.SubscriptionPlan.MaximumAutomation,
                                MaximumNumberOfChannel = br.SubscriptionPlan.MaximumNumberOfChannel,
                                MaximumAPICall = br.SubscriptionPlan.MaximumAPICall,
                                Currency = br.SubscriptionPlan.Currency,
                                StripePlanId = br.SubscriptionPlan.StripePlanId,
                                SubscriptionTier = br.SubscriptionPlan.SubscriptionTier.ToString(),
                                Version = br.SubscriptionPlan.Version,
                                CountryTier = br.SubscriptionPlan.CountryTier,
                                MaximumAgentsLimit = br.SubscriptionPlan.MaximumAgentsLimit
                            },
                        PeriodStart = br.PeriodStart,
                        PeriodEnd = br.PeriodEnd,
                        PayAmount = br.PayAmount,
                        Currency = br.Currency,
                        Quantity = br.Quantity,
                        PaymentBreakDowns = br.PaymentBreakDowns,
                        invoice_Id = br.invoice_Id,
                        stripe_subscriptionId = br.stripe_subscriptionId,
                        customerId = br.customerId,
                        customer_email = br.customer_email,
                        hosted_invoice_url = br.hosted_invoice_url,
                        invoice_pdf = br.invoice_pdf,
                        chargeId = br.chargeId,
                        amount_due = br.amount_due,
                        amount_paid = br.amount_paid,
                        amount_remaining = br.amount_remaining,
                        IsFreeTrial = br.IsFreeTrial,
                        Status = br.Status.ToString(),
                        PaymentStatus = br.PaymentStatus.ToString(),
                        PurchaseStaffId = br.PurchaseStaffId,
                        PurchaseStaffName = br.PurchaseStaffName,
                        Created = br.Created,
                        IsDeletable = br.IsDeletable,
                        UpgradeFromBillRecordId = br.UpgradeFromBillRecordId,
                        DowngradeFromBillRecordId = br.DowngradeFromBillRecordId,
                        UpdatedAt = br.UpdatedAt,
                        IsIrregularPlan = br.IsIrregularPlan,
                        PaymentIntervalType = br.PaymentIntervalType,
                        PaymentTerms = br.PaymentTerms,
                        SubscriptionInterval = br.SubscriptionInterval,
                        NoticePeriod = br.NoticePeriod,
                        Metadata = br.Metadata,
                        IsAutoRenewalEnabled = br.IsAutoRenewalEnabled
                    })
                    .ToList()
            },
            WorkflowCounts = new SimplifiedWorkflowCounts
            {
                NumOfNormalWorkflows = normalWorkflowCounts.Data.NumOfWorkflows,
                NumOfNormalActiveWorkflows = normalWorkflowCounts.Data.NumOfActiveWorkflows,
                NumOfAiWorkflows = aiWorkflowCounts.Data.NumOfWorkflows,
                NumOfAiActiveWorkflows = aiWorkflowCounts.Data.NumOfActiveWorkflows
            }
        };

        return Ok(simplified);
    }
}