location_name: eastasia
sku_config:
  sleekflow_core:
    name: P3V3
    tier: PremiumV3
  sleekflow_core_db:
    name: HS_PRMS
    tier: Hyperscale
    family: PRMS
    capacity: 2
  sleekflow_analytic_db:
    name: HS_S_Gen5
    tier: Hyperscale
    family: Gen5
    capacity: 2
  sleekflow_powerflow:
    name: P1V3
    tier: PremiumV3
  sleekflow_sleek_pay:
    name: P0V3
    tier: PremiumV3
  sleekflow_core_worker:
    name: P2V3
    tier: PremiumV3
  redis:
    default:
      name: Standard
      family: C
      capacity: 1
    caching:
      name: Standard
      family: C
      capacity: 1
sql_db_config:
  administrator_login_random_secret: TNH0D0aItooes12nsg7xa0i/rAjrqdjvg7IkW3IduWPX9tmuzZmMPvwDCQtvaf7YaEyXJeVnfeAmC5DP
  administrator_login_password_random_secret: M4pGUrmcwC/KZmvJCfq+o5nDkVXUctz0O6a0aBlB0pIhvRVJ0D2wAScVMBGe9Wk8=At4k0Y8pkfwZq02
  whitelist_ip_ranges:
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: **************
      end_ip_address: **************
  is_read_scale_enable: "true"
  high_availability_replica_count: 1
vnet:
  default_address_space: *********/16
  default_subnet_address_prefix: *********/24
  sleekflow_core_db_address_prefix: *********/24
  sleekflow_core_address_prefix: *********/24
  sleekflow_powerflow_address_prefix: *********/24
  sleekflow_sleek_pay_address_prefix: *********/24
  sleekflow_core_worker_address_prefix: *********/24
auto_scale_config:
  sleekflow_core:
    capacity:
      default: "3"
      maximum: "10"
      minimum: "2"
    scale_out_instances: "1"
    scale_down_instances: "1"
  core_worker:
    capacity:
      default: "1"
      maximum: "10"
      minimum: "1"
    scale_out_instances: "1"
    scale_down_instances: "1"
sleekflow_core_config:
  aspnetcore_environment: development
  logger:
    gcp_is_enabled: "TRUE"
    gcp_project_id: my-staging-project-405815
    gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  audit_hub:
    endpoint: https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io/v1/audit-hub
    key: KkeP/F1bW8EEQIWH19PUdl13vIYTZPUS4F7dQfJ+5oSvLkaxRNUBUqxd12ne7HvTxxUXlFlhqDXG4zXG4dhhsmeZgv0TcFE55Cj1MKh/Fjc=U3lhWBrba1PEXy61
  auth0:
    action_audience: https://api-staging.sleekflow.io/
    action_issuer: https://sso-staging.sleekflow.io/
    audience: https://api-staging.sleekflow.io
    client_id: b8xN94MKdx5rZCYg32r0SSRVJiYyNw4Z
    client_secret: CjDRQq8JlwYeYIkp1mkXZ9AZZigEpo/QyzWWncRhrhCSlvp1kp2NzyUuiuP4bvMGbNUIu4JPfl4gq+g/P/daiI0kh3F/4gblKzBONCqLezs=gE0s41SUJ24puBlO
    database_connection_name: Sleekflow-Username-Password-Authentication
    domain: sleekflow-staging.eu.auth0.com
    http_retries: 10
    issuers:
      - https://sso-staging.sleekflow.io/
      - https://sleekflow-staging.eu.auth0.com
    namespace: https://app.sleekflow.io/
    role_claim_type: roles
    user_email_claim_type: email
    user_id_claim_type: user_id
    username_claim_type: user_name
    tenant_hub_secret_key: mFDb0HY/JoJRdN74hPxFfl2uy8Of5yFrJPMDrYHVjYHYEcH7FZ6FpXX9MuvUo/Zweegk2LwRu8znSzfuCVFatXXRxNDfxfq2y6VW/uy7hnc=thp8eHeNGbc3WCBj
    health_check:
      is_enabled: "true"
      client_id: iluSPIuTen44fySBfKy4EAFEDltbBn28
      client_secret: sMacRreexfDPybKIfR6IWak7h8ArFfRXfep/NimyqMMUJZE7XxVc93LbrO8NMaPgsOgT5VH/0r5+/AzBTL2kFSxl81HoubYBtj4LkFZKd/8=k5MHt755iJJuiVBc
      username: <EMAIL>
      password: yzg8ezg@rfb*DQF-dbt
  azure:
    media_service:
      account_name: sfmediastaging
      client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
      client_secret: dLC1JmqB/82iBnefG2EVShdb0d2PIgnIjAFfpmEqAL1X6Waj5Ck2zalaJKj9VT8mMXd31mMdce4A99F3
      resource_group: sleekflow-resource-group-staging7417fc06
      subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
      tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
    text_analytics_credentials: 4a76771e5629485ba34bcdfca9ed3487
    text_analytics_url: https://sleekflowtextanalytics.cognitiveservices.azure.com/
  application_insights:
    is_telemetry_tracer_enabled: "true"
    is_sampling_disabled: "false"
  beamer:
    api_key: ""
    api_url: ""
    webhook_verify_key: ""
  chat_api:
    api_key: 9+ooLFalLDX5dA2m0NwTkzzkD9gQcE9D7uPANYel1JU=JqTWNAFYPWLa9=Nj
    api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
  commerce_hub:
    endpoint: https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io/v1/commerce-hub
    key: TJKO5SE1eJoWjoKZylHiBVKlKXe/XFUBJHgEUKs6dBFU7OvFTpzhb7hD9Qc7CY41n8a9cFvTIzgxOJf2FJ0dSCMfP51gHZV6+eXN40zPN+WrMgMBA85IJzHCZ943pT5D/UtbSHhEdzPQYtTy6nd6NzE5+e8sZV4Q7Vodj3d7TBmvmW4lFfVHy3qacVyXqTPgAPcUvNk6qPk2QOsR
  crm_hub:
    endpoint: https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io/v1/crm-hub
    key: QZkYNYrvR+Pai4+1kr0Ig2QApm9tnT8lbTT5de4dJAXdzXf7fiCL42Wo5at3BqODcLcmUJaIDBW5UiVe
  data_snapshot:
    is_enable: "false"
  development:
    redirect_fb_webhook: https://9633-42-200-140-118.ngrok.io/facebook/Webhook
  environment_features:
    is_recurring_job_enabled: "false"
  epplus:
    excel_package:
      license_context: NonCommercial
  facebook:
    client_id: 812364635796464
    client_secret: WS/Z1XnsRgXGWOJHeeIYLqhWk3pqjgHFqbr3Z2NGI764kQDZadXBf4izk3OlGxt7FCgl2RrFWy8cKd58
  ffmpeg:
    ffmpeg_exe_name: /usr/bin/ffmpeg
  flow_hub:
    endpoint: https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io/v1/flow-hub
    key: iW0FK9ekyXddx774ytGkn2fM9d19sfz8rLTbHeYbdT1ml2YRd/jPXNpK0YUFPaXR0rueklje/VkYxqC0v0xDTFaSoDpc6qPcetrVZc2JXdk=pSeAE1gwBgPsSCXo
  hub_spot:
    internal_hub_spot_api_key: ""
    is_enable: "false"
  instrumentation_engine_extension_version: disabled
  internal_google_cloud:
    credential:
  general_google_cloud:
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    project_id: my-staging-project-405815
    server_location: asia-east1
    google_storage_bucket_name: sleekflow-transcoder-staging
  ip_look_up:
    key: aMfEajU9FwFHxaLka4LFzpJeFFPS3+P8GnGiJ98EBdvQYTq1a6HDqauQnDnD6vUcYT7TAi=3DpzORgIR
  intelligent_hub:
    endpoint: https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io/v1/intelligent-hub
    key: B51GVcPsrSX6C+ZUwcunCvO5LiXewgAHAJRseZJX+cY3RBcTMSDfPyDOlycqwr3rt9Nuaw6DDDx655VnlXPrZCzUc+7dBPgL67+Pe1dswWc=Q36Pbh5va01sHi2K
  webhook_hub:
    endpoint: https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io/v1/webhook-hub
    key: 4LElvJT6123GIorKkYnFFZkmsHwSGhi6HIzLw+BzRwjCr/1RB9Rz+/o12pEnbsPWj6JoL7XkCfJgedgsjnNivmf4Mr/JIAK417DgPbwZLdE=9fnKHHPFjS2irO1A
    auth_secret_key: L/QvjHtHzp4/8RHKoynhtgrOiOtIYxgvOoVdjSQFiE8nw8A8YRwIxRtQgyx+J/Hwn1OA0Q/EzLQZic+qLlweJ55wdxVaISKmv7zSEPmZ/vk=8BXAsl8qvTKY1QXM
  message_hub:
    endpoint: https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io/v1/messaging-hub
    key: nr9FIEslPXeKiQSCN1PFJ9UsMNSvHHMkhJxwtz48dfVDWyxob14rns6rrA2KBXSl5yzFkoO37Hy8UyI0
  mixpanel:
    token: ef72dd13a0ffccb584cfdf75d3160e25
  mobile_app_management_extension_version: latest
  notification_hub:
    connection_string: Endpoint=sb://sleekflowtesting.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=l5XR33aDeefA79lau7IjY252oJRpdB/h4pP6v3F6hVY=
    hub_name: sleekflowTesting
  public_api_gateway:
    endpoint: https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io/v1/public-api-gateway
    key: axlxAab+Rdg/Km6sZ/ng5TUlbsaTqgEiURwyL4pwlaDyXJHOO6ASURq6NQySMh2/z0WwfkJQRlRx=Sm9
  reseller:
    domain_name: https://partner-uat.sleekflow.io/
  rewardful:
    api_secret: kV09t8aLX7bEsIm9313Bq1eP12R51C5wk93HMk6CI9XJxeY4QlOuWZaraBJxyCVa73Yhh1n=Wmsu6wGv
  salesforce:
    custom_active_web_app: https://staging-sfmc-custom-activity.vercel.app
  share_hub:
    endpoint: https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io/v1/share-hub
    key: fgyvrAX1HLEHZHIW1E4oIn2sh3qmOU8BcLQvK1uNob8jTHN7XXCvC4tHlED2LgFoLP3AW1JI60sBIEGO
  shopify:
    shopify_api_key: LZo8KiOPUloShRtlVlb3I0byD+VAKvgEGHeSzs7vNyXKKL3ti/cTYR0IhRZj4zmnlFewYgfmVFEMxZy3
    shopify_secret_key: 4noj5t7OF6yHFNCy2a2GWUcFdYOeCWtl+EGovnegyxRIJORpBcpZ/00on6urAiFT3kXpNmqwwAh15KXp
  sleek_pay:
    domain_name: https://pay-eas-staging.sleekflow.io
    shopify_graphql_admin_api_version: 2022-10
    stripe:
      public_keys:
        default: j7evoyvuPPZuPTCQ1951y2Uje6HrUGmvc/vN/GwrLLNKqwq3z7AFqqvwAVyqX633TAMehoa4yGsTz8QQGDkL6hJaAouJz/f2poV+N2CNwlSjMB9Yvaie+eo2+UW3DxwaZ5DThC+4fSydYy3fj9MiNQ==eJsBF0IRL3O7uRWX
        gb: gIQJlv3cXSxTFfjTBCTnyou6MAY9xYURlRpSvaa0Sm6xzqSGWoHgTMwOcimxLU3vRnwBMpB8B/5/glSdJa1l6+UMcu1FiucVFH0Z/KIu12zjyEJUf04R6gSx4WhEFpOKrIqnAqnJLB98gMWB9Jrsuw==bw6L4qGYdAnxobnb
        hk: t503Vx/hCiEvfOP4Pm5T21oclOGT4A25OTMtS6myPfHcKO2v6bRYge6K+vxmh2b3SSDUC+NJZnbSXjkEs4tP7P3b4ndbM9op9MsQAGdk6nA3+IR4x1oxNjYk1HdaVfVB4DAPR02allueHIQqqSDAtg==aBDU0Mal1EBOStgM
        my: P+bM0VqZRuYonN1eWk7nxu2tvJOnvXRubYg4fqEBG7fPcwlk+Zp8huUrUt0/GU/OU1C0MJXzCUALy6iT6Zh90jiy9iBf8DCBrxAyP3B77BOOAFMyRORW2PJzt46TnHfVAQMHhn0QWKP+XWOJsMr6Wg==mZciDMQOGbR59mVP
        sg: RUVtiws6PyPxg1oCyFVEOAxcoZ6yKaP/4RA/SB3HkG8ntNfVpXnTKNyhqiWrfTXhydrRbmhWqJWHikg1M9mTk6P6BVmgfkqXPogOwW6QRE1DNzBErmPik8VRmflIpPaEmMqrzqA97Ltwa5OTz1gNgA==Xg9PxKfKwsp3LBc1
      secret_keys:
        default: g9Omaf2tl+sqtZRWzKqEIo3cGVzxseWUwUr9qEuG7VnA/DGiTj2nwLycA2EIXhtp0dd8P7a071d7k2frXW5T7tXGw2gqgnzrUKWrYcp6k8sfDVz4QepV1Sn7FWKVn1NUPp0uBE1AIXvBO+50wNG6cg==KBCuBY3xYP4WFh4s
        gb: p3Y5vcava0XK5cyAEWdvIGfGrASvyiVmKMHaq6Uko+/FnOJMN1Ru5iqsrVJH2+kelJfXRqFJlNH9phuM1WPMP6BXrFs6bf9P8woGLjuCnh9Fh0FtXM7e+vGAEgWT9/ynZsaux3/K9NSGdhX5IA1pWg==Wf01WmK7h8MqxTLx
        hk: bkp42qCPDKBhAPPnevIDr9hmdg3cS81q20Bu5/F5On3T7j0uUicCjW59lMp8FuBkNjVSjlSml89Eu8pMJPqJ3cW3e1BtRJuhSBmjitZCtzyg7akrJmaV6fD/Yxr2II1BXV1Z6PuGwmSE70WyRGQ4cg==tkdQj4DffV8FIesF
        my: kD+IErxhr9rDgimUwyI0kWrfENwSXIEE8tdbuF9J/8l65kJ/FV1Ay6M0OCUYp+bh1ZS//QYqcvOgrDSbkK0JU+BU/nwG5GGVUWRj9ykLLQJAiRaO6lup4kIRDd+P9ZIbOKhnbVgamgVyGpa97RMQGQ==XmsVIGzeYm0awCW7
        sg: +nI5GVJBnH27uUazmBaX4K0FzN+UGLaDF0ZagNPbR+NEqknd9AoRROvj0fByiqu7NLjy2QwUn7HuJInwrlUSQDLh6Q8BLDEOq0LbkCZWqS9mUrkx4MV2oFhegFzUFSYfM4W6zFaQOsa93xJxZEQz2g==TrHVP8qBtasAtfbS
      connect_webhook_secrets:
        gb: KpXh+zYjIjOJUeXIwcsbK8o5hciDT0wjdpweSF4BMFfKA4CgXgYHJp7cVBdA5bCcDRq5blE1OFtMYDuC
        hk: u4iwRiTy0VsdBGRGfDhpbH62PHJtIP6ztuZ9we0zo6XlYk72U2t+le2J+lJTpuei=JNBLUCRQywtqebV
        my: fgpRnAoJN6hsIYkpJTZAjYB9qIjnvjmLW7L2UY1OLW8ty7ca+hb7xUZYnUXwrRE8FWNBQ0mV8CZe8Hpw
        sg: mpT1UfGnRWO8QTvwEbUYF02eKGBvUtqmO5j3C/+fpzaLzz7rW8qDAWAxsxHTQdXDy9xKILUDqk76PdS2
      report_webhook_secrets:
        gb:
        hk:
        my:
        sg:
      webhook_secrets:
        default: RLL8DSObuA795KNfIKLTdAYF28OZ1Jv19af5ZQCXmVJo+vR5bSehX2UhftcBxD6+5FNZGZYj9KK0nKWs
        gb: aBziI/7LSpz70b74M9k1dpB4W5Mw5hRfvQ2OBWc1ISIGrW89rH2XVMyEheFsRu8zzUmkKGcrB0t5eTr8
        hk: bvIFp+FzkKEBYUm+QrcUzzTsXHG6SjIzm56wRmnqhhZyNdi2opkdG1lnJvMR34bAWuNxGXZIY9lU4fzn
        my: /+0BK54vUzfzSmysPWaLOXFZE5LEEn+EF8nukvRl0FOvr1Rh10OA+BpnOlYrI5Qyd1hXnl3sTPr15jpS
        sg: 8LAwJt51QsMIvc7tCXR+P1/HZVqE32tQgEokORuQHVNxkrKDYAfM6aKF9RPXTdWLCaRUTRQpo054D1XL
  snapshot_debugger_extension_version: disabled
  sql_performance:
    from_raw_sql: "true"
    is_and_condition_enabled: "true"
    is_or_condition_enabled: "true"
    is_conversation_analytics_condition_enabled: "true"
    is_shopify_order_statistics_enabled: "true"
    is_sales_performance_enabled: "true"
    is_public_api_new_upsert_enabled: "false"
    is_zapier_new_upsert_enabled: "false"
  stripe:
    stripe_public_key: alYRxky2zCnPWOX76RWk6a09cKREpo3uJ8pXCRFhtGfGF7s+0Mg4aHcr7EbK3lA5KV6MnetlhKCrXZHe
    stripe_report_key: UEDsxtrL4mZBoYm8pv4Rcw8dpEaDIMcLcHGj47aYiN1OIP0OXz4X5NMaHGBX5lXrXG3cD99RBRZgAgKxzEnoCHY3ngZVjVjDu6ayAQKLRfjDr5cPlt12GNdhzz9HHYmCpdqlZyElVRuWueshg7ESeA==PbNLXnguZU8r2BYc
    stripe_secret_key: DKZb4beX5wQsZ/wzpKncUnNksfQbP0lwU5Z9A3jIxE5kFjEN2wQC+fNQ6sbdYmrJDFTHSgb=68DV=B03
    stripe_webhook_secret: YRdtfX+4guNF2nDVcNu/PfRcwVwxMM2Dnh+l9a18Gg/8xIKcMUbVu7ce/YQ/1f/3Gh2rDbA6Yldo7itl
  stripe_payment:
    stripe_payment_secret_key_gb: v4xzis6AkP6dOrWLyCDrbAt0Ne0BkP5l7nk68JH5PzSj7u1WbvHpDvsV0K2L02pPto5k88cQTw/00lL0rP0NSeKHEbvDLu2pBEQGQka5XQUW/yY5Kk2Rois7PRLxZIzrP5vtzaNqFpyTi7rdsVfu1g==wxXLadU4dSA4BZtf
    stripe_payment_secret_key_hk: ti9Bsv8MnffP5a5WD7rlV7n2x06VwoX/djHWo03EfZArx1uLYkm8Nl1uzA+EtabYhFTYyEoM3rUExK+j1ddXcE9qPajNJzb0+h3xNAwkjNWYhgowbt6CFe5ubm/w9wTTXiBLmHOpk9EhtKo1Klxsnw==xQiJMe=Fn=4annUB
    stripe_payment_secret_key_my: R3gRL8DtKmgS07h4fJVFskah/0L+AL754kZjmOzYW0loymSWj04PD1w2EARMGtQbIbjpHE0o9AkVLWOhuQuNg3C7NdFfwSdoGYyrpcLRO6w5szkbppwSHANmjEmiqmE62PRaTY0UHKFcxoZJ2BG/LQ==va2xeGx0wfzyCiUA
    stripe_payment_secret_key_sg: Kp1SN9RqYm6j8S4jJPAzX30IIMFHgnmlJxltrJ+4x3+UnnaVWS52WDoKJe3KHALGEClm1AR4mGwOSkdO7opdT+efEdKJ2wROTsKByNQxLakyaZqLVYB/swwBy15VGRhLfjrnBB8m8BGMc/mGBIFrLw==1rJC2azHp2sGiM58
  stripe_report:
    stripe_report_webhook_secret_gb:
    stripe_report_webhook_secret_hk:
    stripe_report_webhook_secret_my:
    stripe_report_webhook_secret_sg:
  tenant_hub:
    endpoint: https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io/v1/tenant-hub
    key: /j5baf5L0uqMQ4COVJ8bBng+lOLzDYJCkCNnNNlJXI+HgkkHnvNDzJ5QO4GFeI9qvqrIQdaW7P3hsIC5FjnEfu933mcAwV6nzLLgJwZydFs=L5mJO=wq8D==Pdgo
    is_enable_tenant_logic: "true"
  ticketing_hub:
    endpoint: https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io/v1/ticketing-hub
    key: 4UxGYfmezBBsWvvwBAYp16Cd/hz4bFpBtC42x9vRjq1FPuJRYYImHGhiXb3ZEm6RjcgGj3DI5aJnDsICtsWWcEPFZtXiT9A+FPpiTqTrXAs=d427YAB3GbAw0c=q
    is_enable_ticketing_logic: "false"
  test_swaping: 1
  token:
    audience: https://sleekflow-prod-api.azurewebsites.net
  tokens:
    audience: https://uat.sleekflow.io
    issuer: https://uat.sleekflow.io
    key: +vyLeDRWJGOOUydrBnVZ8OwMadM9NcANpk3JHmo8QmZeGwlkNkeKkr10EdQ0BIvqUyNhlJbEUbgE03h+H/dp7xlu2DSeByHzMWmW5a2XDac=54B8lKuutHo0OhT4
    lifetime: 365
  user_event_hub:
    endpoint: https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io/v1/user-event-hub
    key: yNUi32a/YBFQjO9HnIcABp6dSuCRs9EEio1lNX0hQ+VilEYc+xaAdod0r81KyX2+xk4OhAdOLKzGHWyjclNmzKPhRB4nb9ttOaSLiJgNUh8=U59Webg55aCGVuoT
  values:
    app_domain_name: https://staging.sleekflow.io
    app_domain_name_v1: https://v1-staging.sleekflow.io
    app_domain_name_v2: https://staging.sleekflow.io
    share_link_function: https://sleekflow-share-tracking-uat.azurewebsites.net
    sleekflow_api_gateway: https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io
    sleekflow_company_id: 39ee5f12-7997-45fa-a960-e4feecba425c
    sleekflow_public_api_url: https://api-staging.sleekflow.io
    sleekflow_public_api_key: 199KopYgGyydChE1lc9cMSMEyDSeOF2moCEenvgIt7gg4yzWia8e0So6qpcRVXAeqjWXKyZXk2TT3R0m
    sleekflow_company_should_use_public_api: "true"
  website_http_logging_retention_days: 5
  website_node_default_version: 6.9.1
  whatsapp_cloud_api_template:
    default_image_blob_id: sleekflow.png
  xdt_microsoft_application_insights_base_extensions: disabled
  xdt_microsoft_application_insight_mode: default
  global_pricing:
    is_feature_enabled: "true"
    plan_migration_incentives_start_date: "2024-10-20"
    plan_migration_incentives_end_date: "2025-02-20"
  feature_flags:
    - feature_name: FlowBuilderMonetisation
      is_enabled: "true"
    - feature_name: CancelledSubscriptionTermination
      is_enabled: "true"
    - feature_name: Rbac
      is_enabled: "false"
  contact_safe_deletion:
    is_feature_enabled: "true"
  partner_stack:
    public_key: 0WRWBhmLnmDj2XKJOnlvnu1befMzDBmLsewKMIUSLkw/Qmh2m+8kziMqWALa4W6jjGJtYR09NCyf3kC=
    secret_key: dGqfEKNYRHTGOs4jdoSR7u4G63QapQif8JLwh4KJLFJbcXUlwTOMQ9egPbclA2Qs0=OYjBzySQ5KVD8y
  hangfire_worker:
    worker_count: 20
  internal_integration_hub:
    endpoint: "https://sleekflow-apigw-app.bravesky-1b267126.eastasia.azurecontainerapps.io/v1/internal-integration-hub"
    key: MxQmeW8C82LKk8CIZZyFWZyldeWRN5DqQ+lgs4JXC23kRItYGn0VftJuUYtrVneiNmlE0cmOxx6OfsTT2jseWZPynBCvggo3Z0ItBKhKxSY=Fq5t1xvp5sU28mOc
  hangfire_queues:
    disable_instances: "low,default"
  integration_alert:
    endpoint: "https://sleekflow-core-staging-dycncqcebbf4ggag.z01.azurefd.net/api/notifications/integration-disconnected"
    api_key: 8T9sMYNS+Be/FKIfS8dePspUvrSoLDBmEQQEysvGth2d9d5BLkOx/HnjZNrrGmCHFi8KjWdXchFccM09
    host_company_id: "39ee5f12-7997-45fa-a960-e4feecba425c"
    from_phone_number: "15419458252"
    template_name: "integration_disconnect_noti"
    facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"
  rbac:
    is_middleware_verification_enabled: "false"
  flow_builder_flow_enrollments_incentives:
    period_start: "2025-01-24"
    period_end: "2025-04-24"
  hub_spot_smtp:
    username: "<EMAIL>"
    password: "jP3Ox4fOjweWF0fxcbKfLoB3glxOFU"
    send_execution_usage_reached_threshold_email:
      username: "<EMAIL>"
      password: "lBjfD8BpWl8PBsmYYJqKEkXX2ag85w"
  legacy_premium_opt_in_upgrade_incentives:
    period_start: "2025-02-10"
    period_end: "2025-06-10"
  live_chat:
    secret_key: "/kDWSMFJ1rADVeuB3a2dLMZ4yiPUazVgUgT48uPsk+62f+JRojUxPw5UbsHUE40NDMaZ=mt0qVpMo=Tu"