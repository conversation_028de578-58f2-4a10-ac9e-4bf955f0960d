using System.Diagnostics;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;

namespace Sleekflow.Core.Tests.IntegrationTests;

/// <summary>
/// Integration tests for variance analysis between old and new MRR calculation methods
/// using simulated historical data to ensure data team requirements are met.
/// </summary>
[TestFixture]
public class HistoricalDataVarianceTests
{
    private Mock<ITimezoneAwareMrrCalculationService> _timezoneService = null!;
    private BillRecordRevenueCalculatorService _billRecordRevenueCalculatorService = null!;
    private Mock<ILogger<BillRecordRevenueCalculatorService>> _mockBillRecordRevenueCalculatorServiceLogger = null!;

    [SetUp]
    public void Setup()
    {
        _timezoneService = new Mock<ITimezoneAwareMrrCalculationService>();
        _mockBillRecordRevenueCalculatorServiceLogger = new Mock<ILogger<BillRecordRevenueCalculatorService>>();
        _billRecordRevenueCalculatorService = new BillRecordRevenueCalculatorService(
            _mockBillRecordRevenueCalculatorServiceLogger.Object,
            _timezoneService.Object);
    }

    #region Historical Data Simulation Tests

    [Test]
    public void HistoricalVariance_Q1_2023_Data_ShouldMeetVarianceRequirements()
    {
        // Arrange - Simulate Q1 2023 historical data
        var historicalBillRecords = GenerateHistoricalBillRecords_Q1_2023();
        var companies = GenerateCompaniesWithTimezones();

        SetupTimezoneServiceMocks(companies);

        var varianceResults = new List<VarianceAnalysisResult>();

        // Act - Calculate variance for each historical bill record
        foreach (var billRecord in historicalBillRecords)
        {
            var company = companies.First(c => c.Id == billRecord.CompanyId);

            var originalMrr = CalculateOriginalMrr(billRecord, company.TimeZoneInfoId);
            var enhancedMrr = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                [billRecord],
                company.TimeZoneInfoId,
                billRecord.PeriodStart);

            var variance = Math.Abs(enhancedMrr - originalMrr);
            var variancePercentage = originalMrr != 0 ? (variance / originalMrr) * 100 : 0;

            varianceResults.Add(
                new VarianceAnalysisResult
                {
                    BillRecordId = billRecord.Id,
                    CompanyId = billRecord.CompanyId,
                    OriginalMrr = originalMrr,
                    EnhancedMrr = enhancedMrr,
                    Variance = variance,
                    VariancePercentage = variancePercentage,
                    PeriodStart = billRecord.PeriodStart,
                    PeriodEnd = billRecord.PeriodEnd
                });
        }

        // Assert - Variance requirements
        var averageVariancePercentage = varianceResults.Average(r => r.VariancePercentage);
        var maxVariancePercentage = varianceResults.Max(r => r.VariancePercentage);
        var highVarianceCount = varianceResults.Count(r => r.VariancePercentage > 1.0m);

        Console.WriteLine($"Historical Data Analysis - Q1 2023:");
        Console.WriteLine($"Total Records: {varianceResults.Count}");
        Console.WriteLine($"Average Variance: {averageVariancePercentage:F4}%");
        Console.WriteLine($"Maximum Variance: {maxVariancePercentage:F4}%");
        Console.WriteLine(
            $"Records with >1% variance: {highVarianceCount} ({(double) highVarianceCount / varianceResults.Count * 100:F2}%)");

        // Data team requirements validation
        averageVariancePercentage.Should().BeLessThan(1.0m, "Average variance should be less than 1%");
        maxVariancePercentage.Should().BeLessThan(5.0m, "Maximum variance should be reasonable (<5%)");
        ((double) highVarianceCount / varianceResults.Count).Should().BeLessThan(
            0.05,
            "Less than 5% of records should have >1% variance");
    }

    [Test]
    public void HistoricalVariance_CrossYearData_2022To2023_ShouldHandleYearBoundaries()
    {
        // Arrange - Simulate data across year boundary
        var historicalBillRecords = GenerateHistoricalBillRecords_CrossYear();
        var companies = GenerateCompaniesWithTimezones();

        SetupTimezoneServiceMocks(companies);

        var varianceResults = new List<VarianceAnalysisResult>();

        // Act
        foreach (var billRecord in historicalBillRecords)
        {
            var company = companies.First(c => c.Id == billRecord.CompanyId);
            var originalMrr = CalculateOriginalMrr(billRecord, company.TimeZoneInfoId);
            var enhancedMrr = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                [billRecord],
                company.TimeZoneInfoId,
                billRecord.PeriodStart);

            var variance = Math.Abs(enhancedMrr - originalMrr);
            var variancePercentage = originalMrr != 0 ? (variance / originalMrr) * 100 : 0;

            varianceResults.Add(
                new VarianceAnalysisResult
                {
                    BillRecordId = billRecord.Id,
                    CompanyId = billRecord.CompanyId,
                    OriginalMrr = originalMrr,
                    EnhancedMrr = enhancedMrr,
                    Variance = variance,
                    VariancePercentage = variancePercentage,
                    PeriodStart = billRecord.PeriodStart,
                    PeriodEnd = billRecord.PeriodEnd
                });
        }

        // Assert - Year boundary calculations should still meet variance requirements
        var averageVariancePercentage = varianceResults.Average(r => r.VariancePercentage);

        Console.WriteLine($"Cross-Year Historical Data Analysis:");
        Console.WriteLine($"Average Variance: {averageVariancePercentage:F4}%");

        averageVariancePercentage.Should().BeLessThan(1.0m, "Cross-year variance should still be less than 1%");
    }

    [Test]
    public void HistoricalVariance_DstTransitionPeriods_ShouldHandleTimeChanges()
    {
        // Arrange - Simulate data during DST transition periods
        var historicalBillRecords = GenerateHistoricalBillRecords_DstTransitions();
        var companies = GenerateCompaniesWithDstTimezones();

        SetupTimezoneServiceMocks(companies);

        var varianceResults = new List<VarianceAnalysisResult>();

        // Act
        foreach (var billRecord in historicalBillRecords)
        {
            var company = companies.First(c => c.Id == billRecord.CompanyId);
            var originalMrr = CalculateOriginalMrr(billRecord, company.TimeZoneInfoId);
            var enhancedMrr = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                [billRecord],
                company.TimeZoneInfoId,
                billRecord.PeriodStart);

            var variance = Math.Abs(enhancedMrr - originalMrr);
            var variancePercentage = originalMrr != 0 ? (variance / originalMrr) * 100 : 0;

            varianceResults.Add(
                new VarianceAnalysisResult
                {
                    BillRecordId = billRecord.Id,
                    CompanyId = billRecord.CompanyId,
                    OriginalMrr = originalMrr,
                    EnhancedMrr = enhancedMrr,
                    Variance = variance,
                    VariancePercentage = variancePercentage,
                    PeriodStart = billRecord.PeriodStart,
                    PeriodEnd = billRecord.PeriodEnd
                });
        }

        // Assert - DST transitions should be handled properly
        var dstAffectedRecords = varianceResults.Where(r =>
            IsDstTransitionPeriod(r.PeriodStart, r.PeriodEnd)).ToList();

        Console.WriteLine($"DST Transition Analysis:");
        Console.WriteLine($"DST-affected records: {dstAffectedRecords.Count}");

        if (dstAffectedRecords.Count > 0)
        {
            var dstAverageVariance = dstAffectedRecords.Average(r => r.VariancePercentage);
            Console.WriteLine($"DST Average Variance: {dstAverageVariance:F4}%");

            dstAverageVariance.Should().BeLessThan(2.0m, "DST transitions should have reasonable variance (<2%)");
        }
    }

    #endregion

    #region Performance Impact Analysis Tests

    [Test]
    public void PerformanceImpact_LargeDataset_CompareCalculationTimes()
    {
        // Arrange
        var largeBillRecordSet = GenerateLargeBillRecordDataset(1000);
        var companies = GenerateCompaniesForPerformanceTest();

        SetupTimezoneServiceMocks(companies);

        // Act - Measure original calculation time (without timezone service)
        var originalStopwatch = Stopwatch.StartNew();
        var originalResults = new List<decimal>();
        foreach (var billRecord in largeBillRecordSet)
        {
            var company = companies.First(c => c.Id == billRecord.CompanyId);
            var originalMrr = CalculateOriginalMrr(billRecord, company.TimeZoneInfoId);
            originalResults.Add(originalMrr);
        }

        originalStopwatch.Stop();

        // Measure enhanced calculation time (with timezone service)
        var enhancedStopwatch = Stopwatch.StartNew();
        var enhancedResults = new List<decimal>();
        foreach (var billRecord in largeBillRecordSet)
        {
            var enhancedMrr = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                [billRecord],
                "UTC",
                billRecord.PeriodStart);
            enhancedResults.Add(enhancedMrr);
        }

        enhancedStopwatch.Stop();

        // Assert - Performance impact analysis
        var performanceImpactPercentage =
            ((double) (enhancedStopwatch.ElapsedMilliseconds - originalStopwatch.ElapsedMilliseconds) /
             originalStopwatch.ElapsedMilliseconds) * 100;

        Console.WriteLine($"Performance Impact Analysis:");
        Console.WriteLine($"Dataset size: {largeBillRecordSet.Count} records");
        Console.WriteLine($"Original calculation time: {originalStopwatch.ElapsedMilliseconds}ms");
        Console.WriteLine($"Enhanced calculation time: {enhancedStopwatch.ElapsedMilliseconds}ms");
        Console.WriteLine($"Performance impact: {performanceImpactPercentage:F2}%");

        // Performance requirements
        enhancedStopwatch.ElapsedMilliseconds.Should().BeLessThan(
            originalStopwatch.ElapsedMilliseconds * 3,
            "Enhanced calculations should not be more than 3x slower");

        performanceImpactPercentage.Should().BeLessThan(
            200,
            "Performance impact should be reasonable (<200% increase)");

        // Results should still be similar
        originalResults.Count.Should().Be(enhancedResults.Count);
    }

    #endregion

    #region Data Generation Helpers

    private List<BillRecord> GenerateHistoricalBillRecords_Q1_2023()
    {
        var billRecords = new List<BillRecord>();
        var random = new Random(12345); // Fixed seed for reproducible tests
        var companyIds = new[]
        {
            "company-1",
            "company-2",
            "company-3",
            "company-4",
            "company-5"
        };

        for (int i = 0; i < 100; i++)
        {
            var companyId = companyIds[random.Next(companyIds.Length)];
            var startDate = new DateTime(2023, 1, 1).AddDays(random.Next(0, 90)); // Q1 2023
            var durationDays = random.Next(28, 35); // Roughly monthly
            var endDate = startDate.AddDays(durationDays);

            billRecords.Add(
                new BillRecord
                {
                    Id = i + 1,
                    CompanyId = companyId,
                    PayAmount = random.Next(100, 5000), // $100-$5000
                    currency = "usd",
                    PeriodStart = startDate,
                    PeriodEnd = endDate,
                    CmsSalesPaymentRecords = GenerateRandomPaymentRecords(random),
                    Status = Travis_backend.Enums.BillStatus.Active
                });
        }

        return billRecords;
    }

    private List<BillRecord> GenerateHistoricalBillRecords_CrossYear()
    {
        var billRecords = new List<BillRecord>();
        var random = new Random(54321);
        var companyIds = new[]
        {
            "year-boundary-1",
            "year-boundary-2",
            "year-boundary-3"
        };

        for (int i = 0; i < 50; i++)
        {
            var companyId = companyIds[random.Next(companyIds.Length)];
            var startDate = new DateTime(2022, 11, 1).AddDays(random.Next(0, 60)); // Nov-Dec 2022
            var endDate = new DateTime(2023, 1, 1).AddDays(random.Next(0, 60)); // Jan-Feb 2023

            billRecords.Add(
                new BillRecord
                {
                    Id = i + 1000,
                    CompanyId = companyId,
                    PayAmount = random.Next(200, 3000),
                    currency = "usd",
                    PeriodStart = startDate,
                    PeriodEnd = endDate,
                    CmsSalesPaymentRecords = GenerateRandomPaymentRecords(random),
                    Status = Travis_backend.Enums.BillStatus.Active
                });
        }

        return billRecords;
    }

    private List<BillRecord> GenerateHistoricalBillRecords_DstTransitions()
    {
        var billRecords = new List<BillRecord>();
        var random = new Random(98765);
        var companyIds = new[]
        {
            "dst-company-1",
            "dst-company-2"
        };

        // March 2023 DST transition (second Sunday in March)
        var marchDstDate = new DateTime(2023, 3, 12);
        // November 2023 DST transition (first Sunday in November)
        var novemberDstDate = new DateTime(2023, 11, 5);

        var dstTransitionDates = new[]
        {
            marchDstDate,
            novemberDstDate
        };

        foreach (var dstDate in dstTransitionDates)
        {
            for (int i = 0; i < 20; i++)
            {
                var companyId = companyIds[random.Next(companyIds.Length)];
                var startDate = dstDate.AddDays(random.Next(-7, 7)); // Week around DST
                var endDate = startDate.AddDays(random.Next(28, 35));

                billRecords.Add(
                    new BillRecord
                    {
                        Id = i + 2000 + (dstDate.Month * 100),
                        CompanyId = companyId,
                        PayAmount = random.Next(300, 2000),
                        currency = "usd",
                        PeriodStart = startDate,
                        PeriodEnd = endDate,
                        CmsSalesPaymentRecords = GenerateRandomPaymentRecords(random),
                        Status = Travis_backend.Enums.BillStatus.Active
                    });
            }
        }

        return billRecords;
    }

    private List<BillRecord> GenerateLargeBillRecordDataset(int count)
    {
        var billRecords = new List<BillRecord>();
        var random = new Random(11111);
        var companyIds = Enumerable.Range(1, 20).Select(i => $"perf-company-{i}").ToArray();

        for (int i = 0; i < count; i++)
        {
            var companyId = companyIds[random.Next(companyIds.Length)];
            var startDate = new DateTime(2023, 1, 1).AddDays(random.Next(0, 365));
            var endDate = startDate.AddDays(random.Next(25, 35));

            billRecords.Add(
                new BillRecord
                {
                    Id = i + 10000,
                    CompanyId = companyId,
                    PayAmount = random.Next(50, 10000),
                    currency = "usd",
                    SubscriptionPlanId = "sleekflow_v10_countrytier1_whatsapp_phone_number_monthly_aed",
                    created = startDate,
                    PeriodStart = startDate,
                    PeriodEnd = endDate,
                    CmsSalesPaymentRecords = GenerateRandomPaymentRecords(random),
                    Status = Travis_backend.Enums.BillStatus.Active
                });
        }

        return billRecords;
    }

    private List<CmsSalesPaymentRecord> GenerateRandomPaymentRecords(Random random)
    {
        var records = new List<CmsSalesPaymentRecord>();
        var recordCount = random.Next(0, 3); // 0-2 additional payment records

        for (int i = 0; i < recordCount; i++)
        {
            records.Add(
                new CmsSalesPaymentRecord
                {
                    Id = random.Next(1, 10000),
                    SubscriptionFee = random.Next(0, 1000),
                    OneTimeSetupFee = random.Next(0, 500),
                    WhatsappCreditAmount = random.Next(0, 200),
                    Currency = "usd"
                });
        }

        return records;
    }

    private List<Company> GenerateCompaniesWithTimezones()
    {
        return
        [
            new Company
            {
                Id = "company-1", TimeZoneInfoId = "Asia/Singapore"
            },
            new Company
            {
                Id = "company-2", TimeZoneInfoId = "Europe/London"
            },
            new Company
            {
                Id = "company-3", TimeZoneInfoId = "America/New_York"
            },
            new Company
            {
                Id = "company-4", TimeZoneInfoId = "Australia/Sydney"
            },
            new Company
            {
                Id = "company-5", TimeZoneInfoId = "UTC"
            },
            new Company
            {
                Id = "year-boundary-1", TimeZoneInfoId = "America/Los_Angeles"
            },
            new Company
            {
                Id = "year-boundary-2", TimeZoneInfoId = "Asia/Tokyo"
            },
            new Company
            {
                Id = "year-boundary-3", TimeZoneInfoId = "Europe/Berlin"
            }
        ];
    }

    private List<Company> GenerateCompaniesWithDstTimezones()
    {
        return
        [
            new Company
            {
                Id = "dst-company-1", TimeZoneInfoId = "America/New_York"
            },
            new Company
            {
                Id = "dst-company-2", TimeZoneInfoId = "Europe/London"
            }
        ];
    }

    private List<Company> GenerateCompaniesForPerformanceTest()
    {
        var companies = new List<Company>();
        var timezones = new[]
        {
            "UTC",
            "America/New_York",
            "Europe/London",
            "Asia/Singapore",
            "Australia/Sydney"
        };

        for (int i = 1; i <= 20; i++)
        {
            companies.Add(
                new Company
                {
                    Id = $"perf-company-{i}", TimeZoneInfoId = timezones[i % timezones.Length]
                });
        }

        return companies;
    }

    #endregion

    #region Setup Helpers

    private void SetupTimezoneServiceMocks(List<Company> companies)
    {
        foreach (var company in companies)
        {
            // Setup realistic timezone-aware calculations with small variances
            _timezoneService.Setup(x => x.GetPreciseMonthDiff(
                    It.IsAny<DateTime>(),
                    It.IsAny<DateTime>(),
                    company.Id,
                    It.IsAny<string?>()))
                .Returns((DateTime start, DateTime end, string _, string? _) =>
                {
                    // Simulate realistic timezone-aware calculations
                    var originalMonthDiff = BillRecordRevenueCalculatorHelper.GetMonthDiff(start, end);
                    var variance = GenerateRealisticVariance(company.TimeZoneInfoId, start, end);
                    return originalMonthDiff * variance;
                });
        }
    }

    private decimal GenerateRealisticVariance(string timezoneId, DateTime start, DateTime end)
    {
        // Generate realistic variance based on timezone characteristics
        var random = new Random(timezoneId.GetHashCode());

        // Base variance range: -0.5% to +0.5%
        var baseVariance = (decimal) (random.NextDouble() * 0.01 - 0.005);

        // Additional variance for DST timezones during transition periods
        if (IsDstTimezone(timezoneId) && IsDstTransitionPeriod(start, end))
        {
            baseVariance += (decimal) (random.NextDouble() * 0.005); // Additional 0-0.5% for DST
        }

        return 1.0m + baseVariance;
    }

    private bool IsDstTimezone(string timezoneId)
    {
        var dstTimezones = new[]
        {
            "America/New_York",
            "Europe/London",
            "Europe/Berlin",
            "America/Los_Angeles"
        };
        return dstTimezones.Contains(timezoneId);
    }

    private bool IsDstTransitionPeriod(DateTime start, DateTime end)
    {
        // Check if the period overlaps with typical DST transition months (March, November)
        return (start.Month == 3 || start.Month == 11 || end.Month == 3 || end.Month == 11);
    }

    private decimal CalculateOriginalMrr(BillRecord billRecord, string timezone)
    {
        // Simulate original calculation without timezone awareness
        billRecord.CompanyId = null;
        var billRecords = new List<BillRecord>
        {
            billRecord
        };
        var result = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
            billRecords,
            timezone,
            billRecord.PeriodStart);
        return result;
    }

    #endregion

    #region Helper Classes

    private class VarianceAnalysisResult
    {
        public long BillRecordId { get; set; }
        public string CompanyId { get; set; } = string.Empty;
        public decimal OriginalMrr { get; set; }
        public decimal EnhancedMrr { get; set; }
        public decimal Variance { get; set; }
        public decimal VariancePercentage { get; set; }
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }
    }

    #endregion
}