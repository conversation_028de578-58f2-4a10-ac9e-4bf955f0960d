### Login

POST https://localhost:5000/auth0/account/getusertoken
content-type: application/json-patch+json

{
  "username": "<EMAIL>",
  "password": "MP13005926"
}

> {% client.global.set("token", response.body.accessToken); %}

### Create new sender
POST https://{{host}}/LiveChatV2/Public/Senders
content-type: application/json

{
  "liveChatId": "ce2f0c3c-82d1-40cd-b8cc-46b55ab60643",
  "companyId": "b6d7e442-38ae-4b9a-b100-2951729768bc",
  "metadata": { "geolocation": { "lat": "38.8951", "long": "-77.0364" } }
}

> {% client.global.set("senderId", response.body.id); %}

### Update sender metadata
PATCH https://{{host}}/LiveChatV2/Public/Senders/{{senderId}}
content-type: application/json

{
  "metadata": {
    "geolocation": { "lat": "40.7128", "long": "-74.0060" },
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "sessionInfo": { "startTime": "2024-01-15T10:30:00Z", "pageViews": 3 }
  }
}

### Get sender
GET https://{{host}}/LiveChatV2/Senders/2226c90f-1769-4a52-9ad8-48893c4ca38d
content-type: application/json
Authorization: Bearer {{token}}

{

}
> {% client.global.set("senderId", response.body.id); %}

### Update sender metadata
PATCH https://{{host}}/LiveChatV2/Public/Senders/{{senderId}}
content-type: application/json

{
  "metadata": {
    "geolocation": { "lat": "40.7128", "long": "-74.0060" },
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "sessionInfo": { "startTime": "2024-01-15T10:30:00Z", "pageViews": 3 }
  }
}

### Get sender
GET https://{{host}}/LiveChatV2/Senders/501e164b-13b1-4869-a2d2-30a94815ee96
content-type: application/json
Authorization: Bearer {{token}}

{

}

### Create a new tracking event for a user
POST https://{{host}}/FlowHub/TriggerEventSamples/livechat-website-url-detected
content-type: application/json
Authorization: Bearer {{token}}

{

}