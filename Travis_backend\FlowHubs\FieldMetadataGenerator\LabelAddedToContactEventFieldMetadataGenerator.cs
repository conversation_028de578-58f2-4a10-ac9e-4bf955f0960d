﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Database.Services;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Models;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Utils;
using Newtonsoft.Json.Linq;

namespace Travis_backend.FlowHubs.FieldMetadataGenerator;

public sealed class LabelAddedToContactEventFieldMetadataGenerator :
    FlowHubEventFieldMetadataGeneratorBase,
    IFlowHubEventFieldMetadataGenerator
{
    public string EventName => TriggerIds.LabelAdded;

    public Dictionary<string, string> FieldPathOptionRequestPathDict { get; } = new()
    {
        { "contact.labels[*].LabelValue", "FlowHub/SelectOptions/Labels" },
        { "added_to_labels[*].label_value", "FlowHub/SelectOptions/Labels" },
        { "labels[*].label_value", "FlowHub/SelectOptions/Labels" },
        { "lists[*].id", "FlowHub/SelectOptions/ContactLists" },
        { "sleekflow_staff_id", "FlowHub/SelectOptions/Staffs" },
        { "contact.ContactOwner", "FlowHub/SelectOptions/Staffs" },
        { "contactOwner.id", "FlowHub/SelectOptions/Staffs" },
        { "contact.LastChannel", "FlowHub/SelectOptions/ChannelTypes" }
    };

    public LabelAddedToContactEventFieldMetadataGenerator(
        IDbContextService dbContextService,
        IFieldMetadataVariableVisibilityService fieldMetadataVariableVisibilityService)
        : base(dbContextService, fieldMetadataVariableVisibilityService)
    {
    }

    public async Task<(HashSet<FieldMetadata> FieldMetadataSet, Type EventBodyType, string ContactObjectName)> GenerateAsync(
        string companyId,
        Dictionary<string, object?>? parameters)
    {
        var (userProfileDict, customFieldOptionsRequestPathDict) = await GenerateUserProfileDictAsync(companyId);
        var eventBody = JsonUtils.GenerateSampleObjectFromType<OnContactLabelRelationshipsChangedEventBody>();

        eventBody.Contact = userProfileDict;
        eventBody.RemovedFromLabels = null!;

        var eventBodyJsonString = eventBody.ToJson();
        var eventBodyJson = JObject.Parse(eventBodyJsonString);
        AddContactExpansionToEventBodyJson(eventBodyJson);

        var jsonData = JsonUtils.SimplifyJsonData(eventBodyJson.ToString());

        var fieldMetadataSet = GenerateFilteredFieldMetadata(jsonData, EventName);

        PopulateOptionRequestPath(
            fieldMetadataSet,
            customFieldOptionsRequestPathDict,
            FieldPathOptionRequestPathDict);

        return (fieldMetadataSet, typeof(OnContactLabelRelationshipsChangedEventBody), nameof(eventBody.Contact));
    }
}