using System;
using System.Collections.Generic;
using System.Linq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.InternalDomain.Models;

namespace Travis_backend.Helpers;

public static class BillRecordRevenueCalculatorHelper
{
    public static int GetMonthDiff(DateTime periodStart, DateTime periodEnd)
    {
        if (periodStart > periodEnd)
        {
            return 0;
        }

        periodStart = periodStart.Date;
        periodEnd = periodEnd.Date;

        var monthDiff = 1;

        while (periodEnd > periodStart.AddMonths(monthDiff))
        {
            monthDiff++;
        }

        monthDiff--;

        if ((periodEnd - periodStart.AddMonths(monthDiff)).TotalDays >= 28)
        {
            monthDiff++;
        }

        return monthDiff > 0 ? monthDiff : 1;
    }

    public static DateTime GetActualPeriodEndFromMetadata(BillRecord billRecord)
    {
        if (billRecord.metadata?.TryGetValue("actual_period_end", out var actualPeriodEndStr) == true)
        {
            if (DateTime.TryParse(actualPeriodEndStr, out var actualPeriodEnd))
            {
                return actualPeriodEnd;
            }
        }

        return billRecord.PeriodEnd;
    }

    public static List<PaymentSplit> GetValidPaymentSplits(BillRecord billRecord)
    {
        var actualPeriodEnd = GetActualPeriodEndFromMetadata(billRecord);
        return billRecord.PaymentSplits!
            .Where(ps => ps.PaymentDate.Date >= billRecord.PeriodStart.Date &&
                         ps.PaymentDate.Date <= actualPeriodEnd.Date)
            .OrderBy(ps => ps.PaymentDate)
            .ToList();
    }
}