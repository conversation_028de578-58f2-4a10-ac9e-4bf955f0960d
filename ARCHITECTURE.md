# SleekFlow Backend Architecture Documentation

## Overview

SleekFlow is a comprehensive customer communication platform that enables businesses to manage conversations across multiple channels (WhatsApp, Facebook Messenger, Instagram, Email, etc.) through a unified interface. The backend is built on .NET 8 with a microservices-oriented architecture deployed on Microsoft Azure.

## Table of Contents

- [Solution Structure](#solution-structure)
- [Infrastructure Architecture](#infrastructure-architecture)
- [Core Services](#core-services)
- [Domain Architecture](#domain-architecture)
- [Technology Stack](#technology-stack)
- [Deployment & DevOps](#deployment--devops)
- [Multi-Region Setup](#multi-region-setup)
- [Monitoring & Observability](#monitoring--observability)
- [Database Architecture](#database-architecture)
- [Security & Authentication](#security--authentication)

## Solution Structure

The SleekFlow backend consists of multiple .NET projects organized in a solution:

### Main Projects

| Project | Purpose | Type |
|---------|---------|------|
| `Travis_backend` | Main API service with business logic | ASP.NET Core Web API |
| `Travis_backend.Auth0` | Authentication & authorization service | ASP.NET Core Web API |
| `Sleekflow.Powerflow.Apis` | Workflow automation APIs | ASP.NET Core Web API |
| `Sleekflow.SleekPay` | Payment processing service | Azure Functions |
| `Sleekflow.Core.Tests` | Comprehensive test suite | Test Project |

### Supporting Projects

| Project | Purpose |
|---------|---------|
| `Sleekflow.Core.Infra` | Infrastructure as Code (Pulumi) |
| `Sleekflow.Core.Infra.Perf` | Performance infrastructure |
| `Sleekflow.Core.DataMigrator` | Database migration utilities |
| `Sleekflow.Core.Benchmarks` | Performance benchmarking |
| `Sleekflow.Core.StressTests` | Load testing |
| `Sleekflow.Core.Exporter` | Data export utilities |

## Infrastructure Architecture

### Cloud Provider
- **Primary**: Microsoft Azure
- **Infrastructure as Code**: Pulumi (C#)
- **Container Registry**: Azure Container Registry
- **CDN**: Azure Front Door

### Pulumi Infrastructure Components

```
Sleekflow.Core.Infra/
├── Components/
│   ├── SleekflowCore/           # Main application infrastructure
│   ├── BlobStorage.cs           # File storage configuration
│   ├── Redis.cs                 # Caching infrastructure
│   ├── SignalR.cs              # Real-time communication
│   ├── VNet.cs                 # Virtual network setup
│   └── FrontDoor.cs            # Global load balancing
├── Configs/                     # Environment configurations
└── RegionalConfigs/            # Region-specific settings
```

### Multi-Environment Setup

| Environment | Purpose | Regions |
|-------------|---------|---------|
| **Development** | Development & testing | East Asia |
| **Staging** | Pre-production testing | East Asia, East US |
| **Production** | Live environment | East Asia, East US, Southeast Asia, UAE North, West Europe |

### Infrastructure Components

- **App Services**: Container-based web applications with auto-scaling
- **Azure SQL Database**: Primary data storage with read replicas
- **Redis Cache**: Distributed caching and session management
- **Azure Storage**: Blob storage for files and media
- **SignalR Service**: Real-time communication
- **Application Insights**: Monitoring and telemetry
- **Azure Front Door**: Global load balancing and CDN

## Core Services

### 1. SleekFlow Core (`Travis_backend`)
The main API service handling:
- **Message Processing**: Multi-channel message routing and processing
- **Conversation Management**: Unified conversation threads
- **Channel Integrations**: WhatsApp, Facebook, Instagram, Email, SMS
- **User Management**: Authentication, authorization, profiles
- **Company Management**: Multi-tenant architecture
- **Analytics**: Real-time conversation analytics

**Key Features:**
- Domain-driven design with 30+ business domains
- Multi-tenant architecture
- Real-time message processing with SignalR
- Comprehensive API with Swagger documentation
- Background job processing with Hangfire

### 2. Auth0 Service (`Travis_backend.Auth0`)
Dedicated authentication service:
- JWT token management
- Role-based access control (RBAC)
- Auth0 integration
- User session management

### 3. Powerflow APIs (`Sleekflow.Powerflow.Apis`)
Workflow automation service:
- Automated response workflows
- Business process automation
- Integration with third-party services
- Rule-based message routing

### 4. SleekPay (`Sleekflow.SleekPay`)
Payment processing service (Azure Functions):
- Stripe integration
- Shopify integration
- Payment webhooks
- Billing and subscription management
- Financial reporting

## Domain Architecture

The main service follows Domain-Driven Design (DDD) principles with clear domain boundaries:

### Core Domains

#### Communication Domains
- **MessageDomain**: Core message processing and routing
- **ConversationDomain**: Conversation management and threading
- **ChannelDomain**: Multi-channel integration framework
- **BroadcastDomain**: Bulk messaging capabilities

#### Integration Domains
- **FacebookInstagramIntegrationDomain**: Meta platform integration
- **ShopifyIntegrationDomain**: E-commerce platform integration
- **StripeIntegrationDomain**: Payment processing
- **ZapierIntegrationDomain**: Workflow automation
- **HubSpotIntegrationDomain**: CRM integration

#### Business Domains
- **CompanyDomain**: Multi-tenant company management
- **ContactDomain**: Customer contact management
- **UserProfiles**: Staff and user management
- **SubscriptionPlanDomain**: Billing and plan management
- **AnalyticsDomain**: Business intelligence and reporting

#### Platform Domains
- **AutomationDomain**: Workflow automation rules
- **FlowHubDomain**: Advanced workflow engine
- **IntelligentHubDomain**: AI-powered features
- **NotificationHubs**: Push notification management

### Domain Structure Pattern

```
{DomainName}/
├── Models/           # Domain entities and value objects
├── Services/         # Domain services and business logic
├── ViewModels/       # API contracts and DTOs
├── Repositories/     # Data access abstractions
├── Exceptions/       # Domain-specific exceptions
└── Constants/        # Domain constants and enums
```

## Technology Stack

### Backend Technologies
- **.NET 8**: Primary development framework
- **ASP.NET Core**: Web API framework
- **Entity Framework Core 8**: ORM with SQL Server
- **SignalR**: Real-time communication
- **Hangfire**: Background job processing
- **AutoMapper**: Object-to-object mapping
- **FluentValidation**: Input validation
- **Polly**: Resilience and transient-fault handling

### Data & Caching
- **Azure SQL Database**: Primary database with read replicas
- **Redis**: Distributed caching and session storage
- **Azure Blob Storage**: File and media storage

### Monitoring & Logging
- **Serilog**: Structured logging
- **Application Insights**: APM and monitoring
- **OpenTelemetry**: Distributed tracing
- **Azure Log Analytics**: Log aggregation

### Third-Party Integrations
- **Auth0**: Identity and access management
- **Stripe**: Payment processing
- **Twilio**: SMS communication
- **SendGrid**: Email delivery
- **Various Channel APIs**: WhatsApp, Facebook, Instagram, etc.

## Deployment & DevOps

### Containerization
- **Docker**: Application containerization
- **Azure Container Registry**: Container image storage
- **Multi-stage builds**: Optimized container images

### CI/CD Pipeline
- **Docker Compose**: Local development environment
- **Azure DevOps**: CI/CD pipeline management
- **Blue-green deployment**: Zero-downtime deployments
- **Slot swapping**: Production deployment strategy

### Configuration Management
- **Environment-specific configurations**: appsettings.{env}.json
- **Azure Key Vault**: Secrets management
- **Feature flags**: Controlled feature rollouts

## Multi-Region Setup

### Primary Regions

| Region | Code | Purpose | Services |
|--------|------|---------|----------|
| East Asia | `eas` | Primary region | All services |
| East US | `eus` | Secondary region | Core services |
| Southeast Asia | `seas` | Regional expansion | Core services |
| UAE North | `uaen` | MENA market | Core services |
| West Europe | `weu` | European market | Core services |

### Global Distribution
- **Azure Front Door**: Global load balancing and traffic routing
- **CDN**: Content delivery network for static assets
- **Regional databases**: Data locality for compliance
- **Cross-region replication**: Disaster recovery

### Auto-Scaling Configuration
- **CPU-based scaling**: Scale out at >50% CPU, scale in at <30%
- **Memory-based scaling**: Scale out at >70% memory, scale in at <50%
- **Time-based scaling**: Increased capacity during business hours
- **Instance limits**: Configurable min/max instances per region

## Monitoring & Observability

### Application Performance Monitoring
- **Application Insights**: Real-time performance monitoring
- **Custom metrics**: Business-specific KPIs
- **Dependency tracking**: External service monitoring
- **Exception tracking**: Error monitoring and alerting

### Alerting Strategy
- **P1 Alerts (Critical)**: CPU >90%, Memory >90%, Response time >3s
- **P2 Alerts (Warning)**: CPU >80%, Memory >80%, Response time >1s
- **Integration**: OpsGenie for incident management
- **Health checks**: Automated endpoint monitoring

### Logging
- **Structured logging**: JSON-formatted logs with Serilog
- **Correlation IDs**: Request tracing across services
- **Multi-sink logging**: Console, Azure Analytics, Google Cloud
- **Log levels**: Environment-specific log verbosity

## Database Architecture

### Primary Database (Azure SQL)
- **Multi-tenant design**: Company-scoped data isolation
- **Read replicas**: Geographic read scaling
- **Connection pooling**: Optimized database connections
- **Migration strategy**: EF Core migrations with SQL scripts

### Caching Strategy
- **Redis Cluster**: Distributed caching across regions
- **Cache patterns**: Cache-aside, write-through
- **Session storage**: Distributed session management
- **Background job state**: Hangfire job persistence

### Data Models
- **Entity Framework**: Code-first approach
- **Soft deletes**: Audit-friendly data retention
- **Temporal tables**: Change tracking for audit
- **Indexes**: Performance-optimized queries

## Security & Authentication

### Authentication Flow
1. **Auth0 Integration**: External identity provider
2. **JWT Tokens**: Stateless authentication
3. **Refresh Tokens**: Secure token renewal
4. **Multi-factor Authentication**: Enhanced security

### Authorization
- **Role-Based Access Control (RBAC)**: Hierarchical permissions
- **Company-scoped access**: Multi-tenant data isolation
- **API Key management**: Service-to-service authentication
- **Rate limiting**: API abuse prevention

### Data Protection
- **Encryption at rest**: Azure SQL TDE, Blob Storage encryption
- **Encryption in transit**: TLS 1.2+ for all communications
- **PII masking**: Sensitive data protection
- **GDPR compliance**: Data privacy and right to be forgotten

### Network Security
- **Virtual Networks**: Private communication channels
- **Private endpoints**: Secure service connectivity
- **Network Security Groups**: Traffic filtering
- **Azure Front Door**: DDoS protection and WAF

## Performance Optimization

### Caching Strategy
- **Multi-level caching**: Application, Redis, CDN
- **Cache invalidation**: Event-driven cache updates
- **Cache warming**: Proactive cache population

### Database Optimization
- **Read replicas**: Query load distribution
- **Query optimization**: Index tuning and query analysis
- **Connection pooling**: Efficient database connections
- **Bulk operations**: Optimized data processing

### Auto-Scaling
- **Horizontal scaling**: Instance-based scaling
- **Vertical scaling**: Resource-based scaling
- **Predictive scaling**: AI-driven capacity planning

---

*This architecture documentation provides a comprehensive overview of the SleekFlow backend system. For specific implementation details, refer to the individual service documentation and code comments.*
