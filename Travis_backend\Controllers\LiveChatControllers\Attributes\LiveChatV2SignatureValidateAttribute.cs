using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.DependencyInjection;
using Travis_backend.ChannelDomain.Services.LiveChatV2;

namespace Travis_backend.Controllers.LiveChatControllers.Attributes;

[AttributeUsage(AttributeTargets.Method)]
public class LiveChatV2SignatureValidateAttribute : Attribute, IAsyncActionFilter
{
    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        var senderId = context.HttpContext.GetRouteValue("senderId")?.ToString();

        // Extract senderId from route
        if (string.IsNullOrEmpty(senderId))
        {
            context.Result = new BadRequestObjectResult("Missing senderId in route");
            return;
        }

        var request = context.HttpContext.Request;
        var providedSignature = request.Headers["X-Sleekflow-LiveChat-Signature"].ToString();

        // Get provided signature/hash from header
        if (string.IsNullOrEmpty(providedSignature))
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        var liveChatV2AuthService = context.HttpContext.RequestServices.GetRequiredService<ILiveChatV2AuthManager>();
        var expectedSignature = liveChatV2AuthService.GenerateSignature(senderId);
        if (!string.Equals(providedSignature, expectedSignature))
        {
            context.Result = new UnauthorizedResult();
            return;
        }

        await next();
    }
}