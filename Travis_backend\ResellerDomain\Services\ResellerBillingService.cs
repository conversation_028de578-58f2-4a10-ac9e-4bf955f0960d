using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FlowHubs;
using Travis_backend.Helpers;
using Travis_backend.IntelligentHubDomain.Services;
using Travis_backend.InternalDomain.Services;
using Travis_backend.ResellerDomain.Constants;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.SleekflowCrmHubDomain.Services;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.TenantHubDomain.Services;

namespace Travis_backend.ResellerDomain.Services;

public interface IResellerBillingService
{
    Task MonthlyResellerBilling(List<string> resellerProfileIds = null);

    Task CheckAndRenewResellerClientBillRecord(string companyId);

    Task ResetCanceledBillRecordUsage(long billRecordId);
}

public class ResellerBillingService : IResellerBillingService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IUserRoleStaffRepository _userRoleStaffRepository;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;
    private readonly IResellerBaseService _resellerBaseService;
    private readonly IUsageCycleCalculator _usageCycleCalculator;
    private readonly IEnabledFeaturesService _enabledFeaturesService;
    private readonly ILogger<ResellerBillingService> _logger;

    public ResellerBillingService(
        ApplicationDbContext appDbContext,
        IUserRoleStaffRepository userRoleStaffRepository,
        ICompanyInfoCacheService companyInfoCacheService,
        IResellerBaseService resellerBaseService,
        IUsageCycleCalculator usageCycleCalculator,
        IEnabledFeaturesService enabledFeaturesService,
        ILogger<ResellerBillingService> logger)
    {
        _appDbContext = appDbContext;
        _userRoleStaffRepository = userRoleStaffRepository;
        _companyInfoCacheService = companyInfoCacheService;
        _resellerBaseService = resellerBaseService;
        _usageCycleCalculator = usageCycleCalculator;
        _enabledFeaturesService = enabledFeaturesService;
        _logger = logger;
    }

    public async Task MonthlyResellerBilling(List<string> resellerProfileIds = null)
    {
        List<ResellerClientCompanyProfile> resellerClientCompanyProfiles;

        // monthly billing at the start of every day
        if (resellerProfileIds != null && resellerProfileIds.Any())
        {
            resellerClientCompanyProfiles = await _appDbContext.ResellerClientCompanyProfiles
                .Where(x => resellerProfileIds.Contains(x.ResellerCompanyProfileId))
                .ToListAsync();
        }
        else
        {
            resellerClientCompanyProfiles = await _appDbContext.ResellerClientCompanyProfiles
                .ToListAsync();
        }

        foreach (var resellerClientCompanyProfile in resellerClientCompanyProfiles)
        {
            await CheckAndRenewResellerClientBillRecord(resellerClientCompanyProfile.ClientCompanyId);
        }
    }

    public async Task CheckAndRenewResellerClientBillRecord(string companyId)
    {
        var resellerClientProfile = await _appDbContext.ResellerClientCompanyProfiles
            .Include(x => x.ResellerCompanyProfile)
            .Include(x => x.ClientCompany)
            .ThenInclude(x => x.BillRecords)
            .ThenInclude(x => x.SubscriptionPlan)
            .FirstOrDefaultAsync(
                x => x.ClientCompanyId == companyId &&
                     x.ClientCompany.CompanyType == CompanyType.ResellerClient);

        if (resellerClientProfile == null || resellerClientProfile.ClientCompany.IsDeleted)
        {
            return;
        }

        var resellerProfile = resellerClientProfile.ResellerCompanyProfile;

        resellerProfile = await _resellerBaseService.CheckAndUpdateResellerSubscriptionPlanConfig(resellerProfile);

        var clientCompany = resellerClientProfile.ClientCompany;
        var billRecords = clientCompany.BillRecords
            .OrderByDescending(x => x.UpdatedAt)
            .ToList();

        // check plan still active one day before period ends, sort by subscription plans first, then following add-on plans
        var subscriptionPlans = resellerClientProfile.ClientCompany.BillRecords
            .Where(
                x => x.Status is BillStatus.Active or BillStatus.Canceled &&
                     x.PeriodEnd.AddDays(-1).Date == DateTime.UtcNow.Date)
            .Select(x => x.SubscriptionPlan)
            .Distinct()
            .OrderByDescending(
                x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.Id))
            .ToList();

        foreach (var subscriptionPlan in subscriptionPlans)
        {
            if (ValidSubscriptionPlan.FreePlans.Contains(subscriptionPlan.Id))
            {
                var lastBillRecord = billRecords.OrderByDescending(x => x.Id).FirstOrDefault(
                    x => x.SubscriptionPlanId == subscriptionPlan.Id &&
                         x.Status is BillStatus.Active or BillStatus.Canceled &&
                         x.PeriodStart < DateTime.UtcNow && x.PeriodEnd > DateTime.UtcNow);

                if (lastBillRecord == null)
                {
                    continue;
                }

                switch (lastBillRecord.Status)
                {
                    case BillStatus.Active:

                        var billRecord = new BillRecord()
                        {
                            CompanyId = resellerClientProfile.ClientCompanyId,
                            SubscriptionPlanId = subscriptionPlan.Id,
                            PaidByReseller = true,
                            Status = BillStatus.Active,
                            PaymentStatus = PaymentStatus.FreeOfCharge,
                            SubscriptionTier = subscriptionPlan.SubscriptionTier,
                            PayAmount = 0,
                            currency = resellerProfile.Currency,
                            PeriodStart = lastBillRecord.PeriodEnd,
                            PeriodEnd = lastBillRecord.PeriodEnd.AddMonths(
                                BillRecordRevenueCalculatorHelper.GetMonthDiff(
                                    lastBillRecord.PeriodStart,
                                    lastBillRecord.PeriodEnd)),
                            quantity = 1,
                            IsCustomized = lastBillRecord.IsCustomized
                        };

                        if (subscriptionPlan.PlanTypeCode == PlanTypeCodes.BasePlan)
                        {
                            var usageCycle = _usageCycleCalculator.GetUsageCycle(billRecord.PeriodStart, billRecord.PeriodEnd);
                            billRecord.UsageCycleStart = usageCycle.From;
                            billRecord.UsageCycleEnd = usageCycle.To;
                        }

                        await _appDbContext.CompanyBillRecords.AddAsync(billRecord);
                        lastBillRecord.Status = BillStatus.Renewed;
                        lastBillRecord.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        resellerProfile.Debited += 0;

                        var transactionLog = new ResellerTransactionLog()
                        {
                            ResellerCompanyProfileId = resellerProfile.Id,
                            Amount = 0,
                            Currency = resellerProfile.Currency,
                            BillRecordId = billRecord.Id,
                            ClientCompanyId = resellerClientProfile.ClientCompanyId,
                            TransactionMode = TransactionMode.Debit,
                            TransactionCategory = ResellerTransactionCategory.Subscription,
                            TransactionAction = "Renew " + _resellerBaseService.SetTransactionAction(subscriptionPlan)
                        };

                        await _appDbContext.ResellerTransactionLogs.AddAsync(transactionLog);
                        await _appDbContext.SaveChangesAsync();

                        break;

                    case BillStatus.Canceled:
                        // schedule from one day before to reset company record usage when period end time comes
                        var cancelTs = lastBillRecord.PeriodEnd - DateTime.UtcNow;
                        BackgroundJob.Schedule<IResellerBillingService>(
                            x => x.ResetCanceledBillRecordUsage(lastBillRecord.Id),
                            cancelTs);

                        break;
                }
            }
            else if (ValidSubscriptionPlan.ProTier.Contains(subscriptionPlan.Id) ||
                     ValidSubscriptionPlan.PremiumTier.Contains(subscriptionPlan.Id))
            {
                var lastBillRecord = billRecords
                    .OrderByDescending(x => x.Id)
                    .FirstOrDefault(
                        x => x.SubscriptionPlanId == subscriptionPlan.Id &&
                             x.Status is BillStatus.Active or BillStatus.Canceled &&
                             x.PeriodStart < DateTime.UtcNow && x.PeriodEnd > DateTime.UtcNow);

                if (lastBillRecord == null)
                {
                    continue;
                }

                switch (lastBillRecord.Status)
                {
                    case BillStatus.Active:
                        var monthDifference = BillRecordRevenueCalculatorHelper.GetMonthDiff(
                            lastBillRecord.PeriodStart,
                            lastBillRecord.PeriodEnd);

                        var fee = lastBillRecord.IsCustomized
                            ? Math.Round(Convert.ToDecimal(lastBillRecord.PayAmount), 0)
                            : Math.Round(
                                Convert.ToDecimal(subscriptionPlan.Amount) * (1 - resellerProfile.ResellerDiscount),
                                0) * monthDifference;

                        if (resellerProfile.Balance - fee < resellerProfile.BalanceMinimumLimit)
                        {
                            lastBillRecord.Status = BillStatus.Canceled;
                            await _appDbContext.SaveChangesAsync();
                            TimeSpan ts = lastBillRecord.PeriodEnd - DateTime.UtcNow;
                            BackgroundJob.Schedule<IResellerBillingService>(
                                x => x.ResetCanceledBillRecordUsage(lastBillRecord.Id),
                                ts);

                            var freePlanBillRecord = new BillRecord()
                            {
                                CompanyId = lastBillRecord.CompanyId,
                                SubscriptionPlanId =
                                    resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId],
                                PaidByReseller = true,
                                Status = BillStatus.Active,
                                PaymentStatus = PaymentStatus.FreeOfCharge,
                                SubscriptionTier = SubscriptionTier.Free,
                                PayAmount = 0,
                                currency = resellerProfile.Currency,
                                PeriodStart = lastBillRecord.PeriodEnd,
                                PeriodEnd = lastBillRecord.PeriodEnd.AddMonths(monthDifference),
                                quantity = 1,
                                IsCustomized = lastBillRecord.IsCustomized
                            };

                            await _appDbContext.CompanyBillRecords.AddAsync(freePlanBillRecord);
                            await _appDbContext.SaveChangesAsync();

                            var freePlanTransactionLog = new ResellerTransactionLog()
                            {
                                ResellerCompanyProfileId = resellerProfile.Id,
                                Amount = 0,
                                Currency = resellerProfile.Currency,
                                BillRecordId = freePlanBillRecord.Id,
                                ClientCompanyId = resellerClientProfile.ClientCompanyId,
                                TransactionMode = TransactionMode.Debit,
                                TransactionCategory = ResellerTransactionCategory.Subscription,
                                TransactionAction = "Renew " +
                                                    _resellerBaseService.SetTransactionAction(subscriptionPlan) +
                                                    " To Free Plan"
                            };

                            await _appDbContext.ResellerTransactionLogs.AddAsync(freePlanTransactionLog);
                            await _appDbContext.SaveChangesAsync();

                            var freePlanMaximumApiCalls = await _appDbContext.CoreSubscriptionPlans
                                .Where(
                                    x => x.Id ==
                                         resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds
                                             .SubscriptionFreePlanId])
                                .AsNoTracking()
                                .Select(x => x.MaximumAPICall)
                                .FirstOrDefaultAsync();

                            // Update Company API Keys limit
                            await _appDbContext.CompanyAPIKeys
                                .Where(x => x.CompanyId == companyId)
                                .ExecuteUpdateAsync(
                                    key =>
                                        key
                                            .SetProperty(k => k.CallLimit, freePlanMaximumApiCalls)
                                            .SetProperty(k => k.Calls, 0));

                            await _appDbContext.SaveChangesAsync();

                            // Feature Flag related
                            try
                            {
                                await _enabledFeaturesService.UpdateDefaultFeatureFlagForCompanyBasedOnSubscriptionTier(
                                    resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId],
                                    companyId);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "Error updating feature flag for company");
                            }

                            // HubSpot
                            BackgroundJob.Enqueue<IInternalHubSpotService>(x => x.SyncCompany(companyId, null));

                            var staff = await _userRoleStaffRepository.GetFirstAdmin(companyId);

                            BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(companyId, null));
                            BackgroundJob.Enqueue<IIntelligentHubService>(
                                x => x.RefreshIntelligentHubConfigAsync(companyId));
                            BackgroundJob.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(companyId, staff));

                            BackgroundJob.Enqueue<IInternalAnalyticService>(
                                x => x.UpdateCompanyAllTimeRevenueAnalyticInfo(
                                    new List<string>()
                                    {
                                        companyId
                                    }));

                            await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);

                            continue;
                        }

                        var billRecord = new BillRecord()
                        {
                            CompanyId = resellerClientProfile.ClientCompanyId,
                            SubscriptionPlanId = subscriptionPlan.Id,
                            PaidByReseller = true,
                            Status = BillStatus.Active,
                            PaymentStatus = PaymentStatus.Paid,
                            SubscriptionTier = subscriptionPlan.SubscriptionTier,
                            PayAmount = Convert.ToDouble(fee),
                            currency = resellerProfile.Currency,
                            PeriodStart = lastBillRecord.PeriodEnd,
                            PeriodEnd = lastBillRecord.PeriodEnd.AddMonths(monthDifference),
                            IsCustomized = lastBillRecord.IsCustomized,
                            quantity = 1
                        };

                        if (subscriptionPlan.PlanTypeCode == PlanTypeCodes.BasePlan)
                        {
                            var usageCycle = _usageCycleCalculator.GetUsageCycle(billRecord.PeriodStart, billRecord.PeriodEnd);
                            billRecord.UsageCycleStart = usageCycle.From;
                            billRecord.UsageCycleEnd = usageCycle.To;
                        }

                        await _appDbContext.CompanyBillRecords.AddAsync(billRecord);
                        lastBillRecord.Status = BillStatus.Renewed;
                        lastBillRecord.UpdatedAt = DateTime.UtcNow;
                        await _appDbContext.SaveChangesAsync();

                        resellerProfile.Debited += fee;

                        var transactionLog = new ResellerTransactionLog()
                        {
                            ResellerCompanyProfileId = resellerProfile.Id,
                            Amount = fee,
                            Currency = resellerProfile.Currency,
                            BillRecordId = billRecord.Id,
                            ClientCompanyId = resellerClientProfile.ClientCompanyId,
                            TransactionMode = TransactionMode.Debit,
                            TransactionCategory = ResellerTransactionCategory.Subscription,
                            TransactionAction = "Renew " + _resellerBaseService.SetTransactionAction(subscriptionPlan)
                        };

                        await _appDbContext.ResellerTransactionLogs.AddAsync(transactionLog);
                        await _appDbContext.SaveChangesAsync();

                        // Feature Flag related
                        try
                        {
                            await _enabledFeaturesService.UpdateDefaultFeatureFlagForCompanyBasedOnSubscriptionTier(
                                subscriptionPlan.Id,
                                companyId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error updating feature flag for company");
                        }

                        break;

                    case BillStatus.Canceled:
                        // schedule from one day before to reset company record usage when period end time comes
                        var cancelTs = lastBillRecord.PeriodEnd - DateTime.UtcNow;
                        BackgroundJob.Schedule<IResellerBillingService>(
                            x => x.ResetCanceledBillRecordUsage(lastBillRecord.Id),
                            cancelTs);

                        break;
                }
            }
            else if (ValidSubscriptionPlan.CmsAllAddOn.Contains(subscriptionPlan.Id))
            {
                var lastBillRecords = billRecords
                    .Where(
                        x => x.SubscriptionPlanId == subscriptionPlan.Id &&
                             x.Status is BillStatus.Active or BillStatus.Canceled &&
                             x.PeriodStart < DateTime.UtcNow && x.PeriodEnd > DateTime.UtcNow)
                    .OrderByDescending(x => x.PeriodEnd)
                    .ToList();

                foreach (var lastBillRecord in lastBillRecords)
                {
                    var monthDifference = BillRecordRevenueCalculatorHelper.GetMonthDiff(
                        lastBillRecord.PeriodStart,
                        lastBillRecord.PeriodEnd);

                    decimal fee;

                    if (lastBillRecord.IsCustomized)
                    {
                        fee = Math.Round(Convert.ToDecimal(lastBillRecord.PayAmount), 0);
                    }
                    else
                    {
                        if (ValidSubscriptionPlan.WhatsAppPhoneNumberAddOns.Contains(lastBillRecord.SubscriptionPlanId))
                        {
                            fee = Math.Round(
                                Convert.ToDecimal(subscriptionPlan.Amount),
                                0) * lastBillRecord.quantity * monthDifference;
                        }
                        else
                        {
                            fee = Math.Round(
                                Convert.ToDecimal(subscriptionPlan.Amount) * (1 - resellerProfile.ResellerDiscount),
                                0) * lastBillRecord.quantity * monthDifference;
                        }
                    }

                    switch (lastBillRecord.Status)
                    {
                        case BillStatus.Active:
                            if (resellerProfile.Balance - fee < resellerProfile.BalanceMinimumLimit)
                            {
                                lastBillRecord.Status = BillStatus.Canceled;
                                await _appDbContext.SaveChangesAsync();
                                TimeSpan ts = lastBillRecord.PeriodEnd - DateTime.UtcNow;
                                BackgroundJob.Schedule<IResellerBillingService>(
                                    x => x.ResetCanceledBillRecordUsage(lastBillRecord.Id),
                                    ts);

                                continue;
                            }

                            var billRecord = new BillRecord()
                            {
                                CompanyId = resellerClientProfile.ClientCompanyId,
                                SubscriptionPlanId = subscriptionPlan.Id,
                                PaidByReseller = true,
                                Status = BillStatus.Active,
                                PaymentStatus = PaymentStatus.Paid,
                                SubscriptionTier = subscriptionPlan.SubscriptionTier,
                                PayAmount = Convert.ToDouble(fee),
                                currency = resellerProfile.Currency,
                                PeriodStart = lastBillRecord.PeriodEnd,
                                PeriodEnd = lastBillRecord.PeriodEnd.AddMonths(monthDifference),
                                IsCustomized = lastBillRecord.IsCustomized,
                                quantity = lastBillRecord.quantity
                            };

                            if (subscriptionPlan.PlanTypeCode == PlanTypeCodes.BasePlan)
                            {
                                var usageCycle = _usageCycleCalculator.GetUsageCycle(billRecord.PeriodStart, billRecord.PeriodEnd);
                                billRecord.UsageCycleStart = usageCycle.From;
                                billRecord.UsageCycleEnd = usageCycle.To;
                            }

                            await _appDbContext.CompanyBillRecords.AddAsync(billRecord);
                            lastBillRecord.Status = BillStatus.Renewed;
                            lastBillRecord.UpdatedAt = DateTime.UtcNow;
                            await _appDbContext.SaveChangesAsync();

                            resellerProfile.Debited += fee;

                            var transactionLog = new ResellerTransactionLog()
                            {
                                ResellerCompanyProfileId = resellerProfile.Id,
                                Amount = fee,
                                Currency = resellerProfile.Currency,
                                BillRecordId = billRecord.Id,
                                ClientCompanyId = resellerClientProfile.ClientCompanyId,
                                TransactionMode = TransactionMode.Debit,
                                TransactionCategory = ResellerTransactionCategory.Subscription,
                                TransactionAction =
                                    "Renew " + _resellerBaseService.SetTransactionAction(subscriptionPlan)
                            };

                            await _appDbContext.ResellerTransactionLogs.AddAsync(transactionLog);
                            await _appDbContext.SaveChangesAsync();

                            break;

                        case BillStatus.Canceled:
                            // schedule from one day before to reset company record usage when period end time comes
                            var cancelTs = lastBillRecord.PeriodEnd - DateTime.UtcNow;
                            BackgroundJob.Schedule<IResellerBillingService>(
                                x => x.ResetCanceledBillRecordUsage(lastBillRecord.Id),
                                cancelTs);

                            break;
                    }
                }
            }
        }
    }

    public async Task ResetCanceledBillRecordUsage(long billRecordId)
    {
        var billRecord = await _appDbContext.CompanyBillRecords.Include(x => x.SubscriptionPlan)
            .FirstOrDefaultAsync(x => x.Id == billRecordId);
        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(
            x => x.Id == billRecord.CompanyId && x.CompanyType == CompanyType.ResellerClient);

        if (company == null)
        {
            return;
        }

        if (billRecord is { Status: BillStatus.Canceled })
        {
            // Reset Usage Limit
            if (ValidSubscriptionPlan.SubscriptionPlan.Contains(billRecord.SubscriptionPlanId))
            {
                company.MaximumAutomations = null;
                company.MaximumAgents = 0;
                company.MaximumContacts = null;
                company.MaximumWhAutomatedMessages = null;
                company.MaximumWhatsappInstance = 0;
            }
            else if (ValidSubscriptionPlan.AgentPlan
                     .Contains(billRecord.SubscriptionPlanId))
            {
                company.MaximumAgents = 0;
            }
            else if (ValidSubscriptionPlan.AdditionalContactAddOns
                     .Contains(billRecord.SubscriptionPlanId))
            {
                company.MaximumContacts = null;
            }
            else if (ValidSubscriptionPlan.WhatsAppPhoneNumberAddOns
                     .Contains(billRecord.SubscriptionPlanId))
            {
                company.MaximumWhatsappInstance = 0;
            }

            billRecord.UpdatedAt = DateTime.UtcNow;
            await _appDbContext.SaveChangesAsync();

            await _companyInfoCacheService.RemoveCompanyInfoCache(company.Id);
        }
    }
}