﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Database.Services;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Models;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Utils;
using Newtonsoft.Json.Linq;

namespace Travis_backend.FlowHubs.FieldMetadataGenerator;

public sealed class TicketCreatedEventFieldMetadataGenerator
    : FlowHubEventFieldMetadataGeneratorBase,
        IFlowHubEventFieldMetadataGenerator
{
    public string EventName => TriggerIds.TicketCreated;

    public Dictionary<string, string> FieldPathOptionRequestPathDict { get; } = new()
    {
        { "ticket.status_id", "FlowHub/SelectOptions/TicketStatuses" },
        { "ticket.priority_id", "FlowHub/SelectOptions/TicketPriorities" },
        { "ticket.type_id", "FlowHub/SelectOptions/TicketTypes" },
        { "ticket.channel.channel_identity_id", "FlowHub/SelectOptions/Channels" },
        { "contactOwner.id", "FlowHub/SelectOptions/Staffs" }
    };

    public TicketCreatedEventFieldMetadataGenerator(
        IDbContextService dbContextService, IFieldMetadataVariableVisibilityService fieldMetadataVariableVisibilityService)
        : base(dbContextService, fieldMetadataVariableVisibilityService)
    {
    }

    public async Task<(HashSet<FieldMetadata> FieldMetadataSet, Type EventBodyType, string ContactObjectName)> GenerateAsync(
        string companyId,
        Dictionary<string, object?>? parameters)
    {
        var (userProfileDict, customFieldOptionsRequestPathDict) = await GenerateUserProfileDictAsync(companyId);
        var eventBody = JsonUtils.GenerateSampleObjectFromType<OnTicketCreatedEventBody>();
        eventBody.Contact = userProfileDict;

        var eventBodyJson = JObject.FromObject(eventBody);
        AddContactExpansionToEventBodyJson(eventBodyJson);

        var fieldMetadataSet = JsonUtils
            .SimplifyJsonData(eventBodyJson.ToString())
            .GetFieldMetadata();

        PopulateOptionRequestPath(
            fieldMetadataSet,
            customFieldOptionsRequestPathDict,
            FieldPathOptionRequestPathDict);

        return (fieldMetadataSet, typeof(OnTicketCreatedEventBody), nameof(eventBody.Contact));
    }
}