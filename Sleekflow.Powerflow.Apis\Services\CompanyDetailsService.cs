using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sleekflow.Powerflow.Apis.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.ResellerDomain.Services;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices.Models;
using CmsCompanyResponse = Sleekflow.Powerflow.Apis.ViewModels.CmsCompanyResponse;
using CmsCompanyStaffDto = Sleekflow.Powerflow.Apis.ViewModels.CmsCompanyStaffDto;
using CmsCompanyTeamDto = Sleekflow.Powerflow.Apis.ViewModels.CmsCompanyTeamDto;
using CmsContactOwnerAssignLogDto = Sleekflow.Powerflow.Apis.ViewModels.CmsContactOwnerAssignLogDto;
using CmsResellerClientResponse = Sleekflow.Powerflow.Apis.ViewModels.CmsResellerClientResponse;
using CmsResellerResponse = Sleekflow.Powerflow.Apis.ViewModels.CmsResellerResponse;
using CmsWhatsApp360DialogUsageRecordViewModel = Sleekflow.Powerflow.Apis.ViewModels.CmsWhatsApp360DialogUsageRecordViewModel;

namespace Sleekflow.Powerflow.Apis.Services
{
    public interface ICompanyDetailsService
    {
        Task<GetAllCompanyDetailsResponse?> GetAllCompanyDetailsAsync(string companyId);
    }

    public class CompanyDetailsService : ICompanyDetailsService
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMapper _mapper;
        private readonly ILogger<CompanyDetailsService> _logger;
        private readonly ICompanyUsageService _companyUsageService;
        private readonly IPowerflowManageResellerRepository _powerflowManageResellerRepository;
        private readonly IResellerPortalRepository _resellerPortalRepository;
        private readonly IBillRecordRevenueCalculatorService _billRecordRevenueCalculatorService;

        public CompanyDetailsService(
            ApplicationDbContext appDbContext,
            IMapper mapper,
            ILogger<CompanyDetailsService> logger,
            ICompanyUsageService companyUsageService,
            IPowerflowManageResellerRepository powerflowManageResellerRepository,
            IResellerPortalRepository resellerPortalRepository,
            IBillRecordRevenueCalculatorService billRecordRevenueCalculatorService)
        {
            _appDbContext = appDbContext;
            _mapper = mapper;
            _logger = logger;
            _companyUsageService = companyUsageService;
            _powerflowManageResellerRepository = powerflowManageResellerRepository;
            _resellerPortalRepository = resellerPortalRepository;
            _billRecordRevenueCalculatorService = billRecordRevenueCalculatorService;
        }

        public async Task<GetAllCompanyDetailsResponse?> GetAllCompanyDetailsAsync(string companyId)
        {
            var exists = await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == companyId);
            if (!exists)
            {
                return null;
            }

            var result = new GetAllCompanyDetailsResponse();

            var companyDetail = await GetCompanyDetailAsync(companyId);
            var companyUsage = await GetCompanyUsageAsync(companyId, companyDetail.TimeZoneInfo?.Id);
            var billRecord = await GetCompanyBillRecordsAsync(
                companyId,
                companyDetail,
                companyUsage.CompanyUsage?.billingPeriodUsages?.FirstOrDefault()?.BillRecord?.Id);

            // Update periodEnd if actual_period_end exists in metadata
            if (billRecord?.BillRecords != null)
            {
                foreach (var record in billRecord.BillRecords)
                {
                    if (record.Metadata?.TryGetValue("actual_period_end", out string actualPeriodEndString) == true)
                    {
                        if (DateTime.TryParse(actualPeriodEndString, out var parsedDate))
                        {
                            record.PeriodEnd = parsedDate;
                        }
                    }
                }
            }

            // Update periodStart and periodEnd for CmsBillingPeriodUsages if actual values exist in metadata
            if (companyUsage?.CmsBillingPeriodUsages != null)
            {
                foreach (var usage in companyUsage.CmsBillingPeriodUsages)
                {
                    if (usage.Metadata?.TryGetValue("actual_period_end", out string actualPeriodEndString) == true)
                    {
                        if (DateTime.TryParse(actualPeriodEndString, out var parsedDate))
                        {
                            usage.PeriodEnd = parsedDate;
                        }
                    }
                }
            }

            var staff = await GetCompanyStaffAsync(companyId);

            result.CompanyDetail = companyDetail;
            result.CompanyUsage = companyUsage;
            result.CompanyBillRecord = billRecord;
            result.CompanyStaff = staff;

            return result;
        }

        private async Task<CmsCompanyResponse> GetCompanyDetailAsync(string companyId)
        {
            var company = await _appDbContext.CompanyCompanies
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.FacebookConfigs)
                .Include(x => x.WhatsAppConfigs)
                .Include(x => x.EmailConfig)
                .Include(x => x.WeChatConfig)
                .Include(x => x.LineConfigs)
                .Include(x => x.ViberConfigs)
                .Include(x => x.TelegramConfigs)
                .Include(x => x.SMSConfigs)
                .Include(x => x.ShopifyConfigs)
                .Include(x => x.ShoplineConfigs)
                .Include(x => x.InstagramConfigs)
                .Include(x => x.CompanyIconFile)
                .Include(x => x.TwilioUsageRecords)
                .Include(x => x.CmsCompanyOwner)
                .Include(x => x.CmsActivationOwner)
                .Include(x => x.CmsHubSpotCompanyMap)
                .Include(x => x.CmsContactOwnerChangeLogs)
                .ThenInclude(x => x.AssignedByUser)
                .Include(x => x.CmsContactOwnerChangeLogs)
                .ThenInclude(x => x.FromContactOwner)
                .Include(x => x.CmsContactOwnerChangeLogs)
                .ThenInclude(x => x.ToContactOwner)
                .Include(x => x.Whatsapp360DialogTopUpConfig)
                .Include(x => x.WhatsApp360DialogConfigs)
                .Include(x => x.WhatsappCloudApiConfigs)
                .Include(x => x.StripePaymentConfigs)
                .Include(x => x.CmsCompanyAdditionalInfo)
                .Include(x => x.CmsPartnerStackCustomerMap)
                .FirstOrDefaultAsync(x => x.Id == companyId);

            var companyDetails = _mapper.Map<CmsCompanyResponse>(company);
            var messenger = company.FacebookConfigs.Where(x => x.SubscribedFields.Contains("messages")).ToList();
            var leadAds = company.FacebookConfigs.Where(x => x.SubscribedFields.Contains("leadgen")).ToList();

            companyDetails.LeadAdsFacebookConfigs = _mapper.Map<List<FacebookConfigViewModel>>(leadAds);
            companyDetails.FacebookConfigs = _mapper.Map<List<FacebookConfigViewModel>>(messenger);

            if (!string.IsNullOrEmpty(company.TimeZoneInfoId))
            {
                companyDetails.TimeZoneInfo = TimeZoneHelper.GetTimeZoneById(company.TimeZoneInfoId);
            }

            companyDetails.CurrentAgents = await _appDbContext.UserRoleStaffs.AsNoTracking()
                .CountAsync(x => x.CompanyId == companyDetails.Id);

            companyDetails.CmsCompanyOwnerChangeLogs = _mapper.Map<List<CmsContactOwnerAssignLogDto>>(
                company.CmsContactOwnerChangeLogs.Where(x => x.ContactOwnerType == CmsContactOwnerType.CompanyOwner)
                    .OrderByDescending(x => x.CreatedAt)
                    .ToList());

            companyDetails.CmsActivationOwnerChangeLogs = _mapper.Map<List<CmsContactOwnerAssignLogDto>>(
                company.CmsContactOwnerChangeLogs.Where(x => x.ContactOwnerType == CmsContactOwnerType.ActivationOwner)
                    .OrderByDescending(x => x.CreatedAt)
                    .ToList());

            companyDetails.CmsCsOwnerChangeLogs = _mapper.Map<List<CmsContactOwnerAssignLogDto>>(
                company.CmsContactOwnerChangeLogs.Where(x => x.ContactOwnerType == CmsContactOwnerType.CsOwner)
                    .OrderByDescending(x => x.CreatedAt)
                    .ToList());

            if (!string.IsNullOrWhiteSpace(company.CmsCsOwnerId))
            {
                var cmsCsOwner = await _appDbContext.Users
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == company.CmsCsOwnerId);

                if (cmsCsOwner != null)
                {
                    companyDetails.CmsCsOwner = _mapper.Map<UserInfoResponse>(cmsCsOwner);
                }
            }

            companyDetails.CmsHubSpotCompanyMap = _mapper.Map<CmsHubSpotCompanyMapDto>(company.CmsHubSpotCompanyMap);
            companyDetails.CmsWhatsApp360DialogUsageRecords = await _appDbContext
                .CompanyWhatsApp360DialogUsageRecords
                .AsSplitQuery()
                .AsNoTracking()
                .Include(x => x.TransactionLogs)
                .Where(x => x.CompanyId == companyId)
                .ProjectTo<CmsWhatsApp360DialogUsageRecordViewModel>(_mapper.ConfigurationProvider)
                .ToListAsync();

            companyDetails.CmsWhatsApp360DialogUsageRecords.ForEach(
                x =>
                    x.TransactionLogs.Sort((t, y) => t.CreatedAt > y.CreatedAt ? -1 : 1));

            // PartnerStack
            if (company.CmsPartnerStackCustomerMap is { IndividualCommissionConfig: null })
            {
                var cmsPartnerStackCustomerMapToUpdate = await _appDbContext.CmsPartnerStackCustomerMaps
                    .FirstOrDefaultAsync(x => x.CompanyId == companyId);

                if (cmsPartnerStackCustomerMapToUpdate != null)
                {
                    cmsPartnerStackCustomerMapToUpdate.IndividualCommissionConfig = new IndividualCommissionConfig
                    {
                        SyncType = PartnerStackConstants.MrrSyncType,
                        IndividualCommissionRate = null,
                        CommissionEndDate = null
                    };

                    await _appDbContext.SaveChangesAsync();

                    company.CmsPartnerStackCustomerMap = cmsPartnerStackCustomerMapToUpdate;
                }
            }

            companyDetails.CmsPartnerStackCustomerMap =
                _mapper.Map<CmsPartnerStackCustomerMapDto>(company.CmsPartnerStackCustomerMap);

            if (!string.IsNullOrWhiteSpace(
                    companyDetails.CmsPartnerStackCustomerMap?.PartnerStackCustomerInformation?.PartnershipDealOwnerId))
            {
                var user = await _appDbContext.Users.FirstOrDefaultAsync(x =>
                    x.Id == companyDetails.CmsPartnerStackCustomerMap.PartnerStackCustomerInformation
                        .PartnershipDealOwnerId);
                if (user != null)
                {
                    companyDetails.CmsPartnerStackCustomerMap.PartnershipDealOwnerName = user.DisplayName;
                }
            }

            // Too Slow
            // var webClientSenders = await _appDbContext.SenderWebClientSenders
            //     .OrderByDescending(x=>x.CreatedAt)
            //     .FirstOrDefaultAsync(x => x.CreatedAt > DateTime.UtcNow.AddDays(-30) && x.CompanyId == companyId);
            //
            // if (webClientSenders != null)
            // {
            //     var liveChatInfo = await _appDbContext.SenderWebClientIPAddressInfos.Where(info =>
            //             webClientSenders.Id == info.WebClientSenderId
            //         )
            //         .Select(info => new
            //         {
            //             info.WebPath,
            //             info.CreatedAt,
            //         })
            //         .FirstOrDefaultAsync();
            //
            //     if (liveChatInfo != null && !string.IsNullOrWhiteSpace(liveChatInfo.WebPath) &&
            //         !liveChatInfo.WebPath.Contains("preview.sleekflow.io"))
            //     {
            //         var host = new Uri(liveChatInfo.WebPath);
            //
            //         companyDetails.LiveChatConfig = new CmsLiveChatConfig()
            //         {
            //             Host = host.Scheme + Uri.SchemeDelimiter + host.Host,
            //             CreateAt = liveChatInfo.CreatedAt,
            //             RecentUserCount = await _appDbContext.SenderWebClientSenders.CountAsync(x => x.CompanyId == companyId && x.CreatedAt > DateTime.UtcNow.AddDays(-30))
            //         };
            //     }
            // }
            if (company.CompanyType == CompanyType.Reseller)
            {
                var resellerCompanyResponse = new CmsResellerResponse();
                resellerCompanyResponse.ResellerInformation =
                    await _powerflowManageResellerRepository.GetResellerInformation(company.Id);

                resellerCompanyResponse.ResellerCompanyIds = await _appDbContext.ResellerClientCompanyProfiles
                    .Where(
                        x =>
                            x.ResellerCompanyProfileId == resellerCompanyResponse.ResellerInformation.ResellerProfileId)
                    .Select(x => x.ClientCompanyId)
                    .Distinct()
                    .ToListAsync();

                resellerCompanyResponse.ResellerTransactionLogs =
                    await _resellerPortalRepository.GetAllCmsTransactionLogs(
                        resellerCompanyResponse.ResellerInformation.ResellerProfileId);
                companyDetails.ResellerInfo = resellerCompanyResponse;
            }

            if (company.CompanyType == CompanyType.ResellerClient)
            {
                var resellerCompanyResponse = new CmsResellerClientResponse();
                var resellerCompanyId = await _appDbContext.ResellerClientCompanyProfiles
                    .Where(x => x.ClientCompanyId == company.Id).Select(x => x.ResellerCompanyProfile.CompanyId)
                    .FirstOrDefaultAsync();
                if (resellerCompanyId != null)
                {
                    resellerCompanyResponse.ResellerInformation =
                        await _powerflowManageResellerRepository.GetResellerInformation(resellerCompanyId);
                }

                companyDetails.ResellerClientInfo = resellerCompanyResponse;
            }

            return companyDetails;
        }

        private async Task<GetCmsCompanyBillRecordsResponse> GetCompanyBillRecordsAsync(
            string companyId,
            CmsCompanyResponse company,
            long? currentBillRecordId = null)
        {
            var result = new GetCmsCompanyBillRecordsResponse();

            var billRecords = await _appDbContext.CompanyBillRecords
                .AsNoTracking()
                .Include(x => x.SubscriptionPlan)
                .Include(x => x.PurchaseStaff.Identity)
                .Where(x => x.CompanyId == companyId)
                .OrderByDescending(x => x.created)
                .ThenByDescending(x => x.PayAmount)
                .ProjectTo<CmsBillRecordDto>(_mapper.ConfigurationProvider)
                .ToListAsync();

            billRecords.ForEach(
                billRecord =>
                {
                    billRecord.IsDeletable = billRecord.PayAmount == 0 &&
                                             string.IsNullOrWhiteSpace(billRecord.hosted_invoice_url) &&
                                             string.IsNullOrWhiteSpace(billRecord.invoice_Id) &&
                                             billRecord.CmsSalesPaymentRecords.Count == 0;

                    // if (currentBillRecordId == billRecord.Id)
                    //     billRecord.IsDeletable = false;
                });

            result.BillRecords = billRecords;

            var cmsSalesPaymentRecords = await _appDbContext.CmsSalesPaymentRecords
                .Include(x => x.Files)
                .Where(x => x.CompanyId == companyId)
                .ProjectTo<CmsSalesPaymentRecordDto>(_mapper.ConfigurationProvider)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();

            result.CmsSalesPaymentRecords = cmsSalesPaymentRecords;

            foreach (var bill in billRecords)
            {
                bill.CmsSalesPaymentRecords = cmsSalesPaymentRecords.Where(x => x.BillRecordId == bill.Id).ToList();
            }

            var end = DateTime.UtcNow.Date.AddHours(8);

            var firstDate = company.CreatedAt;
            var allBillRecordInitialPaidDate = billRecords.Select(x => x.PeriodStart).ToList();
            allBillRecordInitialPaidDate.AddRange(
                billRecords.SelectMany(x => x.CmsSalesPaymentRecords?.Select(b => b.PaidAt)).Where(x => x != null)
                    .Select(x => x.Value).ToList());

            if (allBillRecordInitialPaidDate.MinBy(x => x) < firstDate)
            {
                firstDate = allBillRecordInitialPaidDate.MinBy(x => x);
            }

            result.DailyAnalytics = _billRecordRevenueCalculatorService.GetDailyAnalytics(
                new List<CmsCompanyAnalyticDto>
                {
                new ()
                {
                    Id = company.Id,
                    CompanyName = company.CompanyName,
                    CompanyCountry = company.CompanyCountry,
                    CreatedAt = company.CreatedAt,
                    CmsLeadSource = company.CmsLeadSource,
                    CmsCompanyIndustry = company.CmsCompanyIndustry,
                    BillRecords = _mapper.Map<List<CmsAnalyticBillRecordDto>>(
                        billRecords.Where(x => x.Status != BillStatus.Inactive).ToList())
                }
                },
                firstDate,
                end);

            return result;
        }

        private async Task<CmsCompanyUsageResponse> GetCompanyUsageAsync(string companyId, string timezoneInfoId)
        {
            var result = new CmsCompanyUsageResponse();

            var usage = await _companyUsageService.GetCompanyUsage(companyId);
            result.CompanyUsage = usage;

            var allValidPlans = await _appDbContext.CompanyBillRecords
                .AsNoTracking()
                .Include(x => x.SubscriptionPlan)
                .Include(x => x.PurchaseStaff)
                .Include(x => x.CmsSalesPaymentRecords)
                .ThenInclude(x => x.Files)
                .Where(
                    x => x.CompanyId == companyId &&
                         (ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId) ||
                          ValidSubscriptionPlan.CmsMrrAllAddOnWithoutRevenuePlan.Contains(x.SubscriptionPlanId)) &&
                         x.PeriodStart < DateTime.UtcNow &&
                         x.PeriodEnd > DateTime.UtcNow &&
                         x.Status != BillStatus.Inactive)
                .OrderByDescending(br => br.created)
                .ToListAsync();

            result.MonthlyRecurringRevenue =
                _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                    allValidPlans,
                    timezoneInfoId);

            var orderedCmsBillingPeriodUsages = new List<BillRecord>();

            var subscriptionBillPlan = allValidPlans
                .Where(x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId))
                .MaxBy(x => x.created);

            orderedCmsBillingPeriodUsages.Add(subscriptionBillPlan);

            orderedCmsBillingPeriodUsages.AddRange(
                allValidPlans
                    .Where(x => ValidSubscriptionPlan.CmsAllAddOn.Contains(x.SubscriptionPlanId))
                    .OrderByDescending(x => x.created));

            orderedCmsBillingPeriodUsages = orderedCmsBillingPeriodUsages.Where(x => x != null).ToList();

            result.CmsBillingPeriodUsages = _mapper.Map<List<CmsBillRecordDto>>(orderedCmsBillingPeriodUsages);
            result.CmsBillingPeriodUsages.ForEach(x =>
            {
                x.PaymentBreakDowns =
                    _billRecordRevenueCalculatorService.GetPaymentBreakDowns(x, allValidPlans, timezoneInfoId);
            });

            // Slow
            // var conversationMessagesAnalytics = await _appDbContext.ConversationMessages
            //     .Where(x => x.CompanyId == request.CompanyId)
            //     .GroupBy(x => x.CompanyId)
            //     .Select(g => new
            //     {
            //         TotalConversations = g.Select(x => x.ConversationId).Distinct().Count(),
            //         TotalMessagesSentFromSleekFlow = g.Count(x => x.IsSentFromSleekflow && x.Channel != ChannelTypes.Note),
            //         TotalMessages = g.Count(x => !x.IsSentFromSleekflow),
            //
            //     })
            //     .ToListAsync();
            //
            //
            // usage.totalConversations = conversationMessagesAnalytics.FirstOrDefault()?.TotalConversations ?? 0;
            // usage.totalMessagesSentFromSleekFlow = conversationMessagesAnalytics.FirstOrDefault()?.TotalMessagesSentFromSleekFlow ?? 0;
            // usage.totalMessages = conversationMessagesAnalytics.FirstOrDefault()?.TotalMessages ?? 0;
            return result;
        }

        private async Task<GetCompanyStaffsResponse> GetCompanyStaffAsync(string companyId)
        {
            var staffs = await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == companyId && x.Id != 1)
                .Include(x => x.Identity)
                .OrderBy(x => x.Order)
                .ThenBy(x => x.Id)
                .Select(
                    x => new CmsCompanyStaffDto
                    {
                        StaffId = x.Id,
                        UserId = x.Identity.Id,
                        FirstName = x.Identity.FirstName,
                        LastName = x.Identity.LastName,
                        DisplayName = x.Identity.DisplayName,
                        UserName = x.Identity.UserName,
                        Email = x.Identity.Email,
                        EmailConfirmed = x.Identity.EmailConfirmed,
                        PhoneNumber = x.Identity.PhoneNumber,
                        Status = x.Status,
                        CreatedAt = x.Identity.CreatedAt,
                        LastLoginAt = x.Identity.LastLoginAt,
                        RoleType = x.RoleType,
                        TimeZoneInfoId = x.TimeZoneInfoId,
                        Position = x.Position,
                        Order = x.Order
                    })
                .ToListAsync();

            staffs.ForEach(x => x.TimeZoneInfo = TimeZoneHelper.GetTimeZoneById(x.TimeZoneInfoId));

            var associateTeams = await _appDbContext
                .CompanyStaffTeams
                .AsNoTracking()
                .Include(x => x.Members)
                .ThenInclude(x => x.Staff)
                .Where(x => x.CompanyId == companyId)
                .Select(
                    x => new CmsCompanyTeamDto
                    {
                        Id = x.Id,
                        TeamName = x.TeamName,
                        TeamMemberStaffId = x.Members.Select(m => m.Staff.Id).ToList(),
                    })
                .ToListAsync();

            foreach (var staff in staffs)
            {
                var teams = associateTeams.Where(x => x.TeamMemberStaffId.Any(y => y == staff.StaffId)).ToList();

                staff.AssociatedTeams = teams;
            }

            return new GetCompanyStaffsResponse
            {
                CompanyStaffs = staffs
            };
        }
    }
}
