location_name: eastasia
sku_config:
  sleekflow_core:
    name: P3V3
    tier: PremiumV3
  sleekflow_core_db:
    name: HS_S_Gen5
    tier: Hyperscale
    family: Gen5
    capacity: 2
  sleekflow_analytic_db:
    name: HS_S_Gen5
    tier: Hyperscale
    family: Gen5
    capacity: 2
  sleekflow_powerflow:
    name: P1V3
    tier: PremiumV3
  sleekflow_sleek_pay:
    name: P0V3
    tier: PremiumV3
  sleekflow_core_worker:
    name: P2V3
    tier: PremiumV3
  redis:
    default:
      name: Standard
      family: C
      capacity: 1
    caching:
      name: Premium
      family: P
      capacity: 3
sql_db_config:
  administrator_login_random_secret: fpMAbdn+hk1beJ6bila+oHlkxJmRiLYXckPWurepeHF3UjZzRgAGzSYEf5rEU0nhBqiENEu7ZpjhrLdj
  administrator_login_password_random_secret: xyvZ4t/WgxwCkTMKZsPMWEGKyBOml0HvBTzwA0N6yBxotMLeUumG6VGTwX/0/DJsRktF4ICb5T0w3BFo
  whitelist_ip_ranges:
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: *************
      end_ip_address: *************
    - start_ip_address: *************
      end_ip_address: *************
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: ************
      end_ip_address: ************
    - start_ip_address: ************
      end_ip_address: ************
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: *************
      end_ip_address: *************
    - start_ip_address: ***********
      end_ip_address: ***********
    - start_ip_address: ************
      end_ip_address: ************
  is_read_scale_enable: "false"
  high_availability_replica_count: 0
vnet:
  default_address_space: ********/16
  default_subnet_address_prefix: ********/24
  sleekflow_core_db_address_prefix: ********/24
  sleekflow_core_address_prefix: ********/24
  sleekflow_powerflow_address_prefix: ********/24
  sleekflow_sleek_pay_address_prefix: ********/24
  sleekflow_core_worker_address_prefix: ********/24
auto_scale_config:
  sleekflow_core:
    capacity:
      default: "6"
      maximum: "20"
      minimum: "6"
    scale_out_instances: "1"
    scale_down_instances: "1"
  core_worker:
    capacity:
      default: "5"
      maximum: "20"
      minimum: "5"
    scale_out_instances: "1"
    scale_down_instances: "1"
sleekflow_core_config:
  aspnetcore_environment: production
  logger:
    gcp_is_enabled: "TRUE"
    gcp_project_id: my-production-project-405815
    gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  audit_hub:
    endpoint: https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io/v1/audit-hub
    key: 8fcOGvqN3lvZlIHDnb50D3dK5rlOuAShDv7fDnfRtkMpQC/VlLtOC2uaA3C4Xlpx9zi0on4ncuJNJ54lhYOK9V+CYU56TiPxzdqdSGogOIY=CpyUw5ooyKa9Bf83
  auth0:
    action_audience: https://api.sleekflow.io/
    action_issuer: https://sso.sleekflow.io/
    audience: https://api.sleekflow.io
    client_id: txLGs6X2eN17XOXuxZpDakoxpyxoOlHW
    client_secret: N31ORl6bvh97LLIRhGT1CkeT5tfIV07yEDFvCMIXbd/XU4oSWc8EMSyCyT4BSFcgxedjU50ke/ODcASXsBroafIRG/bewPZYSPitrs3QTQU=4A2Z9H673c9leYaz
    database_connection_name: Sleekflow-Username-Password-Authentication
    domain: sleekflow.eu.auth0.com
    http_retries: 10
    issuers:
      - https://sso.sleekflow.io/
      - https://sleekflow.eu.auth0.com
    namespace: https://app.sleekflow.io/
    role_claim_type: roles
    user_email_claim_type: email
    user_id_claim_type: user_id
    username_claim_type: user_name
    tenant_hub_secret_key: 0CDJCQs94JCkLAVE9UR4ehx2nnXiaA7SPG6L9mBv1WHd7qVtWIioaMn23zoEh+ehxeGIUaPUTv+6m1PNq2iseRzjC0W0iGijowgyZNnSTEs=PnLr94X4y10RcuPz
    health_check:
      is_enabled: "false"
      client_id: OrxDhNRbAyWXKoSVoMuS0hIWNo3v2An0
      client_secret: W3rRgOPBeuNQ4p0hwDgM7P3ZSk58xC8YkMWvelu8RjBXCsKPjcxAf5ZhIiZz2syfAWaZcu6FHDYRzI9Nitl/zf+LooP778DWEsbBAyhmSV4=JKLKie8gQx1xq2jR
      username: *******
      password: ********************************
  azure:
    media_service:
      account_name: sfmediaproduction
      client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
      client_secret: SycuW4DUGs3+g9ZPMF9KjE6VYdeGuyG/OyVIIgkzV2bIMG4Oi/qegrYmpI2u2ZgSDAjdFvvoduruOWg9
      resource_group: sleekflow-resource-group-production853b96c8
      subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
      tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
    text_analytics_credentials: 226f2d8217704dd6b223b4a1fd6e51c6
    text_analytics_url: https://sleekflow-prod-sp-text-analytics.cognitiveservices.azure.com/
  application_insights:
    is_telemetry_tracer_enabled: "true"
    is_sampling_disabled: "false"
  beamer:
    api_key: 3+g90GK79Qp+qtfjeYG6nDFJnZasIlWZm3VaCrcWlH8NTCNPN4Mb+12zElRd3YBEis3vsQiUPm06Ba3W
    api_url: https://api.getbeamer.com/v0/nps
    webhook_verify_key: w3pn/AYMmcbPFvnftOHP9Jm56AQYwYXrUyHkuLOiH4u2230tKrlJdyK0954wDHNAGtkTQFQzaD3XjKsP3kQSR8vAErjLo0oRuQNjiwq2O3Q=K7GJlMZSKrRhjaSF
  chat_api:
    api_key: 25+BKhMAhdlvWQBMed/BxiSmY3jVKP5e5jH4x3INQ8Q=JkrVU6gKsN=qnABb
    api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
  commerce_hub:
    endpoint: https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io/v1/commerce-hub
    key: T8N3+xEUOlfvXVz7KfFl6c188WWr7kskku4U/VJZM6EmqQoSVZXmL8rpKfthlzZv2Vk4g4L1rbckjDPFPioNp2Hw+YsbKDF89v88Vwvz7TOJZq4w4qm1WtgdwueO3Mwrsr36Fv5tkeswHM37IBwkQOCbP+9n/SJl9OnV2j8kOU0ZDjI/wXslF8Un3VRDv0/i2tVia5PY0avgoDu4
  crm_hub:
    endpoint: https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io/v1/crm-hub
    key: 9HjtvPEzv8OhoXV/8DpvfM4GMQfSqTrFp/xVvSuK72SUWyrySxYxcLtNGY/AfyjeX=puhDepOeewnZm7
  data_snapshot:
    is_enable: "true"
  development:
    redirect_fb_webhook: https://9633-42-200-140-118.ngrok.io/facebook/Webhook
  environment_features:
    is_recurring_job_enabled: "true"
  epplus:
    excel_package:
      license_context: NonCommercial
  facebook:
    client_id: 812364635796464
    client_secret: s19YAbPqA0SSMLZqRMCtprju0iPD/R7Dvtj1ZHcKTKWP2iuEp3x5dZF7srs/EZxnrWjVehq3QMoY2rh6
  ffmpeg:
    ffmpeg_exe_name: /usr/bin/ffmpeg
  flow_hub:
    endpoint: https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io/v1/flow-hub
    key: e6iAa6yhZXfu+EoriAC9aSg1THji4NL9HgZ8Gz652Z/ojKnuatDvoj2ZwRIBpEl9z0t1iT415MrrDC6Sul/R3nPzMQ+3WTVyNKwDoZto38M=Fs3YXimUpAksmHlX
  geo_sql_db:
    name: sleekflow-crm-prod
  geo_storage:
    connection_string: DefaultEndpointsProtocol=https;AccountName=sleekflowstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
  hub_spot:
    internal_hub_spot_api_key: zZ6IJRdlFkxZnDvc5Wlxm8tYMv3c7CesIBVJUDN9F7E/65/qXFNfGgmYTLKyqIUbmjEpwX5UxR5eFyoi
    is_enable: "true"
  instrumentation_engine_extension_version: disabled
  internal_google_cloud:
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  general_google_cloud:
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    project_id: my-production-project-405815
    server_location: asia-east1
    google_storage_bucket_name: sleekflow-transcoder-prod-asia
  ip_look_up:
    key: Ol8fDPvx27T5XsFSzdm3DV+qrU2Pm+2zKZlJLoguMjhdsZJNWdAQLFQ7FNXcbIiAezB0WSA2RldTjU5p
  intelligent_hub:
    endpoint: https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io/v1/intelligent-hub
    key: RONl7SQHYAUUFAcajOttSjpghV6xWyvKlXtnsiGHy7xo3W02xJgtuIQg29cf4CDwH/TahVp4aryZzb1dOO6UntsEpOfAaBUcnVriVZ27TsA=XbZp5C2jvhmsu7E6
  webhook_hub:
    endpoint: https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io/v1/webhook-hub
    key: CY8dWoyX9LtpIHqIT5IMaAkgjvqZDYqXXYcHraGRmkkyTXAXh18NE5jb18jlEfXrQBLUmr6l++nn7/jdYtkIc237KePLTsmZgQQvX+exp04=0evVuiCfh14HuvY3
    auth_secret_key: 3N1z2/I5WGXlP2I7H6jomCJp7brbdilh3XIE8EO26tf3wzzVYiS3Gy27/G/IH3gnEot2gadR2qlUWaffoyxpKfDrHyyjcbzMNJR2E1raxGY=CC5h5cWb84XUBfPo
  message_hub:
    endpoint: https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io/v1/messaging-hub
    key: qqNsz+ZgGcItwqNxSGAiQMtnGK4S2KOVrLTYGbtlsGR643mgOovzhrOckbyYvzZl09=yM7zl8XsOsONc
  mixpanel:
    token: ef72dd13a0ffccb584cfdf75d3160e25
  mobile_app_management_extension_version: latest
  notification_hub:
    connection_string: Endpoint=sb://sleekflowproduction.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=pbzIBQQoNB8rGgrHdt1vmCIuTIB/Aaey5iION3eiCbQ=
    hub_name: SleekflowProduction
  public_api_gateway:
    endpoint: https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io/v1/public-api-gateway
    key: aAnP2hrfvK1pGHCd74XIDHwRTc3yatz8miAeAzAHdJQ/eTzZu8cmbL2Lt8L2J4VHh556CmoLJESjMgej
  redis:
    connection_string: sleekflow-sp.redis.cache.windows.net:6380,password=rzUiDMeStpK4kX1eA9qRUXFVqFGK8A9Yapa02fzR1i4=,ssl=True,abortConnect=False
  reseller:
    domain_name: https://partner.sleekflow.io
  rewardful:
    api_secret: /EuOjHhsERq7i2VKBDZPAbHbHclT/sAb/iL1t0AxHfRNmpCtz072MnSv86vglf2xwB5wLzWsY0MtbVTN
  salesforce:
    custom_active_web_app: https://sfmc-custom-activity.vercel.app
  share_hub:
    endpoint: https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io/v1/share-hub
    key: fheCbDKkhLEHQM/MOrGW/v2InPNZ8aP+jx/bx3b73Tf481L6Rz37fFxYk942L3Fka7WoozqVY0YZDzW3
  shopify:
    shopify_api_key: vwxGqcwixOTBDd1IRjIx2kyDdUwStF96itYkAXsY2voeaznsDqiEhVbMs8DYwqbrF=3bLtr4abteVAFx
    shopify_secret_key: 48/+Xu7w2+3p6J4lC1GFK7qIuS3KocGog+vybkkZ3RUhH5cWyemgRwmGobi73oSibvIJ=eCqs=UFa68D
  sleek_pay:
    domain_name: https://pay-eas.sleekflow.io
    shopify_graphql_admin_api_version: 2022-10
    stripe:
      public_keys:
        default: jsl6zfhPH2pf7AQV9nAZXGu2zEgP4iVWSsj7tqQr2q6mMlLitrRXfReazyiGDzy6NtS/lgqpF/fXw+e6xq3Wg/Qypp93Mh+1qxobIdnIQhzGORHbRivU8QycTDkWiPv102JpfnYQPA6LNhYQuABOnQ==etO2Y2VdkzhMb=aU
        gb: CWrcT+0pasY7Z2iLFZQM40uo5RvE5GkBnQrNtiNIwIWzDsvkHxh2BBi07gGFrM645nIL//769uWX0C6esL9bfgk3i+TVGdo4sxK5AwySVfdhO8CpfNBmUUE+Ym3q+3ox1Rlx6HSbdYWwlDTRPZjUMg==eQTmFJ5GUmT9u2Jn
        hk: ruMEXIlwY5TrCr76LbrAeHzQGUARM92MiDgYDOtrdHuvcAHb5QR2vgx4pwFLbDmcQh3aMFForRK4pWWKZgTm5eAqUOqlVu8F+1mCXZ3G8Qf6w1vy6hRj2N3a8yhhfQ1vspyXncf0JW0iLd/d398Sag==1z7g0Mr0wUgvNxHA
        my: z9CvWN0aY1LGmp5FZtLHPLNnQrqYgBPruxQQIFHOEF5S/LJuX4CL36Lvn8/80woEwN918fUZo8ZpGXZiQuAOA0rcFKnTB4Pm72FahRKeL+TU9VazJINa/ZtIDWKSlZqgpL0naMDJvq3+dzivFhznSw==2mI2oMLWMzHrZB7c
        sg: ScJT9oaJr/7D0lLc1Q5ekP56AhAY2mPB+T9NsCIbBTrbcAoXATvWLhXdxYlWkcZY60LMlj6VoCRxw02vHxadAXKJyHJEFNCFqwmbyvFpb03q1Zuxxl5fySzhnXQTW9mURvs99l0zu9XTHPR/CV5qcA==RuDWSRFxo276p410
      secret_keys:
        default: UpngkDd4y3d9Bf3ZooIbiki2z29rPCbqadSuOo4rzWqoNF0vrrhtX5ySskdIAqztzeu95XRd5Ybqc43kl0D7ahNy+LfT88PMzLROSsG29VrgedX7x5ZlBMow2q5SyWAP79U497nyZOjkUWsm/xyI/A==Xw2ikTScD=j7krEb
        gb: 3QHVTgiRuzfiEysRxoXLJPczlpUTd7trdbmOX7wZc/a1I5A5EX3s13FCJgo/cACapSeSry7qGMn2cqTCc6K534H9A6TjfD+HfovxICn/UtJHC3nRf8VAx3a+rA4Gg9xHAInVVCaQ3aTG+/IwX3Q1+Q==tFkJv9VYlvC3YWIk
        hk: v2q3U+EYBIjmP5noOgZRGTxtOAxiPHNffRxzrY2xE449KQ85N3ftIsJgAK06wwcRXovi7hs4GyaB4J5OuiZroVVZ5vrFgumr2RyoB4nrrsQDyYQwa2ZI5d096UULu8NYarLRDXeuX5pZv3gaKmcyMA===5LronXfkBq7qDCa
        my: 0hLTlTaN/6A+pJ4+q7M7pqwl21xMjWMpDJZLcVMzEsHgdE2Hx7R67bRMIqQOf4ZhZWJ1jW3IvPnpOCiHe4ZAlm0DfyfJ1I0TkdrvLwqfOWzVceYDqDJ/uTe2Qk1R8nDzm7xiMkeOpwpfYGd87C5rHw==lH9f6mnwEAmLaVuM
        sg: nlRIVkgo/UHWibgTRNsVG+5ABel9EYH/WhXypwAsXOhggTghSO5GzRvBZ/mUgwjfMo68e27XaSLBWrTZMIA+dhlnlA+URQx/LjSwg0QT3PbNZW89wlhTukzXNw+A/TxjpncJkNLKVtV8ii8aJb+LZg==xK8GCRLqivmOtYrY
      connect_webhook_secrets:
        gb: WGOKzl/a838omyHeNzScTznz53GFqUSCfDQqqgMajSaGg9Ger+Z041UHhQw3J0d6mH7kT=Bw=uPzPZHO
        hk: 8t7PpCS+x+Iom58VKn1HMsPcudSdn9zhnnmfLlIpuXvEztMQYf1WxBItfc/E9xN3qQr9=T9slL562eU3
        my: uGWddxoIUWw6PObsFBxGseoT6JkRT5yY46XFuaFiA5ALJIDOryeNAaBincLNpQVOZW1Jr8uqxKPniMoy
        sg: 5kzxnbxmpGtN73NW+VvOwl2qQPjRu9VnTl7SiNXViD06jSLxA6+7gicCTzcX/6hiaUzzVl0ljb4XOiWg
      report_webhook_secrets:
        gb: wA0TSXnPVjwYwzszM/xvf0+UXXd80T49lGSInREMjCiHLgmF1IG9wx/ISUdr4pEAGzcMo4nmYa=3=yt2
        hk: ePbzmn4nMZ4yFmuVptvVTk2E3ibulSjrdNMU9BGYj/XCTbEW5xyXWseoV2zcr8tW3rSHbfnOATwmsMDi
        my: t3SPNeT3jQZ9Cb+MueEFYrQrSeiRNUcEjr5rLiSTEfXuqqcradQ1qarYYN382ReawbbQhetfilNgvDYZ
        sg: +T/Bp9gbnNcQQ2F4nAc01mFFuU2HrwDqrMEizrLvVvKq0x8S3T2Ksic3Ixd71UVetTxfmlZ86YAIAdKB
      webhook_secrets:
        default: g7h2KkpO8z8DRK31xPRVmREVL58d/7+bnYdoMVbSCeZqvc9PyEoCBDd6Rlvpgjt9iYEWCy4GPlMnUcb1
        gb: ySgiFTXqeDmGM0qJW50eTEhAyQrbka6rBSvH0pRoxihRzryZ9sj+zN04PmaxlmGUgzsh2EW8iCLtoMgy
        hk: 0g5k4ysx2VWKOmpFfeqa3ci1FbTbFwplJxpuwPLrNJDZJjSLUfU+guxHM7/nTzt30tSt89BgRqBW9boG
        my: n9x4ToiPBvGXkS0SJ9s5NVoVcdP46QRzVGKHcnmxzcG93sRm7FDDYVvA1ZNZKf5l4tH1TAwsoUVPtKgC
        sg: I7MkfPISCNxDUoWDiH2/Yr7g1vNexuwr7gd9VTZAgIsfQ4vES/1qUMJ70Fs7Hlk0mIPNK3Jw9Ks5srY6
  snapshot_debugger_extension_version: disabled
  sql_performance:
    from_raw_sql: "true"
    is_and_condition_enabled: "true"
    is_or_condition_enabled: "true"
    is_conversation_analytics_condition_enabled: "false"
    is_shopify_order_statistics_enabled: "false"
    is_sales_performance_enabled: "false"
    is_public_api_new_upsert_enabled: "false"
    is_zapier_new_upsert_enabled: "false"
  stripe:
    stripe_public_key: l9CGo6RNRE04HRwQXJpwTnkcY4P7F9tTyYgs1cOguQ9dbPhV6mXcavgIE7ng/VOAjxF4UrCR1K6xoYoG
    stripe_report_key: tuKwgfaDg+DKb43JmjPTPa6uRkAXE0SHSkA7qwuvYrmmNcadsbsMEW0ZlYuQSMp7yKmEkF3FmS1seyzlSyWsSYWsTmypwNNodDXWCnzzRlxeu5XhvxpYKtxEBQcU4s5AdXSkF0OuBk3OMp9gBOkDSw==QYtm==lGe0iKBTT1
    stripe_secret_key: HPe5Xw6QbkBk3DlvQFqXK1qcxSI88IPt+4IphUYJcyV0RzlkBsK4r9sOvW91aISrUzYvuPxevb0F4pTg
    stripe_webhook_secret: T8Zl1HF3rso2DEsgX0XgqQKoR31XYe+TmIRG3bHIywgo8QSbQ3vG8zIt1ar2/zIahGFBw8KXSum8Spv7
  stripe_payment:
    stripe_payment_secret_key_gb: EIbrxf/vB+lZGxyCDkYMZlXmJkzsaoH6K0PdlOgO23waK/dJrB/xChvGDM1ZXV4XHskQXd0zTczP7XexsMSc+JBcIp0eRm1w1vopHU4JJhHzm8gqduym+vsBCsijocb4js+zlv2HQjvA1BbUImgtNQ==EpIzwsQ=2k7hhovW
    stripe_payment_secret_key_hk: G2GQ1zpiIDwsu0xniSRIjXjB/lucbfKatri1it5w1/DQW3ZKQt7c42KwG64wtTtjlhn2M5Uc3Rrj7ZFzqNN7cNUEw2xttoZXmgQK7S5pENSJy/Nkcy3SbTi5QCqSLUAz4tK6yAYfJkKnQCufwO2LdQ==xVr5X4Uopo5HEhFj
    stripe_payment_secret_key_my: XzDZxvCFwTX/t39nKuJ7OFCTaS3ldCj0Mb2EptSESNGWIv4bZmbAqu9cUwWCQlqtqhQo4nIaJJ+bZFSxPDXGtHizEJcDnHCQ54mfmwvi+mRf83h06bd8i5XGRDpVD3vLho+ZxCy1jWYPKhIMuZJX3w==RKh=4NNCPrC4iG7K
    stripe_payment_secret_key_sg: P1sBCKcJuBIgU0BjaaVnIZfpaK+T42y7tHtNn+zb4mJLcJJOF1ppRc+/IeFYuiBkVZ18lhfIKdYfKCI+MNl5H5h110eWarDSPTYvvRQbLSs1JiVtJ6PYjmLQfqdZEhOd7C51B3YYGZLbHEpfngl+lg==A5QAMNNJ1GTGof94
  stripe_report:
    stripe_report_webhook_secret_gb: HqLhGEvxZKmnYdVwmGhUzBgrSwrRLDOliQRjWvfU/MQ4wiQ59S5+wVvUTjIkzbfDroA3vsKVokBbNrVi
    stripe_report_webhook_secret_hk: 0N4zKBJtpQwszL6ULEMNtm3rtT5Q12YJ3uTWnEAUS3lGTmpIbUr1sm1PZjYqqsJL4QMUa4CJbYJnyMxL
    stripe_report_webhook_secret_my: wSxBt3FS6SpntIJLXHdCOYkH1YyXYoxBnAT5AVAuMHSJJWMnWuuyhxsM9aco1Jclhe150XF9TRPtBSzT
    stripe_report_webhook_secret_sg: dd3Ja8XJGzQh781Vp/Mcwn3frpGPZbtBPKT2iauELCYUPUJ5Bx726UGXAbarO24Jv8oUou3aLOXFlL5u
  tenant_hub:
    endpoint: https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io/v1/tenant-hub
    key: wp/3UcAVyaXDT/L7Svhs8d7ALFwe9G3ZhlTTh9c/0Rxj+cIVCUkKVsGepUxrXvmFLmhanjEHm38Ga5GP4iH8wR6Eoq+FyJXR0a7YXe6pUok=kFB1C8lycy71fEW6
    is_enable_tenant_logic: "true"
  ticketing_hub:
    endpoint: https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io/v1/ticketing-hub
    key: wE+t4K07nHnupAaGb+3rJtCLTVLlQTbIJ07XsBP0Y9TaDKKRAVY+YRxUnF+A0nMLaDajB4XqzhJkBrohoPyXPgQ1/a7w5SEpJ40v+3Citzg=7cj6J=8o2fKa8Hcx
    is_enable_ticketing_logic: "false"
  test_swaping: 1
  token:
    audience: https://sleekflow-prod-api.azurewebsites.net
  tokens:
    audience: https://travis-crm-api-hk.azurewebsites.net
    issuer: https://sleekflow-prod-api.azurewebsites.net
    key: u0jZdCOTesdCwjIk8z9kmdO1adNys6fyEYd88yrGYmd79dxActNMvJ99WqoDsVthB8hCYQyjhVe76vpRGsVuHRUkCCtsqpGQjvreC1vY0yY=r5NEDoYkMjaeP0cb
    lifetime: 60
  user_event_hub:
    endpoint: https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io/v1/user-event-hub
    key: Uo1jxuSJyRGkF19OrKl0SXjFa0eujueI9/3qWVX3AzHia+qa4JlakxZNollskq+lIkF3BthRYwWmz5fot0KykkApA30qZN8tvg1XfAZ6n4w=2M2nrX1lof02TnQh
  values:
    app_domain_name: https://app.sleekflow.io
    app_domain_name_v1: https://v1.sleekflow.io
    app_domain_name_v2: https://app.sleekflow.io
    share_link_function: https://share.sleekflow.io
    sleekflow_api_gateway: https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io
    sleekflow_company_id: 471a6289-b9b7-43c3-b6ad-395a1992baea
    sleekflow_public_api_url: https://api.sleekflow.io
    sleekflow_public_api_key: IUAB2CvRDdPEqPeOSylPokIkVd8b3JLEkzKUC98aUFhwf2rAZNv9eCUgpYRXo/p+7M7wpbmp3MQLYNfa
    sleekflow_company_should_use_public_api: "true"
  website_http_logging_retention_days: 5
  website_node_default_version: 6.9.1
  whatsapp_cloud_api_template:
    default_image_blob_id: sleekflow.png
  xdt_microsoft_application_insights_base_extensions: disabled
  xdt_microsoft_application_insight_mode: default
  global_pricing:
    is_feature_enabled: "true"
    plan_migration_incentives_start_date: "2024-11-20"
    plan_migration_incentives_end_date: "2025-03-10"
  feature_flags:
    - feature_name: FlowBuilderMonetisation
      is_enabled: "true"
    - feature_name: CancelledSubscriptionTermination
      is_enabled: "true"
    - feature_name: Rbac
      is_enabled: "true"
  contact_safe_deletion:
    is_feature_enabled: "true"
  partner_stack:
    public_key: IpGVoDpzJ1G/dVAUQEMHNwNHPTa37zE1rfNXnY58zhODKLG8BlyaUgxpsJ0Nx62dZnmngU1oNowkOJNY
    secret_key: 8Qsz9jj5leG0uwwZlLmkJ5ekxfBplrQ7Cx+VXOfUlwMVXbQ8aK426H81HNWgjmTcYm2UrRXLL5=h2nVq
  hangfire_worker:
    worker_count: 5
  internal_integration_hub:
    endpoint: "https://sleekflow-apigw-app.nicemeadow-64d75f44.eastasia.azurecontainerapps.io/v1/internal-integration-hub"
    key: +pc8gHR3XnhUg3tCi4Zm1LUpnCBn6oobcV69ZBqNK7pMkDucA3r12VJEgwakZK5q6Q+RLTDXDywjWWBZn0PiYijxgCHrvhBz+DbQj321/w0=QytcJPrQXDF7obyt
  hangfire_queues:
    disable_instances: "low,default"
  integration_alert:
    endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
    api_key: "T7PxUKQJi574uXlY9NRkKJ5ey3p+ujiAPyHF5NI0VnfJPU8jABXPJ+HoAqvJQJ4rqFawCLkM0IMgRQ37"
    host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
    from_phone_number: "17209612030"
    template_name: "integration_disconnect_noti"
    facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"
  rbac:
    is_middleware_verification_enabled: "false"
  flow_builder_flow_enrollments_incentives:
    period_start: "2025-02-07"
    period_end: "2025-06-25"
  hub_spot_smtp:
    username: "*******"
    password: "yus47rNx7M7Ca0xqdiQNZVGEOXb1yh"
    send_execution_usage_reached_threshold_email:
      username: "*******"
      password: "k4nCTQARqae2a2i3yX2d2qVuiJIvTz"
  legacy_premium_opt_in_upgrade_incentives:
    period_start: "2025-03-10"
    period_end: "2025-06-10"
  live_chat:
    secret_key: "t6V8RaYnHQIIlJv96Rn75mMZhrxcorJ/1zuLIFvGEYoPH7Kpfz5ApWT/7SjWhRpJkw5dJr7HOWaQ1nB7"