using Newtonsoft.Json;

namespace Sleekflow.Powerflow.Apis.ViewModels.InternalCmsCampaign;

public class InternalBroadcastCampaignDto
{
    [JsonProperty("company_id")]
    public string CompanyId { get; set; }

    [JsonProperty("company_name")]
    public string CompanyName { get; set; }

    [JsonProperty("country")]
    public string Country { get; set; }

    [JsonProperty("broadcast_name")]
    public string BroadcastName { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonProperty("channels")]
    public List<string> Channels { get; set; }

    [JsonProperty("channel_names")]
    public List<string> ChannelNames { get; set; }

    [JsonProperty("industry")]
    public string Industry { get; set; }

    [JsonProperty("hubspot_industry")]
    public string HubSpotIndustry { get; set; }

    [JsonProperty("created_by")]
    public string CreatedBy { get; set; }

    [JsonProperty("last_updated")]
    public DateTimeOffset LastUpdated { set; get; }

    [JsonProperty("sent_at")]
    public DateTimeOffset? SentAt { set; get; }

    [JsonProperty("sent", NullValueHandling = NullValueHandling.Ignore)]
    public long? NumberOfSentBroadcastCampaigns { set; get; }

    [JsonProperty("delivered", NullValueHandling = NullValueHandling.Ignore)]
    public long? NumberOfDeliveredBroadcastCampaigns { set; get; }

    [JsonProperty("read", NullValueHandling = NullValueHandling.Ignore)]
    public long? NumberOfReadBroadcastCampaigns { set; get; }

    [JsonProperty("replied", NullValueHandling = NullValueHandling.Ignore)]
    public long? NumberOfRepliedBroadcastCampaigns { set; get; }
}