using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;
using Travis_backend.ChannelDomain.ViewModels.LiveChatV2.DTO;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;

namespace Travis_backend.ChannelDomain.ViewModels.LiveChatV2;

#region Sender

public record CreateLiveChatV2SenderRequest(
    [Required]
    string LiveChatId,
    [Required]
    string CompanyId,
    Dictionary<string, object> Metadata);
public record CreateLiveChatV2SenderResponse(string Id, string LiveChatId, string CompanyId, Dictionary<string, object> Metadata, string SessionSignature);
public record CreateLiveChatV2SenderInput(string LiveChatId, string CompanyId, Dictionary<string, object> Metadata, string IpAddress = null);
public class CreateLiveChatV2SenderOutput : IOutput
{
    public string Id { get; set; }

    public string LiveChatId { get; set; }

    public string CompanyId { get; set; }

    public Dictionary<string, object> Metadata { get; set; }

    public string Message { get; set; } = string.Empty;

    public ExitCode ExitCode { get; set; } = ExitCode.Success;

    public bool IsSuccess => ExitCode == ExitCode.Success;
}

public record GetLiveChatV2SenderInput(string SenderId, string CompanyId);

public class GetLiveChatV2SenderOutput : IOutput
{
    public LiveChatSenderDto Sender { get; set; }

    public List<LiveChatTrackingEventDto> TrackingEvents { get; set; }

    public string Message { get; set; } = string.Empty;

    public ExitCode ExitCode { get; set; } = ExitCode.Success;

    public bool IsSuccess => ExitCode == ExitCode.Success;
}

public class GetLiveChatV2SenderResponse
{
    public LiveChatSenderDto Sender { get; set; }

    public List<LiveChatTrackingEventDto> TrackingEvents { get; set; }
}

public record UpdateLiveChatV2SenderRequest(
    [Required]
    Dictionary<string, object> Metadata);
public record UpdateLiveChatV2SenderResponse(string Id, string LiveChatId, string CompanyId, Dictionary<string, object> Metadata);
public record UpdateLiveChatV2SenderInput(string SenderId, Dictionary<string, object> Metadata);
public class UpdateLiveChatV2SenderOutput : IOutput
{
    public string Id { get; set; }

    public string LiveChatId { get; set; }

    public string CompanyId { get; set; }

    public Dictionary<string, object> Metadata { get; set; }

    public string Message { get; set; } = string.Empty;

    public ExitCode ExitCode { get; set; } = ExitCode.Success;

    public bool IsSuccess => ExitCode == ExitCode.Success;
}

public class UpdateLiveChatV2ConfigInput
{
    [Required]
    public string Settings { get; set; } // JSON string of settings

    public IFormFile CompanyLogoFile { get; set; } // Optional company logo file
}

#endregion

#region Message

public record SendLiveChatV2MessageInput(
    string SenderId,
    string ChannelIdentityId,
    LiveChatV2ExtendedConversationMessageViewModel Message
);

public class SendLiveChatV2MessageOutput : IOutput
{
    public string Message { get; set; } = string.Empty;

    public ExitCode ExitCode { get; set; } = ExitCode.Success;

    public bool IsSuccess => ExitCode == ExitCode.Success;

    // Can be refactored to a dedicated dto for conversation message
    public ConversationMessage MessageDto { get; set; }
}

public record GetLiveChatV2MessagesInput(string SenderId, string ChannelIdentityId, int Offset, int Limit, string Order);

public class GetLiveChatV2MessagesOutput : IOutput
{
    public string Message { get; set; } = string.Empty;

    public ExitCode ExitCode { get; set; } = ExitCode.Success;

    public bool IsSuccess => ExitCode == ExitCode.Success;

    // Should be replaced by a conversation message dto, but with a very limited timeline I break the cross layer rule in CA
    public List<ConversationMessage> MessageDto { get; set; }
}

#endregion

#region Config

public record CreateLiveChatV2ConfigRequest(string LiveChatId, string CompanyId, Dictionary<string, object> Metadata);
public record CreateLiveChatV2ConfigResponse(long Id, string CompanyId, Dictionary<string, object> Metadata);
public record CreateLiveChatV2ConfigInput(Company Company);

#endregion

#region Tracking

public record CreateLiveChatV2TrackingEventRequest(
    string ChannelIdentityId,
    string Name,
    string StartedAt,
    string EndedAt,
    Dictionary<string, object> Metadata);

public record CreateLiveChatV2TrackingEventInput(
    string SenderId,
    string ChannelIdentityId,
    string Name,
    string StartedAt,
    string EndedAt,
    Dictionary<string, object> Metadata);

public class CreateLiveChatV2TrackingEventOutput : IOutput
{
    public string Message { get; set; } = string.Empty;

    public ExitCode ExitCode { get; set; } = ExitCode.Success;

    public bool IsSuccess => ExitCode == ExitCode.Success;
}

#endregion

public enum ExitCode
{
    Success = 0,
    Failure = 1,
    Error = 2
}

// ✅ Interface only has properties and getters - no fluent methods
public interface IOutput
{
    string Message { get; set; }

    ExitCode ExitCode { get; set; }

    bool IsSuccess { get; }
}