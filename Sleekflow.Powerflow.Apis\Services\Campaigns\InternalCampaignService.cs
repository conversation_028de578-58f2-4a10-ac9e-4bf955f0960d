using Sleekflow.Powerflow.Apis.Services.Repositories;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Extensions;

namespace Sleekflow.Powerflow.Apis.Services.Campaigns;

public interface IInternalCampaignService
{
    Task<(List<CompanyMessageTemplate> BroadcastCampaigns, int TotalRecords)> FindSentBroadcastCampaignsAsync(
        string companyName,
        List<string> companyIds,
        DateTimeOffset? startDate = null,
        DateTimeOffset? endDate = null,
        List<string> channels = null,
        List<string> countries = null,
        List<string> industries = null,
        string hubSpotIndustry = null,
        int? offset = null,
        int? limit = null,
        string sortBy = "sentAt",
        string sortOrder = "desc",
        bool enableNoTracking = false);
}

public class InternalCampaignService : IInternalCampaignService
{
    private readonly IInternalCampaignRepository _internalCampaignRepository;
    private readonly IInternalCompanyRepository _internalCompanyRepository;

    public InternalCampaignService(
        IInternalCampaignRepository internalCampaignRepository,
        IInternalCompanyRepository internalCompanyRepository)
    {
        _internalCampaignRepository = internalCampaignRepository;
        _internalCompanyRepository = internalCompanyRepository;
    }

    public async Task<(List<CompanyMessageTemplate> BroadcastCampaigns, int TotalRecords)>
        FindSentBroadcastCampaignsAsync(
            string companyName,
            List<string> companyIds,
            DateTimeOffset? startDate = null,
            DateTimeOffset? endDate = null,
            List<string> channels = null,
            List<string> countries = null,
            List<string> industries = null,
            string hubSpotIndustry = null,
            int? offset = null,
            int? limit = null,
            string sortBy = "sentAt",
            string sortOrder = "desc",
            bool enableNoTracking = false)
    {
        const int fetchLimit = 500;
        var requireBroadcastCampaignsStatuses = new List<string>
        {
            "sent", "sending", "paused"
        };

        offset ??= 0;

        var broadcastCampaigns = new List<CompanyMessageTemplate>();
        int totalRecords;

        var isCompanyFilterApplied = false;

        // The presence of companyIds alone is not a reliable indicator of whether filtering has been applied.
        // There are two possible scenarios:
        // 1. No matching companyIds were found after applying the filter.
        // 2. There was no need to apply a filter to the companyIds in the first place.
        if (ShouldApplyCompanyFilter(companyIds, companyName, countries))
        {
            companyIds = await GetFilteredCompanyIds(companyIds, companyName, countries);
            isCompanyFilterApplied = true;
        }

        if (!industries.IsNullOrEmpty())
        {
            companyIds = await GetIndustriesFilteredCompanyIds(companyIds, industries);
            isCompanyFilterApplied = true;
        }

        if (!hubSpotIndustry.IsNullOrEmpty())
        {
            companyIds = await GetHubSpotIndustryFilteredCompanyIds(companyIds, hubSpotIndustry);
            isCompanyFilterApplied = true;
        }

        while (true)
        {
            var results = await _internalCampaignRepository.FindBroadcastCampaignsAsync(
                companyIds: companyIds,
                isCompanyFilterApplied,
                startDate: startDate,
                endDate: endDate,
                isSent: true,
                statuses: requireBroadcastCampaignsStatuses,
                channels: channels,
                offset: offset,
                limit: fetchLimit,
                sortBy: sortBy,
                sortOrder: sortOrder,
                enableNoTracking: enableNoTracking);


            broadcastCampaigns.AddRange(results.BroadcastCampaigns);
            offset += results.BroadcastCampaigns.Count;

            totalRecords = results.TotalCount;

            if (results.BroadcastCampaigns.Count == 0)
            {
                break;
            }

            if (limit.HasValue && broadcastCampaigns.Count >= limit.Value)
            {
                broadcastCampaigns = broadcastCampaigns.Take(limit.Value).ToList();

                break;
            }
        }

        return (broadcastCampaigns, totalRecords);
    }

    private static bool ShouldApplyCompanyFilter(List<string> companyIds, string companyName, List<string> countries)
    {
        return !companyIds.IsNullOrEmpty() || !string.IsNullOrWhiteSpace(companyName) || !countries.IsNullOrEmpty();
    }

    private async Task<List<string>> GetFilteredCompanyIds(
        List<string> companyIds,
        string companyName,
        List<string> countries)
    {
        companyIds ??= new List<string>();

        var hasFilter = !string.IsNullOrWhiteSpace(companyName) || !countries.IsNullOrEmpty();

        if (!hasFilter)
        {
            return companyIds;
        }

        // Make sure the filter is not null or empty if not GetFilteredCompanyIds will return all undeleted companies
        var filteredCompanyIds = await _internalCompanyRepository.GetFilteredCompanyIds(companyName, countries);
        companyIds.AddRange(filteredCompanyIds);

        return companyIds;
    }

    private async Task<List<string>> GetIndustriesFilteredCompanyIds(
        List<string> companyIds,
        List<string> industries)
    {
        companyIds ??= new List<string>();

        var filteredCompanyIds =
            await _internalCompanyRepository.GetIndustriesFilteredCompanyIds(companyIds, industries);
        companyIds.AddRange(filteredCompanyIds);

        return companyIds;
    }

    private async Task<List<string>> GetHubSpotIndustryFilteredCompanyIds(
        List<string> companyIds,
        string hubSpotIndustry)
    {
        companyIds ??= new List<string>();

        var filteredCompanyIds =
            await _internalCompanyRepository.GetHubSpotIndustryFilteredCompanyIds(companyIds, hubSpotIndustry);
        companyIds.AddRange(filteredCompanyIds);

        return companyIds;
    }
}