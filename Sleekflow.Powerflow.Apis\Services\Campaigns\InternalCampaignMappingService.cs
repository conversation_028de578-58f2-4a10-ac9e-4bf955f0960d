using Sleekflow.Powerflow.Apis.Services.Repositories;
using Travis_backend.BroadcastDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.Extensions;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using InternalBroadcastCampaignDto = Sleekflow.Powerflow.Apis.ViewModels.InternalCmsCampaign.InternalBroadcastCampaignDto;

namespace Sleekflow.Powerflow.Apis.Services.Campaigns;

public interface IInternalCampaignMappingService
{
    Task<List<InternalBroadcastCampaignDto>> MapBroadcastCampaignsToInternalBroadcastCampaignDtos(
        List<CompanyMessageTemplate> companyMessageTemplates);
}

public class InternalCampaignMappingService : IInternalCampaignMappingService
{
    private readonly ILogger<InternalCampaignMappingService> _logger;
    private readonly IInternalCompanyRepository _internalCompanyRepository;

    public InternalCampaignMappingService(
        ILogger<InternalCampaignMappingService> logger,
        IInternalCompanyRepository internalCompanyRepository)
    {
        _logger = logger;
        _internalCompanyRepository = internalCompanyRepository;
    }

    public async Task<List<InternalBroadcastCampaignDto>> MapBroadcastCampaignsToInternalBroadcastCampaignDtos(
        List<CompanyMessageTemplate> companyMessageTemplates)
    {
        var internalBroadcastCampaignDtos = await TransformCompanyMessageTemplatesToDtos(companyMessageTemplates);
        return internalBroadcastCampaignDtos;
    }

    private async Task<List<InternalBroadcastCampaignDto>> TransformCompanyMessageTemplatesToDtos(
        IReadOnlyCollection<CompanyMessageTemplate> companyMessageTemplates)
    {
        if (companyMessageTemplates.IsNullOrEmpty())
        {
            return new List<InternalBroadcastCampaignDto>();
        }

        var companyIds = companyMessageTemplates.Select(x => x.CompanyId).Distinct().ToList();

        var companies = await _internalCompanyRepository.GetCompaniesWithChannelConfigsAsync(companyIds);
        var companyDic = companies.ToDictionary(c => c.Id, c => c);

        var internalBroadcastCampaignDtos = companyMessageTemplates
            .Where(cmt => companyDic.TryGetValue(cmt.CompanyId, out _))
            .Select(cmt => MapToInternalBroadcastCampaignDto(cmt, companyDic[cmt.CompanyId]))
            .ToList();

        return internalBroadcastCampaignDtos;
    }

    private InternalBroadcastCampaignDto MapToInternalBroadcastCampaignDto(
        CompanyMessageTemplate companyMessageTemplate,
        Company company)
    {
        // Parse the datetime to datetimeoffset with timezoneId
        var timezoneId = company.TimeZoneInfoId;
        DateTimeOffset updatedAt;
        DateTimeOffset? sentAt;
        try
        {
            updatedAt = companyMessageTemplate.UpdatedAt.ConvertUtcDateTimeToDateTimeOffset(timezoneId);
            sentAt = companyMessageTemplate.SentAt?.ConvertUtcDateTimeToDateTimeOffset(timezoneId);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[MapToInternalBroadcastCampaignDto] Error when converting datetime to datetimeoffset with timezoneId: {timezoneId}",
                timezoneId);

            updatedAt = companyMessageTemplate.UpdatedAt;
            sentAt = companyMessageTemplate.SentAt;
        }

        var internalBroadcastCampaignDto = new InternalBroadcastCampaignDto
        {
            CompanyId = company.Id,
            CompanyName = company.CompanyName,
            Country = company.CompanyCountry,
            CreatedBy = companyMessageTemplate.SavedBy?.Identity?.DisplayName,
            BroadcastName = companyMessageTemplate.TemplateName,
            Channels = new List<string> { companyMessageTemplate.TargetedChannel.ChannelType },
            ChannelNames = new List<string> { GetBroadcastChannelName(companyMessageTemplate.TargetedChannel, company) },
            Status = companyMessageTemplate.Status,
            LastUpdated = updatedAt,
            SentAt = sentAt,
            Industry = company.CmsCompanyIndustry,
            HubSpotIndustry = company.CmsCompanyAdditionalInfo?.HubSpotCompanyIndustry
        };

        if (companyMessageTemplate.StatisticsData is null)
        {
            return internalBroadcastCampaignDto;
        }

        // Set properties based on the StatisticsData object, if available
        if (companyMessageTemplate.StatisticsData != null)
        {
            SetStatisticsDataProperties(internalBroadcastCampaignDto, companyMessageTemplate.StatisticsData);
        }

        return internalBroadcastCampaignDto;
    }

    private void SetStatisticsDataProperties(
        InternalBroadcastCampaignDto internalBroadcastCampaignDto,
        StatisticsData statisticsData)
    {
        internalBroadcastCampaignDto.NumberOfSentBroadcastCampaigns = statisticsData.Sent;
        internalBroadcastCampaignDto.NumberOfDeliveredBroadcastCampaigns = statisticsData.Delivered;
        internalBroadcastCampaignDto.NumberOfReadBroadcastCampaigns = statisticsData.Read;
        internalBroadcastCampaignDto.NumberOfRepliedBroadcastCampaigns = statisticsData.Replied;
    }

    private static string GetBroadcastChannelName(TargetedChannel targetedChannel, Company company)
    {
        if (targetedChannel is null
            || string.IsNullOrWhiteSpace(targetedChannel.ChannelIdentityId))
        {
            return string.Empty;
        }

        return targetedChannel.ChannelType switch
        {
            ChannelTypes.WhatsappTwilio => company.WhatsAppConfigs
                .Where(x => x.ChannelIdentityId == targetedChannel.ChannelIdentityId)
                .Select(x => x.Name)
                .FirstOrDefault(),
            ChannelTypes.Whatsapp360Dialog => company.WhatsApp360DialogConfigs
                .Where(x => x.ChannelIdentityId == targetedChannel.ChannelIdentityId)
                .Select(x => x.ChannelName)
                .FirstOrDefault(),
            ChannelTypes.WhatsappCloudApi => company.WhatsappCloudApiConfigs
                .Where(x => x.ChannelIdentityId == targetedChannel.ChannelIdentityId)
                .Select(x => x.ChannelName)
                .FirstOrDefault(),
            ChannelTypes.Facebook => company.FacebookConfigs
                .Where(x => x.PageId == targetedChannel.ChannelIdentityId)
                .Select(x => x.PageName)
                .FirstOrDefault(),
            ChannelTypes.Wechat =>
                // Currently we only support one WeChat channel per company
                company.WeChatConfig?.Name ?? "N/A",
            ChannelTypes.Line =>
                // Travis_backend\ConversationServices\ConversationMessageService.cs:4974
                // Currently we only support one line channel per company same as WeChat
                company.LineConfigs.FirstOrDefault()?.Name ?? "N/A",
            ChannelTypes.LiveChat => "Live Chat",
            ChannelTypes.Viber => company.ViberConfigs
                .Where(x => x.ChannelIdentityId == targetedChannel.ChannelIdentityId)
                .Select(x => x.DisplayName)
                .FirstOrDefault(),
            ChannelTypes.Telegram => company.TelegramConfigs
                .Where(x => x.ChannelIdentityId == targetedChannel.ChannelIdentityId)
                .Select(x => x.DisplayName)
                .FirstOrDefault(),
            ChannelTypes.Sms => company.SMSConfigs
                .Where(x => x.ChannelIdentityId == targetedChannel.ChannelIdentityId)
                .Select(x => x.Name)
                .FirstOrDefault(),
            _ => string.Empty
        };
    }
}