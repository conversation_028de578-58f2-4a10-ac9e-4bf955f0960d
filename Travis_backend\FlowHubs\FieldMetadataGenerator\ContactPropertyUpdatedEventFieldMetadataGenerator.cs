﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Database.Services;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Models;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Utils;
using Newtonsoft.Json.Linq;

namespace Travis_backend.FlowHubs.FieldMetadataGenerator;

public sealed class ContactPropertyUpdatedEventFieldMetadataGenerator :
    FlowHubEventFieldMetadataGeneratorBase,
    IFlowHubEventFieldMetadataGenerator
{
    public string EventName => TriggerIds.ContactPropertyUpdated;

    public Dictionary<string, string> FieldPathOptionRequestPathDict { get; } = new()
    {
        { "contact.labels[*].LabelValue", "FlowHub/SelectOptions/Labels" },
        { "post_updated_contact.labels[*].LabelValue", "FlowHub/SelectOptions/Labels" },
        { "pre_updated_contact.labels[*].LabelValue", "FlowHub/SelectOptions/Labels" },
        { "change_entries[*].property_id", "FlowHub/SelectOptions/ContactProperties" },
        { "lists[*].id", "FlowHub/SelectOptions/ContactLists" },
        { "sleekflow_staff_id", "FlowHub/SelectOptions/Staffs" },
        { "contactOwner.id", "FlowHub/SelectOptions/Staffs" },
        { "contact.LastChannel", "FlowHub/SelectOptions/ChannelTypes" }
    };

    public ContactPropertyUpdatedEventFieldMetadataGenerator(
        IDbContextService dbContextService, IFieldMetadataVariableVisibilityService fieldMetadataVariableVisibilityService)
        : base(dbContextService, fieldMetadataVariableVisibilityService)
    {
    }

    public async Task<(HashSet<FieldMetadata> FieldMetadataSet, Type EventBodyType, string ContactObjectName)> GenerateAsync(
        string companyId,
        Dictionary<string, object?>? parameters)
    {
        var (userProfileDict, customFieldOptionsRequestPathDict) = await GenerateUserProfileDictAsync(companyId);
        var eventBody = JsonUtils.GenerateSampleObjectFromType<OnContactUpdatedEventBody>();

        eventBody.Contact = userProfileDict;
        eventBody.PostUpdatedContact = userProfileDict;
        eventBody.PreUpdatedContact = userProfileDict;
        eventBody.ChangeEntries.ForEach(x => x.PropertyName = null!);

        // Convert to JSON and add contactOwner and lists at root level
        var eventBodyJsonString = eventBody.ToJson();
        var eventBodyJson = JObject.Parse(eventBodyJsonString);
        AddContactExpansionToEventBodyJson(eventBodyJson);

        var fieldMetadataSet = JsonUtils
            .SimplifyJsonData(eventBodyJson.ToString())
            .GetFieldMetadata();

        // Get all available field paths
        var allFieldPaths = fieldMetadataSet.Select(f => f.FieldPath);

        // Get visible field paths including contact custom fields
        var visibleFieldPaths = _fieldMetadataVariableVisibilityService
            .GetVisibleFieldPathsWithDynamicSupport(EventName, allFieldPaths);
        var uiCopyMapping = _fieldMetadataVariableVisibilityService.GetFieldUiCopyMapping(EventName);

        // Filter to only visible fields
        var filteredFieldMetadataSet = fieldMetadataSet
            .Where(field => visibleFieldPaths.Contains(field.FieldPath))
            .ToHashSet();

        foreach (var metadata in filteredFieldMetadataSet)
        {
            if (uiCopyMapping.TryGetValue(metadata.FieldPath, out var uiCopy))
            {
                metadata.UiCopy = uiCopy;
            }
        }

        PopulateOptionRequestPath(
            filteredFieldMetadataSet,
            customFieldOptionsRequestPathDict,
            FieldPathOptionRequestPathDict);

        return (filteredFieldMetadataSet, typeof(OnContactUpdatedEventBody), nameof(eventBody.Contact));
    }
}