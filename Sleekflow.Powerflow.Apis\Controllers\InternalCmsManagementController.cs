using System.Globalization;
using System.Linq.Expressions;
using System.Net.Mime;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Hangfire;
using LinqKit;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.VisualBasic.FileIO;
using Newtonsoft.Json;
using Sleekflow.Apis.TenantHub.Api;
using Sleekflow.Apis.TenantHub.Model;
using Sleekflow.Powerflow.Apis.Services;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.Services;
using Travis_backend.Auth0.Services;
using Travis_backend.BackgroundTaskServices;
using Travis_backend.BackgroundTaskServices.Helpers;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Configuration;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.ConversationServices.Models;
using Travis_backend.Data.BackgroundTask;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FacebookInstagramIntegrationDomain.Services;
using Travis_backend.FlowHubs;
using Travis_backend.Helpers;
using Travis_backend.IntegrationServices;
using Travis_backend.IntelligentHubDomain.Services;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.Services;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.InternalIntegrationHubDomain.Services;
using Travis_backend.ResellerDomain.Services;
using Travis_backend.SleekflowCrmHubDomain.Services;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Services;
using Travis_backend.TenantHubDomain.Services;
using Twilio;
using Twilio.Rest.Api.V2010;
using ActivateCompanyQrCodeRequest = Sleekflow.Powerflow.Apis.ViewModels.ActivateCompanyQrCodeRequest;
using AddTwilioCreditRequest = Sleekflow.Powerflow.Apis.ViewModels.AddTwilioCreditRequest;
using AddTwilioSubAccountRequest = Sleekflow.Powerflow.Apis.ViewModels.AddTwilioSubAccountRequest;
using BulkSyncStaffContactToSleekflowAccountRequest =
    Sleekflow.Powerflow.Apis.ViewModels.BulkSyncStaffContactToSleekflowAccountRequest;
using ChangeCompanyOwnerRequest = Sleekflow.Powerflow.Apis.ViewModels.ChangeCompanyOwnerRequest;
using CmsCacheKeyHelper = Sleekflow.Powerflow.Apis.Helpers.CmsCacheKeyHelper;
using CmsCompanyListConnectedChannelView = Sleekflow.Powerflow.Apis.ViewModels.CmsCompanyListConnectedChannelView;
using CmsCompanyListItemView = Sleekflow.Powerflow.Apis.ViewModels.CmsCompanyListItemView;
using CmsCompanyResponse = Sleekflow.Powerflow.Apis.ViewModels.CmsCompanyResponse;
using CmsCompanyStaffData = Sleekflow.Powerflow.Apis.ViewModels.CmsCompanyStaffData;
using CmsCompanyStaffDto = Sleekflow.Powerflow.Apis.ViewModels.CmsCompanyStaffDto;
using CmsCompanyStaffListResponse = Sleekflow.Powerflow.Apis.ViewModels.CmsCompanyStaffListResponse;
using CmsCompanyTeamDto = Sleekflow.Powerflow.Apis.ViewModels.CmsCompanyTeamDto;
using CmsCompanyUsageResponse = Sleekflow.Powerflow.Apis.ViewModels.CmsCompanyUsageResponse;
using CmsContactOwnerAssignLogDto = Sleekflow.Powerflow.Apis.ViewModels.CmsContactOwnerAssignLogDto;
using CmsIndustry = Sleekflow.Powerflow.Apis.Helpers.CmsIndustry;
using CmsLeadSource = Sleekflow.Powerflow.Apis.Helpers.CmsLeadSource;
using CmsResellerClientResponse = Sleekflow.Powerflow.Apis.ViewModels.CmsResellerClientResponse;
using CmsResellerResponse = Sleekflow.Powerflow.Apis.ViewModels.CmsResellerResponse;
using CmsTwilioUsageDto = Sleekflow.Powerflow.Apis.ViewModels.CmsTwilioUsageDto;
using CmsUserDto = Sleekflow.Powerflow.Apis.ViewModels.CmsUserDto;
using CmsWhatsApp360DialogUsageRecordViewModel =
    Sleekflow.Powerflow.Apis.ViewModels.CmsWhatsApp360DialogUsageRecordViewModel;
using Company = Travis_backend.CompanyDomain.Models.Company;
using CompanyIdNamePair = Sleekflow.Powerflow.Apis.ViewModels.CompanyIdNamePair;
using CompanyPublicApiKeyDto = Sleekflow.Powerflow.Apis.ViewModels.CompanyPublicApiKeyDto;
using DeleteCompanyDetailCacheRequest = Sleekflow.Powerflow.Apis.ViewModels.DeleteCompanyDetailCacheRequest;
using DeleteCompanyPublicApiKeyRequest = Sleekflow.Powerflow.Apis.ViewModels.DeleteCompanyPublicApiKeyRequest;
using DeleteCompanyRequest = Sleekflow.Powerflow.Apis.ViewModels.DeleteCompanyRequest;
using DeleteCompanyStaffRequest = Sleekflow.Powerflow.Apis.ViewModels.DeleteCompanyStaffRequest;
using DeleteSandboxRequest = Sleekflow.Powerflow.Apis.ViewModels.DeleteSandboxRequest;
using DeleteTwilioUsageRecordRequest = Sleekflow.Powerflow.Apis.ViewModels.DeleteTwilioUsageRecordRequest;
using FacebookConnectedDto = Sleekflow.Powerflow.Apis.ViewModels.FacebookConnectedDto;
using GetAllCmsSelectionsResponse = Sleekflow.Powerflow.Apis.ViewModels.GetAllCmsSelectionsResponse;
using GetAllCompanyDetailsResponse = Sleekflow.Powerflow.Apis.ViewModels.GetAllCompanyDetailsResponse;
using GetCmsCompaniesResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCmsCompaniesResponse;
using GetCmsCompanyBillRecordsResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCmsCompanyBillRecordsResponse;
using GetCompanyDetailsRequest = Sleekflow.Powerflow.Apis.ViewModels.GetCompanyDetailsRequest;
using GetCompanyIdNamePairResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCompanyIdNamePairResponse;
using GetCompanyIdNamePairsRequest = Sleekflow.Powerflow.Apis.ViewModels.GetCompanyIdNamePairsRequest;
using GetCompanyPublicApiKeysRequest = Sleekflow.Powerflow.Apis.ViewModels.GetCompanyPublicApiKeysRequest;
using GetCompanyPublicApiKeysResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCompanyPublicApiKeysResponse;
using GetCompanyRequest = Sleekflow.Powerflow.Apis.ViewModels.GetCompanyRequest;
using GetCompanyStaffsRequest = Sleekflow.Powerflow.Apis.ViewModels.GetCompanyStaffsRequest;
using GetCompanyStaffsResponse = Sleekflow.Powerflow.Apis.ViewModels.GetCompanyStaffsResponse;
using GetFacebookConnectedResponse = Sleekflow.Powerflow.Apis.ViewModels.GetFacebookConnectedResponse;
using GetOrUpdateTwilioUsageRecordRequest = Sleekflow.Powerflow.Apis.ViewModels.GetOrUpdateTwilioUsageRecordRequest;
using GetResetPasswordResponse = Sleekflow.Powerflow.Apis.ViewModels.GetResetPasswordResponse;
using GetResetStaffPasswordUrlRequest = Sleekflow.Powerflow.Apis.ViewModels.GetResetStaffPasswordUrlRequest;
using GetStaffListRequest = Sleekflow.Powerflow.Apis.ViewModels.GetStaffListRequest;
using GetTwilioTopUpLogRequest = Sleekflow.Powerflow.Apis.ViewModels.GetTwilioTopUpLogRequest;
using GetTwilioTopUpLogResponse = Sleekflow.Powerflow.Apis.ViewModels.GetTwilioTopUpLogResponse;
using GetTwilioUsageResponse = Sleekflow.Powerflow.Apis.ViewModels.GetTwilioUsageResponse;
using ImportStaffResponse = Sleekflow.Powerflow.Apis.ViewModels.ImportStaffResponse;
using IssueCompanyPublicApiKeyRequest = Sleekflow.Powerflow.Apis.ViewModels.IssueCompanyPublicApiKeyRequest;
using IssueCompanyPublicApiKeyResponse = Sleekflow.Powerflow.Apis.ViewModels.IssueCompanyPublicApiKeyResponse;
using PaymentMethod = Travis_backend.InternalDomain.Models.PaymentMethod;
using ReEnqueueBackgroundTaskRequest = Sleekflow.Powerflow.Apis.ViewModels.ReEnqueueBackgroundTaskRequest;
using RemoveBillRecordRequest = Sleekflow.Powerflow.Apis.ViewModels.RemoveBillRecordRequest;
using SetCmsCompanyRemarkRequest = Sleekflow.Powerflow.Apis.ViewModels.SetCmsCompanyRemarkRequest;
using SetCompanyIndustryRequest = Sleekflow.Powerflow.Apis.ViewModels.SetCompanyIndustryRequest;
using SetCompanyLeadSourceRequest = Sleekflow.Powerflow.Apis.ViewModels.SetCompanyLeadSourceRequest;
using SetCompanyLimitRequest = Sleekflow.Powerflow.Apis.ViewModels.SetCompanyLimitRequest;
using SetCompanyTrialDaysRequest = Sleekflow.Powerflow.Apis.ViewModels.SetCompanyTrialDaysRequest;
using SetTwilioCreditResponse = Sleekflow.Powerflow.Apis.ViewModels.SetTwilioCreditResponse;
using SetTwilioVerificationStatusRequest = Sleekflow.Powerflow.Apis.ViewModels.SetTwilioVerificationStatusRequest;
using SyncStaffContactToSleekflowAccountRequest =
    Sleekflow.Powerflow.Apis.ViewModels.SyncStaffContactToSleekflowAccountRequest;
using SyncWFacebookHistoryRequest = Sleekflow.Powerflow.Apis.ViewModels.SyncWFacebookHistoryRequest;
using TwilioTopUpLogDto = Sleekflow.Powerflow.Apis.ViewModels.TwilioTopUpLogDto;
using UpdateCompanyDetailRequest = Sleekflow.Powerflow.Apis.ViewModels.UpdateCompanyDetailRequest;
using UpdateCompanyTypeRequest = Sleekflow.Powerflow.Apis.ViewModels.UpdateCompanyTypeRequest;
using UpdateCompanyUsageLimitOffsetProfileRequest =
    Sleekflow.Powerflow.Apis.ViewModels.UpdateCompanyUsageLimitOffsetProfileRequest;
using UpdateTwilioSubAccountRequest = Sleekflow.Powerflow.Apis.ViewModels.UpdateTwilioSubAccountRequest;
using UpdateTwilioSubAccountResponse = Sleekflow.Powerflow.Apis.ViewModels.UpdateTwilioSubAccountResponse;
using ToggleStripeIntegrationRequest = Sleekflow.Powerflow.Apis.ViewModels.ToggleStripeIntegrationRequest;
using ToggleExpressImportRequest = Sleekflow.Powerflow.Apis.ViewModels.ToggleExpressImportRequest;
using ToggleExperimentalImportRequest = Sleekflow.Powerflow.Apis.ViewModels.ToggleExperimentalImportRequest;
using MigrateShopifyConfigToV2Request = Sleekflow.Powerflow.Apis.ViewModels.MigrateShopifyConfigToV2Request;
using UserInfoResponse = Travis_backend.ConversationDomain.ViewModels.UserInfoResponse;
namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Internal Cms APIs for managing Companies, ChatApi, Twilio, Facebook; Special Access Token required.
/// </summary>
[Authorize(Roles = ApplicationUserRole.InternalCmsUser)] // Basic Role Requirement
[Route("/internal/management/[action]")]
public class InternalCmsManagementController : InternalControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly ICompanyUsageService _companyUsageService;
    private readonly ITwilioService _twilioService;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;
    private readonly IInternalHubSpotService _internalHubSpotService;
    private readonly ICoreService _coreService;
    private readonly ICompanyService _companyService;
    private readonly IPowerflowManageResellerRepository _powerflowManageResellerRepository;
    private readonly IResellerPortalRepository _resellerPortalRepository;
    private readonly IInternalAnalyticService _internalAnalyticService;
    private readonly IEmailNotificationService _emailNotificationService;
    private readonly IUserRoleStaffRepository _userRoleStaffRepository;
    private readonly IStaffHooks _staffHooks;
    private readonly IFlowHubService _flowHubService;
    private readonly IInternalPartnerStackService _internalPartnerStackService;
    private readonly IUsageCycleCalculator _usageCycleCalculator;
    private readonly IEnabledFeaturesService _enabledFeaturesService;
    private readonly ICompaniesService _companiesService;
    private readonly IManagementUsersApi _managementUsersApi;
    private readonly IManagementImportUserApi _managementImportUserApi;
    private readonly IBackgroundJobClient _backgroundJobClient;
    private readonly ISubscriptionPlanService _subscriptionPlanService;
    private readonly IIntelligentHubService _intelligentHubService;
    private readonly IShopifyService _shopifyService;

    private readonly IBillRecordRevenueCalculatorService _billRecordRevenueCalculatorService;
    private readonly ICompanyDetailsService _companyDetailsService;

    public InternalCmsManagementController(
        ApplicationDbContext appDbContext,
        SleekflowUserManager userManager,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<InternalCmsManagementController> logger,
        ICompanyUsageService companyUsageService,
        ITwilioService twilioService,
        ICacheManagerService cacheManagerService,
        ICompanyInfoCacheService companyInfoCacheService,
        IInternalHubSpotService internalHubSpotService,
        ICoreService coreService,
        ICompanyService companyService,
        IPowerflowManageResellerRepository powerflowManageResellerRepository,
        IResellerPortalRepository resellerPortalRepository,
        IInternalAnalyticService internalAnalyticService,
        IEmailNotificationService emailNotificationService,
        IUserRoleStaffRepository userRoleStaffRepository,
        IStaffHooks staffHooks,
        IFlowHubService flowHubService,
        IInternalPartnerStackService internalPartnerStackService,
        IUsageCycleCalculator usageCycleCalculator,
        ICompaniesService companiesService,
        IEnabledFeaturesService enabledFeaturesService,
        ISubscriptionPlanService subscriptionPlanService,
        IManagementImportUserApi managementImportUserApi,
        IBackgroundJobClient backgroundJobClient,
        IIntelligentHubService intelligentHubService,
        IShopifyService shopifyService,
        IBillRecordRevenueCalculatorService billRecordRevenueCalculatorService,
        ICompanyDetailsService companyDetailsService)
        : base(userManager)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _companyUsageService = companyUsageService;
        _twilioService = twilioService;
        _cacheManagerService = cacheManagerService;
        _companyInfoCacheService = companyInfoCacheService;
        _internalHubSpotService = internalHubSpotService;
        _coreService = coreService;
        _companyService = companyService;
        _powerflowManageResellerRepository = powerflowManageResellerRepository;
        _resellerPortalRepository = resellerPortalRepository;
        _internalAnalyticService = internalAnalyticService;
        _emailNotificationService = emailNotificationService;
        _userRoleStaffRepository = userRoleStaffRepository;
        _staffHooks = staffHooks;
        _flowHubService = flowHubService;
        _internalPartnerStackService = internalPartnerStackService;
        _usageCycleCalculator = usageCycleCalculator;
        _enabledFeaturesService = enabledFeaturesService;
        _companiesService = companiesService;
        _managementImportUserApi = managementImportUserApi;
        _backgroundJobClient = backgroundJobClient;
        _subscriptionPlanService = subscriptionPlanService;
        _intelligentHubService = intelligentHubService;
        _shopifyService = shopifyService;
        _billRecordRevenueCalculatorService = billRecordRevenueCalculatorService;
        _companyDetailsService = companyDetailsService;
    }

    #region Cms UI Selections

    /// <summary>
    /// Get All CMS Contact Owners.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetAllCmsSelectionsResponse>> GetAllCmsSelectionsResponse()
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsUser
                }) == null)
        {
            return Unauthorized();
        }

        var resultCacheData =
            await _cacheManagerService.GetCacheWithConstantKeyAsync(CmsCacheKeyHelper.GetSelectionsKey);

        if (!string.IsNullOrWhiteSpace(resultCacheData))
        {
            return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
        }

        var result = new GetAllCmsSelectionsResponse();

        var salesUsers = await _userManager.GetUsersInRoleAsync(ApplicationUserRole.InternalCmsSalesUser);
        var customerSuccessUsers =
            await _userManager.GetUsersInRoleAsync(ApplicationUserRole.InternalCmsCustomerSuccessUser);

        result.SalesContactOwners = _mapper.Map<List<CmsUserDto>>(salesUsers);
        result.CustomerSuccessContactOwners = _mapper.Map<List<CmsUserDto>>(customerSuccessUsers);

        var companyOwners = new List<CmsUserDto>();
        companyOwners.AddRange(result.SalesContactOwners);
        companyOwners.AddRange(result.CustomerSuccessContactOwners);
        companyOwners = companyOwners.DistinctBy(x => x.Id).ToList();

        result.CompanyOwners = companyOwners;

        result.AllSubscriptionPlans = ValidSubscriptionPlan.SubscriptionPlan;
        result.AllCmsLeadSources = CmsLeadSource.AllCmsLeadSources;
        result.AllCmsIndustries = CmsIndustry.AllCmsIndustries;
        result.AllAddOnPlans = ValidSubscriptionPlan.CmsAllAddOn;
        result.AllPartnerStackGroups = await _internalPartnerStackService.GetAllPartnerStackGroups();
        result.AllCmsHubSpotTeams = await _internalHubSpotService.GetHubSpotTeamNames();

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            CmsCacheKeyHelper.GetSelectionsKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Remove CMS Cache.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> RemoveAllCmsSelectionsResponseCache()
    {
        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(CmsCacheKeyHelper.GetSelectionsKey);
        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(CmsCacheKeyHelper.GetAllCompaniesKey);

        return Ok();
    }

    #endregion

    #region Company

    /// <summary>
    /// Get CMS Company List with Filters.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCmsCompaniesResponse>> GetCompanies([FromBody] GetCompanyRequest request)
    {
        if (await GetCurrentValidInternalUser(
                [ApplicationUserRole.InternalCmsUser]) == null)
        {
            return Unauthorized();
        }

        //Filters
        List<Expression<Func<Company, bool>>> listOfExpression = [];

        // It may fail if using Task.WhenAll as DbContext multiple operations are not supported.
        var companiesFlowHubData = await GetCompaniesFlowHubDataAsync(
            request.IsFlowHubDataRequired,
            request.IsAllowCacheFlowHubData);
        var companiesIntelligentHubData = await GetCompaniesIntelligentHubDataAsync(
            request.IsIntelligentHubDataRequired,
            request.IsAllowCacheIntelligentHubData);

        if (request.IsFlowHubDataRequired)
        {
            if (request.EnrolmentsRange != null)
            {
                var companyIds = companiesFlowHubData
                    .Where(
                        x => x.NumOfEnrolments >= request.EnrolmentsRange.RangeFrom &&
                             x.NumOfEnrolments <= request.EnrolmentsRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }

            if (request.EnrolmentPercentageRange != null)
            {
                var companyIds = companiesFlowHubData
                    .Where(
                        x => x.EnrolmentPercentage >= request.EnrolmentPercentageRange.RangeFrom &&
                             x.EnrolmentPercentage <= request.EnrolmentPercentageRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }

            if (request.TotalFlowsRange != null)
            {
                var companyIds = companiesFlowHubData
                    .Where(
                        x => x.NumOfWorkflows >= request.TotalFlowsRange.RangeFrom &&
                             x.NumOfWorkflows <= request.TotalFlowsRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }

            if (request.ActiveFlowsRange != null)
            {
                var companyIds = companiesFlowHubData
                    .Where(
                        x => x.NumOfActiveWorkflows >= request.ActiveFlowsRange.RangeFrom &&
                             x.NumOfActiveWorkflows <= request.ActiveFlowsRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }

            if (request.EnrolmentsLimitRange != null)
            {
                var companyIds = companiesFlowHubData
                    .Where(
                        x => x.MaximumNumOfMonthlyWorkflowExecutions >= request.EnrolmentsLimitRange.RangeFrom &&
                             x.MaximumNumOfMonthlyWorkflowExecutions <= request.EnrolmentsLimitRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }

            if (request.TotalFlowsLimitRange != null)
            {
                var companyIds = companiesFlowHubData
                    .Where(
                        x => x.MaximumNumOfWorkflows >= request.TotalFlowsLimitRange.RangeFrom &&
                             x.MaximumNumOfWorkflows <= request.TotalFlowsLimitRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }

            if (request.ActiveFlowsLimitRange != null)
            {
                var companyIds = companiesFlowHubData
                    .Where(
                        x => x.MaximumNumOfActiveWorkflows >= request.ActiveFlowsLimitRange.RangeFrom &&
                             x.MaximumNumOfActiveWorkflows <= request.ActiveFlowsLimitRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }

            if (request.MaxNodesLimitRange != null)
            {
                var companyIds = companiesFlowHubData
                    .Where(
                        x => x.MaximumNumOfNodesPerWorkflow >= request.MaxNodesLimitRange.RangeFrom &&
                             x.MaximumNumOfNodesPerWorkflow <= request.MaxNodesLimitRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }

            if (request.IsGetPurchasedFlowEnrolmentAddOnCompany)
            {
                request.AddOnPlanIds.AddRange(SubscriptionPlansId.PurchasableFlowBuilderFlowEnrolmentsAddOns);
            }
        }

        if (request.IsIntelligentHubDataRequired)
        {
            if (request.TotalAiFeaturesUsageRange != null)
            {
                var companyIds = companiesIntelligentHubData
                    .Where(
                        x => x.TotalAiFeaturesUsage >= request.TotalAiFeaturesUsageRange.RangeFrom &&
                             x.TotalAiFeaturesUsage <= request.TotalAiFeaturesUsageRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }

            if (request.TotalAiFeaturesUsageLimitRange != null)
            {
                var companyIds = companiesIntelligentHubData
                    .Where(
                        x => x.TotalAiFeaturesUsageLimit >= request.TotalAiFeaturesUsageLimitRange.RangeFrom &&
                             x.TotalAiFeaturesUsageLimit <= request.TotalAiFeaturesUsageLimitRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }

            if (request.TotalAiFeaturesUsagePercentageRange != null)
            {
                var companyIds = companiesIntelligentHubData
                    .Where(
                        x => x.TotalAiFeaturesUsagePercentage >=
                             request.TotalAiFeaturesUsagePercentageRange.RangeFrom &&
                             x.TotalAiFeaturesUsagePercentage <= request.TotalAiFeaturesUsagePercentageRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }

            if (request.TotalAgentFeaturesUsageRange != null)
            {
                var companyIds = companiesIntelligentHubData
                    .Where(
                        x => x.TotalAgentFeaturesUsage >= request.TotalAgentFeaturesUsageRange.RangeFrom &&
                             x.TotalAgentFeaturesUsage <= request.TotalAgentFeaturesUsageRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }

            if (request.TotalAgentFeaturesUsageLimitRange != null)
            {
                var companyIds = companiesIntelligentHubData
                    .Where(
                        x => x.TotalAgentFeaturesUsageLimit >= request.TotalAgentFeaturesUsageLimitRange.RangeFrom &&
                             x.TotalAgentFeaturesUsageLimit <= request.TotalAgentFeaturesUsageLimitRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }

            if (request.TotalAgentFeaturesUsagePercentageRange != null)
            {
                var companyIds = companiesIntelligentHubData
                    .Where(
                        x => x.TotalAgentFeaturesUsagePercentage >=
                             request.TotalAgentFeaturesUsagePercentageRange.RangeFrom &&
                             x.TotalAgentFeaturesUsagePercentage <= request.TotalAgentFeaturesUsagePercentageRange.RangeTo)
                    .Select(x => x.CompanyId)
                    .ToList();

                AddFilterExpression(listOfExpression, true, x => companyIds.Contains(x.Id));
            }
        }

        if (request.IsGetAllCompany && !request.IsExcludeUserProfileCount)
        {
            var resultCacheData =
                await _cacheManagerService.GetCacheWithConstantKeyAsync(CmsCacheKeyHelper.GetAllCompaniesKey);

            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var result = new GetCmsCompaniesResponse();

        IQueryable<Company> companyQuery = _appDbContext.CompanyCompanies
            .AsSplitQuery()
            .Include(x => x.Staffs)
            .Include(x => x.CmsCompanyOwner)
            .Include(x => x.CmsActivationOwner)
            .Include(x => x.BillRecords)
            .ThenInclude(x => x.CmsSalesPaymentRecords)
            .Include(x => x.WebClientSenders)
            .Include(x => x.CmsCompanyAdditionalInfo)
            .AsNoTracking();

        if (request.IsFilter)
        {
            if (!string.IsNullOrWhiteSpace(request.CompanyName))
            {
                request.CompanyName = request.CompanyName.Trim();
            }

            if (!string.IsNullOrWhiteSpace(request.CompanyId))
            {
                request.CompanyId = request.CompanyId.Trim();
            }

            if (!string.IsNullOrWhiteSpace(request.StaffEmail))
            {
                request.StaffEmail = request.StaffEmail.Trim();
            }

            if (!string.IsNullOrWhiteSpace(request.StaffName))
            {
                request.StaffName = request.StaffName.Trim();
            }

            if (!string.IsNullOrWhiteSpace(request.StaffPhoneNumber))
            {
                request.StaffPhoneNumber = request.StaffPhoneNumber.Trim();
            }

            // Bill Plans Grouping
            if (request.BillPlanIds.Count > 0)
            {
                if (request.BillPlanIds.Contains("all_sleekflow_free_plan"))
                {
                    request.BillPlanIds.AddRange(ValidSubscriptionPlan.FreePlans);
                }

                if (request.BillPlanIds.Contains("all_sleekflow_pro_plan"))
                {
                    request.BillPlanIds.AddRange(ValidSubscriptionPlan.ProTier);
                }

                if (request.BillPlanIds.Contains("all_sleekflow_premium_plan"))
                {
                    request.BillPlanIds.AddRange(ValidSubscriptionPlan.PremiumTier);
                }
            }

            // Lead Source Grouping
            if (request.LeadSources.Count > 0)
            {
                if (request.LeadSources.Contains("Inbound"))
                {
                    request.LeadSources.AddRange(CmsLeadSource.Inbound);
                }

                if (request.LeadSources.Contains("Outbound"))
                {
                    request.LeadSources.AddRange(CmsLeadSource.Outbound);
                }

                if (request.LeadSources.Contains("Paid"))
                {
                    request.LeadSources.AddRange(CmsLeadSource.Paid);
                }
            }

            if (!string.IsNullOrWhiteSpace(request.CompanyId))
            {
                request.CompanyIds.AddRange(request.CompanyId.Split(",").ToList());
            }

            // Company Id(s) Filter
            AddFilterExpression(listOfExpression, request.CompanyIds.Count > 0, x => request.CompanyIds.Contains(x.Id));

            // Company Name Filter
            AddFilterExpression(
                listOfExpression,
                !string.IsNullOrEmpty(request.CompanyName),
                x => x.CompanyName.Contains(request.CompanyName));

            // Staff Name Filter
            AddFilterExpression(
                listOfExpression,
                !string.IsNullOrEmpty(request.StaffName),
                x =>
                    _appDbContext.UserRoleStaffs.Where(s => s.Identity.DisplayName.Contains(request.StaffName))
                        .Select(s => s.CompanyId).Contains(x.Id));

            // Staff Email Filter
            AddFilterExpression(
                listOfExpression,
                !string.IsNullOrEmpty(request.StaffEmail),
                x =>
                    _appDbContext.UserRoleStaffs.Where(
                            s => s.Identity.Email != null && s.Identity.Email.Contains(request.StaffEmail))
                        .Select(s => s.CompanyId).Contains(x.Id));

            // Staff Phone Number
            AddFilterExpression(
                listOfExpression,
                !string.IsNullOrEmpty(request.StaffPhoneNumber),
                x =>
                    _appDbContext.UserRoleStaffs
                        .Where(
                            s => s.Identity.PhoneNumber != null &&
                                 s.Identity.PhoneNumber.Contains(request.StaffPhoneNumber))
                        .Select(s => s.CompanyId).Contains(x.Id));

            // Staff Count Filter
            AddFilterExpression(
                listOfExpression,
                request.StaffCountRange != null,
                x => x.Staffs.Count(u => u.Id != 1) >= request.StaffCountRange!.RangeFrom &&
                     x.Staffs.Count(u => u.Id != 1) <= request.StaffCountRange.RangeTo);

            // User Profile Count Filter
            AddFilterExpression(
                listOfExpression,
                request.UserContactCountRange != null,
                x =>
                    _appDbContext.UserProfiles.Count(
                        u => u.CompanyId == x.Id &&
                             u.ActiveStatus == ActiveStatus.Active) >= request.UserContactCountRange!.RangeFrom &&
                    _appDbContext.UserProfiles.Count(
                        u => u.CompanyId == x.Id &&
                             u.ActiveStatus == ActiveStatus.Active) <= request.UserContactCountRange.RangeTo);

            // Create Range Filter
            AddFilterExpression(
                listOfExpression,
                request.CreateDateRange != null,
                x => x.CreatedAt >= request.CreateDateRange!.DateRangeFrom &&
                     x.CreatedAt <= request.CreateDateRange.DateRangeTo);

            // Staff Last Login Date Range Filter
            AddFilterExpression(
                listOfExpression,
                request.StaffLastLoginDateRange != null,
                x => x.Staffs.Max(s => s.Identity.LastLoginAt) >= request.StaffLastLoginDateRange!.DateRangeFrom &&
                     x.Staffs.Max(s => s.Identity.LastLoginAt) <= request.StaffLastLoginDateRange.DateRangeTo);

            // Country
            AddFilterExpression(
                listOfExpression,
                request.Countries.Count > 0,
                x => request.Countries.Contains(x.CompanyCountry));

            // Industries
            AddFilterExpression(
                listOfExpression,
                request.Industries.Count > 0,
                x => request.Industries.Contains(x.CmsCompanyIndustry));
            AddFilterExpression(
                listOfExpression,
                !string.IsNullOrEmpty(request.HubSpotIndustry),
                x => x.CmsCompanyAdditionalInfo.HubSpotCompanyIndustry.Contains(request.HubSpotIndustry));


            // Lead Sources
            AddFilterExpression(
                listOfExpression,
                request.LeadSources.Count > 0,
                x => request.LeadSources.Contains(x.CmsLeadSource));

            // Bill Plan Filter
            AddFilterExpression(
                listOfExpression,
                request.BillPlanIds.Count > 0,
                x => request.BillPlanIds.Contains(
                    x.BillRecords
                        .OrderByDescending(br => br.created)
                        .ThenByDescending(br => br.PayAmount)
                        .First(
                            br => ValidSubscriptionPlan.SubscriptionPlan.Contains(br.SubscriptionPlanId) &&
                                  br.Status != BillStatus.Inactive &&
                                  br.Status != BillStatus.Terminated &&
                                  br.PeriodStart <= DateTime.UtcNow &&
                                  br.PeriodEnd >= DateTime.UtcNow)
                        .SubscriptionPlanId));

            // Add On Ids
            AddFilterExpression(
                listOfExpression,
                request.AddOnPlanIds.Count > 0,
                x => x.BillRecords
                    .Where(
                        br => ValidSubscriptionPlan.AddOn.Contains(br.SubscriptionPlanId) &&
                              br.Status != BillStatus.Inactive &&
                              br.Status != BillStatus.Terminated &&
                              br.PeriodStart <= DateTime.UtcNow &&
                              DateTime.UtcNow <= br.PeriodEnd).Any(
                        br => request.AddOnPlanIds.Contains(br.SubscriptionPlanId)));

            // Subscription Plan Filter
            AddFilterExpression(
                listOfExpression,
                request.IsGetBankTransferEndDay,
                x => x.BillRecords
                    .Where(br => ValidSubscriptionPlan.SubscriptionPlan.Contains(br.SubscriptionPlanId))
                    .OrderByDescending(br => br.created)
                    .ThenByDescending(br => br.PayAmount)
                    .First()
                    .CmsSalesPaymentRecords
                    .Any(b => b.PaymentMethod == PaymentMethod.Bank));

            // Company Owner
            AddFilterExpression(
                listOfExpression,
                request.CompanyOwnerIds.Count > 0,
                x =>
                    request.CompanyOwnerIds.Contains(x.CmsCompanyOwnerId));

            // Customer Success
            AddFilterExpression(
                listOfExpression,
                request.ActivationOwnerIds.Count > 0,
                x =>
                    request.ActivationOwnerIds.Contains(x.CmsActivationOwnerId));

            // Cs Owner
            AddFilterExpression(
                listOfExpression,
                request.CsOwnerIds.Count > 0,
                x =>
                    request.CsOwnerIds.Contains(x.CmsCsOwnerId));

            // Staff Count Filter
            AddFilterExpression(
                listOfExpression,
                request.StaffLimitPercentageRange != null,
                x => (x.MaximumAgents >= 1
                         ? x.Staffs.Count(u => u.Id != 1) /
                           (decimal) x.MaximumAgents
                         : 0.00M) >= request.StaffLimitPercentageRange!.RangeFrom &&
                     (x.MaximumAgents >= 1
                         ? x.Staffs.Count(u => u.Id != 1) /
                           (decimal) x.MaximumAgents
                         : 0.00M) <= request.StaffLimitPercentageRange.RangeTo);

            // User Profile Count Filter
            AddFilterExpression(
                listOfExpression,
                request.UserContactLimitPercentageRange != null,
                x => ((x.MaximumContacts ?? x.BillRecords
                         .Where(
                             b => ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId) &&
                                  b.PeriodStart < DateTime.UtcNow)
                         .OrderByDescending(br => br.created)
                         .ThenByDescending(br => br.PayAmount)
                         .Select(b => b.SubscriptionPlan.MaximumContact)
                         .FirstOrDefault()) >= 1
                         ? _appDbContext.UserProfiles.Count(
                               u => u.CompanyId == x.Id && u.ActiveStatus == ActiveStatus.Active) /
                           (decimal) (x.MaximumContacts ?? x.BillRecords
                               .Where(
                                   b => ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId) &&
                                        b.PeriodStart < DateTime.UtcNow)
                               .OrderByDescending(br => br.created)
                               .ThenByDescending(br => br.PayAmount)
                               .Select(b => b.SubscriptionPlan.MaximumContact)
                               .FirstOrDefault())
                         : 0.00M) >= request.UserContactLimitPercentageRange!.RangeFrom
                     &&
                     ((x.MaximumContacts ?? x.BillRecords
                         .Where(
                             b => ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId) &&
                                  b.PeriodStart < DateTime.UtcNow)
                         .OrderByDescending(br => br.created)
                         .ThenByDescending(br => br.PayAmount)
                         .Select(b => b.SubscriptionPlan.MaximumContact)
                         .FirstOrDefault()) >= 1
                         ? _appDbContext.UserProfiles.Count(
                               u => u.CompanyId == x.Id && u.ActiveStatus == ActiveStatus.Active) /
                           (decimal) (x.MaximumContacts ?? x.BillRecords
                               .Where(
                                   b => ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId) &&
                                        b.PeriodStart < DateTime.UtcNow)
                               .OrderByDescending(br => br.created)
                               .ThenByDescending(br => br.PayAmount)
                               .Select(b => b.SubscriptionPlan.MaximumContact)
                               .FirstOrDefault())
                         : 0.00M) <= request.UserContactLimitPercentageRange.RangeTo);

            // Company Type
            AddFilterExpression(
                listOfExpression,
                request.CompanyTypes.Count > 0,
                x => request.CompanyTypes.Contains(x.CompanyType));

            // Filter Channel Configs
            listOfExpression.AddRange(
                request.ConnectedChannels.Select(
                    channelName => (Expression<Func<Company, bool>>) (channelName switch
                    {
                        ChannelTypes.Instagram => x => x.InstagramConfigs.Count != 0,
                        ChannelTypes.Facebook => x => x.FacebookConfigs.Count != 0,
                        ChannelTypes.WhatsappTwilio => x => x.WhatsAppConfigs.Count != 0,
                        ChannelTypes.Whatsapp360Dialog => x => x.WhatsApp360DialogConfigs.Count != 0,
                        ChannelTypes.WhatsappCloudApi => x => x.WhatsappCloudApiConfigs.Count != 0,
                        "livechat" => x => x.WebClientSenders.Any(),
                        ChannelTypes.Line => x => x.LineConfigs.Count != 0,
                        ChannelTypes.Sms => x => x.SMSConfigs.Count != 0,
                        ChannelTypes.Telegram => x => x.TelegramConfigs.Count != 0,
                        ChannelTypes.Viber => x => x.ViberConfigs.Count != 0,
                        ChannelTypes.Wechat => x => x.WeChatConfigId != null,
                        ChannelTypes.Email => x => x.EmailConfigId != null,
                        "shopline" => x => x.ShoplineConfigs.Count != 0,
                        "shopify" => x => x.ShopifyConfigs.Count != 0,
                        "stripe" => x => x.StripePaymentConfigs.Count != 0,
                        _ => x => true,
                    })));

            if (listOfExpression.Count > 0)
            {
                var predicate = PredicateBuilder.New<Company>(false);
                predicate = listOfExpression.Aggregate(
                    predicate,
                    (current, expression) =>
                        request.IsOrConditionFilter ? current.Or(expression) : current.And(expression));
                companyQuery = companyQuery.Where(predicate);
            }
        }
        else if (request.IsGetRecentlyCreatedCompany)
        {
            // Get more than 100 companies to handle the case where deduplication is needed
            companyQuery = companyQuery.OrderByDescending(x => x.CreatedAt).Take(200);
        }
        else if (request.IsGetAllCompany)
        {
            companyQuery = request.Sort == "desc"
                ? companyQuery.OrderByDescending(x => x.CreatedAt)
                : companyQuery.OrderBy(x => x.CreatedAt);
        }

        // Sort
        companyQuery = request.Sort == "desc"
            ? companyQuery.OrderByDescending(x => x.CreatedAt)
            : companyQuery.OrderBy(x => x.CreatedAt);

        // Fetch Company
        List<CmsCompanyListItemView> companies;
        if (!request.IsExcludeUserProfileCount)
        {
            companies = await companyQuery.Select(
                x => new CmsCompanyListItemView
                {
                    Id = x.Id,
                    CompanyName = x.CompanyName,
                    HubSpotCompanyObjectId = x.CmsHubSpotCompanyMap.HubSpotCompanyObjectId,
                    ConnectedChannel = new CmsCompanyListConnectedChannelView()
                    {
                        WhatsAppConfigCount = x.WhatsAppConfigs.Count,
                        InstagramConfigCount = x.InstagramConfigs.Count,
                        FacebookConfigCount = x.FacebookConfigs.Count,
                        Whatsapp360DialogConfigCount = x.WhatsApp360DialogConfigs.Count,
                        WhatsappCloudApiConfigCount = x.WhatsappCloudApiConfigs.Count,
                        WebClientSenderCount = x.WebClientSenders.Any() ? 1 : 0,
                        LineConfigCount = x.LineConfigs.Count,
                        SMSConfigCount = x.SMSConfigs.Count,
                        ShoplineConfigCount = x.ShoplineConfigs.Count,
                        ShopifyConfigCount = x.ShopifyConfigs.Count,
                        TelegramConfigCount = x.TelegramConfigs.Count,
                        ViberConfigCount = x.ViberConfigs.Count,
                        WeChatConfigCount = x.WeChatConfig != null ? 1 : 0,
                        EmailConfigCount = x.EmailConfig != null ? 1 : 0,
                        StripePaymentConfigCount = x.StripePaymentConfigs.Count,
                    },
                    UserProfileCount = x.UserProfiles.Count(u => u.ActiveStatus == ActiveStatus.Active),
                    StaffCount = x.Staffs.Count(u => u.Id != 1),
                    BillRecords = x.BillRecords
                        .Where(b => b.PeriodStart < DateTime.UtcNow && b.Status != BillStatus.Inactive)
                        .OrderByDescending(br => br.created)
                        .ToList(),
                    CreateAt = x.CreatedAt,
                    LastLoginAt =
                        x.Staffs.Count > 0
                            ? x.Staffs.Where(s => s.Id != 1).Max(s => s.Identity.LastLoginAt)
                            : x.CreatedAt,
                    CompanyOwnerName = x.CmsCompanyOwner.DisplayName,
                    ActivationOwnerName = x.CmsActivationOwner.DisplayName,
                    CsOwnerId = x.CmsCsOwnerId,
                    CompanyCountry = x.CompanyCountry,
                    CmsLeadSource = x.CmsLeadSource,
                    CmsCompanyIndustry = x.CmsCompanyIndustry,
                    HubSpotIndustry = x.CmsCompanyAdditionalInfo.HubSpotCompanyIndustry,
                    IsDeleted = x.IsDeleted,
                    CompanyType = x.CompanyType,
                    NPSScore = x.NPSScore,
                    CmsCompanyAdditionalInfo = x.CmsCompanyAdditionalInfo,
                    TimeZoneInfoId = x.TimeZoneInfoId
                }).ToListAsync();
        }
        else
        {
            companies = await companyQuery.Select(
                x => new CmsCompanyListItemView
                {
                    Id = x.Id,
                    CompanyName = x.CompanyName,
                    HubSpotCompanyObjectId = x.CmsHubSpotCompanyMap.HubSpotCompanyObjectId ?? null,
                    ConnectedChannel = new CmsCompanyListConnectedChannelView()
                    {
                        WhatsAppConfigCount = x.WhatsAppConfigs.Count,
                        InstagramConfigCount = x.InstagramConfigs.Count,
                        FacebookConfigCount = x.FacebookConfigs.Count,
                        Whatsapp360DialogConfigCount = x.WhatsApp360DialogConfigs.Count,
                        WhatsappCloudApiConfigCount = x.WhatsappCloudApiConfigs.Count,
                        WebClientSenderCount = x.WebClientSenders.Any() ? 1 : 0,
                        LineConfigCount = x.LineConfigs.Count,
                        SMSConfigCount = x.SMSConfigs.Count,
                        ShoplineConfigCount = x.ShoplineConfigs.Count,
                        ShopifyConfigCount = x.ShopifyConfigs.Count,
                        TelegramConfigCount = x.TelegramConfigs.Count,
                        ViberConfigCount = x.ViberConfigs.Count,
                        WeChatConfigCount = x.WeChatConfig != null ? 1 : 0,
                        EmailConfigCount = x.EmailConfig != null ? 1 : 0,
                        StripePaymentConfigCount = x.StripePaymentConfigs.Count,
                    },
                    StaffCount = x.Staffs.Count(u => u.Id != 1),
                    BillRecords = x.BillRecords
                        .Where(b => b.PeriodStart < DateTime.UtcNow && b.Status != BillStatus.Inactive)
                        .OrderByDescending(br => br.created)
                        .ToList(),
                    CreateAt = x.CreatedAt,
                    LastLoginAt =
                        x.Staffs.Count > 0
                            ? x.Staffs.Where(s => s.Id != 1).Max(s => s.Identity.LastLoginAt)
                            : x.CreatedAt,
                    CompanyOwnerName = x.CmsCompanyOwner.DisplayName,
                    ActivationOwnerName = x.CmsActivationOwner.DisplayName,
                    CsOwnerId = x.CmsCsOwnerId,
                    CompanyCountry = x.CompanyCountry,
                    CmsLeadSource = x.CmsLeadSource,
                    CmsCompanyIndustry = x.CmsCompanyIndustry,
                    HubSpotIndustry = x.CmsCompanyAdditionalInfo.HubSpotCompanyIndustry,
                    IsDeleted = x.IsDeleted,
                    CompanyType = x.CompanyType,
                    NPSScore = x.NPSScore,
                    CmsCompanyAdditionalInfo = x.CmsCompanyAdditionalInfo,
                    TimeZoneInfoId = x.TimeZoneInfoId
                }).ToListAsync();
        }


        // Post Filter
        // Filter None Channel Configs
        if (request.ConnectedChannels.Contains("none"))
        {
            companies = companies
                .Where(
                    x => x.ConnectedChannel.WhatsAppConfigCount == 0 &&
                         x.ConnectedChannel.InstagramConfigCount == 0 &&
                         x.ConnectedChannel.FacebookConfigCount == 0 &&
                         x.ConnectedChannel.WhatsappChatAPIConfigCount == 0 &&
                         x.ConnectedChannel.Whatsapp360DialogConfigCount == 0 &&
                         x.ConnectedChannel.WebClientSenderCount == 0 &&
                         x.ConnectedChannel.LineConfigCount == 0 &&
                         x.ConnectedChannel.SMSConfigCount == 0 &&
                         x.ConnectedChannel.ShoplineConfigCount == 0 &&
                         x.ConnectedChannel.ShopifyConfigCount == 0 &&
                         x.ConnectedChannel.TelegramConfigCount == 0 &&
                         x.ConnectedChannel.ViberConfigCount == 0 &&
                         x.ConnectedChannel.WeChatConfigCount == 0 &&
                         x.ConnectedChannel.EmailConfigCount == 0)
                .ToList();
        }

        // Filter Any Channel Configs
        else if (request.ConnectedChannels.Contains("any"))
        {
            companies = companies
                .Where(
                    x => x.ConnectedChannel.WhatsAppConfigCount > 0 ||
                         x.ConnectedChannel.InstagramConfigCount > 0 ||
                         x.ConnectedChannel.FacebookConfigCount > 0 ||
                         x.ConnectedChannel.WhatsappChatAPIConfigCount > 0 ||
                         x.ConnectedChannel.Whatsapp360DialogConfigCount > 0 ||
                         x.ConnectedChannel.WebClientSenderCount > 0 ||
                         x.ConnectedChannel.LineConfigCount > 0 ||
                         x.ConnectedChannel.SMSConfigCount > 0 ||
                         x.ConnectedChannel.ShoplineConfigCount > 0 ||
                         x.ConnectedChannel.ShopifyConfigCount > 0 ||
                         x.ConnectedChannel.TelegramConfigCount > 0 ||
                         x.ConnectedChannel.ViberConfigCount > 0 ||
                         x.ConnectedChannel.WeChatConfigCount > 0 ||
                         x.ConnectedChannel.EmailConfigCount > 0)
                .ToList();
        }
        var contractEndDateChanged = false;


        var companiesFlowHubDataLookUp = companiesFlowHubData.ToDictionary(x => x.CompanyId, x => x);
        var companiesIntelligentHubDataLookUp = companiesIntelligentHubData.ToDictionary(x => x.CompanyId, x => x);

        companies.ForEach(
            company =>
            {
                if (request.IsFlowHubDataRequired)
                {
                    if (companiesFlowHubDataLookUp.TryGetValue(company.Id, out var companyFlowHubData))
                    {
                        company.NumOfWorkflows = companyFlowHubData.NumOfWorkflows;
                        company.NumOfActiveWorkflows = companyFlowHubData.NumOfActiveWorkflows;
                        company.NumOfEnrolments = companyFlowHubData.NumOfEnrolments;
                        company.EnrolmentPercentage = Math.Round(companyFlowHubData.EnrolmentPercentage * 100, 2);
                        company.MaximumNumOfWorkflows = companyFlowHubData.MaximumNumOfWorkflows;
                        company.MaximumNumOfActiveWorkflows = companyFlowHubData.MaximumNumOfActiveWorkflows;
                        company.MaximumNumOfMonthlyWorkflowExecutions =
                            companyFlowHubData.MaximumNumOfMonthlyWorkflowExecutions;
                        company.MaximumNumOfNodesPerWorkflow = companyFlowHubData.MaximumNumOfNodesPerWorkflow;
                    }
                    else
                    {
                        company.NumOfWorkflows = 0;
                        company.NumOfActiveWorkflows = 0;
                        company.NumOfEnrolments = 0;
                        company.EnrolmentPercentage = 0.00M;
                        company.MaximumNumOfWorkflows = 0;
                        company.MaximumNumOfActiveWorkflows = 0;
                        company.MaximumNumOfMonthlyWorkflowExecutions = 0;
                        company.MaximumNumOfNodesPerWorkflow = 0;
                    }

                    company.IsPurchasedFlowEnrolmentAddOn = company.AddOnPlanIds != null && company.AddOnPlanIds.Exists(
                        x =>
                            SubscriptionPlansId.PurchasableFlowBuilderFlowEnrolmentsAddOns.Contains(x));
                    // Flow Enrolment Add On MRR
                    company.FlowEnrolmentAddOnMrr =
                        _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                            company.BillRecords.Where(x =>
                                    SubscriptionPlansId.PurchasableFlowBuilderFlowEnrolmentsAddOns.Contains(
                                        x.SubscriptionPlanId))
                                .ToList(),
                            company.TimeZoneInfoId);
                }

                if (request.IsIntelligentHubDataRequired)
                {
                    if (companiesIntelligentHubDataLookUp.TryGetValue(company.Id, out var companyIntelligentHubData))
                    {
                        company.TotalAiFeaturesUsage = companyIntelligentHubData.TotalAiFeaturesUsage;
                        company.TotalAiFeaturesUsageLimit = companyIntelligentHubData.TotalAiFeaturesUsageLimit;
                        company.TotalAiFeaturesUsagePercentage =
                            Math.Round(companyIntelligentHubData.TotalAiFeaturesUsagePercentage * 100, 2);
                        company.TotalAgentFeaturesUsage = companyIntelligentHubData.TotalAgentFeaturesUsage;
                        company.TotalAgentFeaturesUsageLimit = companyIntelligentHubData.TotalAgentFeaturesUsageLimit;
                        company.TotalAgentFeaturesUsagePercentage =
                            Math.Round(companyIntelligentHubData.TotalAgentFeaturesUsagePercentage * 100, 2);
                    }
                    else
                    {
                        company.TotalAiFeaturesUsage = 0;
                        company.TotalAiFeaturesUsageLimit = 0;
                        company.TotalAiFeaturesUsagePercentage = 0.00M;
                        company.TotalAgentFeaturesUsage = 0;
                        company.TotalAgentFeaturesUsageLimit = 0;
                        company.TotalAgentFeaturesUsagePercentage = 0.00M;
                    }
                }

                // Get CsOwnerName
                if (!string.IsNullOrWhiteSpace(company.CsOwnerId))
                {
                    var cmsCsOwner = _appDbContext.Users.FirstOrDefault(x => x.Id == company.CsOwnerId);

                    if (cmsCsOwner != null)
                    {
                        company.CsOwnerName = cmsCsOwner.DisplayName;
                    }
                }

                // Get Current Subscription
                var currentSubscription = company.BillRecords
                    .OrderByDescending(x => x.created)
                    .ThenByDescending(x => x.PayAmount)
                    .FirstOrDefault(
                        x =>
                            x.Status != BillStatus.Inactive
                            && x.Status != BillStatus.Terminated
                            && ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                            && x.PeriodStart <= DateTime.UtcNow
                            && x.PeriodEnd > DateTime.UtcNow);


                if (currentSubscription == null)
                {
                    return;
                }

                var currentActivePaidSubscription = company.BillRecords
                    .OrderByDescending(x => x.created)
                    .ThenByDescending(x => x.PayAmount)
                    .FirstOrDefault(
                        x =>
                            x.Status == BillStatus.Active
                            && ValidSubscriptionPlan.PaidPlan.Contains(x.SubscriptionPlanId)
                            && x.PeriodStart <= DateTime.UtcNow
                            && x.PeriodEnd > DateTime.UtcNow);

                if (company.CmsCompanyAdditionalInfo != null &&
                    currentActivePaidSubscription != null &&
                    company.CmsCompanyAdditionalInfo.ContractEndDate != currentActivePaidSubscription.PeriodEnd)
                {
                    // Attach the entity if it's not already tracked
                    var cmsCompanyAdditionalInfo = company.CmsCompanyAdditionalInfo;
                    var isWithinGracePeriod = false;
                    if ((company.CmsCompanyAdditionalInfo.ChurnReason ?? "").Contains("to be renewed", StringComparison.OrdinalIgnoreCase))
                    {
                        var gracePeriodStart = currentActivePaidSubscription.metadata.TryGetValue("grace_period_start", out string gracePeriodStartString) ? DateTime.Parse(gracePeriodStartString) : DateTime.UtcNow;
                        var gracePeriodEnd = currentActivePaidSubscription.metadata.TryGetValue("grace_period_end", out string gracePeriodEndString) ? DateTime.Parse(gracePeriodEndString) : DateTime.UtcNow;
                        isWithinGracePeriod = DateTime.UtcNow >= gracePeriodStart && DateTime.UtcNow <= gracePeriodEnd;
                    }
                    company.IsWithinGracePeriod = isWithinGracePeriod;

                    _appDbContext.Attach(cmsCompanyAdditionalInfo);

                    // set default ContractEndDate to CmsCompanyAdditionInfo
                    company.CmsCompanyAdditionalInfo.ContractEndDate = currentActivePaidSubscription.PeriodEnd;
                    contractEndDateChanged = true;
                    cmsCompanyAdditionalInfo.UpdatedAt = DateTime.UtcNow;
                    company.CmsCompanyAdditionalInfo.UpdatedAt = DateTime.UtcNow;

                    _appDbContext.Entry(cmsCompanyAdditionalInfo).State = EntityState.Modified;
                }
                else if (company.CmsCompanyAdditionalInfo != null &&
                         company.CmsCompanyAdditionalInfo.ContractEndDate != null &&
                         currentActivePaidSubscription == null)
                {
                    // Set the contractEndDate to null to rectify the previously overwritten invalid value.
                    var cmsCompanyAdditionalInfo = company.CmsCompanyAdditionalInfo;
                    _appDbContext.Attach(cmsCompanyAdditionalInfo);

                    company.CmsCompanyAdditionalInfo.ContractEndDate = null;
                    contractEndDateChanged = true;

                    cmsCompanyAdditionalInfo.UpdatedAt = DateTime.UtcNow;
                    company.CmsCompanyAdditionalInfo.UpdatedAt = DateTime.UtcNow;

                    _appDbContext.Entry(cmsCompanyAdditionalInfo).State = EntityState.Modified;
                }

                company.SubscriptionPlanId = currentSubscription.SubscriptionPlanId;
                company.AddOnPlanIds = company.BillRecords
                    .Where(
                        x => ValidSubscriptionPlan.AddOn.Contains(x.SubscriptionPlanId) &&
                             x.Status != BillStatus.Terminated &&
                             x.PeriodEnd > DateTime.UtcNow)
                    .Select(x => x.SubscriptionPlanId)
                    .Distinct()
                    .ToList();

                if (currentSubscription.CmsSalesPaymentRecords.Any(x => x.PaymentMethod == PaymentMethod.Bank))
                {
                    company.IsBankTransfer = true;
                }

                if (request.IsGetBankTransferEndDay && company.IsBankTransfer)
                {
                    company.SubscriptionPlanEndDay = currentSubscription.PeriodEnd;
                }
                else if (request.IsGetStripeCancelledCompany)
                {
                    company.IsStripePlanCancelled = currentSubscription.amount_paid > 0 &&
                                                    currentSubscription.Status == BillStatus.Canceled;

                    company.SubscriptionPlanEndDay = currentSubscription.PeriodEnd;
                }
                else
                {
                    company.SubscriptionPlanEndDay = null;
                }

                // MRR
                company.MonthlyRecurringRevenue =
                    _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                        company.BillRecords,
                        company.TimeZoneInfoId);
            });

        if (contractEndDateChanged)
        {
            await _appDbContext.SaveChangesAsync();
        }

        if (request.MonthlyRecurringRevenueRange != null)
        {
            companies = companies.Where(
                c => c.MonthlyRecurringRevenue >= request.MonthlyRecurringRevenueRange.RangeFrom &&
                     c.MonthlyRecurringRevenue <= request.MonthlyRecurringRevenueRange.RangeTo).ToList();
        }

        if (request.IsFlowHubDataRequired && request.FlowEnrolmentAddOnMrrRange != null)
        {
            companies = companies.Where(
                c => c.FlowEnrolmentAddOnMrr >= request.FlowEnrolmentAddOnMrrRange.RangeFrom &&
                     c.FlowEnrolmentAddOnMrr <= request.FlowEnrolmentAddOnMrrRange.RangeTo).ToList();
        }

        if (request.IsGetBankTransferEndDay || request.IsGetStripeCancelledCompany)
        {
            companies = companies.OrderBy(c => c.SubscriptionPlanEndDay).ToList();
        }

        if (request.IsGetStripeCancelledCompany)
        {
            companies = companies.Where(x => x.IsStripePlanCancelled).ToList();
        }

        // Deduplication of companies
        var actualCompanies = new List<CmsCompanyListItemView>();

        if (request.IsDeduplicationRequired)
        {
            var serverLocation = _configuration["SF_REGION"] ?? LocationNames.EastAsia;

            var deduplicationCompanyIdServerLocationLookup =
                await _companiesService.GetDeduplicationCompanyIdServerLocationLookup(serverLocation);

            actualCompanies.AddRange(
                serverLocation == LocationNames.EastAsia
                    ? companies.Where(x => !deduplicationCompanyIdServerLocationLookup.ContainsKey(x.Id))
                    : companies.Where(x => deduplicationCompanyIdServerLocationLookup.ContainsKey(x.Id)));
        }
        else
        {
            actualCompanies = companies;
        }

        // Get 100 recently created companies
        if (request.IsGetRecentlyCreatedCompany)
        {
            actualCompanies = actualCompanies.Take(100).ToList();
        }

        result.Companies = actualCompanies;

        if (request.IsExcludeUserProfileCount && request.IsGetAllCompany)
        {
            await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                CmsCacheKeyHelper.GetAllCompaniesKey,
                result,
                TimeSpan.FromHours(1),
                _jsonSerializerSettings
            );
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Company id and name pairs.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCompanyIdNamePairResponse>> GetCompanyIdNamePair(
        [FromBody]
        GetCompanyIdNamePairsRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsUser
                }) == null)
        {
            return Unauthorized();
        }

        var companyNames = await _appDbContext.CompanyCompanies
            .Where(x => request.CompanyIds.Contains(x.Id))
            .Select(
                x => new CompanyIdNamePair
                {
                    CompanyId = x.Id,
                    CompanyName = x.CompanyName
                })
            .ToListAsync();

        return Ok(
            new GetCompanyIdNamePairResponse
            {
                CompanyIdsNamePairs = companyNames
            });
    }

    /// <summary>
    /// Get Add On Free Trial Data.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<List<CmsPlanDistributionDto>>> GetAddOnFreeTrialData()
    {
        var result = await _internalAnalyticService.GetCmsAddOnFreeTrialData();
        return Ok(result);
    }

    /// <summary>
    /// Update Company Type.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult> UpdateCompanyType([FromBody] UpdateCompanyTypeRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        if (request.CompanyType is CompanyType.Reseller or CompanyType.ResellerClient)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Company Type."
                });
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);

        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company Not Found"
                });
        }

        if (company.CompanyType is CompanyType.Reseller or CompanyType.ResellerClient)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Cannot set Company Type to Reseller type company"
                });
        }

        company.CompanyType = request.CompanyType;

        await _appDbContext.SaveChangesAsync();

        return Ok(
            new ResponseViewModel
            {
                message = "Company Type Updated."
            });
    }

    /// <summary>
    /// Get Company Details.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetAllCompanyDetailsResponse>> GetCompany(
        [FromBody]
        GetCompanyDetailsRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var result = new GetAllCompanyDetailsResponse();

        if (!await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == request.CompanyId))
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company Not Found"
                });
        }

        try
        {
            result = await _companyDetailsService.GetAllCompanyDetailsAsync(request.CompanyId);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error on get company detail, companyId: {CompanyId}", request.CompanyId);

            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Error on get company detail."
                }
            );
        }

        return Ok(result);
    }

    /// <summary>
    /// Get Company Details.
    /// </summary>
    /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<bool>> CheckIsCompanyExist(
        [FromBody]
        GetCompanyDetailsRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        return Ok(await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == request.CompanyId));
    }

    /// <summary>
    /// Get Staff Data List for email.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<CmsCompanyStaffListResponse>> GetCmsCompanyStaffList(
        [FromBody]
        GetStaffListRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var companyData = await _appDbContext.CompanyCompanies
            .AsSplitQuery()
            .AsNoTracking()
            .Include(x => x.BillRecords)
            .Include(x => x.Staffs)
            .ThenInclude(x => x.Identity)
            .Where(x => request.CompanyIds.Contains(x.Id))
            .Select(
                x => new
                {
                    x.Id,
                    x.CompanyName,
                    Staffs = x.Staffs,
                    BillRecords = x.BillRecords
                        .Where(
                            b => ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId) &&
                                 b.PeriodStart < DateTime.UtcNow)
                        .OrderByDescending(br => br.created)
                        .ToList()
                })
            .ToListAsync();

        var companyStaffData = new List<CmsCompanyStaffData>();

        foreach (var company in companyData)
        {
            var isOwner = true;

            foreach (var staff in company.Staffs.OrderBy(x => x.Order).ThenBy(x => x.Id))
            {
                var user = new CmsCompanyStaffData
                {
                    CompanyId = company.Id,
                    CompanyName = company.CompanyName,
                    SubscriptionPlanId = company.BillRecords.Where(
                            b => ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId) &&
                                 b.PeriodStart < DateTime.UtcNow)
                        .OrderByDescending(br => br.created)
                        .ThenByDescending(br => br.PayAmount)
                        .FirstOrDefault()?.SubscriptionPlanId,
                    StaffId = staff.Id,
                    UserId = staff.IdentityId,
                    Email = staff.Identity.Email,
                    PhoneNumber = staff.Identity.PhoneNumber,
                    FirstName = staff.Identity.FirstName,
                    LastName = staff.Identity.LastName,
                    DisplayName = staff.Identity.DisplayName,
                    UserRole = staff.RoleType.ToString(),
                    IsOwner = isOwner ? "Yes" : "No", // first user is owner
                    CreatedAt = staff.Identity.CreatedAt,
                    LastLoginAt = staff.Identity.LastLoginAt
                };

                companyStaffData.Add(user);
                isOwner = false;
            }
        }

        // var ms = new MemoryStream();
        //
        // await using (var writer = new StreamWriter(ms, leaveOpen: true))
        // {
        //     await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
        //     {
        //         await csv.WriteRecordsAsync(companyStaffData);
        //     }
        // }
        //
        // return File(ms.ToArray(), "text/csv", $"GetStaffList-{DateTime.UtcNow.AddHours(8):yyyyMMdd}.csv");
        return Ok(
            new CmsCompanyStaffListResponse
            {
                CompanyStaffDataList = companyStaffData
            });
    }

    /// <summary>
    /// Update Company Detail (Name).
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdateCompanyDetail(
        [FromBody]
        UpdateCompanyDetailRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }


        var company = await _appDbContext.CompanyCompanies.Include(x => x.CmsCompanyAdditionalInfo)
            .FirstOrDefaultAsync(x => x.Id == request.CompanyId);

        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company Not Found"
                });
        }

        company.CmsCompanyAdditionalInfo ??= new CmsCompanyAdditionalInfo
        {
            CompanyId = request.CompanyId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        if (!string.IsNullOrWhiteSpace(request.CompanyName))
        {
            company.CompanyName = request.CompanyName.Trim();
        }

        if (!string.IsNullOrWhiteSpace(request.LegalTerms))
        {
            company.CmsCompanyAdditionalInfo.LegalTerms = request.LegalTerms;
        }

        // Parse the date string preserving the UTC time
        if (request.ContractEndDate == "remove")
        {
            company.CmsCompanyAdditionalInfo.ManualContractEndDate = null;
        }
        else if (!string.IsNullOrWhiteSpace(request.ContractEndDate))
        {
            if (!DateTime.TryParse(
                    request.ContractEndDate,
                    null,
                    DateTimeStyles.AdjustToUniversal,
                    out var parsedDate))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Invalid date format."
                    });
            }

            company.CmsCompanyAdditionalInfo.ManualContractEndDate = parsedDate;
        }


        await _appDbContext.SaveChangesAsync();
        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel()
            {
                message = "Company Detail Updated."
            });
    }

    /// <summary>
    /// Delete Company Detail Cache.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetAllCompanyDetailsResponse>> DeleteCompanyDetailCache(
        [FromBody]
        DeleteCompanyDetailCacheRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);
        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId, "AssignmentRuleInfo");

        return Ok(
            new ResponseViewModel()
            {
                message = "Cache deleted"
            });
    }

    /// <summary>
    /// Delete Company.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult> DeleteCompany([FromBody] DeleteCompanyRequest request)
    {
        // if (!ModelState.IsValid) return BadRequest(new ResponseViewModel { message = "Invalid Form" });
        var internalUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });
        if (internalUser == null)
        {
            return Unauthorized();
        }

        if (!await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == request.CompanyId))
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company Not Found"
                });
        }

        _logger.LogWarning(
            "InternalCms - removing company data [{CompanyId}] [{InternalUserId} {InternalUserDisplayName}]",
            request.CompanyId,
            internalUser.Id,
            internalUser.DisplayName);

        if (request.IsDeleteInBackground)
        {
            BackgroundJob.Enqueue<ICoreService>(x => x.RemoveCompanyData(request.CompanyId));
        }
        else
        {
            await _coreService.RemoveCompanyData(request.CompanyId);
        }

        return Ok();
    }

    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult> SyncAIUsageLimitForFreemiumOrStartupCompanyAccount()
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var freePlanSubscriptionIds = new string[]
        {
            "sleekflow_freemium",
            "sleekflow_v10_startup"
        };

        var freemiumCompanies = await _appDbContext.CompanyBillRecords
            .Where(
                x =>
                    x.Status != BillStatus.Inactive &&
                    x.Status != BillStatus.Terminated &&
                    freePlanSubscriptionIds.Contains(x.SubscriptionPlanId) &&
                    x.PeriodStart <= DateTime.UtcNow &&
                    DateTime.UtcNow <= x.PeriodEnd)
            .OrderByDescending(x => x.created)
            .ToListAsync();

        foreach (var company in freemiumCompanies)
        {
            BackgroundJob.Enqueue<IIntelligentHubService>(
                x => x.RefreshIntelligentHubConfigAsync(company.CompanyId));
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "Synced AI Usage Limit for all freemium and startup company."
            });
    }

    // <summary>
    /// Sync AI Usage Limit to specific company by running background job.
    /// </summary>
    /// <returns>A <see cref="Task"/>representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult> SyncAIUsageLimitToCompanyAccount(
        [FromBody]
        SyncAiUsageLimitToCompanyAccountRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);

        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company Not Found"
                });
        }

        BackgroundJob.Enqueue<IIntelligentHubService>(
            x => x.RefreshIntelligentHubConfigAsync(company.Id));

        return Ok(
            new ResponseViewModel()
            {
                message = $"Synced AI Usage Limit for {company.CompanyName}"
            });
    }

    /// <summary>
    /// Activate Company Qr Code.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult> ActivateCompanyQrCode([FromBody] ActivateCompanyQrCodeRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var company = await _appDbContext.CompanyCompanies.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == request.CompanyId);

        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company Not Found"
                });
        }

        if (company.IsQRCodeMappingEnabled)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "This Company have enabled QR code already!"
                });
        }

        await _companyService.ActivateQRCodeMapping(request.CompanyId);

        return Ok(
            new ResponseViewModel()
            {
                message = $"QR code feature enabled for {company.CompanyName}"
            });
    }

    /// <summary>
    /// Remove Sandbox Config.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async ValueTask<ActionResult> DeleteSandbox([FromQuery] DeleteSandboxRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        if (!await _appDbContext.CompanySandboxes
                .AnyAsync(x => x.CompanyId == request.CompanyId))
        {
            return Ok(
                new ResponseViewModel()
                {
                    message = $"Sandbox not existed."
                });
        }

        await _companyService.DeleteSandbox(request.CompanyId);

        return Ok(
            new ResponseViewModel()
            {
                message = $"Removed Sandbox config."
            });
    }

    /// <summary>
    /// Re Enqueue Background Task.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async ValueTask<ActionResult<BackgroundTaskViewModel>> ReEnqueueBackgroundTask(
        [FromQuery]
        ReEnqueueBackgroundTaskRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsUser
                }) == null)
        {
            return Unauthorized();
        }

        var backgroundTask = await _appDbContext.BackgroundTasks
            .AsNoTracking()
            .Where(x => x.Id == request.BackgroundTaskId && x.CompanyId == request.CompanyId)
            .FirstOrDefaultAsync();

        if (backgroundTask == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Background Task not found."
                });
        }

        if (backgroundTask.IsCompleted)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Background Task has completed."
                });
        }

        BackgroundJob.Enqueue<IBackgroundTaskService>(x => x.Handle(backgroundTask.Id, CancellationToken.None));

        return Ok(backgroundTask.MapToResultViewModel());
    }

    /// <summary>
    ///     Import Companies Industry By Upload a Csv.
    /// </summary>
    /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ImportStaffResponse>> ImportCompaniesIndustryByCsv(
        [FromForm]
        ImportSpreadsheetViewModel request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var importSpreadsheet = SerializeCSV(request);
        importSpreadsheet.headers.Add(
            new ImportHeader
            {
                HeaderName = "result",
                IsValid = false,
                CsvFileColumnNumber = importSpreadsheet.headers.Count,
                importAction = ImportAction.Ignored
            });

        // File checking. Make sure all data lines is not out of range.
        for (var i = 0; i < importSpreadsheet.records.Count; i++)
        {
            if (importSpreadsheet.records[i].fields.Count != importSpreadsheet.headers.Count - 1)
            {
                _logger.LogCritical(
                    "[ImportCompanyStaffsByCsv] Out of Range exception at line {line}. " +
                    "header count: {importHeaderCount}, data count: {fieldsCount}",
                    i + 2,
                    importSpreadsheet.headers.Count,
                    importSpreadsheet.records[i].fields.Count);
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"Out of Range exception at line {i + 2}. Please check your csv file."
                    });
            }
        }

        foreach (var record in importSpreadsheet.records)
        {
            try
            {
                var csvCompanyId =
                    record.fields[importSpreadsheet.headers.FindIndex(
                        h => h.HeaderName.ToLower() == "company id")];
                var csvIndustry =
                    record.fields[importSpreadsheet.headers.FindIndex(h => h.HeaderName.ToLower() == "industry")];
            }
            catch (Exception err)
            {
                _logger.LogCritical("[ImportCompanyStaffsByCsv] Missing required field [Username]");
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Missing required field [Username]."
                    });
            }

            try
            {
                var importObject = new ImportUserProfileObject(importSpreadsheet.headers, record.fields);

                var companyId = importObject.GetValueFromList("Company Id");
                var industry = importObject.GetValueFromList("Industry");

                var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == companyId);

                if (company == null)
                {
                    record.fields.Add("Failed: No company found");
                    continue;
                }

                company.CmsCompanyIndustry = industry;

                await _appDbContext.SaveChangesAsync();

                record.fields.Add("Completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ImportStaffCsv] " + ex.Message);
                record.fields.Add("Failed: " + ex.Message);
            }
        }

        var importSummary = ExportResult(importSpreadsheet);
        _logger.LogInformation(
            $"[ImportStaffCsv] Import result:\n{JsonConvert.SerializeObject(importSummary, Formatting.Indented)}");

        return Ok(
            new ImportStaffResponse
            {
                Details = importSpreadsheet,
                Summary = importSummary
            });
    }

    #region Company Lead Sources

    /// <summary>
    /// Set Company Lead Sources.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> SetCmsCompanyRemark(
        [FromBody]
        SetCmsCompanyRemarkRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);

        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company Not Found."
                });
        }

        company.CmsRemark = request.Remark;

        await _appDbContext.SaveChangesAsync();

        return Ok(
            new ResponseViewModel()
            {
                message = "Company Remark Updated."
            });
    }

    /// <summary>
    /// Set Company Lead Sources.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> SetCompanyLeadSource(
        [FromBody]
        SetCompanyLeadSourceRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);

        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company Not Found."
                });
        }

        company.CmsLeadSource = request.LeadSource;

        if (request.LeadSource == "remove")
        {
            company.CmsLeadSource = null;
        }
        else
        {
            company.CmsLeadSource = request.LeadSource;
        }

        await _appDbContext.SaveChangesAsync();

        return Ok(
            new ResponseViewModel()
            {
                message = "Company Lead Source Updated."
            });
    }

    /// <summary>
    /// Set Company Industry.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> SetCompanyIndustry(
        [FromBody]
        SetCompanyIndustryRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);

        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company Not Found."
                });
        }

        if (request.Industry == "remove")
        {
            company.CmsCompanyIndustry = null;
        }
        else
        {
            company.CmsCompanyIndustry = request.Industry;
        }

        await _appDbContext.SaveChangesAsync();

        return Ok(
            new ResponseViewModel()
            {
                message = "Company Industry Updated."
            });
    }

    #endregion

    #region Company Plan

    /// <summary>
    ///     Get Expiring Company Subscription Plan.
    /// </summary>
    /// <returns>A <see cref="Task" /> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<GetExpiringCompanySubscriptionPlanResponse>> GetExpiringCompanySubscriptionPlan(
        [FromBody]
        GetExpiringCompanySubscriptionPlanRequest request)
    {
        var cacheKey = CmsCacheKeyHelper.GetExpiringCompanySubscriptionPlan(new DateOnly(), request.OwnerIds);
        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var companies = await _appDbContext.CompanyCompanies
            .AsSplitQuery()
            .AsNoTracking()
            .Include(company => company.CmsCompanyOwner)
            .Include(company => company.CmsActivationOwner)
            .Include(company => company.CmsHubSpotCompanyMap)
            .Include(company => company.Staffs)
            .ThenInclude(staff => staff.Identity)
            .Where(x => request.OwnerIds.Contains(x.CmsCompanyOwnerId))
            .ToListAsync();

        // Deduplication logic
        var serverLocation = _configuration["SF_REGION"] ?? LocationNames.EastAsia;
        var deduplicationCompanyIdServerLocationLookup =
            await _companiesService.GetDeduplicationCompanyIdServerLocationLookup(serverLocation);

        companies = companies
            .Where(x => serverLocation == LocationNames.EastAsia
                ? !deduplicationCompanyIdServerLocationLookup.ContainsKey(x.Id)
                : deduplicationCompanyIdServerLocationLookup.ContainsKey(x.Id))
            .ToList();

        var result = new List<ExpiringCompanySubscriptionPlan>();
        foreach (var company in companies)
        {
            var expiringPlans = await _appDbContext.CompanyBillRecords
                .AsNoTracking()
                .Include(x => x.SubscriptionPlan)
                .Where(
                    x => x.CompanyId == company.Id &&
                         ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId) &&
                         x.PeriodEnd > DateTime.UtcNow &&
                         x.PeriodEnd < DateTime.UtcNow.AddMonths(3) &&
                         x.Status == BillStatus.Active)
                .OrderByDescending(br => br.created)
                .Select(
                    x => new ExpiringCompanySubscriptionPlan
                    {
                        CompanyId = company.Id,
                        CompanyName = company.CompanyName,
                        PlanId = x.SubscriptionPlanId,
                        PlanName = x.SubscriptionPlan.SubscriptionName,
                        PeriodEnd = x.PeriodEnd,
                        PayAmount = x.PayAmount,
                        StripeId = x.stripeId,
                        CompanyOwnerName = company.CmsCompanyOwner != null ? company.CmsCompanyOwner.DisplayName : null,
                        ActivationOwnerName = company.CmsActivationOwner != null
                            ? company.CmsActivationOwner.DisplayName
                            : null,
                        AccountOwnerName =
                            company.Staffs.Count > 0 ? company.Staffs.FirstOrDefault()!.Identity.DisplayName : null,
                        LastLoginAt = company.Staffs.Count > 0
                            ? company.Staffs.FirstOrDefault()!.Identity.LastLoginAt
                            : null,
                        HubSpotCompanyObjectId = company.CmsHubSpotCompanyMap != null
                            ? company.CmsHubSpotCompanyMap.HubSpotCompanyObjectId
                            : null
                    })
                .ToListAsync();

            result.AddRange(expiringPlans);
        }

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromDays(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Set Company Subscription Plan.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [Obsolete("Replaced by CreateSubscriptionPlan")]
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> SetCompanySubscriptionPlan(
        [FromBody]
        SetCompanySubscriptionPlanRequest request)
    {
        // if (!ModelState.IsValid) return BadRequest(new ResponseViewModel { message = "Invalid Form" });
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans
            .AsNoTracking()
            .Where(x => x.Id == request.SubscriptionPlanId)
            .FirstOrDefaultAsync();

        if (subscriptionPlan == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Subscription Plan does not exist."
                });
        }

        if (request.PeriodStart >= request.PeriodEnd)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Invalid Subscription date range!"
                });
        }

        if (request.PeriodEnd < DateTime.UtcNow)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "You cannot create a Bill Record that is already expired."
                });
        }

        var company = await _appDbContext.CompanyCompanies
            .Where(x => x.Id == request.CompanyId)
            .Include(x => x.BillRecords)
            .FirstOrDefaultAsync();

        company.IsFreeTrial = false;

        var newBill = new BillRecord()
        {
            SubscriptionPlanId = request.SubscriptionPlanId,
            PayAmount = 0,
            CompanyId = company.Id,
            Status = BillStatus.Active,
            PaymentStatus = PaymentStatus.FreeOfCharge,
            PeriodStart = request.PeriodStart,
            PeriodEnd = request.PeriodEnd,
            quantity = 1,
            UpgradeFromBillRecordId = request.UpgradeFromBillRecordId,
            DowngradeFromBillRecordId = request.DowngradeFromBillRecordId,
            SubscriptionTier = subscriptionPlan.SubscriptionTier,
        };

        var usageCycle = _usageCycleCalculator.GetUsageCycle(newBill.PeriodStart, newBill.PeriodEnd);
        newBill.UsageCycleStart = usageCycle.From;
        newBill.UsageCycleEnd = usageCycle.To;

        company.BillRecords.Add(newBill);

        company.MaximumContacts = null;
        company.MaximumWhAutomatedMessages = null;
        company.MaximumAutomations = null;

        // DEVS-6196 Refresh the Shopify billing record when set Subscription plan on Powerful
        if (ValidSubscriptionPlan.ShopifyIntegrationAddOns.Contains(newBill.SubscriptionPlanId))
        {
            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .Include(sc => sc.BillRecord).FirstOrDefaultAsync(
                    sc =>
                        sc.CompanyId == newBill.CompanyId);

            if (shopifyConfig != null &&
                (shopifyConfig.BillRecord == null ||
                 shopifyConfig.BillRecord.PeriodEnd < newBill.PeriodEnd))
            {
                shopifyConfig.BillRecord = newBill;

                await _appDbContext.SaveChangesAsync();
            }
        }

        await _appDbContext.CompanyAPIKeys
            .Where(x => x.CompanyId == company.Id)
            .ExecuteUpdateAsync(
                key =>
                    key
                        .SetProperty(k => k.CallLimit, subscriptionPlan.MaximumAPICall)
                        .SetProperty(k => k.Calls, 0));

        await _appDbContext.SaveChangesAsync();

        _logger.LogInformation("Subscription Plan: {PlanId} for Company: {Company} Created/Updated Successful", request.SubscriptionPlanId, company.CompanyName);

        if (request.IsTriggerHubSpot)
        {
            var lastPayment = await _appDbContext.CompanyBillRecords
                .Where(
                    x => x.CompanyId == request.CompanyId &&
                         ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId))
                .OrderByDescending(x => x.Id).Include(x => x.SubscriptionPlan).FirstOrDefaultAsync();

            if (ValidSubscriptionPlan.PremiumTier.Contains(request.SubscriptionPlanId) &&
                (ValidSubscriptionPlan.ProTier.Contains(lastPayment?.SubscriptionPlanId) ||
                 ValidSubscriptionPlan.FreePlans.Contains(lastPayment?.SubscriptionPlanId)))
            {
                await _internalHubSpotService.SetCompanyOwnerUpgradeToPremiumPlanFlag(request.CompanyId);
                BackgroundJob.Enqueue<ICoreService>(
                    x => x.SyncCompanyStaffsToSleekFlowContactsBackground(request.CompanyId));
            }

            if (ValidSubscriptionPlan.ProTier.Contains(request.SubscriptionPlanId) &&
                ValidSubscriptionPlan.FreePlans.Contains(lastPayment?.SubscriptionPlanId))
            {
                await _internalHubSpotService.SetCompanyOwnerUpgradeToProPlanFlag(request.CompanyId);
                BackgroundJob.Enqueue<ICoreService>(
                    x => x.SyncCompanyStaffsToSleekFlowContactsBackground(request.CompanyId));
            }

            await _internalHubSpotService.SyncCompany(request.CompanyId);
        }

        var staff = await _userRoleStaffRepository.GetFirstAdmin(request.CompanyId);

        // Feature Flag related
        try
        {
            await _enabledFeaturesService.UpdateDefaultFeatureFlagForCompanyBasedOnSubscriptionTier(
                request.SubscriptionPlanId,
                request.CompanyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating feature flag for company");
        }

        await _companyUsageService.EnableUsageLimitOffsetProfileAsync(request.CompanyId, true);

        BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(request.CompanyId, null));
        BackgroundJob.Enqueue<IIntelligentHubService>(x => x.RefreshIntelligentHubConfigAsync(request.CompanyId));
        BackgroundJob.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = "Subscription Plan have updated."
            });
    }

    /// <summary>
    /// Add Company Add on Bill Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [Obsolete("Replaced by CreateCompanyAddOn")]
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> AddCompanyAddOn([FromBody] AddCompanyAddOnRequest request)
    {
        // if (!ModelState.IsValid) return BadRequest(new ResponseViewModel { message = "Invalid Form" });
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans
            .AsNoTracking()
            .Where(x => x.Id == request.AddOnPlanId)
            .FirstOrDefaultAsync();

        if (subscriptionPlan == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Subscription Plan does not exist."
                });
        }

        if (ValidSubscriptionPlan.CmsAllAddOn.All(x => x != request.AddOnPlanId))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Add On Plan does not exist."
                });
        }

        if (request.PeriodStart >= request.PeriodEnd)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Invalid Subscription date range!"
                });
        }

        if (request.PeriodEnd < DateTime.UtcNow)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "You cannot create a Bill Record that is already expired."
                });
        }

        var company = await _appDbContext.CompanyCompanies
            .Where(x => x.Id == request.CompanyId)
            .Include(x => x.BillRecords)
            .FirstOrDefaultAsync();

        var newBill = new BillRecord()
        {
            SubscriptionPlanId = subscriptionPlan.Id,
            PayAmount = 0,
            CompanyId = company.Id,
            Status = BillStatus.Active,
            PaymentStatus = PaymentStatus.FreeOfCharge,
            PeriodStart = request.PeriodStart,
            PeriodEnd = request.PeriodEnd,
            quantity = request.Quantity ?? 1,
            UpgradeFromBillRecordId = request.UpgradeFromBillRecordId,
            DowngradeFromBillRecordId = request.DowngradeFromBillRecordId,
            SubscriptionTier = subscriptionPlan.SubscriptionTier,
        };

        var usageCycle = _usageCycleCalculator.GetUsageCycle(newBill.PeriodStart, newBill.PeriodEnd);
        newBill.UsageCycleStart = usageCycle.From;
        newBill.UsageCycleEnd = usageCycle.To;

        company.BillRecords.Add(newBill);

        await _appDbContext.SaveChangesAsync();

        if (request.IsTriggerHubSpot)
        {
            await _internalHubSpotService.SyncCompany(request.CompanyId);
        }

        var staff = await _userRoleStaffRepository.GetFirstAdmin(request.CompanyId);

        _backgroundJobClient.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(request.CompanyId, null));
        _backgroundJobClient.Enqueue<IIntelligentHubService>(x => x.RefreshIntelligentHubConfigAsync(request.CompanyId));
        _backgroundJobClient.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = "Subscription Plan have updated."
            });
    }

    /// <summary>
    /// Set Company Subscription Plan.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [Obsolete("Replaced by CreateSubscriptionPlan")]
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> SetCompanySubscriptionPlanV2(
        [FromBody]
        SetCompanySubscriptionPlanV2Request request)
    {
        if (await GetCurrentValidInternalUser(
                [ApplicationUserRole.InternalCmsSuperUser]) == null)
        {
            return Unauthorized();
        }

        var (company, errorResult) = await GetCompanyWithBillRecordsOrBadRequestAsync(request.CompanyId);
        if (errorResult != null) return errorResult;

        company.IsFreeTrial = false;

        var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans
            .AsNoTracking()
            .Where(x => x.Id == request.SubscriptionPlanId)
            .FirstOrDefaultAsync();

        if (subscriptionPlan == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Subscription Plan does not exist."
                });
        }

        if (request.DurationInMonths < 1)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Duration in months must be at least 1 month."
                });
        }

        var periodEnd = request.PeriodStart.AddMonths(request.DurationInMonths);

        if (request.PeriodStart >= periodEnd)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Subscription date range!"
                });
        }

        if (periodEnd < DateTime.UtcNow)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "You cannot create a Bill Record that is already expired."
                });
        }

        // Adding grace period
        var metadata = new Dictionary<string, string>();
        var actualStart = request.PeriodStart;
        var gracePeriodEnd = periodEnd.AddDays(7);

        metadata["grace_period_start"] = periodEnd.ToString("O");
        metadata["grace_period_end"] = gracePeriodEnd.ToString("O");
        metadata["actual_period_start"] = actualStart.ToString("O");
        metadata["actual_period_end"] = periodEnd.ToString("O");

        var newBill = new BillRecord
        {
            SubscriptionPlanId = request.SubscriptionPlanId,
            PayAmount = 0,
            CompanyId = company.Id,
            Status = BillStatus.Active,
            PaymentStatus = PaymentStatus.FreeOfCharge,
            PeriodStart = request.PeriodStart,
            PeriodEnd = gracePeriodEnd,
            quantity = 1,
            UpgradeFromBillRecordId = request.UpgradeFromBillRecordId,
            DowngradeFromBillRecordId = request.DowngradeFromBillRecordId,
            SubscriptionTier = subscriptionPlan.SubscriptionTier,
            metadata = metadata,
        };

        var usageCycle = _usageCycleCalculator.GetUsageCycle(newBill.PeriodStart, newBill.PeriodEnd);
        newBill.UsageCycleStart = usageCycle.From;
        newBill.UsageCycleEnd = usageCycle.To;

        company.BillRecords.Add(newBill);

        company.MaximumContacts = null;
        company.MaximumWhAutomatedMessages = null;
        company.MaximumAutomations = null;

        // DEVS-6196 Refresh the Shopify billing record when set Subscription plan on Powerful
        if (ValidSubscriptionPlan.ShopifyIntegrationAddOns.Contains(newBill.SubscriptionPlanId))
        {
            var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
                .Include(sc => sc.BillRecord).FirstOrDefaultAsync(sc =>
                    sc.CompanyId == newBill.CompanyId);

            if (shopifyConfig != null &&
                (shopifyConfig.BillRecord == null ||
                 shopifyConfig.BillRecord.PeriodEnd < newBill.PeriodEnd))
            {
                shopifyConfig.BillRecord = newBill;

                await _appDbContext.SaveChangesAsync();
            }
        }

        await _appDbContext.CompanyAPIKeys
            .Where(x => x.CompanyId == company.Id)
            .ExecuteUpdateAsync(key =>
                key
                    .SetProperty(k => k.CallLimit, subscriptionPlan.MaximumAPICall)
                    .SetProperty(k => k.Calls, 0));

        await _appDbContext.SaveChangesAsync();

        _logger.LogInformation(
            "Subscription Plan: {PlanId} for Company: {Company} Created/Updated Successful",
            request.SubscriptionPlanId,
            company.CompanyName);

        if (request.IsTriggerHubSpot)
        {
            var lastPayment = await _appDbContext.CompanyBillRecords
                .Where(x => x.CompanyId == request.CompanyId &&
                            ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId))
                .OrderByDescending(x => x.Id).Include(x => x.SubscriptionPlan).FirstOrDefaultAsync();

            if (ValidSubscriptionPlan.PremiumTier.Contains(request.SubscriptionPlanId) &&
                (ValidSubscriptionPlan.ProTier.Contains(lastPayment?.SubscriptionPlanId) ||
                 ValidSubscriptionPlan.FreePlans.Contains(lastPayment?.SubscriptionPlanId)))
            {
                await _internalHubSpotService.SetCompanyOwnerUpgradeToPremiumPlanFlag(request.CompanyId);
                BackgroundJob.Enqueue<ICoreService>(x =>
                    x.SyncCompanyStaffsToSleekFlowContactsBackground(request.CompanyId));
            }

            if (ValidSubscriptionPlan.ProTier.Contains(request.SubscriptionPlanId) &&
                ValidSubscriptionPlan.FreePlans.Contains(lastPayment?.SubscriptionPlanId))
            {
                await _internalHubSpotService.SetCompanyOwnerUpgradeToProPlanFlag(request.CompanyId);
                BackgroundJob.Enqueue<ICoreService>(x =>
                    x.SyncCompanyStaffsToSleekFlowContactsBackground(request.CompanyId));
            }

            await _internalHubSpotService.SyncCompany(request.CompanyId);
        }

        var staff = await _userRoleStaffRepository.GetFirstAdmin(request.CompanyId);

        // Feature Flag related
        try
        {
            await _enabledFeaturesService.UpdateDefaultFeatureFlagForCompanyBasedOnSubscriptionTier(
                request.SubscriptionPlanId,
                request.CompanyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating feature flag for company");
        }

        await _companyUsageService.EnableUsageLimitOffsetProfileAsync(request.CompanyId, true);

        BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(request.CompanyId, null));
        BackgroundJob.Enqueue<IIntelligentHubService>(x => x.RefreshIntelligentHubConfigAsync(request.CompanyId));
        BackgroundJob.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = "Subscription Plan have updated."
            });
    }

    /// <summary>
    /// Add Company Add on Bill Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [Obsolete("Replaced by CreateCompanyAddOn")]
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> AddCompanyAddOnV2([FromBody] AddCompanyAddOnV2Request request)
    {
        if (await GetCurrentValidInternalUser(
                [ApplicationUserRole.InternalCmsSuperUser]) == null)
        {
            return Unauthorized();
        }

        var (company, errorResult) = await GetCompanyWithBillRecordsOrBadRequestAsync(request.CompanyId);
        if (errorResult != null) return errorResult;

        var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans
            .AsNoTracking()
            .Where(x => x.Id == request.AddOnPlanId)
            .FirstOrDefaultAsync();

        if (subscriptionPlan == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Subscription Plan does not exist."
                });
        }

        if (ValidSubscriptionPlan.CmsAllAddOn.All(x => x != request.AddOnPlanId))
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Add On Plan does not exist."
                });
        }

        // Validate DurationInMonths: must be either -1 or >= 1
        if (request.DurationInMonths != -1 && request.DurationInMonths < 1)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Duration in months is not valid."
                });
        }

        DateTime periodEnd;
        var metadata = new Dictionary<string, string>();

        if (request.DurationInMonths == -1)
        {
            var currentSubscription = await _companyUsageService.GetCompanySubscriptionPlan(request.CompanyId);

            if (currentSubscription == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "No active subscription found."
                    });
            }

            periodEnd = currentSubscription.PeriodEnd;

            if (currentSubscription.metadata != null &&
                currentSubscription.metadata.TryGetValue("actual_period_end", out var actualPeriodEnd))
            {
                metadata = new Dictionary<string, string>
                {
                    {
                        "actual_period_start", request.PeriodStart.ToString("O")
                    },
                    {
                        "actual_period_end", actualPeriodEnd
                    }
                };

                // Only add grace period metadata if the keys exist in currentSubscription.metadata
                if (currentSubscription.metadata.TryGetValue("grace_period_start", out var gracePeriodStart))
                {
                    metadata["grace_period_start"] = gracePeriodStart;
                }

                if (currentSubscription.metadata.TryGetValue("grace_period_end", out var gracePeriodEnd))
                {
                    metadata["grace_period_end"] = gracePeriodEnd;
                }
            }
        }
        else
        {
            periodEnd = request.PeriodStart.AddMonths(request.DurationInMonths);
        }

        if (request.PeriodStart >= periodEnd)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Subscription date range!"
                });
        }

        if (periodEnd < DateTime.UtcNow)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "You cannot create a Bill Record that is already expired."
                });
        }

        // Add grace period if duration is not -1
        if (request.DurationInMonths != -1)
        {
            periodEnd = periodEnd.AddHours(3);
        }

        var newBill = new BillRecord
        {
            SubscriptionPlanId = subscriptionPlan.Id,
            PayAmount = 0,
            CompanyId = company.Id,
            Status = BillStatus.Active,
            PaymentStatus = PaymentStatus.FreeOfCharge,
            PeriodStart = request.PeriodStart,
            PeriodEnd = periodEnd,
            quantity = request.Quantity ?? 1,
            UpgradeFromBillRecordId = request.UpgradeFromBillRecordId,
            DowngradeFromBillRecordId = request.DowngradeFromBillRecordId,
            SubscriptionTier = subscriptionPlan.SubscriptionTier,
            metadata = metadata.Count > 0 ? metadata : null
        };

        var usageCycle = _usageCycleCalculator.GetUsageCycle(newBill.PeriodStart, newBill.PeriodEnd);
        newBill.UsageCycleStart = usageCycle.From;
        newBill.UsageCycleEnd = usageCycle.To;

        company.BillRecords.Add(newBill);

        await _appDbContext.SaveChangesAsync();

        if (request.IsTriggerHubSpot)
        {
            await _internalHubSpotService.SyncCompany(request.CompanyId);
        }

        var staff = await _userRoleStaffRepository.GetFirstAdmin(request.CompanyId);

        _backgroundJobClient.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(request.CompanyId, null));
        _backgroundJobClient.Enqueue<IIntelligentHubService>(x =>
            x.RefreshIntelligentHubConfigAsync(request.CompanyId));
        _backgroundJobClient.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = "Subscription Plan have updated."
            });
    }

    /// <summary>
    /// Update Company Bill Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [Obsolete("Replaced by UpdateSubscriptionPlanBillRecord and UpdateAddOnPlanBillRecord")]
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> UpdateBillRecordV2([FromBody] UpdateBillRecordV2Request request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                [ApplicationUserRole.InternalCmsSuperUser]) == null)
        {
            return Unauthorized();
        }

        var now = DateTime.UtcNow;

        var billRecord = await _appDbContext.CompanyBillRecords
            .FirstOrDefaultAsync(x => x.Id == request.BillRecordId);

        if (billRecord == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Bill Record Not Found."
                });
        }

        var subscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanAsync(billRecord.SubscriptionPlanId);

        if (subscriptionPlan == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Subscription Plan does not exist."
                });
        }

        if (request.PeriodStart.HasValue)
        {
            billRecord.PeriodStart = request.PeriodStart.Value;
        }

        if (request.DurationInMonths.HasValue)
        {
            // Validate DurationInMonths: must be either -1 or >= 1
            if (request.DurationInMonths.Value != -1 && request.DurationInMonths.Value < 1)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "DurationInMonths is not valid."
                    });
            }

            if (request.DurationInMonths.Value != -1)
            {
                billRecord.PeriodEnd = billRecord.PeriodStart.AddMonths(request.DurationInMonths.Value);
            }
            else
            {
                var currentSubscription = await _companyUsageService.GetCompanySubscriptionPlan(request.CompanyId);

                if (currentSubscription == null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "No active subscription found."
                        });
                }

                billRecord.PeriodEnd = currentSubscription.PeriodEnd;

                // Add actual period metadata if current subscription has actual period metadata
                if (currentSubscription.metadata != null &&
                    currentSubscription.metadata.TryGetValue("actual_period_end", out var actualPeriodEnd))
                {
                    var metadata = billRecord.metadata ?? new Dictionary<string, string>();
                    metadata["actual_period_start"] = billRecord.PeriodStart.ToString("O");
                    metadata["actual_period_end"] = actualPeriodEnd;

                    // Only add grace period metadata if the keys exist in currentSubscription.metadata
                    if (currentSubscription.metadata.TryGetValue("grace_period_start", out var gracePeriodStart))
                    {
                        metadata["grace_period_start"] = gracePeriodStart;
                    }

                    if (currentSubscription.metadata.TryGetValue("grace_period_end", out var gracePeriodEnd))
                    {
                        metadata["grace_period_end"] = gracePeriodEnd;
                    }

                    billRecord.metadata = new Dictionary<string, string>(metadata);
                }
            }
        }

        if (!string.IsNullOrWhiteSpace(request.SubscriptionPlanId))
        {
            subscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanAsync(request.SubscriptionPlanId);

            if (subscriptionPlan == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Subscription Plan does not exist."
                    });
            }

            billRecord.SubscriptionPlanId = request.SubscriptionPlanId;
            billRecord.SubscriptionTier = subscriptionPlan.SubscriptionTier;
        }

        if (subscriptionPlan!.PlanTypeCode == PlanTypeCodes.BasePlan)
        {
            // Update metadata
            var metadata = billRecord.metadata ?? new Dictionary<string, string>();

            if (request.PeriodStart.HasValue)
            {
                metadata["actual_period_start"] = billRecord.PeriodStart.ToString("O");
            }

            if (request.DurationInMonths is > 0)
            {
                var gracePeriodEnd = billRecord.PeriodEnd.AddDays(7);

                metadata["grace_period_start"] = billRecord.PeriodEnd.ToString("O");
                metadata["grace_period_end"] = gracePeriodEnd.ToString("O");
                metadata["actual_period_end"] = billRecord.PeriodEnd.ToString("O");

                billRecord.PeriodEnd = gracePeriodEnd;
            }

            billRecord.metadata = new Dictionary<string, string>(metadata);

            // Calculate usage cycle AFTER setting the final PeriodEnd
            var usageCycle = _usageCycleCalculator.GetUsageCycle(billRecord.PeriodStart, billRecord.PeriodEnd);
            billRecord.UsageCycleStart = usageCycle.From;
            billRecord.UsageCycleEnd = usageCycle.To;
        }
        else
        {
            billRecord.UsageCycleStart = null;
            billRecord.UsageCycleEnd = null;

            if (request.DurationInMonths.HasValue && request.DurationInMonths != -1)
            {
                billRecord.PeriodEnd = billRecord.PeriodEnd.AddHours(3);

                // Remove actual period metadata if exist
                if (billRecord.metadata is { Count: > 0 } &&
                    billRecord.metadata.ContainsKey("actual_period_end"))
                {
                    billRecord.metadata.Remove("actual_period_start");
                    billRecord.metadata.Remove("actual_period_end");
                    billRecord.metadata.Remove("grace_period_start");
                    billRecord.metadata.Remove("grace_period_end");

                    billRecord.metadata = new Dictionary<string, string>(billRecord.metadata);
                }
            }
        }

        if (request.Quantity.HasValue)
        {
            billRecord.quantity = request.Quantity.Value;
        }

        if (billRecord.PeriodStart >= billRecord.PeriodEnd)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Invalid Bill Record date range!"
                });
        }

        if (request.UpgradeFromBillRecordId.HasValue)
        {
            if (request.UpgradeFromBillRecordId.Value == -1)
            {
                billRecord.UpgradeFromBillRecordId = null;
            }
            else
            {
                if (await _appDbContext
                        .CompanyBillRecords
                        .AnyAsync(x => x.CompanyId == request.CompanyId && x.Id == request.UpgradeFromBillRecordId))
                {
                    billRecord.UpgradeFromBillRecordId = request.UpgradeFromBillRecordId;
                }
                else
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Upgrade From Bill Record Id not found."
                        });
                }
            }
        }

        if (request.DowngradeFromBillRecordId.HasValue)
        {
            if (request.DowngradeFromBillRecordId.Value == -1)
            {
                billRecord.DowngradeFromBillRecordId = null;
            }
            else
            {
                if (await _appDbContext
                        .CompanyBillRecords
                        .AnyAsync(x => x.CompanyId == request.CompanyId && x.Id == request.DowngradeFromBillRecordId))
                {
                    billRecord.DowngradeFromBillRecordId = request.DowngradeFromBillRecordId;
                }
                else
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Downgrade From Bill Record Id not found."
                        });
                }
            }
        }

        if (billRecord.UpgradeFromBillRecordId.HasValue && billRecord.DowngradeFromBillRecordId.HasValue)
        {
            return Ok(
                new ResponseViewModel
                {
                    message = "Bill Record cannot be both upgraded and downgraded!"
                });
        }

        if (subscriptionPlan.PlanTypeCode == PlanTypeCodes.BasePlan)
        {
            if (billRecord.PeriodStart <= now && now < billRecord.PeriodEnd)
            {
                await _companyUsageService.EnableUsageLimitOffsetProfileAsync(request.CompanyId, true);
            }
        }

        billRecord.UpdatedAt = DateTime.UtcNow;
        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        var staff = await _userRoleStaffRepository.GetFirstAdmin(request.CompanyId);

        // Feature Flag related
        try
        {
            await _enabledFeaturesService.UpdateDefaultFeatureFlagForCompanyBasedOnSubscriptionTier(
                request.SubscriptionPlanId,
                request.CompanyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating feature flag for company");
        }

        BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(billRecord.CompanyId, null));
        BackgroundJob.Enqueue<IIntelligentHubService>(x => x.RefreshIntelligentHubConfigAsync(request.CompanyId));
        BackgroundJob.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

        BackgroundJob.Enqueue<IInternalAnalyticService>(x => x.UpdateCompanyAllTimeRevenueAnalyticInfo(
            new List<string>()
            {
                request.CompanyId
            }));

        return Ok(
            new ResponseViewModel
            {
                message = "Bill Record have updated."
            });
    }

    /// <summary>
    /// Create Subscription Plan in Powerflow.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> CreateSubscriptionPlan(
        [FromBody]
        CreateSubscriptionPlanRequest request)
    {
        if (await GetCurrentValidInternalUser([ApplicationUserRole.InternalCmsSuperUser]) == null)
        {
            return Unauthorized();
        }

        var (company, errorResult) = await GetCompanyWithBillRecordsOrBadRequestAsync(request.CompanyId);
        if (errorResult != null) return errorResult;

        var lastPayment = company.BillRecords
            .Where(x =>
                x.Status != BillStatus.Inactive
                && x.Status != BillStatus.Terminated
                && ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                && x.PeriodStart < DateTime.UtcNow)
            .OrderByDescending(x => x.created)
            .ThenByDescending(x => x.PayAmount)
            .FirstOrDefault();

        company.IsFreeTrial = false;

        var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans
            .AsNoTracking()
            .Where(x => x.Id == request.SubscriptionPlanId)
            .FirstOrDefaultAsync();

        if (subscriptionPlan == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Subscription Plan does not exist."
                });
        }

        var defaultSubscriptionPeriodInMonth = subscriptionPlan.SubscriptionInterval == "yearly" ? 12 : 1;
        var periodEnd = request.PeriodStart.AddMonths(defaultSubscriptionPeriodInMonth);
        var payAmount = 0.00;
        var paymentTerms = CalculatePaymentTerms(
            request.PaymentTerms,
            request.IsIrregularPlan,
            request.PaymentIntervalType,
            subscriptionPlan);
        var noticePeriod = request.NoticePeriod ?? 30;

        if (request.IsIrregularPlan)
        {
            if (request.DurationInMonths is not > 0)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Duration In Months is required for irregular plans."
                    });
            }

            periodEnd = request.PeriodStart.AddMonths(request.DurationInMonths.Value);

            if (!PaymentIntervalType.AllPaymentIntervalTypes.Contains(request.PaymentIntervalType))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Invalid Payment Interval Type."
                    });
            }

            // Filter invalid payment interval type option
            if ((request.DurationInMonths == 1 &&
                 request.PaymentIntervalType is PaymentIntervalType.Monthly or PaymentIntervalType.Custom) ||
                (request.DurationInMonths == 12 &&
                 request.PaymentIntervalType is PaymentIntervalType.Yearly))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Please create a regular plan instead of irregular plan."
                    });
            }

            if (request.PayAmount is null or < 0)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Pay Amount is required for irregular plans."
                    });
            }

            payAmount = Convert.ToDouble(request.PayAmount);

            // Check if payment split is valid
            if (request.PaymentIntervalType == PaymentIntervalType.Custom)
            {
                if (request.PaymentSplits == null || request.PaymentSplits.Count == 0)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Payment Splits is required for custom payment interval type."
                        });
                }

                if (request.PaymentSplits.Sum(x => x.SplitPercentage) != 100.0M)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Payment Splits must be 100%."
                        });
                }

                if (request.PaymentSplits.Any(paymentSplit => paymentSplit.PaymentDate.Date < request.PeriodStart.Date ||
                                                              paymentSplit.PaymentDate.Date > periodEnd.Date))
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Payment Date is not in the period."
                        });
                }
            }
        }

        if (periodEnd < DateTime.UtcNow)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "You cannot create a Bill Record that is already expired."
                });
        }

        // Adding grace period
        var metadata = new Dictionary<string, string>();
        var actualStart = request.PeriodStart;
        var gracePeriodEnd = periodEnd.AddDays(7);

        metadata["grace_period_start"] = periodEnd.ToString("O");
        metadata["grace_period_end"] = gracePeriodEnd.ToString("O");
        metadata["actual_period_start"] = actualStart.ToString("O");
        metadata["actual_period_end"] = periodEnd.ToString("O");

        // Check if UpgradeFromBillRecordId and DowngradeFromBillRecordId are valid
        if (request.UpgradeFromBillRecordId.HasValue)
        {
            if (!await _appDbContext
                    .CompanyBillRecords
                    .AnyAsync(x => x.CompanyId == request.CompanyId && x.Id == request.UpgradeFromBillRecordId))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Upgrade From Bill Record Id not found."
                    });
            }
        }

        if (request.DowngradeFromBillRecordId.HasValue)
        {
            if (!await _appDbContext
                    .CompanyBillRecords
                    .AnyAsync(x => x.CompanyId == request.CompanyId && x.Id == request.DowngradeFromBillRecordId))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Downgrade From Bill Record Id not found."
                    });
            }
        }

        if (request.UpgradeFromBillRecordId.HasValue && request.DowngradeFromBillRecordId.HasValue)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "You cannot set both Upgrade From and Downgrade From Bill Record Ids."
                });
        }

        var isEnterprisePlan = request.SubscriptionPlanId.Contains("enterprise");

        var newBill = new BillRecord
        {
            SubscriptionPlanId = request.SubscriptionPlanId,
            PayAmount = payAmount,
            currency = string.IsNullOrWhiteSpace(request.Currency) ? null : request.Currency,
            CompanyId = company.Id,
            Status = BillStatus.Active,
            PaymentStatus = payAmount > 0 ? PaymentStatus.Paid : PaymentStatus.FreeOfCharge,
            PeriodStart = request.PeriodStart,
            PeriodEnd = gracePeriodEnd,
            quantity = 1,
            UpgradeFromBillRecordId = request.UpgradeFromBillRecordId,
            DowngradeFromBillRecordId = request.DowngradeFromBillRecordId,
            SubscriptionTier = subscriptionPlan.SubscriptionTier,
            IsIrregularPlan = request.IsIrregularPlan,
            PaymentIntervalType = request.PaymentIntervalType,
            PaymentSplits =
                request.PaymentIntervalType == "Custom"
                    ? request.PaymentSplits.OrderBy(x => x.PaymentDate).ToList()
                    : null,
            PaymentTerms = paymentTerms,
            metadata = metadata,
            NoticePeriod = isEnterprisePlan ? noticePeriod : null,
            IsAutoRenewalEnabled = request.IsIrregularPlan && isEnterprisePlan && noticePeriod > 0
        };

        var usageCycle = _usageCycleCalculator.GetUsageCycle(newBill.PeriodStart, newBill.PeriodEnd);
        newBill.UsageCycleStart = usageCycle.From;
        newBill.UsageCycleEnd = usageCycle.To;

        company.BillRecords.Add(newBill);

        company.MaximumContacts = null;
        company.MaximumWhAutomatedMessages = null;
        company.MaximumAutomations = null;

        await _appDbContext.CompanyAPIKeys
            .Where(x => x.CompanyId == company.Id)
            .ExecuteUpdateAsync(key =>
                key
                    .SetProperty(k => k.CallLimit, subscriptionPlan.MaximumAPICall)
                    .SetProperty(k => k.Calls, 0));

        await _appDbContext.SaveChangesAsync();

        _logger.LogInformation(
            "Subscription Plan: {PlanId} for Company: {Company} Created/Updated Successful",
            request.SubscriptionPlanId,
            company.CompanyName);

        // NetSuite related
        if (request.NetsuiteInvoiceId != null)
        {
            var jobId = BackgroundJob.Enqueue<IInternalIntegrationService>(x =>
                x.CreatePaymentRecordByInvoiceIdAsync(request.NetsuiteInvoiceId.Value, newBill.Id, company.Id));

            _logger.LogInformation(
                "Created background job for create payment record by NetSuite Invoice Internal Id. JobId: {JobId}, NetSuiteInvoiceId: {NetSuiteInvoiceId}, CompanyId: {CompanyId}",
                jobId,
                request.NetsuiteInvoiceId,
                request.CompanyId);
        }

        // HubSpot related
        if (ValidSubscriptionPlan.PremiumTier.Contains(request.SubscriptionPlanId) &&
            (ValidSubscriptionPlan.ProTier.Contains(lastPayment?.SubscriptionPlanId) ||
             ValidSubscriptionPlan.FreePlans.Contains(lastPayment?.SubscriptionPlanId)))
        {
            BackgroundJob.Enqueue<IInternalHubSpotService>(x =>
                x.SetCompanyOwnerUpgradeToPremiumPlanFlag(request.CompanyId));
            BackgroundJob.Enqueue<ICoreService>(x =>
                x.SyncCompanyStaffsToSleekFlowContactsBackground(request.CompanyId));
        }

        if (ValidSubscriptionPlan.ProTier.Contains(request.SubscriptionPlanId) &&
            ValidSubscriptionPlan.FreePlans.Contains(lastPayment?.SubscriptionPlanId))
        {
            BackgroundJob.Enqueue<IInternalHubSpotService>(x =>
                x.SetCompanyOwnerUpgradeToProPlanFlag(request.CompanyId));
            BackgroundJob.Enqueue<ICoreService>(x =>
                x.SyncCompanyStaffsToSleekFlowContactsBackground(request.CompanyId));
        }

        BackgroundJob.Enqueue<IInternalHubSpotService>(x => x.SyncCompany(request.CompanyId, null));


        // Feature Flag related
        try
        {
            await _enabledFeaturesService.UpdateDefaultFeatureFlagForCompanyBasedOnSubscriptionTier(
                request.SubscriptionPlanId,
                request.CompanyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating feature flag for company");
        }

        await _companyUsageService.EnableUsageLimitOffsetProfileAsync(request.CompanyId, true);

        var staff = await _userRoleStaffRepository.GetFirstAdmin(request.CompanyId);

        BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(request.CompanyId, null));
        BackgroundJob.Enqueue<IIntelligentHubService>(x => x.RefreshIntelligentHubConfigAsync(request.CompanyId));
        BackgroundJob.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = "Subscription Plan have updated."
            });
    }

    /// <summary>
    /// Add Company Add on Bill Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> CreateCompanyAddOn([FromBody] CreateCompanyAddOnRequest request)
    {
        if (await GetCurrentValidInternalUser(
                [ApplicationUserRole.InternalCmsSuperUser]) == null)
        {
            return Unauthorized();
        }

        var (company, errorResult) = await GetCompanyWithBillRecordsOrBadRequestAsync(request.CompanyId);
        if (errorResult != null) return errorResult;

        var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans
            .AsNoTracking()
            .Where(x => x.Id == request.AddOnPlanId)
            .FirstOrDefaultAsync();

        if (subscriptionPlan == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Subscription Plan does not exist."
                });
        }

        if (ValidSubscriptionPlan.CmsAllAddOn.All(x => x != request.AddOnPlanId))
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Add On Plan does not exist."
                });
        }

        var defaultSubscriptionPeriodInMonth = subscriptionPlan.SubscriptionInterval == "yearly" ? 12 : 1;
        var periodEnd = request.PeriodStart.AddMonths(defaultSubscriptionPeriodInMonth);
        var payAmount = 0.00;
        var paymentTerms = CalculatePaymentTerms(
            request.PaymentTerms,
            request.IsIrregularPlan,
            request.PaymentIntervalType,
            subscriptionPlan);
        var metadata = new Dictionary<string, string>();
        var noticePeriod = request.NoticePeriod ?? 30;

        // To the end of subscription
        if (request.DurationInMonths == -1)
        {
            var currentSubscription = company.BillRecords
                .Where(x =>
                    x.Status != BillStatus.Inactive
                    && x.Status != BillStatus.Terminated
                    && ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                    && x.PeriodStart < DateTime.UtcNow)
                .OrderByDescending(x => x.created)
                .ThenByDescending(x => x.PayAmount)
                .FirstOrDefault();

            if (currentSubscription == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Company does not have a subscription plan."
                    });
            }

            // No need to add grace period cause already align with subscription period end.
            periodEnd = currentSubscription.PeriodEnd;

            if (currentSubscription.metadata != null && currentSubscription.metadata.ContainsKey("actual_period_end"))
            {
                metadata = new Dictionary<string, string>
                {
                    {
                        "actual_period_start", request.PeriodStart.ToString("O")
                    },
                    {
                        "actual_period_end", currentSubscription.metadata["actual_period_end"]
                    },
                    {
                        "grace_period_start", currentSubscription.metadata["grace_period_start"]
                    },
                    {
                        "grace_period_end", currentSubscription.metadata["grace_period_end"]
                    }
                };
            }
        }
        // Irregular and not to the end of subscription
        else if (request.IsIrregularPlan)
        {
            if (request.DurationInMonths is not > 0)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Duration In Months is required."
                    });
            }

            periodEnd = request.PeriodStart.AddMonths(request.DurationInMonths.Value);

            // Adding grace period
            periodEnd = periodEnd.AddHours(3);
        }
        // Regular and not to the end of subscription
        else
        {
            // Adding grace period
            periodEnd = periodEnd.AddHours(3);
        }

        if (request.IsIrregularPlan)
        {
            if (!PaymentIntervalType.AllPaymentIntervalTypes.Contains(request.PaymentIntervalType))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Invalid Payment Interval Type."
                    });
            }

            // Filter invalid payment interval type option
            var durationInMonths = BillRecordRevenueCalculatorHelper.GetMonthDiff(request.PeriodStart, periodEnd);

            if ((durationInMonths == 1 &&
                 request.PaymentIntervalType is PaymentIntervalType.Monthly or PaymentIntervalType.Custom) ||
                (durationInMonths == 12 &&
                 request.PaymentIntervalType is PaymentIntervalType.Yearly))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Please create a regular plan instead of irregular plan."
                    });
            }

            if (request.PayAmount is null or < 0)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Pay Amount is required for irregular plans."
                    });
            }

            payAmount = Convert.ToDouble(request.PayAmount);

            // Check if payment split is valid
            if (request.PaymentIntervalType == PaymentIntervalType.Custom)
            {
                if (request.PaymentSplits == null || request.PaymentSplits.Count == 0)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Payment Splits is required for custom payment interval type."
                        });
                }

                if (request.PaymentSplits.Sum(x => x.SplitPercentage) != 100.0M)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Payment Splits must be 100%."
                        });
                }

                if (request.PaymentSplits.Any(paymentSplit =>
                        paymentSplit.PaymentDate.Date < request.PeriodStart.Date ||
                        paymentSplit.PaymentDate.Date > periodEnd.Date))
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Payment Date is not in the period."
                        });
                }
            }
        }

        if (request.PeriodStart >= periodEnd)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Subscription date range!"
                });
        }

        if (periodEnd < DateTime.UtcNow)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "You cannot create a Bill Record that is already expired."
                });
        }

        var isEnterprisePlan = subscriptionPlan.Id.Contains("enterprise");

        var newBill = new BillRecord
        {
            SubscriptionPlanId = subscriptionPlan.Id,
            PayAmount = payAmount,
            currency = string.IsNullOrWhiteSpace(request.Currency) ? null : request.Currency,
            CompanyId = company.Id,
            Status = BillStatus.Active,
            PaymentStatus = payAmount > 0 ? PaymentStatus.Paid : PaymentStatus.FreeOfCharge,
            PeriodStart = request.PeriodStart,
            PeriodEnd = periodEnd,
            quantity = request.Quantity ?? 1,
            SubscriptionTier = subscriptionPlan.SubscriptionTier,
            IsIrregularPlan = request.IsIrregularPlan,
            PaymentIntervalType = request.PaymentIntervalType,
            PaymentSplits =
                request.PaymentIntervalType == "Custom"
                    ? request.PaymentSplits.OrderBy(x => x.PaymentDate).ToList()
                    : null,
            PaymentTerms = paymentTerms,
            metadata = metadata.Count > 0 ? metadata : null,
            NoticePeriod = isEnterprisePlan ? noticePeriod : null,
            IsAutoRenewalEnabled = request.IsIrregularPlan && isEnterprisePlan && noticePeriod > 0
        };

        company.BillRecords.Add(newBill);

        await _appDbContext.SaveChangesAsync();

        // NetSuite related
        if (request.NetsuiteInvoiceId != null)
        {
            BackgroundJob.Enqueue<IInternalIntegrationService>(x =>
                x.CreatePaymentRecordByInvoiceIdAsync(request.NetsuiteInvoiceId.Value, newBill.Id, company.Id));
        }

        var staff = await _userRoleStaffRepository.GetFirstAdmin(request.CompanyId);

        _backgroundJobClient.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(request.CompanyId, null));
        _backgroundJobClient.Enqueue<IIntelligentHubService>(x =>
            x.RefreshIntelligentHubConfigAsync(request.CompanyId));
        _backgroundJobClient.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = "Subscription Plan have updated."
            });
    }

    /// <summary>
    /// Update Company CRM Bill Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> UpdateCrmBillRecord(
        [FromBody] UpdateCrmBillRecordRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                [ApplicationUserRole.InternalCmsSuperUser]) == null)
        {
            return Unauthorized();
        }

        var billRecord = await _appDbContext.CompanyBillRecords
            .FirstOrDefaultAsync(x => x.Id == request.BillRecordId);

        if (billRecord == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Bill Record Not Found."
                });
        }

        if (request.PeriodEnd.HasValue)
        {
            billRecord.PeriodEnd = request.PeriodEnd.Value;
        }

        if (billRecord.PeriodStart >= billRecord.PeriodEnd)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Bill Record date range!"
                });
        }

        billRecord.UpdatedAt = DateTime.UtcNow;
        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        var staff = await _userRoleStaffRepository.GetFirstAdmin(request.CompanyId);

        BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(billRecord.CompanyId, null));
        BackgroundJob.Enqueue<IIntelligentHubService>(x => x.RefreshIntelligentHubConfigAsync(request.CompanyId));
        BackgroundJob.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

        BackgroundJob.Enqueue<IInternalAnalyticService>(x => x.UpdateCompanyAllTimeRevenueAnalyticInfo(
            new List<string>()
            {
                request.CompanyId
            }));

        return Ok(
            new ResponseViewModel
            {
                message = "Bill Record have updated."
            });
    }

    /// <summary>
    /// Update Company Bill Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [Obsolete("Replaced by UpdateSubscriptionPlanBillRecord and UpdateAddOnPlanBillRecord")]
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> UpdateBillRecord([FromBody] UpdateBillRecordRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var now = DateTime.UtcNow;

        var billRecord = await _appDbContext.CompanyBillRecords
            .FirstOrDefaultAsync(x => x.Id == request.BillRecordId);

        if (billRecord == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Bill Record Not Found."
                });
        }

        var subscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanAsync(billRecord.SubscriptionPlanId);

        if (request.PeriodStart.HasValue)
        {
            billRecord.PeriodStart = request.PeriodStart.Value;
        }

        if (request.PeriodEnd.HasValue)
        {
            billRecord.PeriodEnd = request.PeriodEnd.Value;
        }

        if (!string.IsNullOrWhiteSpace(request.SubscriptionPlanId))
        {
            subscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanAsync(request.SubscriptionPlanId);

            if (subscriptionPlan == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Subscription Plan does not exist."
                    });
            }

            billRecord.SubscriptionPlanId = request.SubscriptionPlanId;
            billRecord.SubscriptionTier = subscriptionPlan.SubscriptionTier;
        }

        if (subscriptionPlan!.PlanTypeCode == PlanTypeCodes.BasePlan)
        {
            var usageCycle = _usageCycleCalculator.GetUsageCycle(billRecord.PeriodStart, billRecord.PeriodEnd);
            billRecord.UsageCycleStart = usageCycle.From;
            billRecord.UsageCycleEnd = usageCycle.To;
        }
        else
        {
            billRecord.UsageCycleStart = null;
            billRecord.UsageCycleEnd = null;
        }

        if (request.Quantity.HasValue)
        {
            billRecord.quantity = request.Quantity.Value;
        }

        if (billRecord.PeriodStart >= billRecord.PeriodEnd)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Invalid Bill Record date range!"
                });
        }

        if (request.UpgradeFromBillRecordId.HasValue)
        {
            if (request.UpgradeFromBillRecordId.Value == -1)
            {
                billRecord.UpgradeFromBillRecordId = null;
            }
            else
            {
                if (await _appDbContext
                        .CompanyBillRecords
                        .AnyAsync(x => x.CompanyId == request.CompanyId && x.Id == request.UpgradeFromBillRecordId))
                {
                    billRecord.UpgradeFromBillRecordId = request.UpgradeFromBillRecordId;
                }
                else
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Upgrade From Bill Record Id not found."
                        });
                }
            }
        }

        if (request.DowngradeFromBillRecordId.HasValue)
        {
            if (request.DowngradeFromBillRecordId.Value == -1)
            {
                billRecord.DowngradeFromBillRecordId = null;
            }
            else
            {
                if (await _appDbContext
                        .CompanyBillRecords
                        .AnyAsync(
                            x => x.CompanyId == request.CompanyId && x.Id == request.DowngradeFromBillRecordId))
                {
                    billRecord.DowngradeFromBillRecordId = request.DowngradeFromBillRecordId;
                }
                else
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "Downgrade From Bill Record Id not found."
                        });
                }
            }
        }

        if (billRecord.UpgradeFromBillRecordId.HasValue && billRecord.DowngradeFromBillRecordId.HasValue)
        {
            return Ok(
                new ResponseViewModel
                {
                    message = "Bill Record cannot be both upgraded and downgraded!"
                });
        }

        if (subscriptionPlan!.PlanTypeCode == PlanTypeCodes.BasePlan)
        {
            if (billRecord.PeriodStart <= now && now < billRecord.PeriodEnd)
            {
                await _companyUsageService.EnableUsageLimitOffsetProfileAsync(request.CompanyId, true);
            }
        }

        billRecord.UpdatedAt = DateTime.UtcNow;
        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        var staff = await _userRoleStaffRepository.GetFirstAdmin(request.CompanyId);

        // Feature Flag related
        try
        {
            await _enabledFeaturesService.UpdateDefaultFeatureFlagForCompanyBasedOnSubscriptionTier(
                request.SubscriptionPlanId,
                request.CompanyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating feature flag for company");
        }

        BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(billRecord.CompanyId, null));
        BackgroundJob.Enqueue<IIntelligentHubService>(x => x.RefreshIntelligentHubConfigAsync(request.CompanyId));
        BackgroundJob.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

        BackgroundJob.Enqueue<IInternalAnalyticService>(
            x => x.UpdateCompanyAllTimeRevenueAnalyticInfo(
                new List<string>()
                {
                    request.CompanyId
                }));

        return Ok(
            new ResponseViewModel
            {
                message = "Bill Record have updated."
            });
    }

    /// <summary>
    /// Update Subscription Plan Bill Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> UpdateSubscriptionPlanBillRecord(
        [FromBody]
        UpdateSubscriptionPlanBillRecordRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                [ApplicationUserRole.InternalCmsSuperUser]) == null)
        {
            return Unauthorized();
        }

        var now = DateTime.UtcNow;

        var billRecord = await _appDbContext.CompanyBillRecords
            .FirstOrDefaultAsync(x => x.Id == request.BillRecordId);

        if (billRecord == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Bill Record Not Found."
                });
        }

        var subscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanAsync(billRecord.SubscriptionPlanId);

        if (!string.IsNullOrWhiteSpace(request.SubscriptionPlanId))
        {
            subscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanAsync(request.SubscriptionPlanId);

            if (subscriptionPlan == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Subscription Plan does not exist."
                    });
            }

            billRecord.SubscriptionPlanId = request.SubscriptionPlanId;
            billRecord.SubscriptionTier = subscriptionPlan.SubscriptionTier;

            if (!billRecord.SubscriptionPlanId.Contains("enterprise"))
            {
                billRecord.NoticePeriod = null;
            }
            else
            {
                billRecord.NoticePeriod = 30;
            }
        }

        if (subscriptionPlan == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Subscription Plan does not exist."
                });
        }

        // Irregular plan
        if (request.PayAmount.HasValue)
        {
            if (request.PayAmount < 0)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Pay Amount cannot be negative."
                    });
            }

            billRecord.PayAmount = Convert.ToDouble(request.PayAmount.Value);
            billRecord.PaymentStatus = billRecord.PayAmount == 0 ? PaymentStatus.FreeOfCharge : PaymentStatus.Paid;
        }

        if (!string.IsNullOrWhiteSpace(request.Currency))
        {
            billRecord.currency = request.Currency;
        }

        if (request.PaymentTerms.HasValue)
        {
            billRecord.PaymentTerms = request.PaymentTerms.Value;
        }

        if (request.PeriodStart.HasValue)
        {
            var originalSubscriptionInterval =
                BillRecordRevenueCalculatorHelper.GetMonthDiff(billRecord.PeriodStart, billRecord.PeriodEnd);

            billRecord.PeriodStart = request.PeriodStart.Value;
            billRecord.PeriodEnd = !billRecord.IsIrregularPlan
                ? billRecord.PeriodStart.AddMonths(subscriptionPlan.SubscriptionInterval == "yearly" ? 12 : 1)
                : billRecord.PeriodStart.AddMonths(originalSubscriptionInterval);
        }

        if (request.DurationInMonths.HasValue)
        {
            billRecord.PeriodEnd = billRecord.PeriodStart.AddMonths(request.DurationInMonths.Value);
        }

        if (billRecord.PeriodStart >= billRecord.PeriodEnd)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Bill Record date range!"
                });
        }

        if (!string.IsNullOrWhiteSpace(request.PaymentIntervalType))
        {
            if (!PaymentIntervalType.AllPaymentIntervalTypes.Contains(request.PaymentIntervalType))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Invalid Payment Interval Type."
                    });
            }

            // Filter invalid payment interval type option
            var durationInMonths = BillRecordRevenueCalculatorHelper.GetMonthDiff(
                billRecord.PeriodStart,
                billRecord.PeriodEnd);

            if ((durationInMonths == 1 &&
                 request.PaymentIntervalType is PaymentIntervalType.Monthly or PaymentIntervalType.Custom) ||
                (durationInMonths == 12 &&
                 request.PaymentIntervalType is PaymentIntervalType.Yearly))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Unavailable Payment Interval Type."
                    });
            }

            billRecord.PaymentIntervalType = request.PaymentIntervalType;

            if (billRecord.PaymentIntervalType != PaymentIntervalType.Custom)
            {
                billRecord.PaymentSplits = null;
            }
        }

        // Avoid update current subscription plan to expired date.
        var currentSubscription = await _companyUsageService.GetCompanySubscriptionPlan(request.CompanyId);

        if (currentSubscription != null)
        {
            if (currentSubscription.Id == billRecord.Id && billRecord.PeriodEnd < now)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Cannot update current subscription plan to expired date."
                    });
            }
        }

        // Adding a grace period
        if (request.PeriodStart.HasValue || request.DurationInMonths.HasValue)
        {
            // Preserve existing metadata or create new if null
            var metadata = billRecord.metadata ?? new Dictionary<string, string>();
            var actualStart = billRecord.PeriodStart;
            var actualPeriodEnd = billRecord.PeriodEnd;
            var gracePeriodEnd = actualPeriodEnd.AddDays(7);

            metadata["grace_period_start"] = actualPeriodEnd.ToString("O");
            metadata["grace_period_end"] = gracePeriodEnd.ToString("O");
            metadata["actual_period_start"] = actualStart.ToString("O");
            metadata["actual_period_end"] = actualPeriodEnd.ToString("O");

            billRecord.PeriodEnd = gracePeriodEnd;
            billRecord.metadata = new Dictionary<string, string>(metadata);
        }

        var usageCycle = _usageCycleCalculator.GetUsageCycle(billRecord.PeriodStart, billRecord.PeriodEnd);
        billRecord.UsageCycleStart = usageCycle.From;
        billRecord.UsageCycleEnd = usageCycle.To;

        if (billRecord.PaymentIntervalType == PaymentIntervalType.Custom)
        {
            var paymentSplits = request.PaymentSplits ?? billRecord.PaymentSplits;

            if (paymentSplits == null || paymentSplits.Count == 0)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Payment Splits cannot be empty when payment interval type is custom."
                    });
            }

            var sumPaymentSplit = paymentSplits.Sum(x => x.SplitPercentage);

            if (sumPaymentSplit != 100.0M)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Payment Split must be 100%."
                    });
            }

            var actualPeriodEnd =
                billRecord.metadata?.TryGetValue("actual_period_end", out var actualPeriodEndString) == true &&
                !string.IsNullOrWhiteSpace(actualPeriodEndString)
                    ? DateTime.TryParse(actualPeriodEndString, out var actualPeriodEndDateTime)
                        ? actualPeriodEndDateTime
                        : billRecord.PeriodEnd
                    : billRecord.PeriodEnd;

            if (paymentSplits.Any(paymentSplit => paymentSplit.PaymentDate.Date < billRecord.PeriodStart.Date ||
                                                  paymentSplit.PaymentDate.Date > actualPeriodEnd.Date))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Payment Date is not in the period."
                    });
            }

            billRecord.PaymentSplits = paymentSplits.OrderBy(x => x.PaymentDate).ToList();
        }

        if (request.UpgradeFromBillRecordId.HasValue)
        {
            if (request.UpgradeFromBillRecordId.Value == -1)
            {
                billRecord.UpgradeFromBillRecordId = null;
            }
            else
            {
                if (await _appDbContext
                        .CompanyBillRecords
                        .AnyAsync(x => x.CompanyId == request.CompanyId && x.Id == request.UpgradeFromBillRecordId))
                {
                    billRecord.UpgradeFromBillRecordId = request.UpgradeFromBillRecordId;
                }
                else
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Upgrade From Bill Record Id not found."
                        });
                }
            }
        }

        if (request.DowngradeFromBillRecordId.HasValue)
        {
            if (request.DowngradeFromBillRecordId.Value == -1)
            {
                billRecord.DowngradeFromBillRecordId = null;
            }
            else
            {
                if (await _appDbContext
                        .CompanyBillRecords
                        .AnyAsync(x => x.CompanyId == request.CompanyId && x.Id == request.DowngradeFromBillRecordId))
                {
                    billRecord.DowngradeFromBillRecordId = request.DowngradeFromBillRecordId;
                }
                else
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Downgrade From Bill Record Id not found."
                        });
                }
            }
        }

        if (billRecord.UpgradeFromBillRecordId.HasValue && billRecord.DowngradeFromBillRecordId.HasValue)
        {
            return Ok(
                new ResponseViewModel
                {
                    message = "Bill Record cannot be both upgraded and downgraded!"
                });
        }

        if (request.NoticePeriod.HasValue)
        {
            billRecord.NoticePeriod = request.NoticePeriod.Value;
        }

        if (billRecord.PeriodStart <= now && now < billRecord.PeriodEnd)
        {
            await _companyUsageService.EnableUsageLimitOffsetProfileAsync(request.CompanyId, true);
        }

        billRecord.UpdatedAt = DateTime.UtcNow;
        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        var staff = await _userRoleStaffRepository.GetFirstAdmin(request.CompanyId);

        // Feature Flag related
        try
        {
            await _enabledFeaturesService.UpdateDefaultFeatureFlagForCompanyBasedOnSubscriptionTier(
                request.SubscriptionPlanId,
                request.CompanyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating feature flag for company");
        }

        BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(billRecord.CompanyId, null));
        BackgroundJob.Enqueue<IIntelligentHubService>(x => x.RefreshIntelligentHubConfigAsync(request.CompanyId));
        BackgroundJob.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

        BackgroundJob.Enqueue<IInternalAnalyticService>(x => x.UpdateCompanyAllTimeRevenueAnalyticInfo(
            new List<string>
            {
                request.CompanyId
            }));

        return Ok(
            new ResponseViewModel
            {
                message = "Subscription Bill Record have updated."
            });
    }

    /// <summary>
    /// Update Company Add On Plan Bill Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> UpdateAddOnPlanBillRecord(
        [FromBody]
        UpdateAddOnPlanBillRecordRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                [ApplicationUserRole.InternalCmsSuperUser]) == null)
        {
            return Unauthorized();
        }

        var billRecord = await _appDbContext.CompanyBillRecords
            .FirstOrDefaultAsync(x => x.Id == request.BillRecordId);

        if (billRecord == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Bill Record Not Found."
                });
        }

        var subscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanAsync(billRecord.SubscriptionPlanId);

        if (!string.IsNullOrWhiteSpace(request.SubscriptionPlanId))
        {
            subscriptionPlan = await _subscriptionPlanService.GetSubscriptionPlanAsync(request.SubscriptionPlanId);

            if (subscriptionPlan == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Subscription Plan does not exist."
                    });
            }

            billRecord.SubscriptionPlanId = request.SubscriptionPlanId;
            billRecord.SubscriptionTier = subscriptionPlan.SubscriptionTier;

            if (!billRecord.SubscriptionPlanId.Contains("enterprise"))
            {
                billRecord.NoticePeriod = null;
            }
            else
            {
                billRecord.NoticePeriod = 30;
            }
        }

        if (subscriptionPlan == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Subscription Plan does not exist."
                });
        }

        if (request.Quantity.HasValue)
        {
            billRecord.quantity = request.Quantity.Value;
        }

        if (request.PayAmount.HasValue)
        {
            if (request.PayAmount < 0)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Pay Amount cannot be negative."
                    });
            }

            billRecord.PayAmount = Convert.ToDouble(request.PayAmount.Value);
            billRecord.PaymentStatus = billRecord.PayAmount == 0 ? PaymentStatus.FreeOfCharge : PaymentStatus.Paid;
        }

        if (!string.IsNullOrWhiteSpace(request.Currency))
        {
            billRecord.currency = request.Currency;
        }

        if (request.PaymentTerms.HasValue)
        {
            billRecord.PaymentTerms = request.PaymentTerms.Value;
        }

        if (request.PeriodStart.HasValue)
        {
            var originalSubscriptionInterval =
                BillRecordRevenueCalculatorHelper.GetMonthDiff(billRecord.PeriodStart, billRecord.PeriodEnd);

            billRecord.PeriodStart = request.PeriodStart.Value;
            billRecord.PeriodEnd = !billRecord.IsIrregularPlan
                ? billRecord.PeriodStart.AddMonths(subscriptionPlan.SubscriptionInterval == "yearly" ? 12 : 1)
                : billRecord.PeriodStart.AddMonths(originalSubscriptionInterval);

            // Adding a grace period
            billRecord.PeriodEnd = billRecord.PeriodEnd.AddHours(3);
        }

        if (request.DurationInMonths.HasValue)
        {
            if (request.DurationInMonths.Value == -1)
            {
                var currentSubscription = await _companyUsageService.GetCompanySubscriptionPlan(request.CompanyId);

                if (currentSubscription == null)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Company does not have a subscription plan."
                        });
                }

                // No need to add grace period cause already align with subscription period end.
                billRecord.PeriodEnd = currentSubscription.PeriodEnd;

                // Add actual period metadata if current subscription has actual period metadata
                if (currentSubscription.metadata != null &&
                    currentSubscription.metadata.ContainsKey("actual_period_end"))
                {
                    var metadata = billRecord.metadata ?? new Dictionary<string, string>();
                    metadata["actual_period_start"] = billRecord.PeriodStart.ToString("O");
                    metadata["actual_period_end"] = currentSubscription.metadata["actual_period_end"];
                    metadata["grace_period_start"] = currentSubscription.metadata["grace_period_start"];
                    metadata["grace_period_end"] = currentSubscription.metadata["grace_period_end"];

                    billRecord.metadata = new Dictionary<string, string>(metadata);
                }
            }
            else
            {
                if (request.DurationInMonths.Value <= 0)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Duration In Months is required."
                        });
                }

                billRecord.PeriodEnd = billRecord.PeriodStart.AddMonths(request.DurationInMonths.Value);

                // Adding a grace period
                billRecord.PeriodEnd = billRecord.PeriodEnd.AddHours(3);
            }
        }

        if (billRecord.PeriodStart >= billRecord.PeriodEnd)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Bill Record date range!"
                });
        }

        if (!string.IsNullOrWhiteSpace(request.PaymentIntervalType))
        {
            if (!PaymentIntervalType.AllPaymentIntervalTypes.Contains(request.PaymentIntervalType))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Invalid Payment Interval Type."
                    });
            }

            // Filter invalid payment interval type option
            var durationInMonths = BillRecordRevenueCalculatorHelper.GetMonthDiff(
                billRecord.PeriodStart,
                billRecord.PeriodEnd);

            if ((durationInMonths == 1 &&
                 request.PaymentIntervalType is PaymentIntervalType.Monthly or PaymentIntervalType.Custom) ||
                (durationInMonths == 12 &&
                 request.PaymentIntervalType is PaymentIntervalType.Yearly))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Unavailable Payment Interval Type."
                    });
            }

            billRecord.PaymentIntervalType = request.PaymentIntervalType;

            if (billRecord.PaymentIntervalType != PaymentIntervalType.Custom)
            {
                billRecord.PaymentSplits = null;
            }
        }

        if ((request.PeriodStart.HasValue && !request.DurationInMonths.HasValue) ||
            request.DurationInMonths is > 0)
        {
            // Remove actual period metadata if exist
            if (billRecord.metadata is { Count: > 0 } &&
                billRecord.metadata.ContainsKey("actual_period_end"))
            {
                billRecord.metadata.Remove("actual_period_start");
                billRecord.metadata.Remove("actual_period_end");
                billRecord.metadata.Remove("grace_period_start");
                billRecord.metadata.Remove("grace_period_end");

                billRecord.metadata = new Dictionary<string, string>(billRecord.metadata);
            }
        }

        if (billRecord.PaymentIntervalType == PaymentIntervalType.Custom)
        {
            var paymentSplits = request.PaymentSplits ?? billRecord.PaymentSplits;

            if (paymentSplits == null || paymentSplits.Count == 0)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Payment Splits cannot be empty when payment interval type is custom."
                    });
            }

            var sumPaymentSplit = paymentSplits.Sum(x => x.SplitPercentage);

            if (sumPaymentSplit != 100.0M)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Payment Split must be 100%."
                    });
            }

            var actualPeriodEnd =
                billRecord.metadata?.TryGetValue("actual_period_end", out var actualPeriodEndString) == true &&
                !string.IsNullOrWhiteSpace(actualPeriodEndString)
                    ? DateTime.TryParse(actualPeriodEndString, out var actualPeriodEndDateTime)
                        ? actualPeriodEndDateTime
                        : billRecord.PeriodEnd
                    : billRecord.PeriodEnd;

            if (paymentSplits.Any(paymentSplit => paymentSplit.PaymentDate.Date < billRecord.PeriodStart.Date ||
                                                  paymentSplit.PaymentDate.Date > actualPeriodEnd.Date))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Payment Date is not in the period."
                    });
            }

            billRecord.PaymentSplits = paymentSplits.OrderBy(x => x.PaymentDate).ToList();
        }

        if (request.NoticePeriod.HasValue)
        {
            billRecord.NoticePeriod = request.NoticePeriod.Value;
        }

        billRecord.UpdatedAt = DateTime.UtcNow;
        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        var staff = await _userRoleStaffRepository.GetFirstAdmin(request.CompanyId);

        BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(billRecord.CompanyId, null));
        BackgroundJob.Enqueue<IIntelligentHubService>(x => x.RefreshIntelligentHubConfigAsync(request.CompanyId));
        BackgroundJob.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

        BackgroundJob.Enqueue<IInternalAnalyticService>(x => x.UpdateCompanyAllTimeRevenueAnalyticInfo(
            new List<string>()
            {
                request.CompanyId
            }));

        return Ok(
            new ResponseViewModel
            {
                message = "Bill Record have updated."
            });
    }

    /// <summary>
    /// Remove Company Bill Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> RemoveBillRecord([FromBody] RemoveBillRecordRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var billRecord = await _appDbContext.CompanyBillRecords
            .FirstOrDefaultAsync(x => x.Id == request.BillRecordId && x.CompanyId == request.CompanyId);

        if (billRecord == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Bill Record Not Found."
                });
        }

        if (!(billRecord.PayAmount == 0 &&
              string.IsNullOrWhiteSpace(billRecord.hosted_invoice_url) &&
              string.IsNullOrWhiteSpace(billRecord.invoice_Id) &&
              billRecord.CmsSalesPaymentRecords.Count == 0))
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "This Bill Record can't be deleted."
                });
        }

        _appDbContext.Remove(billRecord);

        await _appDbContext.SaveChangesAsync();

        var staff = await _userRoleStaffRepository.GetFirstAdmin(request.CompanyId);

        BackgroundJob.Enqueue<IFlowHubService>(x => x.UpdateFlowHubConfigAsync(billRecord.CompanyId, null));
        BackgroundJob.Enqueue<IIntelligentHubService>(x => x.RefreshIntelligentHubConfigAsync(request.CompanyId));
        BackgroundJob.Enqueue<ICrmHubService>(x => x.RefreshCrmHubConfigAsync(request.CompanyId, staff));

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = $"Bill Record (ID:{request.BillRecordId}) have deleted."
            });
    }

    /// <summary>
    /// Set Company Usage Limit.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> SetCompanyLimit([FromBody] SetCompanyLimitRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var company = await _appDbContext.CompanyCompanies
            .Where(x => x.Id == request.CompanyId)
            .FirstOrDefaultAsync();

        if (request.AgentCount.HasValue)
        {
            company.MaximumAgents = request.AgentCount.Value;
        }

        if (request.WhatsappCount.HasValue)
        {
            company.MaximumWhatsappInstance = request.WhatsappCount.Value;
        }

        if (request.ContactLimit.HasValue)
        {
            company.MaximumContacts = request.ContactLimit.Value;
        }

        if (request.MaximumAutomations.HasValue)
        {
            company.MaximumAutomations = request.MaximumAutomations.Value;
        }

        if (request.MaximumCampaignMessages.HasValue)
        {
            company.MaximumWhAutomatedMessages = request.MaximumCampaignMessages.Value;
        }

        if (request.MaximumNumberOfChannel.HasValue)
        {
            company.MaximumNumberOfChannel = request.MaximumNumberOfChannel.Value;
        }

        if (request.MaximumShopifyStore.HasValue)
        {
            company.MaximumShopifyStore = request.MaximumShopifyStore.Value;
        }

        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = "Company usage limit have updated."
            });
    }

    /// <summary>
    /// Update Company Usage Limit Offset Profile.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> UpdateCompanyUsageLimitOffsetProfile(
        [FromBody]
        UpdateCompanyUsageLimitOffsetProfileRequest request)
    {
        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);

        company.UsageLimitOffsetProfile = request.CompanyUsageLimitOffsetProfile;
        _appDbContext.CompanyCompanies.Update(company);

        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = "CompanyUsageLimitOffsetProfile Updated"
            });
    }

    #endregion

    #region Company Api Keys

    /// <summary>
    /// Get Company issued Api Keys.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCompanyPublicApiKeysResponse>> GetCompanyPublicApiKeys(
        [FromBody]
        GetCompanyPublicApiKeysRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var usageLimitOffsetProfile = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .Where(c => c.Id == request.CompanyId)
            .Select(c => c.UsageLimitOffsetProfile)
            .FirstOrDefaultAsync();
        var apiCallLimitOffset = usageLimitOffsetProfile?.ApiCallLimitOffSet ?? 0;

        var apiKeys = await _appDbContext.CompanyAPIKeys.Where(x => x.CompanyId == request.CompanyId).Select(
            x => new CompanyPublicApiKeyDto
            {
                APIKey = x.APIKey,
                CallLimit = x.CallLimit,
                CallLimitOffset = apiCallLimitOffset,
                Calls = x.Calls,
                CreatedAt = x.CreatedAt,
            }).ToListAsync();

        return Ok(
            new GetCompanyPublicApiKeysResponse
            {
                ApiKeys = apiKeys
            });
    }

    /// <summary>
    /// Issue a new Company public Api Key when api not exist.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<IssueCompanyPublicApiKeyResponse>> IssueCompanyPublicApiKey(
        [FromBody]
        IssueCompanyPublicApiKeyRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var isCompanyExist = await _appDbContext.CompanyCompanies.AnyAsync(x => x.Id == request.CompanyId);

        if (!isCompanyExist)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "The Company not found."
                });
        }

        var isCompanyHaveApiKeys =
            await _appDbContext.CompanyAPIKeys.AnyAsync(x => x.CompanyId == request.CompanyId);

        if (isCompanyHaveApiKeys)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "The Company already have api key issued"
                });
        }

        var keySeed = Guid.NewGuid().ToString();

        var apiKey = string.Empty;

        using (var sha256 = SHA256.Create())
        {
            byte[] saltedPasswordAsBytes = Encoding.UTF8.GetBytes(keySeed);
            apiKey = RemoveSpecialCharacters(Convert.ToBase64String(sha256.ComputeHash(saltedPasswordAsBytes)));
        }

        var companyApiKey = new CompanyAPIKey
        {
            APIKey = apiKey,
            CompanyId = request.CompanyId,
            Permissions = new List<string>()
        };
        _appDbContext.CompanyAPIKeys.Add(companyApiKey);

        await _appDbContext.SaveChangesAsync();

        return Ok(
            new IssueCompanyPublicApiKeyResponse
            {
                ApiKey = apiKey
            });
    }

    /// <summary>
    /// Delete a public Api Key.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> DeleteCompanyPublicApiKey(
        [FromBody]
        DeleteCompanyPublicApiKeyRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var apiKey = await _appDbContext.CompanyAPIKeys.FirstOrDefaultAsync(
            x => x.CompanyId == request.CompanyId && x.APIKey == request.ApiKey);

        if (apiKey == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "The Api Key Does not exist."
                });
        }

        _appDbContext.CompanyAPIKeys.Remove(apiKey);

        await _appDbContext.SaveChangesAsync();

        return Ok(
            new ResponseViewModel()
            {
                message = "Api Key Deleted"
            });
    }

    /// <summary>
    /// Set the Company Trial days.
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(200)]
    public async Task<ActionResult<ResponseViewModel>> SetCompanyTrialDays(
        [FromBody]
        SetCompanyTrialDaysRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);

        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company Not Found"
                });
        }

        if (request.TrialDays is 0)
        {
            request.TrialDays = null;
        }

        company.SubscriptionTrialDays = request.TrialDays;

        await _appDbContext.SaveChangesAsync();

        return Ok(
            new ResponseViewModel()
            {
                message = "Trial days updated"
            });
    }

    #endregion

    #region Company Staff

    /// <summary>
    /// Get Company Staffs.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetCompanyStaffsResponse>> GetCompanyStaffs(
        [FromBody]
        GetCompanyStaffsRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var staffs = await _appDbContext.UserRoleStaffs
            .Where(x => x.CompanyId == request.CompanyId && x.Id != 1)
            .Include(x => x.Identity)
            .OrderBy(x => x.Order)
            .ThenBy(x => x.Id)
            .Select(
                x => new CmsCompanyStaffDto
                {
                    StaffId = x.Id,
                    UserId = x.Identity.Id,
                    FirstName = x.Identity.FirstName,
                    LastName = x.Identity.LastName,
                    DisplayName = x.Identity.DisplayName,
                    UserName = x.Identity.UserName,
                    Email = x.Identity.Email,
                    EmailConfirmed = x.Identity.EmailConfirmed,
                    PhoneNumber = x.Identity.PhoneNumber,
                    Status = x.Status,
                    CreatedAt = x.Identity.CreatedAt,
                    LastLoginAt = x.Identity.LastLoginAt,
                    RoleType = x.RoleType,
                    TimeZoneInfoId = x.TimeZoneInfoId,
                    Position = x.Position,
                    Order = x.Order
                })
            .ToListAsync();

        staffs.ForEach(x => x.TimeZoneInfo = TimeZoneHelper.GetTimeZoneById(x.TimeZoneInfoId));

        var associateTeams = await _appDbContext
            .CompanyStaffTeams
            .AsNoTracking()
            .Include(x => x.Members)
            .ThenInclude(x => x.Staff)
            .Where(x => x.CompanyId == request.CompanyId)
            .Select(
                x => new CmsCompanyTeamDto
                {
                    Id = x.Id,
                    TeamName = x.TeamName,
                    TeamMemberStaffId = x.Members.Select(m => m.Staff.Id).ToList(),
                })
            .ToListAsync();

        foreach (var staff in staffs)
        {
            var teams = associateTeams.Where(x => x.TeamMemberStaffId.Any(y => y == staff.StaffId)).ToList();

            staff.AssociatedTeams = teams;
        }

        return Ok(
            new GetCompanyStaffsResponse
            {
                CompanyStaffs = staffs
            });
    }

    /// <summary>
    /// Change Company Owner.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> ChangeCompanyOwner(
        [FromBody]
        ChangeCompanyOwnerRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var companyStaffs = await _appDbContext.UserRoleStaffs
            .Where(x => x.CompanyId == request.CompanyId && x.Id != 1)
            .ToListAsync();

        foreach (var companyStaff in companyStaffs)
        {
            if (companyStaff.Id == request.StaffId)
            {
                companyStaff.Order = 0;
            }
            else
            {
                companyStaff.Order = 1;
            }
        }

        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel()
            {
                message = $"Company Owner have changed to {request.StaffId}"
            });
    }

    /// <summary>
    /// Delete Company Staff.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [Obsolete("API moved to Tenant hub")]
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> DeleteCompanyStaff(
        [FromBody]
        DeleteCompanyStaffRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var isRemoved = await _coreService.RemoveStaffData(request.CompanyId, request.StaffId);

        if (!isRemoved)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Company Staff ({request.StaffId}) cannot be deleted!"
                });
        }

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);
        await _staffHooks.OnStaffDeletedAsync(request.CompanyId, request.StaffId);
        return Ok(
            new ResponseViewModel
            {
                message = $"Company Staff ({request.StaffId}) Deleted"
            });
    }

    /// <summary>
    /// Get Reset Company Staff Password Url.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Obsolete("Use GetResetStaffPasswordUrl in Auth0 instead")]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<GetResetPasswordResponse>> GetResetStaffPasswordUrl(
        [FromBody]
        GetResetStaffPasswordUrlRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        ApplicationUser userIdentity = null;

        if (!string.IsNullOrEmpty(request.Email))
        {
            userIdentity = await _userManager.FindByEmailAsync(request.Email);
        }
        else if (!string.IsNullOrEmpty(request.UserId))
        {
            userIdentity = await _userManager.FindByIdAsync(request.UserId);
        }

        if (userIdentity == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "User not found"
                });
        }

        var code = await _userManager.GeneratePasswordResetTokenAsync(userIdentity);

        var appDomainName = _configuration.GetValue<String>("Values:AppDomainName");

        var url =
            $"{appDomainName}/reset/password?userId={userIdentity.Id}&code={HttpUtility.UrlEncode(code)}";

        return Ok(
            new GetResetPasswordResponse
            {
                Url = url
            });
    }

    /// <summary>
    /// Sync Staff Contact to Sleekflow account.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> SyncStaffContactToSleekflowAccount(
        [FromBody]
        SyncStaffContactToSleekflowAccountRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var uploadedContacts = await _coreService.SyncCompanyStaffsToSleekFlowContacts(request.CompanyId);

        if (uploadedContacts == null)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Error"
                });
        }

        var message = $"{uploadedContacts.Count} contacts updated to Sleekflow account:\n";

        for (var index = 0; index < uploadedContacts.Count; index++)
        {
            var userProfile = uploadedContacts[index];

            message += $"{index + 1}. {userProfile.FirstName} {userProfile.LastName}\n";
        }

        return Ok(
            new ResponseViewModel()
            {
                message = message
            });
    }

    /// <summary>
    /// Bulk Sync Staff Contact to Sleekflow account.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> BulkSyncStaffContactToSleekflowAccount(
        [FromBody]
        BulkSyncStaffContactToSleekflowAccountRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var contactsCount = 0;
        var companyCount = 0;
        foreach (var companyId in request.CompanyIds)
        {
            try
            {
                var contacts = await _coreService.SyncCompanyStaffsToSleekFlowContacts(companyId);
                contactsCount += contacts.Count;
                companyCount++;
            }
            catch (Exception e)
            {
                _logger.LogError(
                    e,
                    "Error during BulkSyncStaffContactToSleekflowAccount: {CompanyId}",
                    companyId);
            }
        }

        return Ok(
            new ResponseViewModel()
            {
                message = $"{companyCount} Companies, {contactsCount} Contact Imported"
            });
    }

    [HttpPost]
    public async Task<IActionResult> ResendInvitation(
        [FromBody] ResendInvitationEmailRequest resendInvitationEmailRequest)
    {
        if (ModelState.IsValid)
        {
            var companyAdminUser =
                _appDbContext.UserRoleStaffs
                    .Where(
                        s => s.CompanyId == resendInvitationEmailRequest.CompanyId &&
                             s.RoleType == StaffUserRole.Admin)
                    .Include(u => u.Identity)
                    .Include(c => c.Company)
                    .FirstOrDefault();
            var user = await _userManager.FindByIdAsync(resendInvitationEmailRequest.UserId);
            if (!user.UserName.StartsWith("invite."))
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = $"[{nameof(ResendInvitation)}]User have already completed invitation"
                    });
            }

            var code = await _userManager.GenerateUserTokenAsync(
                user,
                SleekflowTokenProviderOptions.InviteTokenProviderName,
                "InvitationResendInPowerflow");

            await _emailNotificationService.SendInvitationEmail(user, code, companyAdminUser);

            return Ok(
                new ResponseViewModel
                {
                    message = "success"
                });
        }

        return BadRequest(ModelState);
    }

    [HttpPost]
    public async Task<ActionResult<string>> ImportUserFromCsv(
        [FromBody]
        ImportUserFromCsvRequest importUserFromCsvRequest)
    {
        var param = new ManagementImportUserFromCsvInput(
            importUserFromCsvRequest.BlobNames,
            importUserFromCsvRequest.Location,
            importUserFromCsvRequest.IsEnterpriseUsers,
            importUserFromCsvRequest.CompanyId
        );
        var response = await _managementImportUserApi.ManagementImportUserImportUserFromCsvPostAsync(managementImportUserFromCsvInput: param);
        return Ok(
            new ResponseViewModel
            {
                message = response.Data.TaskId
            });
    }

    [HttpPost]
    public async Task<ActionResult<ImportUserProgress>> GetImportUserProgress(
        [FromBody]
        GetImportUserProgressRequest getImportUserProgressRequest
    )

    {
        var param = new ManagementGetImportUserProgressInput(
            getImportUserProgressRequest.TaskId);
        var response =
            await _managementImportUserApi.ManagementImportUserGetImportUserProgressPostAsync(
                managementGetImportUserProgressInput: param);
        return Ok(response.Data.Progress);
    }

    #endregion

    #endregion

    #region Facebook

    /// <summary>
    /// Get Facebook Connected Messenger Instances.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetFacebookConnectedResponse>> GetFacebookConnectedMessengerInstances()
    {
        var response = new GetFacebookConnectedResponse();

        var connectedMessengers = await _appDbContext.ConfigFacebookConfigs
            .Where(x => x.SubscribedFields.Contains("messages"))
            .Select(
                x => new FacebookConnectedDto
                {
                    CompanyId = x.CompanyId,
                    CompanyName =
                        _appDbContext.CompanyCompanies.Where(c => c.Id == x.CompanyId).Select(c => c.CompanyName)
                            .FirstOrDefault(),
                    SubscriptionPlanId = _appDbContext.CompanyBillRecords
                        .Where(b => b.CompanyId == x.CompanyId)
                        .Where(
                            b => ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId) &&
                                 b.PeriodStart < DateTime.UtcNow)
                        .OrderByDescending(br => br.created)
                        .ThenByDescending(br => br.PayAmount)
                        .Select(br => br.SubscriptionPlanId)
                        .FirstOrDefault(),
                    FacebookPageId = x.PageId,
                    FacebookPageName = x.PageName,
                    LastStatus = x.Status.ToString(),
                    CreatedAt = x.ConnectedDateTime,
                    Type = "Connected Messenger"
                })
            .OrderBy(x => x.CompanyId)
            .ToListAsync();

        var connectedLeadAds = await _appDbContext.ConfigFacebookConfigs
            .Where(x => x.SubscribedFields.Contains("leadgen"))
            .Select(
                x => new FacebookConnectedDto
                {
                    CompanyId = x.CompanyId,
                    CompanyName =
                        _appDbContext.CompanyCompanies.Where(c => c.Id == x.CompanyId).Select(c => c.CompanyName)
                            .FirstOrDefault(),
                    SubscriptionPlanId = _appDbContext.CompanyBillRecords
                        .Where(b => b.CompanyId == x.CompanyId)
                        .Where(
                            b => ValidSubscriptionPlan.SubscriptionPlan.Contains(b.SubscriptionPlanId) &&
                                 b.PeriodStart < DateTime.UtcNow)
                        .OrderByDescending(br => br.created)
                        .ThenByDescending(br => br.PayAmount)
                        .Select(br => br.SubscriptionPlanId)
                        .FirstOrDefault(),
                    FacebookPageId = x.PageId,
                    FacebookPageName = x.PageName,
                    LastStatus = x.Status.ToString(),
                    CreatedAt = x.ConnectedDateTime,
                    Type = "Lead Ads"
                })
            .OrderBy(x => x.CompanyId)
            .ToListAsync();

        response.FacebookInstances.AddRange(connectedMessengers);
        response.FacebookInstances.AddRange(connectedLeadAds);

        return Ok(response);
    }

    /// <summary>
    /// Sync Facebook History by page.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> SyncFacebookHistory(
        [FromBody]
        SyncWFacebookHistoryRequest request)
    {
        // if (!ModelState.IsValid) return BadRequest(new ResponseViewModel { message = "Invalid Form" });
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var facebookConfigs = await _appDbContext.ConfigFacebookConfigs
            .Where(x => x.CompanyId == request.CompanyId && x.PageId == request.PageId).FirstOrDefaultAsync();

        if (facebookConfigs == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Facebook config not found."
                });
        }

        BackgroundJob.Enqueue<IFacebookInstagramMessageService>(
            x => x.FetchConversationsBackground(request.PageId, 100, true));

        facebookConfigs.Status = FacebookStatus.Syncing;
        await _appDbContext.SaveChangesAsync();

        return Ok(
            new ResponseViewModel
            {
                message = $"Facebook Messenger(PageId:${request.PageId}) history Sync has started in background."
            });
    }

    #endregion

    #region Twilio

    /// <summary>
    /// Get Twilio Usage.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetTwilioUsageResponse>> GetTwilioUsage()
    {
        var result = new GetTwilioUsageResponse();

        var coreTwilioConfig = await _appDbContext.CoreTwilioConfigs.FirstOrDefaultAsync();
        TwilioClient.Init(coreTwilioConfig.AccountSID, coreTwilioConfig.AccountSecret);
        var accounts = (await AccountResource.ReadAsync()).ToList();

        var subAccountSids = accounts.Select(a => a.Sid).ToList();

        var subAccountTwilioUsages = await _appDbContext
            .CompanyTwilioUsageRecords
            .Join(
                _appDbContext.CompanyCompanies,
                t => t.CompanyId,
                c => c.Id,
                (t, c) =>
                    new CmsTwilioUsageDto
                    {
                        CompanyId = c.Id,
                        CompanyName = c.CompanyName,
                        TwilioAccountId = t.TwilioAccountId,
                        TwilioUsageRecordId = t.Id,
                        TotalCreditValue = t.TotalCreditValue,
                        TotalPrice = t.TotalPrice,
                        Currency = t.Currency,
                    })
            .OrderByDescending(x => x.TwilioAccountId)
            .ToListAsync();

        subAccountTwilioUsages =
            subAccountTwilioUsages.Where(x => subAccountSids.Contains(x.TwilioAccountId)).ToList();

        var subAccountWhatsappConfigs = await _appDbContext.ConfigWhatsAppConfigs
            .Where(x => x.IsSubaccount)
            .Select(
                x => new
                {
                    WhatsAppConfigId = x.Id,
                    WhatsAppSender = x.WhatsAppSender,
                    ChannelName = x.Name,
                    ConnectedDateTime = x.ConnectedDateTime,
                    TwilioAccountId = x.TwilioAccountId,
                    CompanyId = x.CompanyId
                })
            .ToListAsync();

        var connectedSubAccounts = (from twilioUsage in subAccountTwilioUsages
                                    join whatsappConfig in subAccountWhatsappConfigs
                                        on new
                                        {
                                            TwilioAccountId = twilioUsage.TwilioAccountId,
                                            CompanyId = twilioUsage.CompanyId
                                        } equals new
                                        {
                                            TwilioAccountId = whatsappConfig.TwilioAccountId,
                                            CompanyId = whatsappConfig.CompanyId
                                        }
                                        into gj
                                    from wsaConfig in gj.DefaultIfEmpty()
                                    select new CmsTwilioUsageDto
                                    {
                                        CompanyId = twilioUsage.CompanyId,
                                        CompanyName = twilioUsage.CompanyName,
                                        TwilioAccountId = twilioUsage.TwilioAccountId,
                                        TwilioUsageRecordId = twilioUsage.TwilioUsageRecordId,
                                        TotalCreditValue = twilioUsage.TotalCreditValue,
                                        TotalPrice = twilioUsage.TotalPrice,
                                        Currency = twilioUsage.Currency,
                                        WhatsAppConfigId = wsaConfig?.WhatsAppConfigId ?? default,
                                        WhatsAppSender = wsaConfig?.WhatsAppSender,
                                        ChannelName = wsaConfig?.ChannelName,
                                        ConnectedDateTime = wsaConfig?.ConnectedDateTime ?? default,
                                    }).ToList();

        foreach (var connectedSubAccount in connectedSubAccounts)
        {
            var twilioSubAccount = accounts.First(x => x.Sid == connectedSubAccount.TwilioAccountId);

            connectedSubAccount.FriendlyName = twilioSubAccount.FriendlyName;
            connectedSubAccount.SubAccountStatus = twilioSubAccount.Status.ToString();
            connectedSubAccount.DateCreated = twilioSubAccount.DateCreated;
            connectedSubAccount.DateUpdated = twilioSubAccount.DateUpdated;
        }

        var notConnectedSubAccounts = accounts
            .Where(x => !subAccountTwilioUsages.Select(c => c.TwilioAccountId).Contains(x.Sid))
            .Select(
                x => new CmsTwilioUsageDto
                {
                    TwilioAccountId = x.Sid,
                    FriendlyName = x.FriendlyName,
                    SubAccountStatus = x.Status.ToString(),
                    DateCreated = x.DateCreated,
                    DateUpdated = x.DateUpdated,
                })
            .ToList();

        var notSubAccount = await _appDbContext
            .ConfigWhatsAppConfigs
            .Where(x => !x.IsSubaccount)
            .Join(
                _appDbContext.CompanyCompanies,
                t => t.CompanyId,
                c => c.Id,
                (whatsAppConfig, company) =>
                    new CmsTwilioUsageDto
                    {
                        CompanyId = whatsAppConfig.CompanyId,
                        CompanyName = company.CompanyName,
                        WhatsAppConfigId = whatsAppConfig.Id,
                        ChannelName = whatsAppConfig.Name,
                        WhatsAppSender = whatsAppConfig.WhatsAppSender,
                        TwilioAccountId = whatsAppConfig.TwilioAccountId,
                        ConnectedDateTime = whatsAppConfig.ConnectedDateTime,
                    })
            .ToListAsync();

        result.ConnectedSubAccounts = connectedSubAccounts.ToList();
        result.NotConnectedSubAccounts = notConnectedSubAccounts;
        result.NonSubAccounts = notSubAccount;

        return Ok(result);
    }

    /// <summary>
    /// Get Or Update Twilio Usage Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<GetTwilioUsageResponse>> GetOrUpdateTwilioUsageRecord(
        [FromBody]
        GetOrUpdateTwilioUsageRecordRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var twilioUsage = await _companyUsageService.GetTwilioUsage(request.CompanyId, request.TwilioAccountId);

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = "Twilio Usage Record Update"
            });
    }

    /// <summary>
    /// Delete Twilio Usage Record.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<GetTwilioUsageResponse>> DeleteTwilioUsageRecord(
        [FromBody]
        DeleteTwilioUsageRecordRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var twilioUsageRecord = await _appDbContext.CompanyTwilioUsageRecords.FirstOrDefaultAsync(
            x => x.Id == request.TwilioUsageRecordId &&
                 x.CompanyId == request.CompanyId && x.TwilioAccountId == request.TwilioAccountId);

        if (twilioUsageRecord == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Twilio usage record not found"
                });
        }

        _appDbContext.CompanyTwilioUsageRecords.Remove(twilioUsageRecord);

        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = "Twilio usage record deleted."
            });
    }

    /// <summary>
    /// Set Twilio Verification Status.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> SetTwilioVerificationStatus(
        [FromBody]
        SetTwilioVerificationStatusRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var record = await _appDbContext.CompanyTwilioUsageRecords
            .FirstOrDefaultAsync(
                x => x.TwilioAccountId == request.TwilioAccountId && x.CompanyId == request.CompanyId);

        if (record == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Twilio usage records does not exist."
                });
        }

        record.IsVerified = request.IsVerified;

        await _appDbContext.SaveChangesAsync();

        return Ok(
            new ResponseViewModel()
            {
                message = "Twilio usage records verification status updated."
            });
    }

    /// <summary>
    /// Add Twilio Credit to Company.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<SetTwilioCreditResponse>> AddTwilioCredit(
        [FromBody]
        AddTwilioCreditRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        await _twilioService.Topup(
            request.TwilioAccountId,
            request.AddCreditValue,
            TwilioTopUpMethod.Powerflow,
            internalUserId: user.Id,
            internalInvoiceId: request.InvoiceId,
            isInternalTestingUse: request.IsInternalTestingUse);

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel()
            {
                message = "Topup Added"
            });
    }

    /// <summary>
    /// Get Twilio Top up Log.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetTwilioTopUpLogResponse>> GetTwilioTopUpLog(
        [FromBody]
        GetTwilioTopUpLogRequest request)
    {
        List<TwilioTopUpLogDto> topUpLogs = await _appDbContext.TwilioTopUpLogs
            .WhereIf(request.TwilioAccountSid != null, x => x.TwilioAccountSid == request.TwilioAccountSid)
            .WhereIf(request.TwilioUsageRecordId != null, x => x.TwilioUsageRecordId == request.TwilioUsageRecordId)
            .Select(
                x => new TwilioTopUpLogDto()
                {
                    Id = x.Id,
                    CompanyId = x.CompanyId,
                    CompanyName = _appDbContext.CompanyCompanies.First(c => c.Id == x.CompanyId).CompanyName,
                    TwilioUsageRecordId = x.TwilioUsageRecordId,
                    TwilioAccountSid = x.TwilioAccountSid,
                    TopUpAmount = x.TopUpAmount,
                    InternalUserId = x.InternalUserId,
                    InternalUserName =
                        _appDbContext.Users.First(u => u.Id == x.InternalUserId).DisplayName ?? null,
                    AmountTotal = x.AmountTotal,
                    InvoiceId = x.InvoiceId,
                    IsInternalTestingUse = x.IsInternalTestingUse,
                    CustomerId = x.CustomerId,
                    CustomerEmail = x.CustomerEmail,
                    Currency = x.Currency,
                    Method = x.Method,
                    CreatedAt = x.CreatedAt,
                })
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync();

        return Ok(
            new GetTwilioTopUpLogResponse()
            {
                TwilioTopUpLogs = topUpLogs
            });
    }

    /// <summary>
    /// Set Activate Twilio Sub Account.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<UpdateTwilioSubAccountResponse>> SetActivateTwilioSubAccount(
        [FromBody]
        UpdateTwilioSubAccountRequest request)
    {
        // if (!ModelState.IsValid) return BadRequest(new ResponseViewModel { message = "Invalid Form" });
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var coreTwilioConfig = await _appDbContext.CoreTwilioConfigs.FirstOrDefaultAsync();
        TwilioClient.Init(coreTwilioConfig.AccountSID, coreTwilioConfig.AccountSecret);

        var account = await AccountResource.UpdateAsync(
            status: AccountResource.StatusEnum.Active,
            pathSid: request.TwilioAccountId);

        return Ok(
            new UpdateTwilioSubAccountResponse
            {
                AccountSid = account.Sid,
                AccountStatus = account.Status
            });
    }

    /// <summary>
    /// Set Suspend Twilio Sub Account.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<UpdateTwilioSubAccountResponse>> SetSuspendTwilioSubAccount(
        [FromBody]
        UpdateTwilioSubAccountRequest request)
    {
        // if (!ModelState.IsValid) return BadRequest(new ResponseViewModel { message = "Invalid Form" });
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var coreTwilioConfig = await _appDbContext.CoreTwilioConfigs.FirstOrDefaultAsync();
        TwilioClient.Init(coreTwilioConfig.AccountSID, coreTwilioConfig.AccountSecret);

        var account = await AccountResource.UpdateAsync(
            status: AccountResource.StatusEnum.Suspended,
            pathSid: request.TwilioAccountId);

        return Ok(
            new UpdateTwilioSubAccountResponse
            {
                AccountSid = account.Sid,
                AccountStatus = account.Status
            });
    }

    /// <summary>
    ///  Add Twilio Sub Account.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult> AddTwilioSubAccount([FromBody] AddTwilioSubAccountRequest request)
    {
        // if (!ModelState.IsValid) return BadRequest(new ResponseViewModel() { message = "Invalid Form" });
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        // Add prefix to phone number
        request.PhoneNumber = "whatsapp:+" + request.PhoneNumber;

        var companyUsage = await _companyUsageService.GetCompanyUsage(request.CompanyId);

        if (companyUsage.totalChannelAdded > companyUsage.MaximumNumberOfChannel)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message =
                        $"Your subscription plan only could add {companyUsage.MaximumNumberOfChannel} channels"
                });
        }

        var existing = await _appDbContext.ConfigWhatsAppConfigs.FirstOrDefaultAsync(
            x => x.CompanyId == request.CompanyId && x.TwilioAccountId == request.AccountSID &&
                 x.WhatsAppSender == request.PhoneNumber);

        if (existing == null)
        {
            existing = new WhatsAppConfig();
            _appDbContext.ConfigWhatsAppConfigs.Add(existing);
        }

        existing.Name = request.Name;
        existing.CompanyId = request.CompanyId;
        existing.TwilioAccountId = request.AccountSID;
        existing.TwilioSecret = request.AccountSecret;
        existing.WhatsAppSender = request.PhoneNumber;
        existing.ChannelIdentityId = PhoneNumberHelper.NormalizePhoneNumber(existing.WhatsAppSender);
        existing.IsSubaccount = true;

        TwilioClient.Init(existing.TwilioAccountId, existing.TwilioSecret);

        var twilioBill = await _appDbContext.CompanyTwilioUsageRecords
            .Where(x => x.CompanyId == request.CompanyId && x.TwilioAccountId == request.AccountSID)
            .FirstOrDefaultAsync();

        try
        {
            var account = await AccountResource.FetchAsync(pathSid: existing.TwilioAccountId);

            if (account.Status == AccountResource.StatusEnum.Active)
            {
                await _appDbContext.SaveChangesAsync();

                if (await _appDbContext.CompanySandboxes.AnyAsync(x => x.CompanyId == request.CompanyId))
                {
                    BackgroundJob.Enqueue<ICompanyService>(x => x.DeleteSandbox(request.CompanyId));
                }

                // var domainName = _configuration.GetValue<String>("Values:DomainName");

                // var callbackUrl = new Uri($"{domainName}/twilio/usage");
                // string triggerValue = $"+{existing.TriggerValue.Value}";

                // var trigger = TriggerResource.Create(callbackUrl,
                //    triggerValue,
                //    TriggerResource.UsageCategoryEnum.Totalprice,
                //    existing.TwilioAccountId, Twilio.Http.HttpMethod.Post,
                //    "Trigger if $20 credit left",
                //    TriggerResource.RecurringEnum.Alltime,
                //    TriggerResource.TriggerFieldEnum.Price);

                // existing.SID = trigger.Sid;
                // await _appDbContext.SaveChangesAsync();

                // Get twilio usage
                await _companyUsageService.GetTwilioUsage(request.CompanyId, existing.TwilioAccountId);

                var companyName = await _appDbContext.CompanyCompanies.Where(x => x.Id == request.CompanyId)
                    .Select(x => x.CompanyName).FirstOrDefaultAsync();

                try
                {
                    await _twilioService.AddNewTemplate(
                        existing.TwilioAccountId,
                        existing.TwilioSecret,
                        new CreateTemplateViewModel
                        {
                            Name = "optin_1_",
                            Category = "ACCOUNT_UPDATE",
                            Languages = new List<LanguageElement>
                            {
                                new LanguageElement
                                {
                                    Language = "en",
                                    Content =
                                        $"*Notification*\nHello there! You received a new message from {companyName}.",
                                    Components = new List<Component>
                                    {
                                        new Component
                                        {
                                            Type = "BUTTONS",
                                            Buttons = new List<Button>
                                            {
                                                new Button
                                                {
                                                    Text = "Read more", Type = "QUICK_REPLY"
                                                }
                                            }
                                        }
                                    }
                                },
                                new LanguageElement
                                {
                                    Language = "zh_HK",
                                    Content = $"*新訊息通知*\n你好! 你收到來自{companyName}的新訊息。",
                                    Components = new List<Component>
                                    {
                                        new Component
                                        {
                                            Type = "BUTTONS",
                                            Buttons = new List<Button>
                                            {
                                                new Button
                                                {
                                                    Text = "查看更多", Type = "QUICK_REPLY"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        });

                    await _twilioService.AddNewTemplate(
                        existing.TwilioAccountId,
                        existing.TwilioSecret,
                        new CreateTemplateViewModel
                        {
                            Name = "optin_2_",
                            Category = "ACCOUNT_UPDATE",
                            Languages = new List<LanguageElement>
                            {
                                new LanguageElement
                                {
                                    Language = "en",
                                    Content = $"Hello there! You received an update from {companyName}.",
                                    Components = new List<Component>
                                    {
                                        new Component
                                        {
                                            Type = "BUTTONS",
                                            Buttons = new List<Button>
                                            {
                                                new Button
                                                {
                                                    Text = "Read more", Type = "QUICK_REPLY"
                                                }
                                            }
                                        }
                                    }
                                },
                                new LanguageElement
                                {
                                    Language = "zh_HK",
                                    Content = $"你好! 以下為一則來自{companyName}的新訊息。",
                                    Components = new List<Component>
                                    {
                                        new Component
                                        {
                                            Type = "BUTTONS",
                                            Buttons = new List<Button>
                                            {
                                                new Button
                                                {
                                                    Text = "查看更多", Type = "QUICK_REPLY"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        });

                    await _twilioService.AddNewTemplate(
                        existing.TwilioAccountId,
                        existing.TwilioSecret,
                        new CreateTemplateViewModel
                        {
                            Name = "optin_3",
                            Category = "ACCOUNT_UPDATE",
                            Languages = new List<LanguageElement>
                            {
                                new LanguageElement
                                {
                                    Language = "en",
                                    Content =
                                        $"*Notification*\nHello there! You received a new message from {existing.Name}.",
                                    Components = new List<Component>
                                    {
                                        new Component
                                        {
                                            Type = "BUTTONS",
                                            Buttons = new List<Button>
                                            {
                                                new Button
                                                {
                                                    Text = "Read more", Type = "QUICK_REPLY"
                                                }
                                            }
                                        }
                                    }
                                },
                                new LanguageElement
                                {
                                    Language = "zh_HK",
                                    Content = $"*新訊息通知*\n你好! 你收到來自{existing.Name}的新訊息。",
                                    Components = new List<Component>
                                    {
                                        new Component
                                        {
                                            Type = "BUTTONS",
                                            Buttons = new List<Button>
                                            {
                                                new Button
                                                {
                                                    Text = "查看更多", Type = "QUICK_REPLY"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        });

                    await _twilioService.AddNewTemplate(
                        existing.TwilioAccountId,
                        existing.TwilioSecret,
                        new CreateTemplateViewModel
                        {
                            Name = "optin_4",
                            Category = "ACCOUNT_UPDATE",
                            Languages = new List<LanguageElement>
                            {
                                new LanguageElement
                                {
                                    Language = "en",
                                    Content = $"Hello there! You received an update from {existing.Name}.",
                                    Components = new List<Component>
                                    {
                                        new Component
                                        {
                                            Type = "BUTTONS",
                                            Buttons = new List<Button>
                                            {
                                                new Button
                                                {
                                                    Text = "Read more", Type = "QUICK_REPLY"
                                                }
                                            }
                                        }
                                    }
                                },
                                new LanguageElement
                                {
                                    Language = "zh_HK",
                                    Content = $"你好! 以下為一則來自{existing.Name}的新訊息。",
                                    Components = new List<Component>
                                    {
                                        new Component
                                        {
                                            Type = "BUTTONS",
                                            Buttons = new List<Button>
                                            {
                                                new Button
                                                {
                                                    Text = "查看更多", Type = "QUICK_REPLY"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        });

                    await _twilioService.AddNewTemplate(
                        existing.TwilioAccountId,
                        existing.TwilioSecret,
                        new CreateTemplateViewModel
                        {
                            Name = "greetings_1",
                            Category = "ACCOUNT_UPDATE",
                            Languages = new List<LanguageElement>
                            {
                                new LanguageElement
                                {
                                    Language = "en", Content = "Hello {{1}}"
                                },
                                new LanguageElement
                                {
                                    Language = "zh_HK", Content = "你好 {{1}}"
                                }
                            }
                        });
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Add default template error: {CompanyId}",
                        request.CompanyId);
                }

                await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

                return Ok(
                    new ResponseViewModel
                    {
                        message = $"Twilio Sub Account Added."
                    });
            }

            return BadRequest(account);
        }
        catch (Exception ex)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Failed to authenticate, ex: {ex.Message}"
                });
        }
    }

    #endregion

    #region Helpers

    private class ImportUserProfileObject
    {
        public List<ImportHeader> _headers { get; set; }

        public List<string> _fields { get; set; }

        public ImportUserProfileObject(List<ImportHeader> headers, List<string> fields)
        {
            _headers = headers;
            _fields = fields;
        }

        public ImportHeader GetHeaderFromList(string field)
        {
            try
            {
                return _headers[_fields.FindIndex(x => x == field)];
            }
            catch (Exception)
            {
                return null;
            }
        }

        public string GetValueFromList(string key)
        {
            try
            {
                return _fields[GetIndexFromHeader(key).Value];
            }
            catch (Exception)
            {
                return null;
            }
        }

        public int? GetIndexFromHeader(string key)
        {
            try
            {
                return _headers.FindIndex(x => x.HeaderName.ToLower() == key.ToLower());
            }
            catch (Exception)
            {
                return null;
            }
        }
    }

    private ImportSpreadsheet SerializeCSV(ImportSpreadsheetViewModel importSpreadsheetViewModel)
    {
        ImportSpreadsheet importSpreadsheet = new ImportSpreadsheet()
        {
            ImportName = importSpreadsheetViewModel.ImportName
        };

        foreach (IFormFile file in importSpreadsheetViewModel.files)
        {
            using (var reader = new StreamReader(file.OpenReadStream()))
            {
                while (reader.Peek() >= 0)
                {
                    if (importSpreadsheet.headers.Count == 0)
                    {
                        int columnNumber = 0;
                        var headers = reader.ReadLine().Split(",", StringSplitOptions.RemoveEmptyEntries).ToList();

                        foreach (var header in headers)
                        {
                            importSpreadsheet.headers.Add(
                                new ImportHeader
                                {
                                    HeaderName = header,
                                    IsValid = true,
                                    CsvFileColumnNumber = columnNumber
                                });
                            columnNumber++;
                        }
                    }
                    else
                    {
                        var line = reader.ReadLine();
                        var records = line.Split(",").ToList();
                        var isNull = true;

                        foreach (var record in records)
                        {
                            if (!string.IsNullOrEmpty(record))
                            {
                                isNull = false;

                                break;
                            }
                        }

                        if (string.IsNullOrEmpty(line) || isNull)
                        {
                            break;
                        }

                        var parser =
                            new TextFieldParser(new StringReader(line));

                        // You can also read from a file
                        // TextFieldParser parser = new TextFieldParser("mycsvfile.csv");
                        parser.HasFieldsEnclosedInQuotes = true;
                        parser.SetDelimiters(",");

                        string[] fields;

                        while (!parser.EndOfData)
                        {
                            fields = parser.ReadFields();
                            importSpreadsheet.records.Add(
                                new ImportRecord
                                {
                                    fields = fields.ToList()
                                });
                        }

                        parser.Close();
                    }
                }
            }
        }

        return importSpreadsheet;
    }

    private List<Dictionary<string, string>> ExportResult(ImportSpreadsheet sheet)
    {
        var result = new List<Dictionary<string, string>>();
        foreach (var record in sheet.records)
        {
            var keys = sheet.headers.Select(h => h.HeaderName).ToArray();
            var resultRecord = new Dictionary<string, string>();

            for (int i = 0; i < sheet.headers.Count; i++)
            {
                resultRecord.Add(keys[i], record.fields[i]);
            }

            result.Add(resultRecord);
        }

        return result;
    }

    private string ExportCsv(ImportSpreadsheet sheet)
    {
        string result = string.Empty;

        // Write header.
        result = string.Join(", ", sheet.headers.Select(h => h.HeaderName).ToArray()) + "\n";

        foreach (var record in sheet.records)
        {
            result += string.Join(", ", record.fields) + "\n";
        }

        return result;
    }

    private static string RemoveSpecialCharacters(string str)
    {
        return Regex.Replace(str, "[^a-zA-Z0-9_.]+", string.Empty, RegexOptions.Compiled);
    }

    private static void AddFilterExpression(
        List<Expression<Func<Company, bool>>> listOfExpression,
        bool condition,
        Expression<Func<Company, bool>> expression)
    {
        if (condition)
        {
            listOfExpression.Add(expression);
        }
    }

    private async Task<List<CmsCompanyFlowHubData>> GetCompaniesFlowHubDataAsync(
        bool isFlowHubDataRequired,
        bool isAllowCacheFlowHubData)
    {
        if (!isFlowHubDataRequired)
        {
            return [];
        }

        List<CmsCompanyFlowHubData> companiesFlowHubData = new();

        if (isAllowCacheFlowHubData)
        {
            var companiesFlowHubDataDataCache =
                await _cacheManagerService.GetCacheWithConstantKeyAsync(CmsCacheKeyHelper.GetCmsCompaniesFlowHubData());

            if (!string.IsNullOrWhiteSpace(companiesFlowHubDataDataCache))
            {
                companiesFlowHubData =
                    JsonConvert.DeserializeObject<List<CmsCompanyFlowHubData>>(companiesFlowHubDataDataCache);
            }

            if (companiesFlowHubData != null && companiesFlowHubData.Count != 0)
            {
                return companiesFlowHubData;
            }
        }

        companiesFlowHubData = await _flowHubService.GetCompaniesFlowHubData();
        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            CmsCacheKeyHelper.GetCmsCompaniesFlowHubData(),
            companiesFlowHubData,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings);

        return companiesFlowHubData;
    }

    private async Task<List<CmsCompanyIntelligentHubData>> GetCompaniesIntelligentHubDataAsync(
        bool isIntelligentHubDataRequired,
        bool isAllowCacheIntelligentHubData)
    {
        if (!isIntelligentHubDataRequired)
        {
            return [];
        }

        List<CmsCompanyIntelligentHubData> companiesIntelligentHubData = new();

        if (isAllowCacheIntelligentHubData)
        {
            var companiesIntelligentHubDataCache =
                await _cacheManagerService.GetCacheWithConstantKeyAsync(
                    CmsCacheKeyHelper.GetCmsCompaniesIntelligentHubData());

            if (!string.IsNullOrWhiteSpace(companiesIntelligentHubDataCache))
            {
                companiesIntelligentHubData =
                    JsonConvert.DeserializeObject<List<CmsCompanyIntelligentHubData>>(companiesIntelligentHubDataCache);
            }

            if (companiesIntelligentHubData != null && companiesIntelligentHubData.Count != 0)
            {
                return companiesIntelligentHubData;
            }
        }

        companiesIntelligentHubData = await _intelligentHubService.GetCompaniesIntelligentHubDataAsync();
        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            CmsCacheKeyHelper.GetCmsCompaniesIntelligentHubData(),
            companiesIntelligentHubData,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings);

        return companiesIntelligentHubData;
    }

    // Helper to fetch company with BillRecords or return BadRequest if not found
    private async Task<(Company company, ActionResult<ResponseViewModel> errorResult)>
        GetCompanyWithBillRecordsOrBadRequestAsync(string companyId)
    {
        var company = await _appDbContext.CompanyCompanies
            .Where(x => x.Id == companyId)
            .Include(x => x.BillRecords)
            .FirstOrDefaultAsync();

        if (company == null)
        {
            return (null, BadRequest(
                new ResponseViewModel
                {
                    message = "Company does not exist."
                }));
        }

        return (company, null);
    }

    /// <summary>
    /// Calculates payment terms based on individual parameters.
    /// </summary>
    /// <param name="paymentTerms">The payment terms value</param>
    /// <param name="isIrregularPlan">Whether this is an irregular plan</param>
    /// <param name="paymentIntervalType">The payment interval type</param>
    /// <param name="subscriptionPlan">The subscription plan</param>
    /// <returns>The calculated payment terms in days</returns>
    private static int CalculatePaymentTerms(
        int? paymentTerms,
        bool isIrregularPlan,
        string paymentIntervalType,
        SubscriptionPlan subscriptionPlan)
    {
        if (paymentTerms is > 0)
        {
            return paymentTerms.Value;
        }

        if (isIrregularPlan)
        {
            return paymentIntervalType == PaymentIntervalType.Yearly ? 30 : 14;
        }

        return subscriptionPlan.SubscriptionInterval == "yearly" ? 30 : 14;
    }

    #endregion

    #region Stripe

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> ToggleStripeIntegration(
        [FromBody]
        ToggleStripeIntegrationRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);
        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company not found"
                });
        }

        company.IsStripeIntegrationEnabled = request.IsEnabled;

        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = "Stripe integration flag updated."
            });
    }

    #endregion

    #region Shopify

    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<ResponseViewModel>> MigrateShopifyConfigToV2(
        [FromBody]
        MigrateShopifyConfigToV2Request request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        // Get the ShopifyConfig from the database
        var shopifyConfig = await _appDbContext.ConfigShopifyConfigs
            .FirstOrDefaultAsync(x => x.Id == request.ShopifyConfigId);

        if (shopifyConfig == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "ShopifyConfig not found"
                });
        }

        // Check if the current version is 1 or null
        if (shopifyConfig.Version.HasValue && shopifyConfig.Version.Value != 1)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = $"ShopifyConfig version must be 1 or null to migrate to V2. Current version: {shopifyConfig.Version}"
                });
        }

        try
        {
            // Call the existing RegisterNewWebhooksV2 method
            await _shopifyService.RegisterNewWebhooksV2(shopifyConfig.CompanyId, shopifyConfig.Id);

            // Update the version to 2
            shopifyConfig.Version = 2;

            await _appDbContext.SaveChangesAsync();

            return Ok(
                new ResponseViewModel
                {
                    message = "ShopifyConfig successfully migrated to V2"
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to migrate ShopifyConfig {ShopifyConfigId} to V2 for company {CompanyId}",
                request.ShopifyConfigId,
                shopifyConfig.CompanyId);

            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Failed to migrate ShopifyConfig to V2: {ex.Message}"
                });
        }
    }

    #endregion

    #region Contact

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> ToggleExpressImport(
        [FromBody]
        ToggleExpressImportRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);
        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company not found"
                });
        }

        company.IsExpressImportEnabled = request.IsEnabled;

        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = "Express import flag updated."
            });
    }

    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> ToggleExperimentalImport(
        [FromBody] ToggleExperimentalImportRequest request)
    {
        var user = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (user == null)
        {
            return Unauthorized();
        }

        var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == request.CompanyId);
        if (company == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Company not found"
                });
        }

        company.IsExperimentalImportEnabled = request.IsEnabled;

        await _appDbContext.SaveChangesAsync();

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel
            {
                message = "Experimental import flag updated."
            });
    }

    #endregion

    [HttpPost]
    public async Task<ActionResult> UpdateAllOldEnterprisePlanPayAmount()
    {
        var user = await GetCurrentValidInternalUser(
            [ApplicationUserRole.InternalCmsSuperUser]);

        if (user == null)
        {
            return Unauthorized();
        }

        try
        {
            _logger.LogInformation("Starting UpdateAllOldEnterprisePlanPayAmount process");

            // Get all bill records with enterprise subscription plans
            var enterpriseBillRecords = await _appDbContext.CompanyBillRecords
                .Include(br => br.CmsSalesPaymentRecords)
                .Where(br => (br.SubscriptionPlanId == "sleekflow_enterprise" ||
                              br.SubscriptionPlanId == "sleekflow_v10_enterprise") &&
                             br.IsIrregularPlan)
                .ToListAsync();

            if (enterpriseBillRecords.Count == 0)
            {
                return Ok(
                    new ResponseViewModel
                    {
                        message = "No enterprise bill records found"
                    });
            }

            var csvData = new List<string>
            {
                "BillRecordId,PreviousPayAmount,LatestPayAmount,Currency,UpdatedAt"
            };

            var updatedBillRecordIds = new List<string>();

            // Process each bill record
            foreach (var billRecord in enterpriseBillRecords)
            {
                if (billRecord.CmsSalesPaymentRecords is null || billRecord.CmsSalesPaymentRecords.Count == 0)
                {
                    continue;
                }

                var previousPayAmount = billRecord.PayAmount;

                decimal totalSubscriptionFee = 0;
                var allCurrencies = new HashSet<string>();
                var currencyGroups = new Dictionary<string, decimal>();
                var salesPaymentRecordCurrency = "usd";

                foreach (var salesPaymentRecord in billRecord.CmsSalesPaymentRecords)
                {
                    var currency = salesPaymentRecord.Currency?.ToLower() ?? "usd";
                    allCurrencies.Add(currency);
                    currencyGroups.TryAdd(currency, 0);
                    currencyGroups[currency] += salesPaymentRecord.SubscriptionFee;
                }

                // Check if all currencies are the same
                if (allCurrencies.Count == 1)
                {
                    // All currencies are the same, use that currency
                    var singleCurrency = allCurrencies.First();
                    totalSubscriptionFee = currencyGroups[singleCurrency];
                    salesPaymentRecordCurrency = singleCurrency;
                }
                else
                {
                    // Different currencies found, convert all to USD
                    foreach (var currencyGroup in currencyGroups)
                    {
                        var convertedAmount = CurrencyConverter.ConvertToUsd(currencyGroup.Value, currencyGroup.Key);
                        totalSubscriptionFee += convertedAmount;
                    }
                }

                if (totalSubscriptionFee == 0)
                {
                    continue;
                }

                var totalSubscriptionFeeInDouble = (double) totalSubscriptionFee;
                var updatedAt = DateTime.UtcNow;

                if (billRecord.amount_due == 0)
                {
                    if (!(Math.Abs(billRecord.PayAmount - totalSubscriptionFeeInDouble) > 0.000001))
                    {
                        continue;
                    }

                    billRecord.PayAmount = totalSubscriptionFeeInDouble;
                    billRecord.currency = salesPaymentRecordCurrency;
                }
                // A special case need to sum original pay amount and total subscription fee
                else
                {
                    // bill record id 3454044130 case
                    if (billRecord.PayAmount == 0)
                    {
                        billRecord.PayAmount = totalSubscriptionFeeInDouble;
                    }
                    else
                    {
                        billRecord.PayAmount = billRecord.amount_due / 100d + totalSubscriptionFeeInDouble;
                    }
                }

                billRecord.UpdatedAt = updatedAt;
                updatedBillRecordIds.Add(billRecord.Id.ToString());

                // Add to CSV data
                csvData.Add(
                    $"{billRecord.Id},{previousPayAmount:F2},{billRecord.PayAmount:F2},{billRecord.currency},{updatedAt:yyyy-MM-dd HH:mm:ss}");
            }

            await _appDbContext.SaveChangesAsync();

            _logger.LogInformation(
                "UpdateAllOldEnterprisePlanPayAmount completed. Updated bill record ids: {UpdatedBillRecordIds}",
                string.Join(", ", updatedBillRecordIds));

            // Generate CSV content
            var csvContent = string.Join(Environment.NewLine, csvData);
            var csvBytes = Encoding.UTF8.GetBytes(csvContent);

            return File(
                csvBytes,
                "text/csv",
                $"enterprise_plan_pay_amounts_update_{DateTime.UtcNow:yyyyMMdd_HHmmss}.csv");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating old enterprise plan pay amounts");
            return BadRequest(
                new ResponseViewModel
                {
                    message = $"Failed to update old enterprise plan pay amounts: {ex.Message}"
                });
        }
    }
}
