using System.Linq;
using Microsoft.EntityFrameworkCore;
using Travis_backend.ConversationDomain.Models;

namespace Travis_backend.ConversationDomain.Extensions;

public static class ConversationQueryableExtensions
{
    public static IQueryable<Conversation> IncludeChannelRelatedEntities(this IQueryable<Conversation> query)
    {
        return query
            .Include(x => x.InstagramUser)
            .Include(x => x.facebookUser)
            .Include(x => x.InstagramUser)
            .Include(x => x.WhatsappUser)
            .Include(x => x.WhatsApp360DialogUser)
            .Include(x => x.WhatsappCloudApiUser)
            .Include(x => x.EmailAddress)
            .Include(x => x.WebClient)
            .Include(x => x.WeChatUser)
            .Include(x => x.LineUser)
            .Include(x => x.SMSUser)
            .Include(x => x.TelegramUser)
            .Include(x => x.ViberUser)
            .Include(x => x.TikTokUser);
    }
}