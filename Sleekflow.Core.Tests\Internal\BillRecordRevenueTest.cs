﻿using AutoMapper;
using FluentAssertions;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.Mapping;
using Moq;
using Microsoft.Extensions.Logging;
using Travis_backend.CompanyDomain.Services;

namespace Sleekflow.Core.Tests.Internal;

public class BillRecordRevenueTest
{
    private static IMapper _mapper = null!;
    private readonly BillRecordRevenueCalculatorService _billRecordRevenueCalculatorService;

    public BillRecordRevenueTest()
    {
        var mapper = new MapperConfiguration(mc => { mc.AddProfile(new InternalCmsMappingProfile()); })
            .CreateMapper();
        _mapper = mapper;
        var mockTimezoneService = new Mock<ITimezoneAwareMrrCalculationService>();
        var mockBillRecordRevenueCalculatorServiceLogger = new Mock<ILogger<BillRecordRevenueCalculatorService>>();
        _billRecordRevenueCalculatorService = new BillRecordRevenueCalculatorService(
            mockBillRecordRevenueCalculatorServiceLogger.Object,
            mockTimezoneService.Object);
    }

    [SetUp]
    public void Setup()
    {
    }

    [Test]
    public void DailyAnalytics_Test_InputPayment()
    {
        var companyId = "471a6289-b9b7-43c3-b6ad-395a1992baea";

        var data = new Company()
        {
            Id = companyId,
            CompanyName = "Sleekflow",
            CompanyCountry = "Hong Kong SAR",
            TimeZoneInfoId = "GMT Standard Time",
            CreatedAt = new DateTime(2022, 1, 1),
            CmsLeadSource = "CmsLeadSource",
            CmsCompanyIndustry = "CmsCompanyIndustry",
            CmsCompanyOwnerId = "371a6289-b9b7-43c3-b6ad-395a1992baea",
            CmsCompanyOwner = new ApplicationUser()
            {
                Id = "371a6289-b9b7-43c3-b6ad-395a1992baea", DisplayName = "CmsCompanyOwner"
            },
            CmsActivationOwnerId = "271a6289-b9b7-43c3-b6ad-395a1992baea",
            CmsActivationOwner = new ApplicationUser()
            {
                Id = "271a6289-b9b7-43c3-b6ad-395a1992baea", DisplayName = "CmsActivationOwner"
            },
            CmsCsOwnerId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            CmsCsOwner = new ApplicationUser()
            {
                Id = "471a6289-b9b7-43c3-b6ad-395a1992baea", DisplayName = "CmsCsOwner"
            },
            BillRecords = new List<BillRecord>()
            {
                // Free plan at 2022-01-01
                new ()
                {
                    Id = 1,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_freemium",
                    PeriodStart = new DateTime(2022, 1, 1),
                    PeriodEnd = new DateTime(2022, 1, 1).AddMonths(1).EndOfDay(),
                    Status = BillStatus.Active,
                    PayAmount = 0,
                    currency = "usd",
                    created = new DateTime(2022, 1, 1),
                    quantity = 0,
                    SubscriptionTier = SubscriptionTier.Free,
                },
                new ()
                {
                    Id = 2,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_enterprise",
                    PeriodStart = new DateTime(2022, 2, 1),
                    PeriodEnd = new DateTime(2022, 2, 1).AddMonths(12).EndOfDay(),
                    Status = BillStatus.Active,
                    PayAmount = 0,
                    currency = "usd",
                    created = new DateTime(2022, 2, 1),
                    quantity = 0,
                    CmsSalesPaymentRecords =
                    [
                        new CmsSalesPaymentRecord
                        {
                            Id = 21,
                            CompanyId = companyId,
                            BillRecordId = 2,
                            SubscriptionFee = 12000,
                            OneTimeSetupFee = 3000,
                            WhatsappCreditAmount = 1000,
                            Currency = "usd",
                            PaymentMethod = PaymentMethod.Bank,
                            PaidAt = new DateTime(2022, 2, 1),
                        }
                    ],
                    SubscriptionTier = SubscriptionTier.Enterprise,
                },
                new ()
                {
                    Id = 3,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_enterprise",
                    PeriodStart = new DateTime(2023, 2, 1),
                    PeriodEnd = new DateTime(2023, 2, 1).AddMonths(12).EndOfDay(),
                    Status = BillStatus.Active,
                    PayAmount = 0,
                    currency = "usd",
                    created = new DateTime(2023, 2, 1),
                    quantity = 0,
                    CmsSalesPaymentRecords =
                    [
                        new CmsSalesPaymentRecord
                        {
                            Id = 41,
                            CompanyId = companyId,
                            BillRecordId = 3,
                            SubscriptionFee = 12000,
                            OneTimeSetupFee = 3000,
                            WhatsappCreditAmount = 1000,
                            Currency = "usd",
                            PaymentMethod = PaymentMethod.Bank,
                            PaidAt = new DateTime(2023, 1, 20), // Test PaidAt: less then paid start
                        }
                    ],
                    SubscriptionTier = SubscriptionTier.Enterprise,
                },
                new ()
                {
                    Id = 4,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_enterprise",
                    PeriodStart = new DateTime(2024, 2, 1),
                    PeriodEnd = new DateTime(2024, 2, 1).AddMonths(12).EndOfDay(),
                    Status = BillStatus.Active,
                    PayAmount = 0,
                    currency = "usd",
                    created = new DateTime(2024, 2, 1),
                    quantity = 0,
                    CmsSalesPaymentRecords =
                    [
                        new CmsSalesPaymentRecord
                        {
                            Id = 41,
                            CompanyId = companyId,
                            BillRecordId = 4,
                            SubscriptionFee = 12000,
                            OneTimeSetupFee = 3000,
                            WhatsappCreditAmount = 1000,
                            Currency = "usd",
                            PaymentMethod = PaymentMethod.Bank,
                            PaidAt = new DateTime(2024, 2, 10), // Test PaidAt: larger then paid start
                        }
                    ],
                    SubscriptionTier = SubscriptionTier.Enterprise,
                },
            }
        };

        var companies = _mapper.Map<List<CmsCompanyAnalyticDto>>(
            new List<Company>()
            {
                data
            });

        var dailyAnalytics = _billRecordRevenueCalculatorService.GetDailyAnalytics(
            companies,
            new DateTime(2022, 1, 1),
            new DateTime(2024, 3, 1));

        // Asserts
        foreach (var cmsDailyRevenueAnalyticDto in dailyAnalytics.Where(x =>
                     String.CompareOrdinal(x.Date, new DateTime(2022, 1, 1).ToString("yyyy-MM-dd")) == 1 &&
                     String.CompareOrdinal(x.Date, new DateTime(2022, 2, 1).ToString("yyyy-MM-dd")) == -1))
        {
            cmsDailyRevenueAnalyticDto.MonthlyRecurringRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.DailyRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.MarkupRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.CompanyRevenueBreakDowns.Count.Should().Be(0);
        }

        var planStart = dailyAnalytics.First(x => x.Date == new DateTime(2022, 2, 1).ToString("yyyy-MM-dd"));
        planStart.MonthlyRecurringRevenue.Should().Be(1000);
        planStart.DailyRevenue.Should().Be(12000);
        planStart.SubscriptionPlanRevenue.Should().Be(12000);
        planStart.OneTimeSetupFeeRevenue.Should().Be(0);

        planStart.CompanyRevenueBreakDowns.First(x => x.CompanyId == companyId).MonthlyRecurringRevenue.Should()
            .Be(1000);
        planStart.CompanyRevenueBreakDowns.First(x => x.CompanyId == companyId).SubscriptionPlanId.Should()
            .Be("sleekflow_enterprise");

        foreach (var cmsDailyRevenueAnalyticDto in dailyAnalytics.Where(x =>
                     String.CompareOrdinal(x.Date, new DateTime(2022, 2, 1).ToString("yyyy-MM-dd")) == 1
                     &&
                     String.CompareOrdinal(x.Date, new DateTime(2023, 2, 1).ToString("yyyy-MM-dd")) == -1))
        {
            if (cmsDailyRevenueAnalyticDto.Date == new DateTime(2023, 1, 20).ToString("yyyy-MM-dd"))
                continue;
            cmsDailyRevenueAnalyticDto.MonthlyRecurringRevenue.Should().Be(1000);
            cmsDailyRevenueAnalyticDto.DailyRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.MarkupRevenue.Should().Be(0);
        }

        // Input Payment with PaidAt: less than the period start, should be correct to 2023/2/1
        var analyticDataAtPlanWithPaidAtLessThenPaidStart =
            dailyAnalytics.First(x => x.Date == new DateTime(2023, 2, 1).ToString("yyyy-MM-dd"));
        analyticDataAtPlanWithPaidAtLessThenPaidStart.MonthlyRecurringRevenue.Should().Be(1000);
        analyticDataAtPlanWithPaidAtLessThenPaidStart.DailyRevenue.Should().Be(12000);
        analyticDataAtPlanWithPaidAtLessThenPaidStart.SubscriptionPlanRevenue.Should().Be(12000);
        analyticDataAtPlanWithPaidAtLessThenPaidStart.OneTimeSetupFeeRevenue.Should().Be(0);

        analyticDataAtPlanWithPaidAtLessThenPaidStart.CompanyRevenueBreakDowns.First(x => x.CompanyId == companyId)
            .MonthlyRecurringRevenue.Should().Be(1000);
        analyticDataAtPlanWithPaidAtLessThenPaidStart.CompanyRevenueBreakDowns.First(x => x.CompanyId == companyId)
            .SubscriptionPlanId.Should().Be("sleekflow_enterprise");

        // Input Payment with PaidAt: larger than the period start, should be correct to 2024/2/10
        var analyticDataAtPlanWithPaidAtLargerThenPaidStart =
            dailyAnalytics.First(x => x.Date == new DateTime(2024, 2, 10).ToString("yyyy-MM-dd"));
        analyticDataAtPlanWithPaidAtLargerThenPaidStart.MonthlyRecurringRevenue.Should().Be(1000);
        analyticDataAtPlanWithPaidAtLargerThenPaidStart.DailyRevenue.Should().Be(12000);
        analyticDataAtPlanWithPaidAtLargerThenPaidStart.SubscriptionPlanRevenue.Should().Be(12000);
        analyticDataAtPlanWithPaidAtLargerThenPaidStart.OneTimeSetupFeeRevenue.Should().Be(0);

        analyticDataAtPlanWithPaidAtLargerThenPaidStart.CompanyRevenueBreakDowns.First(x => x.CompanyId == companyId)
            .MonthlyRecurringRevenue.Should().Be(1000);
        analyticDataAtPlanWithPaidAtLargerThenPaidStart.CompanyRevenueBreakDowns.First(x => x.CompanyId == companyId)
            .SubscriptionPlanId.Should().Be("sleekflow_enterprise");

        // BillRecordRevenueCalculator.GetCompanyAllTimeRevenueAnalyticData Test: Total
        var allTimeRevenueAnalyticData =
            _billRecordRevenueCalculatorService.GetCompanyAllTimeRevenueAnalyticData(companyId, dailyAnalytics);
        allTimeRevenueAnalyticData.CompanyId.Should().Be(companyId);
        allTimeRevenueAnalyticData.InitialPaidSubscriptionPlanId.Should().Be("sleekflow_enterprise");
        allTimeRevenueAnalyticData.InitialPaidDate.Should().Be(new DateTime(2022, 2, 1).ToString("yyyy-MM-dd"));
        allTimeRevenueAnalyticData.InitialMonthlyRecurringRevenue.Should().Be(1000);
        allTimeRevenueAnalyticData.LastPaidSubscriptionPlanId.Should().Be("sleekflow_enterprise");
        allTimeRevenueAnalyticData.TotalRevenue.Should().Be(36000);
        allTimeRevenueAnalyticData.TotalSubscriptionPlanRevenue.Should().Be(12000 + 12000 + 12000);
        allTimeRevenueAnalyticData.TotalOneTimeSetupFeeRevenue.Should().Be(0);
        allTimeRevenueAnalyticData.TotalMarkupRevenue.Should().Be(0);
    }

    ///free -> premium yearly + one off + 360markup -> premium yearly + addon (monthly) -> premium yearly
    [Test]
    public void DailyAnalytics_Test_StripePayment()
    {
        var companyId = "471a6289-b9b7-43c3-b6ad-395a1992baea";

        var data = new Company()
        {
            Id = companyId,
            CompanyName = "Sleekflow",
            CompanyCountry = "Hong Kong SAR",
            TimeZoneInfoId = "GMT Standard Time",
            CreatedAt = new DateTime(2022, 1, 1),
            CmsLeadSource = "CmsLeadSource",
            CmsCompanyIndustry = "CmsCompanyIndustry",
            CmsCompanyOwnerId = "371a6289-b9b7-43c3-b6ad-395a1992baea",
            CmsCompanyOwner = new ApplicationUser()
            {
                Id = "371a6289-b9b7-43c3-b6ad-395a1992baea", DisplayName = "CmsCompanyOwner"
            },
            CmsActivationOwnerId = "271a6289-b9b7-43c3-b6ad-395a1992baea",
            CmsActivationOwner = new ApplicationUser()
            {
                Id = "271a6289-b9b7-43c3-b6ad-395a1992baea", DisplayName = "CmsActivationOwner"
            },
            CmsCsOwnerId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            CmsCsOwner = new ApplicationUser()
            {
                Id = "471a6289-b9b7-43c3-b6ad-395a1992baea", DisplayName = "CmsCsOwner"
            },
            BillRecords = new List<BillRecord>()
            {
                // Free plan at 2022-01-01
                new ()
                {
                    Id = 1,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_freemium",
                    PeriodStart = new DateTime(2022, 1, 1),
                    PeriodEnd = new DateTime(2022, 1, 1).AddMonths(1).EndOfDay(),
                    Status = BillStatus.Active,
                    PayAmount = 0,
                    currency = "usd",
                    created = new DateTime(2022, 1, 1),
                    quantity = 0,
                    SubscriptionTier = SubscriptionTier.Free,
                },
                new ()
                {
                    Id = 2,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_v9_premium_yearly",
                    PeriodStart = new DateTime(2022, 2, 1),
                    PeriodEnd = new DateTime(2022, 2, 1).AddMonths(12).EndOfDay(),
                    Status = BillStatus.Active,
                    PayAmount = 3588,
                    currency = "usd",
                    created = new DateTime(2022, 2, 1),
                    quantity = 0,
                    SubscriptionTier = SubscriptionTier.Premium,
                },
                new ()
                {
                    Id = 3,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_v9_chatbot_automation_support_basic_oneoff",
                    PeriodStart = new DateTime(2022, 2, 1),
                    PeriodEnd = new DateTime(2099, 2, 1),
                    Status = BillStatus.Active,
                    PayAmount = 1000,
                    currency = "usd",
                    created = new DateTime(2022, 2, 1),
                    quantity = 0,
                    SubscriptionTier = SubscriptionTier.Premium,
                },
                new ()
                {
                    Id = 4,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_markup_360dialog_conversation",
                    PeriodStart = new DateTime(2022, 2, 1),
                    PeriodEnd = new DateTime(2022, 3, 1),
                    Status = BillStatus.Active,
                    PayAmount = 1000,
                    currency = "usd",
                    created = new DateTime(2022, 2, 1),
                    quantity = 0,
                    SubscriptionTier = SubscriptionTier.MarkUpLog,
                },
                new ()
                {
                    Id = 5,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_v9_salesforce_integration",
                    PeriodStart = new DateTime(2022, 3, 1),
                    PeriodEnd = new DateTime(2022, 4, 1),
                    Status = BillStatus.Active,
                    PayAmount = 1000,
                    currency = "usd",
                    created = new DateTime(2022, 3, 1),
                    quantity = 0,
                    SubscriptionTier = SubscriptionTier.Premium,
                },
                new ()
                {
                    Id = 6,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_v9_salesforce_integration",
                    PeriodStart = new DateTime(2022, 4, 1),
                    PeriodEnd = new DateTime(2022, 5, 1),
                    Status = BillStatus.Active,
                    PayAmount = 1000,
                    currency = "usd",
                    created = new DateTime(2022, 4, 1),
                    quantity = 0,
                    SubscriptionTier = SubscriptionTier.AddOn,
                },
                new ()
                {
                    Id = 1,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_freemium",
                    PeriodStart = new DateTime(2023, 2, 2),
                    PeriodEnd = new DateTime(2023, 3, 2).EndOfDay(),
                    Status = BillStatus.Active,
                    PayAmount = 0,
                    currency = "usd",
                    created = new DateTime(2023, 2, 2),
                    quantity = 0,
                    SubscriptionTier = SubscriptionTier.Free,
                },
            },
        };

        var companies = _mapper.Map<List<CmsCompanyAnalyticDto>>(
            new List<Company>()
            {
                data
            });

        var analytics = _billRecordRevenueCalculatorService.GetDailyAnalytics(
            companies,
            new DateTime(2022, 1, 1),
            new DateTime(2023, 2, 1));

        // Asserts
        // Free plan
        foreach (var cmsDailyRevenueAnalyticDto in analytics.Where(x =>
                     String.CompareOrdinal(x.Date, new DateTime(2022, 1, 1).ToString("yyyy-MM-dd")) == 1 &&
                     String.CompareOrdinal(x.Date, new DateTime(2022, 2, 1).ToString("yyyy-MM-dd")) == -1))
        {
            cmsDailyRevenueAnalyticDto.MonthlyRecurringRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.DailyRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.MarkupRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.CompanyRevenueBreakDowns.Count.Should().Be(0);
        }

        // Premium with one off
        var planStartWithOneOffRevenue =
            analytics.First(x => x.Date == new DateTime(2022, 2, 1).ToString("yyyy-MM-dd"));
        planStartWithOneOffRevenue.MonthlyRecurringRevenue.Should().Be(299);
        planStartWithOneOffRevenue.DailyRevenue.Should().Be(3588 + 1000 + 1000);
        planStartWithOneOffRevenue.SubscriptionPlanRevenue.Should().Be(3588);
        planStartWithOneOffRevenue.OneTimeSetupFeeRevenue.Should().Be(1000);
        planStartWithOneOffRevenue.MarkupRevenue.Should().Be(1000);

        planStartWithOneOffRevenue.CompanyRevenueBreakDowns.First(x => x.CompanyId == companyId).MonthlyRecurringRevenue
            .Should().Be(299);
        planStartWithOneOffRevenue.CompanyRevenueBreakDowns.First(x => x.CompanyId == companyId).SubscriptionPlanId
            .Should().Be("sleekflow_v9_premium_yearly");

        // Premium with one off on going
        foreach (var cmsDailyRevenueAnalyticDto in analytics.Where(x =>
                     String.CompareOrdinal(x.Date, new DateTime(2022, 2, 1).ToString("yyyy-MM-dd")) == 1 &&
                     String.CompareOrdinal(x.Date, new DateTime(2022, 3, 1).ToString("yyyy-MM-dd")) == -1))
        {
            cmsDailyRevenueAnalyticDto.MonthlyRecurringRevenue.Should().Be(299);
            cmsDailyRevenueAnalyticDto.DailyRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.MarkupRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.CompanyRevenueBreakDowns.Count.Should().Be(1);
        }

        // Plan With AddOn start
        var planWithAddOn = analytics.First(x => x.Date == new DateTime(2022, 3, 1).ToString("yyyy-MM-dd"));
        planWithAddOn.MonthlyRecurringRevenue.Should().Be(1299);
        planWithAddOn.DailyRevenue.Should().Be(1000);
        planWithAddOn.SubscriptionPlanRevenue.Should().Be(1000);
        planWithAddOn.OneTimeSetupFeeRevenue.Should().Be(0);
        planWithAddOn.MarkupRevenue.Should().Be(0);

        planWithAddOn.CompanyRevenueBreakDowns.First(x => x.CompanyId == companyId).MonthlyRecurringRevenue.Should()
            .Be(1299);
        planWithAddOn.CompanyRevenueBreakDowns.First(x => x.CompanyId == companyId).SubscriptionPlanId.Should()
            .Be("sleekflow_v9_premium_yearly");

        // Plan With AddOn start on going
        foreach (var cmsDailyRevenueAnalyticDto in analytics.Where(x =>
                     String.CompareOrdinal(x.Date, new DateTime(2022, 3, 1).ToString("yyyy-MM-dd")) == 1 &&
                     String.CompareOrdinal(x.Date, new DateTime(2022, 4, 1).ToString("yyyy-MM-dd")) == -1))
        {
            cmsDailyRevenueAnalyticDto.MonthlyRecurringRevenue.Should().Be(1299);
            cmsDailyRevenueAnalyticDto.DailyRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.MarkupRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.CompanyRevenueBreakDowns.Count.Should().Be(1);
        }

        // still have Plan, but AddOn Ended
        foreach (var cmsDailyRevenueAnalyticDto in analytics.Where(x =>
                     String.CompareOrdinal(x.Date, new DateTime(2022, 5, 1).ToString("yyyy-MM-dd")) == 1))
        {
            cmsDailyRevenueAnalyticDto.MonthlyRecurringRevenue.Should().Be(299);
            cmsDailyRevenueAnalyticDto.DailyRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.MarkupRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.CompanyRevenueBreakDowns.Count.Should().Be(1);
        }


        // BillRecordRevenueCalculator.GetCompanyAllTimeRevenueAnalyticData Test
        var allTimeRevenueAnalyticData =
            _billRecordRevenueCalculatorService.GetCompanyAllTimeRevenueAnalyticData(companyId, analytics);
        allTimeRevenueAnalyticData.CompanyId.Should().Be(companyId);
        allTimeRevenueAnalyticData.InitialPaidSubscriptionPlanId.Should().Be("sleekflow_v9_premium_yearly");
        allTimeRevenueAnalyticData.InitialPaidDate.Should().Be(new DateTime(2022, 2, 1).ToString("yyyy-MM-dd"));
        allTimeRevenueAnalyticData.InitialMonthlyRecurringRevenue.Should().Be(299);
        allTimeRevenueAnalyticData.LastPaidSubscriptionPlanId.Should().Be("sleekflow_v9_premium_yearly");
        allTimeRevenueAnalyticData.TotalRevenue.Should().Be(3588 + 1000 + 1000 + 1000 + 1000);
        allTimeRevenueAnalyticData.TotalSubscriptionPlanRevenue.Should().Be(3588 + 1000 + 1000);
        allTimeRevenueAnalyticData.TotalOneTimeSetupFeeRevenue.Should().Be(1000);
        allTimeRevenueAnalyticData.TotalMarkupRevenue.Should().Be(1000);
    }

    [Test]
    public void CmsCompanyAllTimeRevenueAnalyticData_Equal_Test()
    {
        var cmsCompanyAllTimeRevenueAnalyticData = new CmsCompanyAllTimeRevenueAnalyticData
        {
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            InitialPaidSubscriptionPlanId = "sleekflow_v9_premium_yearly",
            InitialPaidDate = "2022-08-11",
            InitialMonthlyRecurringRevenue = 100,
            LastPaidSubscriptionPlanId = "sleekflow_v9_pro_yearly",
            TotalRevenue = 2000,
            TotalSubscriptionPlanRevenue = 1800,
            TotalOneTimeSetupFeeRevenue = 200,
            TotalMarkupRevenue = 0
        };

        var cmsCompanyAllTimeRevenueAnalyticDataSame = new CmsCompanyAllTimeRevenueAnalyticData
        {
            CompanyId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            InitialPaidSubscriptionPlanId = "sleekflow_v9_premium_yearly",
            InitialPaidDate = "2022-08-11",
            InitialMonthlyRecurringRevenue = 100,
            LastPaidSubscriptionPlanId = "sleekflow_v9_pro_yearly",
            TotalRevenue = 2000,
            TotalSubscriptionPlanRevenue = 1800,
            TotalOneTimeSetupFeeRevenue = 200,
            TotalMarkupRevenue = 0
        };

        var cmsCompanyAllTimeRevenueAnalyticDataSameButCompanyIdIsDifferent = new CmsCompanyAllTimeRevenueAnalyticData
        {
            CompanyId = "371a6289-b9b7-43c3-b6ad-395a1992baea",
            InitialPaidSubscriptionPlanId = "sleekflow_v9_premium_yearly",
            InitialPaidDate = "2022-08-11",
            InitialMonthlyRecurringRevenue = 100,
            LastPaidSubscriptionPlanId = "sleekflow_v9_pro_yearly",
            TotalRevenue = 2000,
            TotalSubscriptionPlanRevenue = 1800,
            TotalOneTimeSetupFeeRevenue = 200,
            TotalMarkupRevenue = 0
        };

        var cmsCompanyAllTimeRevenueAnalyticDataDifferent = new CmsCompanyAllTimeRevenueAnalyticData
        {
            CompanyId = "371a6289-b9b7-43c3-b6ad-395a1992baea",
            InitialPaidSubscriptionPlanId = "sleekflow_v9_pro_yearly",
            InitialPaidDate = "2022-08-21",
            InitialMonthlyRecurringRevenue = 50,
            LastPaidSubscriptionPlanId = "sleekflow_v9_pro_yearly",
            TotalRevenue = 500,
            TotalSubscriptionPlanRevenue = 500,
            TotalOneTimeSetupFeeRevenue = 0,
            TotalMarkupRevenue = 0
        };

        cmsCompanyAllTimeRevenueAnalyticData.Equals(cmsCompanyAllTimeRevenueAnalyticDataSame).Should().Be(true);

        cmsCompanyAllTimeRevenueAnalyticData.Equals(cmsCompanyAllTimeRevenueAnalyticDataDifferent).Should().Be(false);
        cmsCompanyAllTimeRevenueAnalyticData.Equals(cmsCompanyAllTimeRevenueAnalyticDataSameButCompanyIdIsDifferent)
            .Should().Be(false);
        cmsCompanyAllTimeRevenueAnalyticData.Equals(null).Should().Be(false);
    }

    [Test]
    public void DailyAccruedAnalytics_SubscriptionAndOneTimeRevenue_Test()
    {
        const string companyId = "471a6289-b9b7-43c3-b6ad-395a1992baea";

        var data = new Company()
        {
            Id = companyId,
            CompanyName = "Sleekflow",
            CompanyCountry = "Hong Kong SAR",
            TimeZoneInfoId = "GMT Standard Time",
            CreatedAt = new DateTime(2022, 1, 1),
            CmsLeadSource = "CmsLeadSource",
            CmsCompanyIndustry = "CmsCompanyIndustry",
            CmsCompanyOwnerId = "371a6289-b9b7-43c3-b6ad-395a1992baea",
            CmsCompanyOwner = new ApplicationUser()
            {
                Id = "371a6289-b9b7-43c3-b6ad-395a1992baea", DisplayName = "CmsCompanyOwner"
            },
            CmsActivationOwnerId = "271a6289-b9b7-43c3-b6ad-395a1992baea",
            CmsActivationOwner = new ApplicationUser()
            {
                Id = "271a6289-b9b7-43c3-b6ad-395a1992baea", DisplayName = "CmsActivationOwner"
            },
            CmsCsOwnerId = "471a6289-b9b7-43c3-b6ad-395a1992baea",
            CmsCsOwner = new ApplicationUser()
            {
                Id = "471a6289-b9b7-43c3-b6ad-395a1992baea", DisplayName = "CmsCsOwner"
            },
            BillRecords = new List<BillRecord>()
            {
                // Free plan at 2022-01-01
                new ()
                {
                    Id = 1,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_freemium",
                    PeriodStart = new DateTime(2022, 1, 1),
                    PeriodEnd = new DateTime(2022, 1, 1).AddMonths(1).EndOfDay(),
                    Status = BillStatus.Active,
                    PayAmount = 0,
                    currency = "usd",
                    created = new DateTime(2022, 1, 1),
                    quantity = 0,
                    SubscriptionTier = SubscriptionTier.Free,
                },
                new ()
                {
                    Id = 3,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_v9_premium_yearly",
                    PeriodStart = new DateTime(2022, 2, 1),
                    PeriodEnd = new DateTime(2022, 2, 1).AddMonths(12).EndOfDay(),
                    Status = BillStatus.Active,
                    PayAmount = 3588,
                    currency = "usd",
                    created = new DateTime(2022, 2, 1),
                    quantity = 0,
                    SubscriptionTier = SubscriptionTier.Premium,
                },
                new ()
                {
                    Id = 4,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_v9_salesforce_integration",
                    PeriodStart = new DateTime(2022, 3, 1),
                    PeriodEnd = new DateTime(2022, 4, 1),
                    Status = BillStatus.Active,
                    PayAmount = 1000,
                    currency = "usd",
                    created = new DateTime(2022, 3, 1),
                    quantity = 0,
                    SubscriptionTier = SubscriptionTier.Premium,
                },
                new ()
                {
                    Id = 5,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_v9_salesforce_integration",
                    PeriodStart = new DateTime(2022, 4, 1),
                    PeriodEnd = new DateTime(2022, 5, 1),
                    Status = BillStatus.Active,
                    PayAmount = 0,
                    currency = "usd",
                    created = new DateTime(2022, 4, 1),
                    quantity = 0,
                    SubscriptionTier = SubscriptionTier.AddOn,
                    CmsSalesPaymentRecords =
                    [
                        new CmsSalesPaymentRecord
                        {
                            CreatedAt = new DateTime(2022, 4, 5), OneTimeSetupFee = 1000
                        }
                    ]
                },
                new ()
                {
                    Id = 6,
                    CompanyId = companyId,
                    SubscriptionPlanId = "sleekflow_freemium",
                    PeriodStart = new DateTime(2023, 2, 2),
                    PeriodEnd = new DateTime(2023, 3, 2).EndOfDay(),
                    Status = BillStatus.Active,
                    PayAmount = 0,
                    currency = "usd",
                    created = new DateTime(2023, 2, 2),
                    quantity = 0,
                    SubscriptionTier = SubscriptionTier.Free,
                },
            },
        };

        var companies = _mapper.Map<List<CmsCompanyAnalyticDto>>(
            new List<Company>()
            {
                data
            });

        var analytics = _billRecordRevenueCalculatorService.GetDailyAccruedAnalytics(
            companies,
            new List<CmsSleekPayCompanyAnalyticDto>(),
            new List<CmsWhatsApp360DialogUsageTopUpTransactionLogDto>(),
            new List<CmsTwilioTopUpLogDto>(),
            new List<CmsWhatsappCloudApiTopUpDto>(),
            new DateTime(2022, 1, 1),
            new DateTime(2023, 2, 1));

        // Asserts
        // 2022-01-01 - 2022-01-31 (Free plan)
        foreach (var cmsDailyRevenueAnalyticDto in analytics.Where(x =>
                     String.CompareOrdinal(x.Date, new DateTime(2021, 12, 31).ToString("yyyy-MM-dd")) == 1 &&
                     String.CompareOrdinal(x.Date, new DateTime(2022, 2, 1).ToString("yyyy-MM-dd")) == -1))
        {
            cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(0);
        }

        // 2022-02-01 - 2022-02-28 (Premium Plan Without Add-on)
        foreach (var cmsDailyRevenueAnalyticDto in analytics.Where(x =>
                     String.CompareOrdinal(x.Date, new DateTime(2022, 1, 31).ToString("yyyy-MM-dd")) == 1 &&
                     String.CompareOrdinal(x.Date, new DateTime(2022, 3, 1).ToString("yyyy-MM-dd")) == -1))
        {
            var subscriptionRevenue =
                3588M / ((new DateTime(2022, 2, 1).AddMonths(12).Date - new DateTime(2022, 2, 1).Date).Days +
                         1);

            cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(subscriptionRevenue);
            cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(1);

            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyId.Should().Be(companyId);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyName.Should().Be("Sleekflow");
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessId.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessName.Should()
                .BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Subscription");
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().DailyAccruedRevenue.Should()
                .Be(subscriptionRevenue);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.Should()
                .BeNullOrEmpty();
        }

        // 2022-03-01 - 2022-04-01 (Premium Plan With Add-on)
        foreach (var cmsDailyRevenueAnalyticDto in analytics.Where(x =>
                     String.CompareOrdinal(x.Date, new DateTime(2022, 2, 28).ToString("yyyy-MM-dd")) == 1 &&
                     String.CompareOrdinal(x.Date, new DateTime(2022, 4, 2).ToString("yyyy-MM-dd")) == -1))
        {
            var subscriptionRevenue =
                3588M / ((new DateTime(2022, 2, 1).AddMonths(12).Date - new DateTime(2022, 2, 1).Date).Days + 1)
                + 1000M / ((new DateTime(2022, 4, 1).Date - new DateTime(2022, 3, 1).Date).Days + 1);

            cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(subscriptionRevenue);
            cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(1);

            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyId.Should().Be(companyId);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyName.Should().Be("Sleekflow");
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessId.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessName.Should()
                .BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Subscription");
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().DailyAccruedRevenue.Should()
                .Be(subscriptionRevenue);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.Should()
                .BeNullOrEmpty();
        }

        // 2022-04-02 - 2022-04-04 (Premium Plan Without Add-on)
        foreach (var cmsDailyRevenueAnalyticDto in analytics.Where(x =>
                     String.CompareOrdinal(x.Date, new DateTime(2022, 4, 1).ToString("yyyy-MM-dd")) == 1 &&
                     String.CompareOrdinal(x.Date, new DateTime(2022, 4, 5).ToString("yyyy-MM-dd")) == -1))
        {
            var subscriptionRevenue =
                3588M / ((new DateTime(2022, 2, 1).AddMonths(12).Date - new DateTime(2022, 2, 1).Date).Days +
                         1);

            cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(subscriptionRevenue);
            cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(1);

            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyId.Should().Be(companyId);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyName.Should().Be("Sleekflow");
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessId.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessName.Should()
                .BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Subscription");
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().DailyAccruedRevenue.Should()
                .Be(subscriptionRevenue);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.Should()
                .BeNullOrEmpty();
        }

        // 2022-04-05 - 2022-04-05 (Premium Plan With One-Time Setup)
        foreach (var cmsDailyRevenueAnalyticDto in analytics.Where(x =>
                     String.CompareOrdinal(x.Date, new DateTime(2022, 4, 4).ToString("yyyy-MM-dd")) == 1 &&
                     String.CompareOrdinal(x.Date, new DateTime(2022, 4, 6).ToString("yyyy-MM-dd")) == -1))
        {
            var subscriptionRevenue =
                3588M / ((new DateTime(2022, 2, 1).AddMonths(12).Date - new DateTime(2022, 2, 1).Date).Days + 1);

            var oneTimeSetupRevenue = 1000M;

            cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(subscriptionRevenue);
            cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(oneTimeSetupRevenue);
            cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(2);

            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].CompanyId.Should().Be(companyId);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].CompanyName.Should().Be("Sleekflow");
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].FacebookBusinessId.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].FacebookBusinessName.Should()
                .BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].Type.Should().Be("Subscription");
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].TopUpType.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].DailyAccruedRevenue.Should()
                .Be(subscriptionRevenue);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].SleekFlowCompanyIds.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].SleekFlowCompanyNames.Should()
                .BeNullOrEmpty();

            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].CompanyId.Should().Be(companyId);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].CompanyName.Should().Be("Sleekflow");
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].FacebookBusinessId.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].FacebookBusinessName.Should()
                .BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].Type.Should().Be("One-Time Revenue");
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].TopUpType.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].DailyAccruedRevenue.Should()
                .Be(oneTimeSetupRevenue);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].SleekFlowCompanyIds.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].SleekFlowCompanyNames.Should()
                .BeNullOrEmpty();
        }

        // 2022-04-06 - 2023-02-01 (Premium Plan Without Add-on)
        foreach (var cmsDailyRevenueAnalyticDto in analytics.Where(x =>
                     String.CompareOrdinal(x.Date, new DateTime(2022, 4, 5).ToString("yyyy-MM-dd")) == 1 &&
                     String.CompareOrdinal(x.Date, new DateTime(2023, 2, 2).ToString("yyyy-MM-dd")) == -1))
        {
            var subscriptionRevenue =
                3588M / ((new DateTime(2022, 2, 1).AddMonths(12).Date - new DateTime(2022, 2, 1).Date).Days +
                         1);

            cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(subscriptionRevenue);
            cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(0);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(1);

            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyId.Should().Be(companyId);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyName.Should().Be("Sleekflow");
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessId.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessName.Should()
                .BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Subscription");
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().DailyAccruedRevenue.Should()
                .Be(subscriptionRevenue);
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.Should().BeNullOrEmpty();
            cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.Should()
                .BeNullOrEmpty();
        }

        // All Total Accrued Revenue
        var totalRevenue = Math.Round(
            analytics.Sum(x => x.SubscriptionPlanRevenue + x.OneTimeSetupFeeRevenue +
                               x.SleekPayRevenue + x.TopUpRevenue),
            2);

        totalRevenue.Should().Be(3588M + 1000M + 1000M);
    }

    [Test]
    public void DailyAccruedAnalytics_SleekPay_Test()
    {
        const string companyId = "471a6289-b9b7-43c3-b6ad-395a1992baea";
        const string companyId2 = "571a6289-b9b7-43c3-b6ad-395a1992baea";

        var sleekPayCompanies = new List<CmsSleekPayCompanyAnalyticDto>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                CompanyName = "Sleekflow",
                CmsSleekPayReportDatas =
                [
                    new CmsSleekPayReportData
                    {
                        StartActivityDate = new DateTime(2022, 2, 1), Currency = "usd", NetEarning = 10.00M
                    },
                    new CmsSleekPayReportData
                    {
                        StartActivityDate = new DateTime(2022, 2, 10), Currency = "usd", NetEarning = 15.00M
                    },

                    new ()
                    {
                        StartActivityDate = new DateTime(2022, 2, 10), Currency = "usd", NetEarning = 13.00M
                    },

                    new ()
                    {
                        StartActivityDate = new DateTime(2022, 2, 19), Currency = "usd", NetEarning = 10.00M
                    },

                    new ()
                    {
                        StartActivityDate = new DateTime(2022, 2, 19), Currency = "usd", NetEarning = -15.00M
                    },

                    new ()
                    {
                        StartActivityDate = new DateTime(2022, 2, 28), Currency = "usd", NetEarning = 10.00M
                    }
                ]
            },
            new ()
            {
                Id = 2,
                CompanyId = companyId2,
                CompanyName = "Sleekflow2",
                CmsSleekPayReportDatas =
                [
                    new CmsSleekPayReportData
                    {
                        StartActivityDate = new DateTime(2022, 2, 28), Currency = "usd", NetEarning = 15.00M
                    }
                ]
            }
        };

        var analytics = _billRecordRevenueCalculatorService.GetDailyAccruedAnalytics(
            new List<CmsCompanyAnalyticDto>(),
            sleekPayCompanies,
            new List<CmsWhatsApp360DialogUsageTopUpTransactionLogDto>(),
            new List<CmsTwilioTopUpLogDto>(),
            new List<CmsWhatsappCloudApiTopUpDto>(),
            new DateTime(2022, 1, 1),
            new DateTime(2022, 3, 1));

        // 2022-02-01
        var cmsDailyRevenueAnalyticDto = analytics
            .First(x => x.Date == new DateTime(2022, 2, 1).ToString("yyyy-MM-dd"));

        cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(10.00M);
        cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(1);

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyId.Should().Be(companyId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyName.Should().Be("Sleekflow");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessName.Should()
            .BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("SleekPay");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().DailyAccruedRevenue.Should().Be(10.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.Should()
            .BeNullOrEmpty();

        // 2022-02-10
        cmsDailyRevenueAnalyticDto = analytics
            .First(x => x.Date == new DateTime(2022, 2, 10).ToString("yyyy-MM-dd"));

        cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(28.00M);
        cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(1);

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyId.Should().Be(companyId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyName.Should().Be("Sleekflow");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessName.Should()
            .BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("SleekPay");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().DailyAccruedRevenue.Should().Be(28.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.Should()
            .BeNullOrEmpty();

        // 2022-02-19
        cmsDailyRevenueAnalyticDto = analytics
            .First(x => x.Date == new DateTime(2022, 2, 19).ToString("yyyy-MM-dd"));

        cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(-5.00M);
        cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(1);

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyId.Should().Be(companyId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyName.Should().Be("Sleekflow");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessName.Should()
            .BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("SleekPay");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().DailyAccruedRevenue.Should().Be(-5.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.Should()
            .BeNullOrEmpty();

        // 2022-02-28
        cmsDailyRevenueAnalyticDto = analytics
            .First(x => x.Date == new DateTime(2022, 2, 28).ToString("yyyy-MM-dd"));

        cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(25.00M);
        cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(2);

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].CompanyId.Should().Be(companyId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].CompanyName.Should().Be("Sleekflow");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].FacebookBusinessId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].FacebookBusinessName.Should()
            .BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].Type.Should().Be("SleekPay");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].TopUpType.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].DailyAccruedRevenue.Should().Be(10.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].SleekFlowCompanyIds.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].SleekFlowCompanyNames.Should()
            .BeNullOrEmpty();

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].CompanyId.Should().Be(companyId2);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].CompanyName.Should().Be("Sleekflow2");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].FacebookBusinessId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].FacebookBusinessName.Should()
            .BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].Type.Should().Be("SleekPay");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].TopUpType.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].DailyAccruedRevenue.Should().Be(15.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].SleekFlowCompanyIds.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].SleekFlowCompanyNames.Should()
            .BeNullOrEmpty();

        // All Total Accrued Revenue
        var totalRevenue = Math.Round(
            analytics.Sum(x => x.SubscriptionPlanRevenue + x.OneTimeSetupFeeRevenue +
                               x.SleekPayRevenue + x.TopUpRevenue),
            2);

        totalRevenue.Should().Be(10.00M + 15.00M + 13.00M + 10.00M - 15.00M + 10.00M + 15.00M);
    }

    [Test]
    public void DailyAccruedAnalytics_360Dialog_Test()
    {
        const string companyId = "471a6289-b9b7-43c3-b6ad-395a1992baea";
        const string companyId2 = "571a6289-b9b7-43c3-b6ad-395a1992baea";

        var whatsapp360DialogTopUps = new List<CmsWhatsApp360DialogUsageTopUpTransactionLogDto>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                CompanyName = "Sleekflow",
                Total = 5.00M,
                Currency = "usd",
                IsInternalTestingUse = false,
                TransactionType = WhatsApp360DialogUsageTransactionType.TopUp,
                CreatedAt = new DateTime(2022, 3, 1),
            },
            new ()
            {
                Id = 2,
                CompanyId = companyId,
                CompanyName = "Sleekflow",
                Total = 15.00M,
                Currency = "usd",
                IsInternalTestingUse = false,
                TransactionType = WhatsApp360DialogUsageTransactionType.TopUp,
                CreatedAt = new DateTime(2022, 3, 9),
            },
            new ()
            {
                Id = 3,
                CompanyId = companyId2,
                CompanyName = "Sleekflow2",
                Total = 23.00M,
                Currency = "usd",
                IsInternalTestingUse = false,
                TransactionType = WhatsApp360DialogUsageTransactionType.TopUp,
                CreatedAt = new DateTime(2022, 3, 9),
            },
            new ()
            {
                Id = 4,
                CompanyId = companyId2,
                CompanyName = "Sleekflow2",
                Total = 77.00M,
                Currency = "usd",
                IsInternalTestingUse = false,
                TransactionType = WhatsApp360DialogUsageTransactionType.TopUp,
                CreatedAt = new DateTime(2022, 3, 17),
            },
        };

        var analytics = _billRecordRevenueCalculatorService.GetDailyAccruedAnalytics(
            new List<CmsCompanyAnalyticDto>(),
            new List<CmsSleekPayCompanyAnalyticDto>(),
            whatsapp360DialogTopUps,
            new List<CmsTwilioTopUpLogDto>(),
            new List<CmsWhatsappCloudApiTopUpDto>(),
            new DateTime(2022, 1, 1),
            new DateTime(2022, 4, 1));

        // 2022-03-01
        var cmsDailyRevenueAnalyticDto = analytics
            .First(x => x.Date == new DateTime(2022, 3, 1).ToString("yyyy-MM-dd"));

        cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(5.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(1);

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyId.Should().Be(companyId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyName.Should().Be("Sleekflow");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessName.Should()
            .BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Top-up");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().Be("360 Dialog");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().DailyAccruedRevenue.Should().Be(5.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.Should()
            .BeNullOrEmpty();

        // 2022-03-09
        cmsDailyRevenueAnalyticDto = analytics
            .First(x => x.Date == new DateTime(2022, 3, 9).ToString("yyyy-MM-dd"));

        cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(38.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(2);

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].CompanyId.Should().Be(companyId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].CompanyName.Should().Be("Sleekflow");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].FacebookBusinessId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].FacebookBusinessName.Should()
            .BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Top-up");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().Be("360 Dialog");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].DailyAccruedRevenue.Should().Be(15.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].SleekFlowCompanyIds.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].SleekFlowCompanyNames.Should()
            .BeNullOrEmpty();

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].CompanyId.Should().Be(companyId2);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].CompanyName.Should().Be("Sleekflow2");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].FacebookBusinessId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].FacebookBusinessName.Should()
            .BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Top-up");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().Be("360 Dialog");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].DailyAccruedRevenue.Should().Be(23.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].SleekFlowCompanyIds.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].SleekFlowCompanyNames.Should()
            .BeNullOrEmpty();

        // 2022-03-17
        cmsDailyRevenueAnalyticDto = analytics
            .First(x => x.Date == new DateTime(2022, 3, 17).ToString("yyyy-MM-dd"));

        cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(77.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(1);

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyId.Should().Be(companyId2);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyName.Should().Be("Sleekflow2");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessName.Should()
            .BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Top-up");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().Be("360 Dialog");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().DailyAccruedRevenue.Should().Be(77.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.Should()
            .BeNullOrEmpty();

        // All Total Accrued Revenue
        var totalRevenue = Math.Round(
            analytics.Sum(x => x.SubscriptionPlanRevenue + x.OneTimeSetupFeeRevenue +
                               x.SleekPayRevenue + x.TopUpRevenue),
            2);

        totalRevenue.Should().Be(5.00M + 15.00M + 23.00M + 77.00M);
    }

    [Test]
    public void DailyAccruedAnalytics_Twilio_Test()
    {
        const string companyId = "471a6289-b9b7-43c3-b6ad-395a1992baea";
        const string companyId2 = "571a6289-b9b7-43c3-b6ad-395a1992baea";

        var twilioTopUps = new List<CmsTwilioTopUpLogDto>
        {
            new ()
            {
                Id = 1,
                CompanyId = companyId,
                CompanyName = "Sleekflow",
                TopUpAmount = 5.00M,
                Currency = "usd",
                IsInternalTestingUse = false,
                CreatedAt = new DateTime(2022, 4, 1),
            },
            new ()
            {
                Id = 2,
                CompanyId = companyId,
                CompanyName = "Sleekflow",
                TopUpAmount = 15.00M,
                Currency = "usd",
                IsInternalTestingUse = false,
                CreatedAt = new DateTime(2022, 4, 11),
            },
            new ()
            {
                Id = 3,
                CompanyId = companyId2,
                CompanyName = "Sleekflow2",
                TopUpAmount = 23.00M,
                Currency = "usd",
                IsInternalTestingUse = false,
                CreatedAt = new DateTime(2022, 4, 11),
            },
            new ()
            {
                Id = 4,
                CompanyId = companyId2,
                CompanyName = "Sleekflow2",
                TopUpAmount = 77.00M,
                Currency = "usd",
                IsInternalTestingUse = false,
                CreatedAt = new DateTime(2022, 4, 23),
            },
        };

        var analytics = _billRecordRevenueCalculatorService.GetDailyAccruedAnalytics(
            new List<CmsCompanyAnalyticDto>(),
            new List<CmsSleekPayCompanyAnalyticDto>(),
            new List<CmsWhatsApp360DialogUsageTopUpTransactionLogDto>(),
            twilioTopUps,
            new List<CmsWhatsappCloudApiTopUpDto>(),
            new DateTime(2022, 1, 1),
            new DateTime(2022, 5, 1));

        // 2022-04-01
        var cmsDailyRevenueAnalyticDto = analytics
            .First(x => x.Date == new DateTime(2022, 4, 1).ToString("yyyy-MM-dd"));

        cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(5.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(1);

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyId.Should().Be(companyId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyName.Should().Be("Sleekflow");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessName.Should()
            .BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Top-up");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().Be("Twilio");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().DailyAccruedRevenue.Should().Be(5.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.Should()
            .BeNullOrEmpty();

        // 2022-04-11
        cmsDailyRevenueAnalyticDto = analytics
            .First(x => x.Date == new DateTime(2022, 4, 11).ToString("yyyy-MM-dd"));

        cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(38.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(2);

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].CompanyId.Should().Be(companyId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].CompanyName.Should().Be("Sleekflow");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].FacebookBusinessId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].FacebookBusinessName.Should()
            .BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Top-up");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().Be("Twilio");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].DailyAccruedRevenue.Should().Be(15.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].SleekFlowCompanyIds.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].SleekFlowCompanyNames.Should()
            .BeNullOrEmpty();

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].CompanyId.Should().Be(companyId2);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].CompanyName.Should().Be("Sleekflow2");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].FacebookBusinessId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].FacebookBusinessName.Should()
            .BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Top-up");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().Be("Twilio");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].DailyAccruedRevenue.Should().Be(23.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].SleekFlowCompanyIds.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].SleekFlowCompanyNames.Should()
            .BeNullOrEmpty();

        // 2022-04-23
        cmsDailyRevenueAnalyticDto = analytics
            .First(x => x.Date == new DateTime(2022, 4, 23).ToString("yyyy-MM-dd"));

        cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(77.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(1);

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyId.Should().Be(companyId2);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyName.Should().Be("Sleekflow2");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessName.Should()
            .BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Top-up");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().Be("Twilio");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().DailyAccruedRevenue.Should().Be(77.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.Should()
            .BeNullOrEmpty();

        // All Total Accrued Revenue
        var totalRevenue = Math.Round(
            analytics.Sum(x => x.SubscriptionPlanRevenue + x.OneTimeSetupFeeRevenue +
                               x.SleekPayRevenue + x.TopUpRevenue),
            2);

        totalRevenue.Should().Be(5.00M + 15.00M + 23.00M + 77.00M);
    }

    [Test]
    public void DailyAccruedAnalytics_WhatsAppCloudAPI_Test()
    {
        const string companyId = "471a6289-b9b7-43c3-b6ad-395a1992baea";
        const string companyId2 = "571a6289-b9b7-43c3-b6ad-395a1992baea";
        const string facebookBusinessId = "672a6289-b9b7-43c3-b6ad-395a1992baea";
        const string facebookBusinessId2 = "772a6289-b9b7-43c3-b6ad-395a1992baea";

        var whatsappCloudApiTopUps = new List<CmsWhatsappCloudApiTopUpDto>
        {
            new ()
            {
                FacebookBusinessId = facebookBusinessId,
                FacebookBusinessName = "Sleekflow-FB",
                CreditAmount = 5.00M,
                Currency = "usd",
                IsInternalTestingUse = false,
                CreatedAt = new DateTime(2022, 5, 1),
                SleekFlowCompanyIds = [companyId],
                SleekFlowCompanyNames = ["Sleekflow"]
            },
            new ()
            {
                FacebookBusinessId = facebookBusinessId2,
                FacebookBusinessName = "Sleekflow-FB2",
                CreditAmount = 15.00M,
                Currency = "usd",
                IsInternalTestingUse = false,
                CreatedAt = new DateTime(2022, 5, 17),
                SleekFlowCompanyIds = [companyId, companyId2],
                SleekFlowCompanyNames = ["Sleekflow", "Sleekflow2"]
            },
            new ()
            {
                FacebookBusinessId = facebookBusinessId,
                FacebookBusinessName = "Sleekflow-FB",
                CreditAmount = 35.00M,
                Currency = "usd",
                IsInternalTestingUse = false,
                CreatedAt = new DateTime(2022, 5, 17),
                SleekFlowCompanyIds = [companyId],
                SleekFlowCompanyNames = ["Sleekflow"]
            },
            new ()
            {
                FacebookBusinessId = facebookBusinessId2,
                FacebookBusinessName = "Sleekflow-FB2",
                CreditAmount = 85.00M,
                Currency = "usd",
                IsInternalTestingUse = false,
                CreatedAt = new DateTime(2022, 5, 31),
                SleekFlowCompanyIds = [companyId, companyId2],
                SleekFlowCompanyNames = ["Sleekflow", "Sleekflow2"]
            },
        };

        var analytics = _billRecordRevenueCalculatorService.GetDailyAccruedAnalytics(
            new List<CmsCompanyAnalyticDto>(),
            new List<CmsSleekPayCompanyAnalyticDto>(),
            new List<CmsWhatsApp360DialogUsageTopUpTransactionLogDto>(),
            new List<CmsTwilioTopUpLogDto>(),
            whatsappCloudApiTopUps,
            new DateTime(2022, 1, 1),
            new DateTime(2022, 6, 1));

        // 2022-05-01
        var cmsDailyRevenueAnalyticDto = analytics
            .First(x => x.Date == new DateTime(2022, 5, 1).ToString("yyyy-MM-dd"));

        cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(5.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(1);

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyName.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessId.Should()
            .Be(facebookBusinessId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessName.Should()
            .Be("Sleekflow-FB");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Top-up");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().Be("WhatsApp Cloud Api");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().DailyAccruedRevenue.Should().Be(5.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.Count.Should().Be(1);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.Count.Should()
            .Be(1);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.First().Should()
            .Be(companyId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.First().Should()
            .Be("Sleekflow");

        // 2022-05-17
        cmsDailyRevenueAnalyticDto = analytics
            .First(x => x.Date == new DateTime(2022, 5, 17).ToString("yyyy-MM-dd"));

        cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(50.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(2);

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].CompanyId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].CompanyName.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].FacebookBusinessId.Should().Be(facebookBusinessId2);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].FacebookBusinessName.Should()
            .Be("Sleekflow-FB2");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].Type.Should().Be("Top-up");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].TopUpType.Should().Be("WhatsApp Cloud Api");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].DailyAccruedRevenue.Should().Be(15.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].SleekFlowCompanyIds.Count.Should().Be(2);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].SleekFlowCompanyNames.Count.Should()
            .Be(2);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].SleekFlowCompanyIds.First().Should()
            .Be(companyId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[0].SleekFlowCompanyNames.First().Should()
            .Be("Sleekflow");

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].CompanyId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].CompanyName.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].FacebookBusinessId.Should().Be(facebookBusinessId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].FacebookBusinessName.Should()
            .Be("Sleekflow-FB");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].Type.Should().Be("Top-up");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].TopUpType.Should().Be("WhatsApp Cloud Api");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].DailyAccruedRevenue.Should().Be(35.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].SleekFlowCompanyIds.Count.Should().Be(1);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].SleekFlowCompanyNames.Count.Should()
            .Be(1);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].SleekFlowCompanyIds.First().Should()
            .Be(companyId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns[1].SleekFlowCompanyNames.First().Should()
            .Be("Sleekflow");

        // 2022-05-31
        cmsDailyRevenueAnalyticDto = analytics
            .First(x => x.Date == new DateTime(2022, 5, 31).ToString("yyyy-MM-dd"));

        cmsDailyRevenueAnalyticDto.SubscriptionPlanRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.OneTimeSetupFeeRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.SleekPayRevenue.Should().Be(0);
        cmsDailyRevenueAnalyticDto.TopUpRevenue.Should().Be(85.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.Count.Should().Be(1);

        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyId.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().CompanyName.Should().BeNullOrEmpty();
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessId.Should()
            .Be(facebookBusinessId2);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().FacebookBusinessName.Should()
            .Be("Sleekflow-FB2");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().Type.Should().Be("Top-up");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().TopUpType.Should().Be("WhatsApp Cloud Api");
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().DailyAccruedRevenue.Should().Be(85.00M);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.Count.Should().Be(2);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.Count.Should()
            .Be(2);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyIds.First().Should()
            .Be(companyId);
        cmsDailyRevenueAnalyticDto.CmsAccruedRevenueBreakDowns.First().SleekFlowCompanyNames.First().Should()
            .Be("Sleekflow");

        // All Total Accrued Revenue
        var totalRevenue = Math.Round(
            analytics.Sum(x => x.SubscriptionPlanRevenue + x.OneTimeSetupFeeRevenue +
                               x.SleekPayRevenue + x.TopUpRevenue),
            2);

        totalRevenue.Should().Be(5.00M + 15.00M + 35.00M + 85.00M);
    }
}