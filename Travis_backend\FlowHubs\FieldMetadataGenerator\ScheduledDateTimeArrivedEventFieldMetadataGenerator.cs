﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Database.Services;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Models;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Utils;

namespace Travis_backend.FlowHubs.FieldMetadataGenerator;

public sealed class ScheduledDateTimeArrivedEventFieldMetadataGenerator
    : FlowHubEventFieldMetadataGeneratorBase,
        IFlowHubEventFieldMetadataGenerator
{
    public string EventName => TriggerIds.ScheduledDataAndTimeArrived;

    public Dictionary<string, string> FieldPathOptionRequestPathDict { get; } = new()
    {
        {
            "contact.labels[*].LabelValue", "FlowHub/SelectOptions/Labels"
        },
        {
            "lists[*].id", "FlowHub/SelectOptions/ContactLists"
        },
        {
            "contactOwner.id", "FlowHub/SelectOptions/Staffs"
        },
    };

    public ScheduledDateTimeArrivedEventFieldMetadataGenerator(
        IDbContextService dbContextService,
        IFieldMetadataVariableVisibilityService fieldMetadataVariableVisibilityService)
        : base(dbContextService, fieldMetadataVariableVisibilityService)
    {
    }

    public async Task<(HashSet<FieldMetadata> FieldMetadataSet, Type EventBodyType, string ContactObjectName)> GenerateAsync(
        string companyId,
        Dictionary<string, object?>? parameters)
    {
        var (userProfileDict, customFieldOptionsRequestPathDict) = await GenerateUserProfileDictAsync(companyId);

        var eventBody = JsonUtils.GenerateSampleObjectFromType<OnDateAndTimeArrivedCommonEventBody>();
        eventBody.Contact = userProfileDict;
        eventBody.WorkflowId = null!;
        eventBody.WorkflowVersionedId = null!;

        var fieldMetadataSet = JsonUtils
            .SimplifyJsonData(eventBody.ToJson())
            .GetFieldMetadata();

        PopulateOptionRequestPath(
            fieldMetadataSet,
            customFieldOptionsRequestPathDict,
            FieldPathOptionRequestPathDict);

        return (fieldMetadataSet, typeof(OnDateAndTimeArrivedCommonEventBody), nameof(eventBody.Contact));
    }
}