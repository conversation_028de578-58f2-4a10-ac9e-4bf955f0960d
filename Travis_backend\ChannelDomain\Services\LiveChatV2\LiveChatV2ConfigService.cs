using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels.LiveChatV2;
using Travis_backend.ConversationServices;
using Travis_backend.Database.Services;
using Travis_backend.FileDomain.Services;

namespace Travis_backend.ChannelDomain.Services.LiveChatV2;

public interface ILiveChatV2ConfigService
{
    Task CreateConfigAsync(CreateLiveChatV2ConfigInput input);

    Task UpdateConfigAsync(string id, UpdateLiveChatV2ConfigInput input, string companyId);

    Task<LiveChatV2Config> GetConfigByChannelIdentityIdAsync(string channelIdentityId);

    Task<LiveChatV2Config> GetConfigAsync(string id);
}

public class LiveChatV2ConfigService : ILiveChatV2ConfigService
{
    private readonly IDbContextService _dbContextService;
    private readonly IUploadService _uploadService;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly ILogger<LiveChatV2ConfigService> _logger;

    public LiveChatV2ConfigService(IDbContextService dbContextService, IUploadService uploadService, IAzureBlobStorageService azureBlobStorageService, ILogger<LiveChatV2ConfigService> logger)
    {
        _dbContextService = dbContextService;
        _uploadService = uploadService;
        _azureBlobStorageService = azureBlobStorageService;
        _logger = logger;
    }

    public async Task CreateConfigAsync(CreateLiveChatV2ConfigInput input)
    {
        var dbContext = _dbContextService.GetDbContext();

        // Create the default configuration
        var defaultSettings = new
        {
            branding = new
            {
                companyLogoUrl = "",
                widgetDisplayName = $"{input.Company.CompanyName} Live Chat",
                senderDisplayName = "Agent"
            },
            languages = new
            {
                availableLanguage = new Dictionary<string, bool>()
                {
                    { "en", true },
                    { "zh", false },
                    { "zh-HK", false },
                    { "zh-CN", false },
                    { "de", false },
                    { "pt-BR", false },
                    { "it", false },
                    { "id-ID", false }
                },
                defaultLanguage = "en"
            },
            displayLogic = new
            {
                widgetPosition = "bottom-right",
                buttonColor = "#0066FF",
                messageBackgroundColor = "#0D122C"
            },
            preChatForm = new
            {
                enabled = false,
                isEmailRequired = true,
                isPhoneNumberRequired = false
            },
            popupMessages = new object[0],
            channels = new
            {
                options = new
                {
                    whatsapp = (object) null,
                    facebook = (object) null,
                    instagram = (object) null,
                    wechat = (object) null,
                    line = (object) null,
                    viber = (object) null,
                    telegram = (object) null,
                    livechat = (object) null
                },
                availableChannels = new
                {
                    whatsapp = false,
                    facebook = false,
                    instagram = false,
                    wechat = false,
                    line = false,
                    viber = false,
                    telegram = false,
                    livechat = true
                }
            },
            additionalSettings = new
            {
                welcomeMessage = "Hello and welcome! Got questions or need assistance? Just let me know—I'm all ears!"
            }
        }
        ;

        var liveChatV2Config = new LiveChatV2Config
        {
            CompanyId = input.Company.Id,
            Settings = JsonConvert.SerializeObject(defaultSettings),
            ChannelIdentityId = Guid.NewGuid().ToString(), // Generate a unique ID for this config
            ChannelDisplayName = $"{input.Company.CompanyName} Live Chat",
            IsDeleted = false,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        dbContext.ConfigLiveChatV2Configs.Add(liveChatV2Config);
        await dbContext.SaveChangesAsync();
    }

    public async Task UpdateConfigAsync(string id, UpdateLiveChatV2ConfigInput input, string companyId)
    {
        var dbContext = _dbContextService.GetDbContext();

        var config = await dbContext.ConfigLiveChatV2Configs
            .FirstOrDefaultAsync(c => c.Id == long.Parse(id) && c.CompanyId == companyId && !c.IsDeleted);

        if (config == null)
        {
            var errorMessage = $"[{nameof(UpdateConfigAsync)}] LiveChat V2 configuration with ID {id} not found, does not belong to your company, or has been deleted.";
            _logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        // Parse the new settings from JSON string
        var newSettings = string.IsNullOrEmpty(input.Settings)
            ? new Settings()
            : JsonConvert.DeserializeObject<Settings>(input.Settings);

        _logger.LogInformation("[{MethodName}] New settings: {NewSettings}", nameof(UpdateConfigAsync), JsonConvert.SerializeObject(JsonConvert.DeserializeObject<Settings>(input.Settings)));

        // Handle company logo file upload if provided
        if (input.CompanyLogoFile != null)
        {
            var fileName = $"LiveChatV2/CompanyLogo/{id}/{DateTime.UtcNow:yyyyMMddHHmmss}/{input.CompanyLogoFile.FileName}";
            var uploadResult = await _uploadService.UploadFile(companyId, fileName, input.CompanyLogoFile);
            var url = _azureBlobStorageService.GetAzureBlobSasUriForever(fileName, companyId);

            if (url != null)
            {
                // Ensure branding object exists in settings
                newSettings.Branding.CompanyLogoUrl = url;
                _logger.LogInformation("[{MethodName}] New Branding object: {Branding}", nameof(UpdateConfigAsync), newSettings.Branding);
            }
        }

        // Channel DisplayName is always the widget displayName
        config.ChannelDisplayName = newSettings.Branding.WidgetDisplayName;

        // Update the configuration
        config.Settings = JsonConvert.SerializeObject(newSettings);
        config.UpdatedAt = DateTime.UtcNow;

        await dbContext.SaveChangesAsync();

        _logger.LogInformation("[{MethodName}] Updated LiveChat V2 configuration with ID {Id}", nameof(UpdateConfigAsync), id);
    }

    public async Task<LiveChatV2Config> GetConfigAsync(string id)
    {
        var dbContext = _dbContextService.GetDbContext();

        var config = await dbContext.ConfigLiveChatV2Configs
            .FirstOrDefaultAsync(c => c.ChannelIdentityId == id && !c.IsDeleted);

        if (config == null)
        {
            var errorMessage = $"[{nameof(GetConfigAsync)}] LiveChat V2 configuration with ChannelIdentityId {id} not found or has been deleted.";
            _logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        return config;
    }

    public async Task<LiveChatV2Config> GetConfigByChannelIdentityIdAsync(string channelIdentityId)
    {
        var dbContext = _dbContextService.GetDbContext();

        var config = await dbContext.ConfigLiveChatV2Configs
            .FirstOrDefaultAsync(c => c.ChannelIdentityId == channelIdentityId && !c.IsDeleted);

        if (config == null)
        {
            var errorMessage = $"[{nameof(GetConfigByChannelIdentityIdAsync)}] LiveChat V2 configuration with ChannelIdentityId {channelIdentityId} not found or has been deleted.";
            _logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        return config;
    }
}