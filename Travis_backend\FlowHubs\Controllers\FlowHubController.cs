#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net.Mime;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PhoneNumbers;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.BackgroundTaskServices;
using Travis_backend.BackgroundTaskServices.Helpers;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.Data.BackgroundTask;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Filters;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Infrastructures.Attributes;
using Travis_backend.TenantHubDomain.Models;
using Travis_backend.TenantHubDomain.Services;
using GetSchemafulObjectInput = Sleekflow.Apis.CrmHub.Model.GetSchemafulObjectInput;
using ISchemafulObjectsApi = Sleekflow.Apis.CrmHub.Api.ISchemafulObjectsApi;
using UsageLimit = Sleekflow.Apis.FlowHub.Model.UsageLimit;
using Microsoft.Extensions.Logging;

namespace Travis_backend.FlowHubs.Controllers;

[Authorize]
[Route("FlowHub")]
[TypeFilter(typeof(FlowHubExceptionFilter))]
public class FlowHubController : ControllerBase
{
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ICoreService _coreService;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IBlobsApi _blobsApi;
    private readonly IExecutionsApi _executionsApi;
    private readonly IFlowHubConfigsApi _flowHubConfigsApi;
    private readonly IWorkflowsApi _workflowsApi;
    private readonly IWorkflowGroupsApi _workflowGroupsApi;
    private readonly INeedConfigsApi _needConfigsApi;
    private readonly IStatesApi _statesApi;
    private readonly ISchemafulObjectsApi _schemafulObjectsApi;
    private readonly ICompanyTeamService _companyTeamService;
    private readonly ICompanySubscriptionService _companySubscriptionService;
    private readonly IConfiguration _configuration;
    private readonly IFlowHubScheduledWorkflowEnrollmentService _flowHubScheduledWorkflowEnrollmentService;
    private readonly IFlowHubService _flowHubService;
    private readonly IFlowHubConfigService _flowHubConfigService;
    private readonly IBackgroundTaskService _backgroundTaskService;
    private readonly IEnabledFeaturesService _enabledFeaturesService;
    private readonly IFeaturesService _featuresService;
    private readonly IAiFlowAutoSetUpService _aiFlowAutoSetUpService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<FlowHubController> _logger;
    private readonly IAiWorkflowsApi _aiWorkflowsApi;

    public FlowHubController(
        UserManager<ApplicationUser> userManager,
        ICoreService coreService,
        ApplicationDbContext appDbContext,
        IBlobsApi blobsApi,
        IExecutionsApi executionsApi,
        IFlowHubConfigsApi flowHubConfigsApi,
        IWorkflowsApi workflowsApi,
        IWorkflowGroupsApi workflowGroupsApi,
        INeedConfigsApi needConfigsApi,
        IStatesApi statesApi,
        ISchemafulObjectsApi schemafulObjectsApi,
        ICompanyTeamService companyTeamService,
        ICompanySubscriptionService companySubscriptionService,
        IConfiguration configuration,
        IFlowHubScheduledWorkflowEnrollmentService flowHubScheduledWorkflowEnrollmentService,
        IFlowHubService flowHubService,
        IFlowHubConfigService flowHubConfigService,
        IBackgroundTaskService backgroundTaskService,
        IEnabledFeaturesService enabledFeaturesService,
        IFeaturesService featuresService,
        IAiFlowAutoSetUpService aiFlowAutoSetUpService,
        IAiWorkflowsApi aiWorkflowsApi,
        IHttpContextAccessor httpContextAccessor,
        ILogger<FlowHubController> logger
    )
    {
        _userManager = userManager;
        _coreService = coreService;
        _appDbContext = appDbContext;
        _blobsApi = blobsApi;
        _executionsApi = executionsApi;
        _flowHubConfigsApi = flowHubConfigsApi;
        _workflowsApi = workflowsApi;
        _workflowGroupsApi = workflowGroupsApi;
        _needConfigsApi = needConfigsApi;
        _statesApi = statesApi;
        _schemafulObjectsApi = schemafulObjectsApi;
        _companyTeamService = companyTeamService;
        _configuration = configuration;
        _flowHubScheduledWorkflowEnrollmentService = flowHubScheduledWorkflowEnrollmentService;
        _flowHubService = flowHubService;
        _flowHubConfigService = flowHubConfigService;
        _backgroundTaskService = backgroundTaskService;
        _companySubscriptionService = companySubscriptionService;
        _enabledFeaturesService = enabledFeaturesService;
        _featuresService = featuresService;
        _aiFlowAutoSetUpService = aiFlowAutoSetUpService;
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
        _aiWorkflowsApi = aiWorkflowsApi;
    }

    private async Task<List<string>> GetTeamIdsBySleekflowStaffAsync(Staff sleekflowStaff)
    {
        var companyId = sleekflowStaff.CompanyId;
        var identityId = sleekflowStaff.IdentityId;

        var companyTeams = await _companyTeamService.GetCompanyteamFromStaffId(companyId, identityId);

        return companyTeams.Select(t => t.Id.ToString()).ToList();
    }

    public class CreateBlobDownloadSasUrlsRequest
    {
        [JsonProperty("blob_names")]
        public List<string> BlobNames { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    [HttpPost("Blobs/CreateBlobDownloadSasUrls")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateBlobDownloadSasUrlsOutputOutput>> CreateBlobDownloadSasUrls(
        [FromBody]
        CreateBlobDownloadSasUrlsRequest createBlobDownloadSasUrlsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createBlobDownloadSasUrlsOutputOutput = await _blobsApi.BlobsCreateBlobDownloadSasUrlsPostAsync(
            createBlobDownloadSasUrlsInput: new CreateBlobDownloadSasUrlsInput(
                staff.CompanyId,
                createBlobDownloadSasUrlsRequest.BlobNames,
                createBlobDownloadSasUrlsRequest.BlobType));

        return Ok(createBlobDownloadSasUrlsOutputOutput);
    }

    public class CreateBlobUploadSasUrlsRequest
    {
        [JsonProperty("number_of_blobs")]
        public int NumberOfBlobs { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }
    }

    [HttpPost("Blobs/CreateBlobUploadSasUrls")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateBlobUploadSasUrlsOutputOutput>> CreateBlobUploadSasUrls(
        [FromBody]
        CreateBlobUploadSasUrlsRequest createBlobUploadSasUrlsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createBlobUploadSasUrlsOutputOutput = await _blobsApi.BlobsCreateBlobUploadSasUrlsPostAsync(
            createBlobUploadSasUrlsInput: new CreateBlobUploadSasUrlsInput(
                staff.CompanyId,
                createBlobUploadSasUrlsRequest.NumberOfBlobs,
                createBlobUploadSasUrlsRequest.BlobType));

        return Ok(createBlobUploadSasUrlsOutputOutput);
    }

    public class GetStateStepExecutionsRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("state_id")]
        public string StateId { get; set; }
    }

    [HttpPost("Executions/GetStateStepExecutions")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetStateStepExecutionsOutputOutput>> GetStateStepExecutions(
        [FromBody]
        GetStateStepExecutionsRequest getStateStepExecutionsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getStateStepExecutionsOutputOutput = await _executionsApi.ExecutionsGetStateStepExecutionsPostAsync(
            getStateStepExecutionsInput: new GetStateStepExecutionsInput(
                staff.CompanyId,
                getStateStepExecutionsRequest.ContinuationToken,
                getStateStepExecutionsRequest.Limit,
                getStateStepExecutionsRequest.StateId));

        var contactIdToName = await GetContactIdToNameDictAsync(
            staff.CompanyId,
            getStateStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact" or "Contact.Id")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var phoneNumberToIdName = await GetPhoneNumberToIdNameDictAsync(
            staff.CompanyId,
            getStateStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.PhoneNumber")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g =>
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(g.Key)))
                .ToList());

        var emailToIdName = await GetEmailToIdNameDictAsync(
            staff.CompanyId,
            getStateStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.Email")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var schemafulObjectToIdName = await GetSchemafulObjectToNameDictAsync(
            staff.CompanyId,
            getStateStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "SchemafulObject")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        foreach (var stepExecution in getStateStepExecutionsOutputOutput.Data.StepExecutions)
        {
            ProfileDisplayName? profileIdMatch = null;
            ProfileDisplayName? phoneMatch = null;
            ProfileDisplayName? emailMatch = null;
            ProfileDisplayName? schemafulObjectMatch = null;

            stepExecution.StateIdentity.ObjectLabel = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => contactIdToName.TryGetValue(stepExecution.StateIdentity.ObjectId, out profileIdMatch)
                    ? profileIdMatch.GetDisplayName()
                    : string.Empty,
                "Contact.PhoneNumber" => phoneNumberToIdName.TryGetValue(
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(stepExecution.StateIdentity.ObjectId)), out phoneMatch)
                    ? phoneMatch.GetDisplayName()
                    : string.Empty,
                "Contact.Email" => emailToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out emailMatch)
                    ? emailMatch.GetDisplayName()
                    : string.Empty,
                "SchemafulObject" => schemafulObjectToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out schemafulObjectMatch)
                    ? schemafulObjectMatch.GetDisplayName()
                    : string.Empty,
                _ => stepExecution.StateIdentity.ObjectLabel
            };

            stepExecution.StateIdentity.ObjectResolvedId = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => profileIdMatch?.ProfileId ?? string.Empty,
                "Contact.PhoneNumber" => phoneMatch?.ProfileId ?? string.Empty,
                "Contact.Email" => emailMatch?.ProfileId ?? string.Empty,
                "SchemafulObject" => schemafulObjectMatch?.ProfileId ?? string.Empty,
                _ => stepExecution.StateIdentity.ObjectResolvedId
            };
        }

        return Ok(getStateStepExecutionsOutputOutput);
    }

    public class GetWorkflowExecutionsRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("filters")]
        public GetWorkflowExecutionsInputFilters Filters { get; set; }
    }

    [HttpPost("Executions/GetWorkflowExecutions")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowExecutionsOutputOutput>> GetWorkflowExecutions(
        [FromBody]
        GetWorkflowExecutionsRequest getWorkflowExecutionsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowExecutionsOutputOutput = await _executionsApi.ExecutionsGetWorkflowExecutionsPostAsync(
            getWorkflowExecutionsInput: new GetWorkflowExecutionsInput(
                staff.CompanyId,
                getWorkflowExecutionsRequest.ContinuationToken,
                getWorkflowExecutionsRequest.Limit,
                getWorkflowExecutionsRequest.Filters));

        var contactIdToName = await GetContactIdToNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact" or "Contact.Id")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var phoneNumberToIdName = await GetPhoneNumberToIdNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.PhoneNumber")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g =>
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(g.Key)))
                .ToList());

        var emailToIdName = await GetEmailToIdNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.Email")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var schemafulObjectToIdName = await GetSchemafulObjectToNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "SchemafulObject")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        foreach (var stepExecution in getWorkflowExecutionsOutputOutput.Data.WorkflowExecutions)
        {
            ProfileDisplayName? profileIdMatch = null;
            ProfileDisplayName? phoneMatch = null;
            ProfileDisplayName? emailMatch = null;
            ProfileDisplayName? schemafulObjectMatch = null;

            stepExecution.StateIdentity.ObjectLabel = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => contactIdToName.TryGetValue(stepExecution.StateIdentity.ObjectId, out profileIdMatch)
                    ? profileIdMatch.GetDisplayName()
                    : string.Empty,
                "Contact.PhoneNumber" => phoneNumberToIdName.TryGetValue(
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(stepExecution.StateIdentity.ObjectId)),
                    out phoneMatch)
                    ? phoneMatch.GetDisplayName()
                    : string.Empty,
                "Contact.Email" => emailToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out emailMatch)
                    ? emailMatch.GetDisplayName()
                    : string.Empty,
                "SchemafulObject" => schemafulObjectToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out schemafulObjectMatch)
                    ? schemafulObjectMatch.GetDisplayName()
                    : string.Empty,
                _ => stepExecution.StateIdentity.ObjectLabel
            };

            stepExecution.StateIdentity.ObjectResolvedId = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => profileIdMatch?.ProfileId ?? string.Empty,
                "Contact.PhoneNumber" => phoneMatch?.ProfileId ?? string.Empty,
                "Contact.Email" => emailMatch?.ProfileId ?? string.Empty,
                "SchemafulObject" => schemafulObjectMatch?.ProfileId ?? string.Empty,
                _ => stepExecution.StateIdentity.ObjectResolvedId
            };
        }

        return Ok(getWorkflowExecutionsOutputOutput);
    }

    public class GetWorkflowExecutionRequest
    {
        [JsonProperty("state_id")]
        public string StateId { get; set; }
    }

    [HttpPost("Executions/GetWorkflowExecution")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowExecutionOutput>> GetWorkflowExecution(
        [FromBody]
        GetWorkflowExecutionRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var getWorkflowExecutionOutputOutput =
            await _executionsApi.ExecutionsGetWorkflowExecutionPostAsync(
                getWorkflowExecutionInput: new GetWorkflowExecutionInput(
                    staff.CompanyId,
                    request.StateId));

        switch (getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectType)
        {
            case "Contact" or "Contact.Id":
                var contactId = getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectId;

                var contactIdToName = await GetContactIdToNameDictAsync(
                    staff.CompanyId,
                    new List<string> { contactId });

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectLabel =
                    contactIdToName.TryGetValue(contactId, out var profileIdMatch)
                        ? profileIdMatch.GetDisplayName()
                        : string.Empty;

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectResolvedId =
                    contactIdToName.ContainsKey(contactId)
                        ? contactId
                        : string.Empty;

                break;

            case "Contact.PhoneNumber":
                var phoneNumber = PhoneNumberUtil.ExtractPossibleNumber(
                    PhoneNumberUtil.NormalizeDigitsOnly(
                        getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectId));

                var phoneNumberToIdName = await GetPhoneNumberToIdNameDictAsync(
                    staff.CompanyId,
                    new List<string> { phoneNumber });

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectLabel =
                    phoneNumberToIdName.TryGetValue(phoneNumber, out var phoneMatch)
                    ? phoneMatch.GetDisplayName()
                    : string.Empty;

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectResolvedId =
                    phoneMatch?.ProfileId ?? string.Empty;

                break;

            case "Contact.Email":
                var email = getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectId;

                var emailToIdName = await GetEmailToIdNameDictAsync(
                    staff.CompanyId,
                    new List<string> { email });

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectLabel =
                    emailToIdName.TryGetValue(email, out var emailMatch)
                    ? emailMatch.GetDisplayName()
                    : string.Empty;

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectResolvedId =
                    emailMatch?.ProfileId ?? string.Empty;

                break;

            case "SchemafulObject":
                var schemafulObjectId = getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectId;

                var schemafulObjectToIdName = await GetSchemafulObjectToNameDictAsync(
                    staff.CompanyId,
                    new List<string>() { schemafulObjectId });

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectLabel =
                    schemafulObjectToIdName.TryGetValue(schemafulObjectId, out var schemafulObjectMatch)
                        ? schemafulObjectMatch.GetDisplayName()
                        : string.Empty;

                getWorkflowExecutionOutputOutput.Data.WorkflowExecution.StateIdentity.ObjectResolvedId =
                    schemafulObjectMatch?.ProfileId ?? string.Empty;

                break;
        }

        return Ok(getWorkflowExecutionOutputOutput);
    }

    public class GetWorkflowExecutionsByStateRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("filters")]
        public GetWorkflowExecutionsByStateInputFilters Filters { get; set; }
    }

    [HttpPost("Executions/GetWorkflowExecutionsByState")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowExecutionsByStateOutputOutput>> GetWorkflowExecutionsByState(
        [FromBody]
        GetWorkflowExecutionsByStateRequest getWorkflowExecutionsByStateRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowExecutionsByStateOutputOutput =
            await _executionsApi.ExecutionsGetWorkflowExecutionsByStatePostAsync(
                getWorkflowExecutionsByStateInput: new GetWorkflowExecutionsByStateInput(
                    staff.CompanyId,
                    getWorkflowExecutionsByStateRequest.ContinuationToken,
                    getWorkflowExecutionsByStateRequest.Limit,
                    getWorkflowExecutionsByStateRequest.Filters));

        var contactIdToName = await GetContactIdToNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsByStateOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact" or "Contact.Id")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var phoneNumberToIdName = await GetPhoneNumberToIdNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsByStateOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.PhoneNumber")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g =>
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(g.Key)))
                .ToList());

        var emailToIdName = await GetEmailToIdNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsByStateOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.Email")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var schemafulObjectToIdName = await GetSchemafulObjectToNameDictAsync(
            staff.CompanyId,
            getWorkflowExecutionsByStateOutputOutput.Data.WorkflowExecutions
                .Where(se => se.StateIdentity.ObjectType is "SchemafulObject")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        foreach (var stepExecution in getWorkflowExecutionsByStateOutputOutput.Data.WorkflowExecutions)
        {
            ProfileDisplayName? profileIdMatch = null;
            ProfileDisplayName? phoneMatch = null;
            ProfileDisplayName? emailMatch = null;
            ProfileDisplayName? schemafulObjectMatch = null;

            stepExecution.StateIdentity.ObjectLabel = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => contactIdToName.TryGetValue(stepExecution.StateIdentity.ObjectId, out profileIdMatch)
                    ? profileIdMatch.GetDisplayName()
                    : string.Empty,
                "Contact.PhoneNumber" => phoneNumberToIdName.TryGetValue(
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(stepExecution.StateIdentity.ObjectId)),
                    out phoneMatch)
                    ? phoneMatch.GetDisplayName()
                    : string.Empty,
                "Contact.Email" => emailToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out emailMatch)
                    ? emailMatch.GetDisplayName()
                    : string.Empty,
                "SchemafulObject" => schemafulObjectToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out schemafulObjectMatch)
                    ? schemafulObjectMatch.GetDisplayName()
                    : string.Empty,
                _ => stepExecution.StateIdentity.ObjectLabel
            };

            stepExecution.StateIdentity.ObjectResolvedId = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => profileIdMatch?.ProfileId ?? string.Empty,
                "Contact.PhoneNumber" => phoneMatch?.ProfileId ?? string.Empty,
                "Contact.Email" => emailMatch?.ProfileId ?? string.Empty,
                "SchemafulObject" => schemafulObjectMatch?.ProfileId ?? string.Empty,
                _ => stepExecution.StateIdentity.ObjectResolvedId
            };
        }

        return Ok(getWorkflowExecutionsByStateOutputOutput);
    }

    public class GetWorkflowExecutionStatisticsRequest
    {
        [JsonProperty("filters")]
        public GetWorkflowExecutionStatisticsInputFilters Filters { get; set; }
    }

    [HttpPost("Executions/GetWorkflowExecutionStatistics")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowExecutionStatisticsOutputOutput>> GetWorkflowExecutionStatistics(
        [FromBody]
        GetWorkflowExecutionStatisticsRequest getWorkflowExecutionStatisticsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowExecutionStatisticsOutputOutput =
            await _executionsApi.ExecutionsGetWorkflowExecutionStatisticsPostAsync(
                getWorkflowExecutionStatisticsInput:new GetWorkflowExecutionStatisticsInput(
                    staff.CompanyId,
                    getWorkflowExecutionStatisticsRequest.Filters));

        return Ok(getWorkflowExecutionStatisticsOutputOutput);
    }

    [HttpPost("Executions/GetMonetizedWorkflowExecutionStatistics")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetMonetizedWorkflowExecutionStatisticsOutputOutput>>
        GetMonetizedWorkflowExecutionStatistics(
            [FromBody]
            GetWorkflowExecutionStatisticsRequest getWorkflowExecutionStatisticsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getMonetizedWorkflowExecutionStatisticsOutput =
            await _executionsApi.ExecutionsGetMonetizedWorkflowExecutionStatisticsPostAsync(
                getMonetizedWorkflowExecutionStatisticsInput: new GetMonetizedWorkflowExecutionStatisticsInput(
                    staff.CompanyId,
                    getWorkflowExecutionStatisticsRequest.Filters));
        return Ok(getMonetizedWorkflowExecutionStatisticsOutput);
    }

    public class GetWorkflowStepExecutionsRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [JsonProperty("step_id")]
        public string StepId { get; set; }
    }

    [HttpPost("Executions/GetWorkflowStepExecutions")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowStepExecutionsOutputOutput>> GetWorkflowStepExecutions(
        [FromBody]
        GetWorkflowStepExecutionsRequest getWorkflowStepExecutionsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowStepExecutionsOutputOutput = await _executionsApi.ExecutionsGetWorkflowStepExecutionsPostAsync(
            getWorkflowStepExecutionsInput: new GetWorkflowStepExecutionsInput(
                staff.CompanyId,
                getWorkflowStepExecutionsRequest.ContinuationToken,
                getWorkflowStepExecutionsRequest.Limit,
                getWorkflowStepExecutionsRequest.WorkflowId,
                getWorkflowStepExecutionsRequest.WorkflowVersionedId,
                getWorkflowStepExecutionsRequest.StepId));

        var contactIdToName = await GetContactIdToNameDictAsync(
            staff.CompanyId,
            getWorkflowStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact" or "Contact.Id")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var phoneNumberToIdName = await GetPhoneNumberToIdNameDictAsync(
            staff.CompanyId,
            getWorkflowStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.PhoneNumber")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g =>
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(g.Key)))
                .ToList());

        var emailToIdName = await GetEmailToIdNameDictAsync(
            staff.CompanyId,
            getWorkflowStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "Contact.Email")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        var schemafulObjectToIdName = await GetSchemafulObjectToNameDictAsync(
            staff.CompanyId,
            getWorkflowStepExecutionsOutputOutput.Data.StepExecutions
                .Where(se => se.StateIdentity.ObjectType is "SchemafulObject")
                .GroupBy(se => se.StateIdentity.ObjectId)
                .Select(g => g.Key)
                .ToList());

        foreach (var stepExecution in getWorkflowStepExecutionsOutputOutput.Data.StepExecutions)
        {
            ProfileDisplayName? profileIdMatch = null;
            ProfileDisplayName? phoneMatch = null;
            ProfileDisplayName? emailMatch = null;
            ProfileDisplayName? schemafulObjectMatch = null;

            stepExecution.StateIdentity.ObjectLabel = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => contactIdToName.TryGetValue(stepExecution.StateIdentity.ObjectId, out profileIdMatch)
                    ? profileIdMatch.GetDisplayName()
                    : string.Empty,
                "Contact.PhoneNumber" => phoneNumberToIdName.TryGetValue(
                    PhoneNumberUtil.ExtractPossibleNumber(
                        PhoneNumberUtil.NormalizeDigitsOnly(stepExecution.StateIdentity.ObjectId)), out phoneMatch)
                    ? phoneMatch.GetDisplayName()
                    : string.Empty,
                "Contact.Email" => emailToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out emailMatch)
                    ? emailMatch.GetDisplayName()
                    : string.Empty,
                "SchemafulObject" => schemafulObjectToIdName.TryGetValue(stepExecution.StateIdentity.ObjectId, out schemafulObjectMatch)
                    ? schemafulObjectMatch.GetDisplayName()
                    : string.Empty,
                _ => stepExecution.StateIdentity.ObjectLabel
            };

            stepExecution.StateIdentity.ObjectResolvedId = stepExecution.StateIdentity.ObjectType switch
            {
                "Contact" or "Contact.Id" => profileIdMatch?.ProfileId ?? string.Empty,
                "Contact.PhoneNumber" => phoneMatch?.ProfileId ?? string.Empty,
                "Contact.Email" => emailMatch?.ProfileId ?? string.Empty,
                "SchemafulObject" => schemafulObjectMatch?.ProfileId ?? string.Empty,
                _ => stepExecution.StateIdentity.ObjectResolvedId
            };
        }

        return Ok(getWorkflowStepExecutionsOutputOutput);
    }

    private async Task<Dictionary<string, ProfileDisplayName>> GetContactIdToNameDictAsync(
        string companyId,
        ICollection<string> contactIds)
    {
        if (contactIds is not { Count: > 0 })
        {
            return new Dictionary<string, ProfileDisplayName>();
        }

        var contactIdToName = await _appDbContext.UserProfiles
            .Where(up =>
                up.CompanyId == companyId
                && contactIds.Contains(up.Id)
                && up.ActiveStatus == ActiveStatus.Active)
            .Select(
                up => new
                {
                    up.Id,
                    up.FirstName,
                    up.LastName,
                    up.PhoneNumber,
                    up.Email
                })
            .ToDictionaryAsync(
                up => up.Id,
                up => new ProfileDisplayName(
                    up.Id,
                    up.FirstName + " " + up.LastName,
                    up.PhoneNumber,
                    up.Email));

        return contactIdToName;
    }

    private sealed record ProfileDisplayName(string ProfileId, string DisplayName, string PhoneNumber, string Email)
    {
        public string GetDisplayName()
        {
            if (!string.IsNullOrWhiteSpace(DisplayName))
            {
                return DisplayName;
            }

            if (!string.IsNullOrWhiteSpace(PhoneNumber))
            {
                return PhoneNumber;
            }

            if (!string.IsNullOrWhiteSpace(Email))
            {
                return Email;
            }

            return string.Empty;
        }
    }

    private async Task<Dictionary<string, ProfileDisplayName>> GetPhoneNumberToIdNameDictAsync(
        string companyId,
        ICollection<string> phoneNumbers)
    {
        if (phoneNumbers is not { Count: > 0 })
        {
            return new Dictionary<string, ProfileDisplayName>();
        }

        var phoneNumberToIdName = await _appDbContext.UserProfiles
            .Where(up =>
                up.CompanyId == companyId
                && phoneNumbers.Contains(up.PhoneNumber)
                && up.ActiveStatus == ActiveStatus.Active)
            .Select(
                up => new
                {
                    up.Id,
                    up.FirstName,
                    up.LastName,
                    up.PhoneNumber,
                    up.Email,
                })
            .ToDictionaryAsync(
                up => up.PhoneNumber,
                up => new ProfileDisplayName(
                    up.Id,
                    up.FirstName + " " + up.LastName,
                    up.PhoneNumber,
                    up.Email));

        return phoneNumberToIdName;
    }

    private async Task<Dictionary<string, ProfileDisplayName>> GetEmailToIdNameDictAsync(
        string companyId,
        ICollection<string> emails)
    {
        if (emails is not { Count: > 0 })
        {
            return new Dictionary<string, ProfileDisplayName>();
        }

        var emailToIdName = await _appDbContext.UserProfiles
            .Where(up =>
                up.CompanyId == companyId
                && emails.Contains(up.Email)
                && up.ActiveStatus == ActiveStatus.Active)
            .Select(
                up => new
                {
                    up.Id,
                    up.FirstName,
                    up.LastName,
                    up.PhoneNumber,
                    up.Email
                })
            .ToDictionaryAsync(
                up => up.Email,
                up => new ProfileDisplayName(
                    up.Id,
                    up.FirstName + " " + up.LastName,
                    up.PhoneNumber,
                    up.Email));

        return emailToIdName;
    }

    /// <summary>
    /// DEVS-7841 - State.Identity of schemaful object related triggers has been changed.
    /// Retain this method for handling old data.
    ///
    /// Get schemaful object referenced contact name.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="objectIds">Format [SchemaId]:[SchemafulObjectId].</param>
    /// <returns>ProfileDisplayName collection.</returns>
    private async Task<Dictionary<string, ProfileDisplayName>> GetSchemafulObjectToNameDictAsync(
        string companyId,
        ICollection<string> objectIds)
    {
        if (objectIds is not { Count: > 0 })
        {
            return new Dictionary<string, ProfileDisplayName>();
        }

        var objectIdToContactIdDict = new Dictionary<string, string>();
        foreach (var objectId in objectIds)
        {
            try
            {
                var parts = objectId.Split(':');
                var schemaId = parts[0];
                var schemafulObjectId = parts[1];

                var contactId = (await _schemafulObjectsApi.SchemafulObjectsGetSchemafulObjectPostAsync(
                        getSchemafulObjectInput: new GetSchemafulObjectInput(
                            companyId,
                            schemaId,
                            schemafulObjectId)))
                    .Data
                    .SchemafulObject
                    .SleekflowUserProfileId;

                objectIdToContactIdDict.Add(objectId, contactId);
            }
            catch (Exception)
            {
                // ignored
            }
        }

        var contactIdToNameDict = await GetContactIdToNameDictAsync(companyId, objectIdToContactIdDict.Values);

        var objectIdToNameDict = objectIdToContactIdDict
            .Where(kvp => contactIdToNameDict.ContainsKey(kvp.Value))
            .ToDictionary(
                kvp => kvp.Key,
                kvp => new ProfileDisplayName(
                    kvp.Value,
                    contactIdToNameDict[kvp.Value].DisplayName,
                    contactIdToNameDict[kvp.Value].PhoneNumber,
                    contactIdToNameDict[kvp.Value].Email));

        return objectIdToNameDict;
    }

    public class CancelWorkflowExecutionRequest
    {
        [JsonProperty("state_id")]
        public string StateId { get; set; }
    }

    [HttpPost("Executions/CancelWorkflowExecution")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowStepExecutionsOutputOutput>> CancelWorkflowExecution(
        [FromBody] CancelWorkflowExecutionRequest cancelWorkflowExecutionRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var cancelWorkflowExecutionOutput =
            await _executionsApi.ExecutionsCancelWorkflowExecutionPostAsync(
                cancelWorkflowExecutionInput: new CancelWorkflowExecutionInput(
                    staff.CompanyId,
                    cancelWorkflowExecutionRequest.StateId,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(cancelWorkflowExecutionOutput);
    }

    public class GetWorkflowExecutionUsagesRequest
    {
        [JsonProperty("filters")]
        public WorkflowExecutionUsageFilters? Filters { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }
    }

    [HttpPost("Executions/GetWorkflowExecutionUsages")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowExecutionUsagesOutputOutput>> GetWorkflowExecutionUsages(
        [FromBody] GetWorkflowExecutionUsagesRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var getWorkflowExecutionUsagesOutputOutput =
            await _executionsApi.ExecutionsGetWorkflowExecutionUsagesPostAsync(
                getWorkflowExecutionUsagesInput: new GetWorkflowExecutionUsagesInput(
                    staff.CompanyId,
                    request.Filters,
                    request.Limit,
                    request.ContinuationToken));

        return Ok(getWorkflowExecutionUsagesOutputOutput);
    }

    public class GetUniqueWorkflowExecutionCountRequest
    {
        [Required]
        [JsonProperty("execution_from_date_time")]
        public DateTimeOffset ExecutionFromDateTime { get; set; }

        [Required]
        [JsonProperty("execution_to_date_time")]
        public DateTimeOffset ExecutionToDateTime { get; set; }

        [JsonProperty("workflow_type")]
        public string? WorkflowType { get; set; }
    }

    [HttpPost("Executions/GetUniqueWorkflowExecutionCount")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetUniqueWorkflowExecutionCountOutputOutput>> GetUniqueWorkflowExecutionCount(
        [FromBody] GetUniqueWorkflowExecutionCountRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var getUniqueWorkflowExecutionCountOutputOutput =
            await _executionsApi.ExecutionsGetUniqueWorkflowExecutionCountPostAsync(
                getUniqueWorkflowExecutionCountInput: new GetUniqueWorkflowExecutionCountInput(
                    staff.CompanyId,
                    request.ExecutionFromDateTime,
                    request.ExecutionToDateTime,
                    request.WorkflowType));

        return Ok(getUniqueWorkflowExecutionCountOutputOutput);
    }

    public class ExportWorkflowExecutionUsagesToCsvRequest
    {
        [JsonProperty("filters")]
        public WorkflowExecutionUsageFilters? Filters { get; set; }
    }

    [HttpPost("Executions/ExportWorkflowExecutionUsagesToCsv")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<BackgroundTaskViewModel>> ExportWorkflowExecutionUsagesToCsv(
        [FromBody] ExportWorkflowExecutionUsagesToCsvRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var backgroundTask = await _backgroundTaskService.EnqueueExportFlowHubWorkflowExecutionUsagesToCsvTask(
            staff.IdentityId,
            staff.CompanyId,
            staff.Id,
            request.Filters);

        return Ok(backgroundTask.MapToResultViewModel());
    }

    public class EnrollFlowHubRequest
    { }

    [HttpPost("FlowHubConfigs/EnrollFlowHub")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<EnrollFlowHubOutputOutput>> EnrollFlowHub(
        [FromBody]
        EnrollFlowHubRequest enrollFlowHubRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var enrollFlowHubOutputOutput = await _flowHubConfigsApi.FlowHubConfigsEnrollFlowHubPostAsync(
            enrollFlowHubInput: new EnrollFlowHubInput(
                staff.CompanyId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff),
                _configuration.GetValue<string>("Values:DomainName")));

        return Ok(enrollFlowHubOutputOutput);
    }

    public class GetFlowHubConfigRequest
    { }

    [HttpPost("FlowHubConfigs/GetFlowHubConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetFlowHubConfigOutputOutput>> GetFlowHubConfig(
        [FromBody]
        GetFlowHubConfigRequest getFlowHubConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getFlowHubConfigOutputOutput = await _flowHubConfigsApi.FlowHubConfigsGetFlowHubConfigPostAsync(
            getFlowHubConfigInput: new GetFlowHubConfigInput(
                staff.CompanyId));

        return Ok(getFlowHubConfigOutputOutput);
    }

    public class EnableUsageLimitAutoScalingRequest
    {
        [Required]
        [JsonProperty("is_enable_usage_limit_auto_scaling")]
        public bool IsEnableUsageLimitAutoScaling { get; set; }
    }

    [HttpPost("FlowHubConfigs/EnableUsageLimitAutoScaling")]
    [Consumes("application/json")]
    [Produces("application/json")]
    [AuthoriseUser]
    public async Task<IActionResult> EnableUsageLimitAutoScaling([FromBody] EnableUsageLimitAutoScalingRequest request)
    {
        var companyId = _httpContextAccessor.HttpContext.GetRequestCompanyId();

        var enableUsageLimitAutoScalingOutputOutput = await _flowHubConfigsApi.FlowHubConfigsEnableUsageLimitAutoScalingPostAsync(
            enableUsageLimitAutoScalingInput: new EnableUsageLimitAutoScalingInput(
                companyId,
                request.IsEnableUsageLimitAutoScaling));

        return Ok(enableUsageLimitAutoScalingOutputOutput);
    }

    public class UnenrollFlowHubRequest
    { }

    [HttpPost("FlowHubConfigs/UnenrollFlowHub")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UnenrollFlowHubOutputOutput>> UnenrollFlowHub(
        [FromBody]
        UnenrollFlowHubRequest unenrollFlowHubRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var unenrollFlowHubOutputOutput = await _flowHubConfigsApi.FlowHubConfigsUnenrollFlowHubPostAsync(
            unenrollFlowHubInput: new UnenrollFlowHubInput(
                staff.CompanyId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(unenrollFlowHubOutputOutput);
    }

    public class UpdateFlowHubConfigRequest
    { }

    [HttpPost("FlowHubConfigs/UpdateFlowHubConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateFlowHubConfigOutputOutput>> UpdateFlowHubConfig(
        [FromBody]
        UpdateFlowHubConfigRequest updateFlowHubConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        var usageLimit = await _flowHubConfigService.CalculateFlowHubUsageLimitAsync(staff.CompanyId);

        var updateFlowHubConfigOutputOutput = await _flowHubConfigsApi.FlowHubConfigsUpdateFlowHubConfigPostAsync(
            updateFlowHubConfigInput: new UpdateFlowHubConfigInput(
                sleekflowCompanyId: staff.CompanyId,
                sleekflowStaffId: staff.Id.ToString(),
                sleekflowStaffTeamIds: await GetTeamIdsBySleekflowStaffAsync(staff),
                usageLimit: usageLimit,
                origin: _configuration.GetValue<string>("Values:DomainName")));

        return Ok(updateFlowHubConfigOutputOutput);
    }

    public class GetObjectStatesRequest
    {
        [JsonProperty("object_id")]
        public string ObjectId { get; set; }

        [JsonProperty("object_type")]
        public string ObjectType { get; set; }

        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }
    }

    [HttpPost("States/GetObjectStates")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetObjectStatesOutputOutput>> GetObjectStates(
        [FromBody]
        GetObjectStatesRequest getObjectStatesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getObjectStatesOutputOutput = await _statesApi.StatesGetObjectStatesPostAsync(
            getObjectStatesInput: new GetObjectStatesInput(
                staff.CompanyId,
                getObjectStatesRequest.ObjectId,
                getObjectStatesRequest.ObjectType,
                getObjectStatesRequest.ContinuationToken,
                getObjectStatesRequest.Limit));

        return Ok(getObjectStatesOutputOutput);
    }

    public class BulkReenrollStatesRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("reenrollment_period")]
        public string ReenrollmentPeriod { get; set; }
    }

    [HttpPost("States/BulkReenrollStates")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<BulkReenrollStatesOutputOutput>> BulkReenrollStates(
        [FromBody]
        BulkReenrollStatesRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var bulkReenrollStatesOutputOutput = await _statesApi.StatesBulkReenrollStatesPostAsync(
            bulkReenrollStatesInput: new BulkReenrollStatesInput(
                staff.CompanyId,
                request.WorkflowId,
                request.ReenrollmentPeriod));

        return Ok(bulkReenrollStatesOutputOutput);
    }

    public class GetStatesRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("filters")]
        public GetStatesInputFilters Filters { get; set; }
    }

    [HttpPost("States/GetStates")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetStatesOutputOutput>> GetStates(
        [FromBody]
        GetStatesRequest getStatesRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getStatesOutputOutput = await _statesApi.StatesGetStatesPostAsync(
            getStatesInput: new GetStatesInput(
                staff.CompanyId,
                getStatesRequest.ContinuationToken,
                getStatesRequest.Limit,
                getStatesRequest.Filters));

        return Ok(getStatesOutputOutput);
    }

    public class CreateWorkflowRequest
    {
        [JsonProperty("triggers")]
        public WorkflowTriggers Triggers { get; set; }

        [JsonProperty("steps")]
        public List<JObject> Steps { get; set; }

        [JsonProperty("workflow_enrollment_settings")]
        public WorkflowEnrollmentSettings WorkflowEnrollmentSettings { get; set; }

        [JsonProperty("workflow_schedule_settings")]
        public WorkflowScheduleSettings WorkflowScheduleSettings { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("workflow_type")]
        public string WorkflowType { get; set; }

        [JsonProperty("workflow_group_id")]
        public string WorkflowGroupId { get; set; }

        [JsonProperty("metadata")]
        public Dictionary<string, object> Metadata { get; set; }

        [JsonProperty("version")]
        public string Version { get; set; }

        [JsonProperty("is_dynamic_variable_enabled")]
        public bool IsDynamicVariableEnabled { get; set; }
    }

    [HttpPost("Workflows/CreateWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateWorkflowOutputOutput>> CreateWorkflow(
        [FromBody]
        CreateWorkflowRequest createWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }


        // For new workflows, we always set isDynamicVariableEnabled to true
        createWorkflowRequest.IsDynamicVariableEnabled = true;

        var createWorkflowOutputOutput = await _workflowsApi.WorkflowsCreateWorkflowPostAsync(
            createWorkflowInput: new CreateWorkflowInput(
                sleekflowCompanyId: staff.CompanyId,
                triggers: createWorkflowRequest.Triggers,
                workflowEnrollmentSettings: createWorkflowRequest.WorkflowEnrollmentSettings,
                workflowScheduleSettings: createWorkflowRequest.WorkflowScheduleSettings,
                steps: createWorkflowRequest.Steps.Select(s => (object) s).ToList(),
                name: createWorkflowRequest.Name,
                workflowType: createWorkflowRequest.WorkflowType,
                workflowGroupId: createWorkflowRequest.WorkflowGroupId,
                metadata: createWorkflowRequest.Metadata,
                isDynamicVariableEnabled: createWorkflowRequest.IsDynamicVariableEnabled,
                version: createWorkflowRequest.Version,
                sleekflowStaffId: staff.Id.ToString(),
                sleekflowStaffTeamIds: await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(createWorkflowOutputOutput);
    }

    public class GetWorkflowWebhookTriggersRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/GetWorkflowWebhookTriggers")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowWebhookTriggersOutputOutput>> GetWorkflowWebhookTriggers(
        [FromBody] GetWorkflowWebhookTriggersRequest getWorkflowWebhookTriggersRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowWebhookTriggersOutputOutput =
            await _workflowsApi.WorkflowsGetWorkflowWebhookTriggersPostAsync(
                getWorkflowWebhookTriggersInput: new GetWorkflowWebhookTriggersInput(
                    staff.CompanyId,
                    getWorkflowWebhookTriggersRequest.WorkflowId));

        return Ok(getWorkflowWebhookTriggersOutputOutput);
    }

    public class CreateWorkflowWebhookTriggerRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("object_id_expression")]
        public string ObjectIdExpression { get; set; }

        [JsonProperty("object_type")]
        public string ObjectType { get; set; }
    }

    [HttpPost("Workflows/CreateWorkflowWebhookTrigger")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateWorkflowWebhookTriggerOutputOutput>> CreateWorkflowWebhookTrigger(
        [FromBody]
        CreateWorkflowWebhookTriggerRequest createWorkflowWebhookTriggerRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createWorkflowWebhookTriggerOutputOutput =
            await _workflowsApi.WorkflowsCreateWorkflowWebhookTriggerPostAsync(
                createWorkflowWebhookTriggerInput: new CreateWorkflowWebhookTriggerInput(
                    staff.CompanyId,
                    createWorkflowWebhookTriggerRequest.WorkflowId,
                    createWorkflowWebhookTriggerRequest.ObjectIdExpression,
                    createWorkflowWebhookTriggerRequest.ObjectType,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(createWorkflowWebhookTriggerOutputOutput);
    }

    public class GetOrCreateWorkflowWebhookTriggerRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/GetOrCreateWorkflowWebhookTrigger")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateWorkflowWebhookTriggerOutputOutput>> GetOrCreateWorkflowWebhookTrigger(
        [FromBody]
        GetOrCreateWorkflowWebhookTriggerRequest getOrCreateWorkflowWebhookTriggerRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getOrCreateWorkflowWebhookTriggerOutputOutput =
            await _workflowsApi.WorkflowsGetOrCreateWorkflowWebhookTriggerPostAsync(
                getOrCreateWorkflowWebhookTriggerInput: new GetOrCreateWorkflowWebhookTriggerInput(
                    staff.CompanyId,
                    getOrCreateWorkflowWebhookTriggerRequest.WorkflowId,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(getOrCreateWorkflowWebhookTriggerOutputOutput);
    }

    public class UpdateWorkflowWebhookTriggerRequest
    {
        [JsonProperty("workflow_webhook_trigger_id")]
        public string WorkflowWebhookTriggerId { get; set; }

        [JsonProperty("object_id_expression")]
        public string ObjectIdExpression { get; set; }

        [JsonProperty("object_type")]
        public string ObjectType { get; set; }
    }

    [HttpPost("Workflows/UpdateWorkflowWebhookTrigger")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateWorkflowWebhookTriggerOutputOutput>> UpdateWorkflowWebhookTrigger(
        [FromBody] UpdateWorkflowWebhookTriggerRequest updateWorkflowWebhookTriggerRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var updateWorkflowWebhookTriggerOutputOutput =
            await _workflowsApi.WorkflowsUpdateWorkflowWebhookTriggerPostAsync(
                updateWorkflowWebhookTriggerInput: new UpdateWorkflowWebhookTriggerInput(
                    staff.CompanyId,
                    updateWorkflowWebhookTriggerRequest.WorkflowWebhookTriggerId,
                    updateWorkflowWebhookTriggerRequest.ObjectIdExpression,
                    updateWorkflowWebhookTriggerRequest.ObjectType,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(updateWorkflowWebhookTriggerOutputOutput);
    }


    public class GetWorkflowWebhookUrlOutput
    {
        [JsonProperty("workflow_webhook_trigger_url")]
        public string WorkflowWebhookTriggerUrl { get; set; }

        public GetWorkflowWebhookUrlOutput(string workflowWebhookTriggerUrl)
        {
            WorkflowWebhookTriggerUrl = workflowWebhookTriggerUrl;
        }
    }

    [HttpGet("Workflows/GetWorkflowWebhookTriggerUrl")]
    public ActionResult<GetWorkflowWebhookUrlOutput> GetWorkflowWebhookTriggerUrl()
    {
        var workflowWebhookTriggerUrl = new Uri(_workflowsApi.Configuration.BasePath)
            .AppendPath("Public/e")
            .AbsoluteUri;

        return new GetWorkflowWebhookUrlOutput(workflowWebhookTriggerUrl);
    }

    public class DeleteVersionedWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    [HttpPost("Workflows/DeleteVersionedWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteVersionedWorkflowOutputOutput>> DeleteVersionedWorkflow(
        [FromBody]
        DeleteVersionedWorkflowRequest deleteVersionedWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteVersionedWorkflowOutputOutput = await _workflowsApi.WorkflowsDeleteVersionedWorkflowPostAsync(
            deleteVersionedWorkflowInput: new DeleteVersionedWorkflowInput(
                staff.CompanyId,
                deleteVersionedWorkflowRequest.WorkflowId,
                deleteVersionedWorkflowRequest.WorkflowVersionedId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(deleteVersionedWorkflowOutputOutput);
    }

    public class DeleteWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/DeleteWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteWorkflowOutputOutput>> DeleteWorkflow(
        [FromBody]
        DeleteWorkflowRequest deleteWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var latestWorkflowRequest = await _workflowsApi.WorkflowsGetLatestWorkflowPostAsync(getLatestWorkflowInput: new GetLatestWorkflowInput(
                staff.CompanyId,
                workflowId: deleteWorkflowRequest.WorkflowId));

        var workflow = latestWorkflowRequest.Data.LatestWorkflow;

        if (_aiFlowAutoSetUpService.IsAiFlow(workflow))
        {
            var isDeleted = await _aiFlowAutoSetUpService.DeleteDependencyTags(staff.CompanyId, workflow.Steps, deleteWorkflowRequest.WorkflowId);
            if (!isDeleted)
            {
                _logger.LogError("Failed to delete dependency tags");
            }
        }

        var deleteWorkflowOutputOutput = await _workflowsApi.WorkflowsDeleteWorkflowPostAsync(
            deleteWorkflowInput: new DeleteWorkflowInput(
                staff.CompanyId,
                deleteWorkflowRequest.WorkflowId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        if (deleteWorkflowOutputOutput.Success)
        {
            _flowHubScheduledWorkflowEnrollmentService.CancelScheduledWorkflowEnrollment(
                staff.CompanyId,
                deleteWorkflowRequest.WorkflowId);
        }

        return Ok(deleteWorkflowOutputOutput);
    }

    public class ScheduleDeleteWorkflowsRequest
    {
        [JsonProperty("workflow_ids")]
        public List<string> WorkflowIds { get; set; }
    }

    [HttpPost("Workflows/ScheduleDeleteWorkflows")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ScheduleDeleteWorkflowsOutputOutput>> ScheduleDeleteWorkflows(
        [FromBody]
        ScheduleDeleteWorkflowsRequest scheduleDeleteWorkflowsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var scheduleDeleteWorkflowsOutputOutput = await _workflowsApi.WorkflowsScheduleDeleteWorkflowsPostAsync(
            scheduleDeleteWorkflowsInput: new ScheduleDeleteWorkflowsInput(
                staff.CompanyId,
                scheduleDeleteWorkflowsRequest.WorkflowIds,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        if (scheduleDeleteWorkflowsOutputOutput.Success)
        {
            foreach (var workflowId in scheduleDeleteWorkflowsRequest.WorkflowIds)
            {
                _flowHubScheduledWorkflowEnrollmentService.CancelScheduledWorkflowEnrollment(staff.CompanyId, workflowId);
            }
        }

        return Ok(scheduleDeleteWorkflowsOutputOutput);
    }

    public class DisableWorkflowRequest
    {
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    [HttpPost("Workflows/DisableWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DisableWorkflowOutputOutput>> DisableWorkflow(
        [FromBody]
        DisableWorkflowRequest disableWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getVersionedWorkflowOutputOutput = await _workflowsApi.WorkflowsGetVersionedWorkflowPostAsync(
           getVersionedWorkflowInput: new GetVersionedWorkflowInput(
               staff.CompanyId,
               disableWorkflowRequest.WorkflowVersionedId));

        if (!getVersionedWorkflowOutputOutput.Success)
        {
            throw new Exception($"Failed to get versioned workflow {disableWorkflowRequest.WorkflowVersionedId}");
        }

        var workflow = getVersionedWorkflowOutputOutput.Data.Workflow;
        if (_aiFlowAutoSetUpService.IsAiFlow(workflow))
        {
            var isDeleted = await _aiFlowAutoSetUpService.DeleteDependencyTags(staff.CompanyId, workflow.Steps, workflow.WorkflowId);
            if (!isDeleted)
            {
                _logger.LogError("Failed to delete dependency tags");
            }
        }

        var disableWorkflowOutputOutput = await _workflowsApi.WorkflowsDisableWorkflowPostAsync(
            disableWorkflowInput: new DisableWorkflowInput(
                staff.CompanyId,
                disableWorkflowRequest.WorkflowVersionedId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        if (disableWorkflowOutputOutput.Success)
        {
            _flowHubScheduledWorkflowEnrollmentService.CancelScheduledWorkflowEnrollment(
                staff.CompanyId,
                disableWorkflowOutputOutput.Data.Workflow.WorkflowId);
        }

        return Ok(disableWorkflowOutputOutput);
    }

    public class DuplicateWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/DuplicateWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DuplicateWorkflowOutputOutput>> DuplicateWorkflow(
        [FromBody]
        DuplicateWorkflowRequest duplicateWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var duplicateWorkflowOutputOutput = await _workflowsApi.WorkflowsDuplicateWorkflowPostAsync(
            duplicateWorkflowInput: new DuplicateWorkflowInput(
                staff.CompanyId,
                duplicateWorkflowRequest.WorkflowId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(duplicateWorkflowOutputOutput);
    }

    public class EnableWorkflowRequest
    {
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    [HttpPost("Workflows/EnableWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<EnableWorkflowOutputOutput>> EnableWorkflow(
        [FromBody]
        EnableWorkflowRequest enableWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var enableWorkflowOutputOutput = await _workflowsApi.WorkflowsEnableWorkflowPostAsync(
            enableWorkflowInput: new EnableWorkflowInput(
                staff.CompanyId,
                enableWorkflowRequest.WorkflowVersionedId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        if (enableWorkflowOutputOutput.Success)
        {
            _flowHubScheduledWorkflowEnrollmentService.EnqueueScheduledWorkflowEnrollment(enableWorkflowOutputOutput.Data.Workflow);
        }

        return Ok(enableWorkflowOutputOutput);
    }

    public class EnableWorkflowDraftRequest
    {
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    [HttpPost("Workflows/EnableWorkflowDraft")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<EnableWorkflowDraftOutputOutput>> EnableWorkflowDraft(
        [FromBody]
        EnableWorkflowDraftRequest enableWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getVersionedWorkflowOutputOutput = await _workflowsApi.WorkflowsGetVersionedWorkflowPostAsync(
            getVersionedWorkflowInput: new GetVersionedWorkflowInput(
                staff.CompanyId,
                enableWorkflowRequest.WorkflowVersionedId));
        if (!getVersionedWorkflowOutputOutput.Success)
        {
            throw new Exception($"Failed to get versioned workflow {enableWorkflowRequest.WorkflowVersionedId}");
        }

        var workflow = getVersionedWorkflowOutputOutput.Data.Workflow;
        var workflowVersionedId = workflow.WorkflowVersionedId;

        if (_aiFlowAutoSetUpService.IsAiFlow(workflow))
        {
            Dictionary<string, object> schemaful_object = new Dictionary<string, object>();
            Dictionary<string, object> contact_property = new Dictionary<string, object>();

            try
            {
                var dependencyResult = await _aiFlowAutoSetUpService.CreateDependencyForAiFlow(enableWorkflowRequest.WorkflowVersionedId, staff.CompanyId);

                if (dependencyResult != null && dependencyResult.SchemafulObject != null)
                {
                    schemaful_object = new Dictionary<string, object>
                    {
                        { "id", dependencyResult.SchemafulObject["id"] },
                        { "properties", dependencyResult.SchemafulObject["properties"] }
                    };
                }

                if (dependencyResult != null && dependencyResult.ContactProperty != null)
                {
                    contact_property = new Dictionary<string, object>
                    {
                        { "id", dependencyResult.ContactProperty.Id },
                        { "label", dependencyResult.ContactProperty.FieldName }
                    };
                }
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ManualEnrollWorkflowResponse
                    {
                        Success = false,
                        Message = "Failed to create dependency for AI flow"
                    });
            }

            var input = new UpdateAiAgentWorkflowsByAiNodeInput(
                       sleekflowCompanyId: staff.CompanyId,
                       sleekflowStaffId: staff.Id.ToString(),
                       workflowId: workflow.WorkflowId,
                       schemafulObject: schemaful_object,
                       contactProperty: contact_property);

            var result = await _workflowsApi.WorkflowsUpdateAiAgentWorkflowsByAiNodePostAsync(updateAiAgentWorkflowsByAiNodeInput: input);

            if (!result.Success)
            {
                return BadRequest(
                    new ManualEnrollWorkflowResponse
                    {
                        Success = false,
                        Message = "Failed to create AI flow"
                    });
            }

            var latestWorkflowRequest = await _workflowsApi.WorkflowsGetLatestWorkflowPostAsync(getLatestWorkflowInput: new GetLatestWorkflowInput(
                staff.CompanyId,
                workflowId: workflow.WorkflowId));
            if (!latestWorkflowRequest.Success)
            {
                return BadRequest(
                    new ManualEnrollWorkflowResponse
                    {
                        Success = false,
                        Message = "Failed to get latest work flow"
                    });
            }

            workflowVersionedId = latestWorkflowRequest.Data.LatestWorkflow.WorkflowVersionedId;
        }

        var enableWorkflowDraftOutputOutput = await _workflowsApi.WorkflowsEnableWorkflowDraftPostAsync(
            enableWorkflowDraftInput: new EnableWorkflowDraftInput(
                staff.CompanyId,
                workflowVersionedId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        if (enableWorkflowDraftOutputOutput.Success)
        {
            _flowHubScheduledWorkflowEnrollmentService.EnqueueScheduledWorkflowEnrollment(enableWorkflowDraftOutputOutput.Data.Workflow);
        }

        return Ok(enableWorkflowDraftOutputOutput);
    }

    public class GetWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/GetWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowOutputOutput>> GetWorkflow(
        [FromBody]
        GetWorkflowRequest getWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowOutputOutput = await _workflowsApi.WorkflowsGetWorkflowPostAsync(
            getWorkflowInput: new GetWorkflowInput(
                staff.CompanyId,
                getWorkflowRequest.WorkflowId));

        return Ok(getWorkflowOutputOutput);
    }

    public class GetActiveWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/GetActiveWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetActiveWorkflowOutputOutput>> GetActiveWorkflow(
        [FromBody]
        GetActiveWorkflowRequest getActiveWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getActiveWorkflowOutputOutput = await _workflowsApi.WorkflowsGetActiveWorkflowPostAsync(
            getActiveWorkflowInput: new GetActiveWorkflowInput(
                staff.CompanyId,
                getActiveWorkflowRequest.WorkflowId));

        return Ok(getActiveWorkflowOutputOutput);
    }

    public class GetLatestWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/GetLatestWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetLatestWorkflowOutputOutput>> GetLatestWorkflow(
        [FromBody]
        GetLatestWorkflowRequest getLatestWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getLatestWorkflowOutputOutput = await _workflowsApi.WorkflowsGetLatestWorkflowPostAsync(
            getLatestWorkflowInput: new GetLatestWorkflowInput(
                staff.CompanyId,
                getLatestWorkflowRequest.WorkflowId));

        return Ok(getLatestWorkflowOutputOutput);
    }

    public class GetPostWorkflowPublishedEnrollmentInfoRequest
    {
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }
    }

    [HttpPost("Workflows/GetPostWorkflowPublishedEnrollmentInfo")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetPostWorkflowPublishedEnrollmentInfoOutputOutput>> GetPostWorkflowPublishedEnrollmentInfo(
        [FromBody]
        GetPostWorkflowPublishedEnrollmentInfoRequest getPostWorkflowPublishedEnrollmentInfoRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getPostWorkflowPublishedEnrollmentInfoOutputOutput = await _workflowsApi.WorkflowsGetPostWorkflowPublishedEnrollmentInfoPostAsync(
            getPostWorkflowPublishedEnrollmentInfoInput: new GetPostWorkflowPublishedEnrollmentInfoInput(
                getPostWorkflowPublishedEnrollmentInfoRequest.WorkflowVersionedId,
                staff.CompanyId));

        return Ok(getPostWorkflowPublishedEnrollmentInfoOutputOutput);
    }

    public class GetWorkflowsRequest
    {
        [JsonProperty("continuation_token")]
        public string ContinuationToken { get; set; }

        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("search_name")]
        public string SearchName { get; set; }

        [JsonProperty("workflow_filters")]
        public WorkflowFilters WorkflowFilters { get; set; }
    }

    [HttpPost("Workflows/GetWorkflows")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowsOutputOutput>> GetWorkflows(
        [FromBody] GetWorkflowsRequest getWorkflowsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowsOutputOutput = await _workflowsApi.WorkflowsGetWorkflowsPostAsync(
            getWorkflowsInput: new GetWorkflowsInput(
                staff.CompanyId,
                getWorkflowsRequest.ContinuationToken,
                getWorkflowsRequest.Limit,
                getWorkflowsRequest.SearchName,
                getWorkflowsRequest.WorkflowFilters));

        return Ok(getWorkflowsOutputOutput);
    }

    public class GetVersionedWorkflowRequest
    {
        [JsonProperty("versioned_workflow_id")]
        public string VersionedWorkflowId { get; set; }
    }

    [HttpPost("Workflows/GetVersionedWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetVersionedWorkflowOutputOutput>> GetVersionedWorkflow(
        [FromBody]
        GetVersionedWorkflowRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var getVersionedWorkflowOutputOutput = await _workflowsApi.WorkflowsGetVersionedWorkflowPostAsync(
            getVersionedWorkflowInput: new GetVersionedWorkflowInput(
                staff.CompanyId,
                request.VersionedWorkflowId));

        return Ok(getVersionedWorkflowOutputOutput);
    }

    public class CountWorkflowsRequest
    {
        [JsonProperty("workflow_type")]
        public string? WorkflowType { get; set; }
    }

    [HttpPost("Workflows/CountWorkflows")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CountWorkflowsOutputOutput>> CountWorkflows(
        [FromBody]
        CountWorkflowsRequest countWorkflowsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var countWorkflowsOutputOutput = await _workflowsApi.WorkflowsCountWorkflowsPostAsync(
            countWorkflowsInput: new CountWorkflowsInput(staff.CompanyId, countWorkflowsRequest.WorkflowType!));

        return Ok(countWorkflowsOutputOutput);
    }


    public class SwapWorkflowsRequest
    {
        [JsonProperty("source_workflow_versioned_id")]
        public string SourceWorkflowVersionedId { get; set; }

        [JsonProperty("target_workflow_versioned_id")]
        public string TargetWorkflowVersionedId { get; set; }
    }

    [HttpPost("Workflows/SwapWorkflows")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<SwapWorkflowsOutputOutput>> SwapWorkflows(
        [FromBody]
        SwapWorkflowsRequest swapWorkflowsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var swapWorkflowsOutputOutput = await _workflowsApi.WorkflowsSwapWorkflowsPostAsync(
            swapWorkflowsInput: new SwapWorkflowsInput(
                staff.CompanyId,
                swapWorkflowsRequest.SourceWorkflowVersionedId,
                swapWorkflowsRequest.TargetWorkflowVersionedId,
                staff.Id.ToString(),
                await GetTeamIdsBySleekflowStaffAsync(staff)));

        if (swapWorkflowsOutputOutput.Success)
        {
            _flowHubScheduledWorkflowEnrollmentService.CancelScheduledWorkflowEnrollment(
                staff.CompanyId,
                swapWorkflowsOutputOutput.Data.TargetWorkflow.WorkflowId);

            _flowHubScheduledWorkflowEnrollmentService.EnqueueScheduledWorkflowEnrollment(swapWorkflowsOutputOutput.Data.TargetWorkflow);
        }

        return Ok(swapWorkflowsOutputOutput);
    }

    public class UpdateWorkflowRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("triggers")]
        public WorkflowTriggers Triggers { get; set; }

        [JsonProperty("workflow_enrollment_settings")]
        public WorkflowEnrollmentSettings WorkflowEnrollmentSettings { get; set; }

        [JsonProperty("workflow_schedule_settings")]
        public WorkflowScheduleSettings WorkflowScheduleSettings { get; set; }

        [JsonProperty("steps")]
        public List<JObject> Steps { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("workflow_group_id")]
        public string WorkflowGroupId { get; set; }

        [JsonProperty("metadata")]
        public Dictionary<string, object> Metadata { get; set; }

        [JsonProperty("is_dynamic_variable_enabled")]
        public bool IsDynamicVariableEnabled { get; set; }
    }

    [HttpPost("Workflows/UpdateWorkflow")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateWorkflowOutputOutput>> UpdateWorkflow(
        [FromBody]
        UpdateWorkflowRequest updateWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        // Use our own backend logic to determine the value - ignore the client input
        bool isDynamicVariableEnabled = await GetPreservedDynamicVariableEnabledValue(
            staff.CompanyId,
            updateWorkflowRequest.WorkflowId);

        updateWorkflowRequest.IsDynamicVariableEnabled = isDynamicVariableEnabled;

        var updateWorkflowOutputOutput = await _workflowsApi.WorkflowsUpdateWorkflowPostAsync(
            updateWorkflowInput: new UpdateWorkflowInput(
                sleekflowCompanyId: staff.CompanyId,
                workflowId: updateWorkflowRequest.WorkflowId,
                triggers: updateWorkflowRequest.Triggers,
                workflowEnrollmentSettings: updateWorkflowRequest.WorkflowEnrollmentSettings,
                workflowScheduleSettings: updateWorkflowRequest.WorkflowScheduleSettings,
                steps: updateWorkflowRequest.Steps.Select(s => (object) s).ToList(),
                name: updateWorkflowRequest.Name,
                workflowGroupId: updateWorkflowRequest.WorkflowGroupId,
                sleekflowStaffId: staff.Id.ToString(),
                sleekflowStaffTeamIds: await GetTeamIdsBySleekflowStaffAsync(staff),
                metadata: updateWorkflowRequest.Metadata,
                manualEnrollmentSource: null, // or provide appropriate value if needed
                isDynamicVariableEnabled: updateWorkflowRequest.IsDynamicVariableEnabled
                ));

        return Ok(updateWorkflowOutputOutput);
    }

    public class SaveWorkflowDraftRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [JsonProperty("triggers")]
        public WorkflowTriggers Triggers { get; set; }

        [JsonProperty("workflow_enrollment_settings")]
        public WorkflowEnrollmentSettings WorkflowEnrollmentSettings { get; set; }

        [JsonProperty("workflow_schedule_settings")]
        public WorkflowScheduleSettings WorkflowScheduleSettings { get; set; }

        [JsonProperty("steps")]
        public List<JObject> Steps { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("workflow_group_id")]
        public string WorkflowGroupId { get; set; }

        [JsonProperty("metadata")]
        public Dictionary<string, object> Metadata { get; set; }

        [JsonProperty("is_dynamic_variable_enabled")]
        public bool IsDynamicVariableEnabled { get; set; }
    }

    [HttpPost("Workflows/SaveWorkflowDraft")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<SaveWorkflowDraftOutputOutput>> SaveWorkflowDraft(
        [FromBody]
        SaveWorkflowDraftRequest updateWorkflowRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        bool isDynamicVariableEnabled = await GetPreservedDynamicVariableEnabledValue(
            staff.CompanyId,
            updateWorkflowRequest.WorkflowId);

        updateWorkflowRequest.IsDynamicVariableEnabled = isDynamicVariableEnabled;

        var saveWorkflowDraftOutputOutput = await _workflowsApi.WorkflowsSaveWorkflowDraftPostAsync(
             saveWorkflowDraftInput: new SaveWorkflowDraftInput(
                 staff.CompanyId,
                 updateWorkflowRequest.WorkflowId,
                 updateWorkflowRequest.Triggers,
                 updateWorkflowRequest.WorkflowEnrollmentSettings,
                 updateWorkflowRequest.WorkflowScheduleSettings,
                 updateWorkflowRequest.Steps.Select(s => (object) s).ToList(),
                 updateWorkflowRequest.Name,
                 updateWorkflowRequest.WorkflowGroupId,
                 staff.Id.ToString(),
                 await GetTeamIdsBySleekflowStaffAsync(staff),
                 updateWorkflowRequest.Metadata,
                 updateWorkflowRequest.IsDynamicVariableEnabled));

        return Ok(saveWorkflowDraftOutputOutput);
    }

    public class GetManualEnrollmentStatusRequest
    {
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    public class GetManualEnrollmentStatusResponse
    {
        [JsonProperty("status")]
        public string Status { get; set; }
    }

    [HttpPost("Workflows/GetManualEnrollmentStatus")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetManualEnrollmentStatusResponse>> GetManualEnrollmentStatus(
        [FromBody]
        GetManualEnrollmentStatusRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var manualEnrollmentStatus = _flowHubScheduledWorkflowEnrollmentService.GetScheduledWorkflowEnrollmentStatus(
            staff.CompanyId,
            request.WorkflowId);

        return Ok(
            new GetManualEnrollmentStatusResponse
            {
                Status = manualEnrollmentStatus
            });
    }

    public class ManualEnrollWorkflowRequest
    {
        [Required]
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [JsonProperty("user_profile_ids")]
        public List<string> UserProfileIds { get; set; }

        [JsonProperty("contact_list_ids")]
        public List<long> ContactListIds { get; set; }
    }

    public class ManualEnrollWorkflowResponse
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonProperty("message")]
        public string Message { get; set; }
    }

    [HttpPost("Workflows/ManualEnroll")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManualEnrollWorkflowResponse>> ManualEnrollWorkflow(
        [FromBody]
        ManualEnrollWorkflowRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        if (request.UserProfileIds is not { Count: > 0 }
            && request.ContactListIds is not { Count: > 0 })
        {
            return BadRequest(
                new ManualEnrollWorkflowResponse
                {
                    Success = false,
                    Message = "At least one of user profile ids or contact list ids must be provided."
                });
        }

        var getVersionedWorkflowOutputOutput = await _workflowsApi.WorkflowsGetVersionedWorkflowPostAsync(
            getVersionedWorkflowInput: new GetVersionedWorkflowInput(
                staff.CompanyId,
                request.WorkflowVersionedId));

        var workflow = getVersionedWorkflowOutputOutput.Data.Workflow;

        if (workflow is null)
        {
            return BadRequest(
                new ManualEnrollWorkflowResponse
                {
                    Success = false,
                    Message = "Workflow not found."
                });
        }

        if (workflow is not { ActivationStatus: "Active" })
        {
            return BadRequest(
                new ManualEnrollWorkflowResponse
                {
                    Success = false,
                    Message = "Versioned workflow is not active."
                });
        }

        if (workflow is
            {
                Triggers.ContactManuallyEnrolled: null
            })
        {
            return BadRequest(
                new ManualEnrollWorkflowResponse
                {
                    Success = false,
                    Message = "Workflow does not support manual enrollment."
                });
        }

        _flowHubScheduledWorkflowEnrollmentService.EnqueueManualWorkflowEnrollment(
            workflow,
            staff,
            request.UserProfileIds,
            request.ContactListIds);

        return Ok(new ManualEnrollWorkflowResponse
        {
            Success = true
        });
    }

    public class CancelManualEnrollWorkflowRequest
    {
        [Required]
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/CancelManualEnroll")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<ManualEnrollWorkflowResponse>> CancelManualEnrollWorkflow(
        [FromBody]
        CancelManualEnrollWorkflowRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        _flowHubScheduledWorkflowEnrollmentService.CancelScheduledWorkflowEnrollment(
            staff.CompanyId,
            request.WorkflowId);

        return Ok(new ManualEnrollWorkflowResponse
        {
            Success = true
        });
    }

    public record TriggerScheduledWorkflowRequest
    {
        [Required]
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/TriggerScheduledWorkflow")]
    public async Task<ActionResult<TriggerScheduledWorkflowOutputOutput>> TriggerScheduledWorkflow(
        [FromBody] TriggerScheduledWorkflowRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var output = await _workflowsApi.WorkflowsTriggerScheduledWorkflowPostAsync(
            triggerScheduledWorkflowInput: new TriggerScheduledWorkflowInput(request.WorkflowId, staff.CompanyId));

        return Ok(output);
    }

    public record TerminateScheduledWorkflowRequest
    {
        [Required]
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }
    }

    [HttpPost("Workflows/TerminateScheduledWorkflow")]
    public async Task<ActionResult<TerminateScheduledWorkflowOutputOutput>> TerminateScheduledWorkflow(
        [FromBody] TerminateScheduledWorkflowRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var output =
            await _workflowsApi.WorkflowsTerminateScheduledWorkflowPostAsync(
                terminateScheduledWorkflowInput:
                new TerminateScheduledWorkflowInput(staff.CompanyId, request.WorkflowId));

        return Ok(output);
    }

    public class AssignWorkflowToGroupRequest
    {
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [JsonProperty("workflow_group_id")]
        public string WorkflowGroupId { get; set; }
    }

    [HttpPost("Workflows/AssignWorkflowToGroup")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<AssignWorkflowToGroupOutputOutput>> AssignWorkflowToGroup(
        [FromBody]
        AssignWorkflowToGroupRequest assignWorkflowToGroupRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var assignWorkflowToGroupOutputOutput =
            await _workflowsApi.WorkflowsAssignWorkflowToGroupPostAsync(
                assignWorkflowToGroupInput: new AssignWorkflowToGroupInput(
                    assignWorkflowToGroupRequest.WorkflowVersionedId,
                    assignWorkflowToGroupRequest.WorkflowGroupId,
                    staff.CompanyId,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(assignWorkflowToGroupOutputOutput);
    }

    public class GetWorkflowGroupsRequest
    { }

    [HttpPost("WorkflowGroups/GetWorkflowGroups")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GetWorkflowGroupsOutputOutput>> GetWorkflowGroups(
        [FromBody]
        GetWorkflowGroupsRequest getWorkflowGroupsRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowGroupsOutputOutput =
            await _workflowGroupsApi.WorkflowGroupsGetWorkflowGroupsPostAsync(
                getWorkflowGroupsInput: new GetWorkflowGroupsInput(staff.CompanyId));

        return Ok(getWorkflowGroupsOutputOutput);
    }

    public class CreateWorkflowGroupRequest
    {
        [JsonProperty("workflow_group_name")]
        public string WorkflowGroupName { get; set; }
    }

    [HttpPost("WorkflowGroups/CreateWorkflowGroup")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateWorkflowGroupOutputOutput>> CreateWorkflowGroup(
        [FromBody]
        CreateWorkflowGroupRequest createWorkflowGroupRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var createWorkflowGroupOutputOutput =
            await _workflowGroupsApi.WorkflowGroupsCreateWorkflowGroupPostAsync(
                createWorkflowGroupInput: new CreateWorkflowGroupInput(
                    createWorkflowGroupRequest.WorkflowGroupName,
                    staff.CompanyId,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(createWorkflowGroupOutputOutput);
    }

    public class UpdateWorkflowGroupRequest
    {
        [JsonProperty("workflow_group_id")]
        public string WorkflowGroupId { get; set; }

        [JsonProperty("workflow_group_name")]
        public string WorkflowGroupName { get; set; }
    }

    [HttpPost("WorkflowGroups/UpdateWorkflowGroup")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateWorkflowGroupOutputOutput>> UpdateWorkflowGroup(
        [FromBody]
        UpdateWorkflowGroupRequest updateWorkflowGroupRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var updateWorkflowGroupOutputOutput =
            await _workflowGroupsApi.WorkflowGroupsUpdateWorkflowGroupPostAsync(
                updateWorkflowGroupInput: new UpdateWorkflowGroupInput(
                    updateWorkflowGroupRequest.WorkflowGroupId,
                    updateWorkflowGroupRequest.WorkflowGroupName,
                    staff.CompanyId,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(updateWorkflowGroupOutputOutput);
    }

    public class DeleteWorkflowGroupRequest
    {
        [JsonProperty("workflow_group_id")]
        public string WorkflowGroupId { get; set; }
    }

    [HttpPost("WorkflowGroups/DeleteWorkflowGroup")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<DeleteWorkflowGroupOutputOutput>> DeleteWorkflowGroup(
        [FromBody]
        DeleteWorkflowGroupRequest deleteWorkflowGroupRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var deleteWorkflowGroupOutputOutput =
            await _workflowGroupsApi.WorkflowGroupsDeleteWorkflowGroupPostAsync(
                deleteWorkflowGroupInput: new DeleteWorkflowGroupInput(
                    deleteWorkflowGroupRequest.WorkflowGroupId,
                    staff.CompanyId,
                    staff.Id.ToString(),
                    await GetTeamIdsBySleekflowStaffAsync(staff)));

        return Ok(deleteWorkflowGroupOutputOutput);
    }

    public class GetWorkflowActionsRequest
    {
        [JsonProperty("version")]
        public string? Version { get; set; }
    }

    [HttpPost("NeedConfigs/GetActions")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    public async Task<ActionResult<GetActionsOutputOutput>> GetWorkflowActions(
        [FromBody] GetWorkflowActionsRequest request,
        CancellationToken cancellationToken)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowActionsOutput = await _needConfigsApi.NeedConfigsGetActionsPostAsync(
            getActionsInput: new GetActionsInput(request.Version),
            cancellationToken: cancellationToken);

        return Ok(await GetFeatureEnabledActions(staff.CompanyId, getWorkflowActionsOutput));
    }

    public class GetWorkflowActionNeedsRequest
    {
        [Required]
        [JsonProperty("action_group")]
        public string ActionGroup { get; set; }

        [Required]
        [JsonProperty("action_subgroup")]
        public string ActionSubgroup { get; set; }

        [JsonProperty("version")]
        public string? Version { get; set; }

        [JsonProperty("parameters")]
        public Dictionary<string, object?>? Parameters { get; set; }
    }

    [HttpPost("NeedConfigs/GetActionNeeds")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    public async Task<ActionResult<GetActionsOutputOutput>> GetWorkflowActionNeeds(
        [FromBody] GetWorkflowActionNeedsRequest request,
        CancellationToken cancellationToken)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowActionNeedsOutput = await _needConfigsApi.NeedConfigsGetActionNeedsPostAsync(
            getActionNeedsInput: new GetActionNeedsInput(
                staff.CompanyId,
                request.ActionGroup,
                request.ActionSubgroup,
                request.Version,
                request.Parameters),
            cancellationToken: cancellationToken);

        return Ok(getWorkflowActionNeedsOutput);
    }

    public class GetWorkflowTriggersRequest
    {
        [JsonProperty("version")]
        public string? Version { get; set; }
    }

    [HttpPost("NeedConfigs/GetTriggers")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    public async Task<ActionResult<GetTriggersOutputOutput>> GetWorkflowTriggers(
        [FromBody] GetWorkflowTriggersRequest request,
        CancellationToken cancellationToken)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff == null)
        {
            return Unauthorized();
        }

        var getWorkflowTriggersOutput = await _needConfigsApi.NeedConfigsGetTriggersPostAsync(
            getTriggersInput: new GetTriggersInput(request.Version),
            cancellationToken: cancellationToken);

        return Ok(await GetFeatureEnabledTriggers(staff.CompanyId, getWorkflowTriggersOutput));
    }

    public class GetTriggerNeedsRequest
    {
        [Required]
        [JsonProperty("trigger_id")]
        public string TriggerId { get; set; }

        [JsonProperty("version")]
        public string? Version { get; set; }

        [JsonProperty("parameters")]
        public Dictionary<string, object?>? Parameters { get; set; }
    }

    [HttpPost("NeedConfigs/GetTriggerNeeds")]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces(MediaTypeNames.Application.Json)]
    public async Task<ActionResult<GetTriggerNeedsOutputOutput>> GetWorkflowTriggerNeeds(
        [FromBody] GetTriggerNeedsRequest request,
        CancellationToken cancellationToken)
    {
        var getTriggerFieldsOutput = await _needConfigsApi.NeedConfigsGetTriggerNeedsPostAsync(
            getTriggerNeedsInput: new GetTriggerNeedsInput(
                request.TriggerId,
                request.Version,
                request.Parameters),
            cancellationToken: cancellationToken);

        return Ok(getTriggerFieldsOutput);
    }

    public class CreateWorkflowByAgentConfigRequest
    {
        [JsonProperty("agent_config_id")]
        public string AgentConfigId { get; set; }
    }

    [HttpPost("AiWorkflows/CreateWorkflowByAgentConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateWorkflowByAgentConfigOutputOutput>> CreateWorkflowByAgentConfig(
        [FromBody] CreateWorkflowByAgentConfigRequest createWorkflowByAgentConfigRequest)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createWorkflowByAgentConfigOutputOutput = await _aiWorkflowsApi.AiWorkflowsCreateWorkflowByAgentConfigPostAsync(
            createWorkflowByAgentConfigInput: new CreateWorkflowByAgentConfigInput(
                staff.CompanyId,
                createWorkflowByAgentConfigRequest.AgentConfigId,
                staff.Id.ToString()));

        return Ok(createWorkflowByAgentConfigOutputOutput);
    }

    public class UpdateWorkflowsByAgentConfigRequest
    {
        [JsonProperty("agent_config_id")]
        public string AgentConfigId { get; set; }

        [JsonProperty("publish_type")]
        public string PublishType { get; set; }
    }

    [HttpPost("AiWorkflows/UpdateWorkflowsByAgentConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<UpdateWorkflowsByAgentConfigOutputOutput>> UpdateWorkflowsByAgentConfig(
        [FromBody] UpdateWorkflowsByAgentConfigRequest updateWorkflowsByAgentConfigRequest
    )
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var updateWorkflowsByAgentConfigOutputOutput =
            await _aiWorkflowsApi.AiWorkflowsUpdateWorkflowsByAgentConfigPostAsync(
                updateWorkflowsByAgentConfigInput: new UpdateWorkflowsByAgentConfigInput(
                    staff.CompanyId,
                    updateWorkflowsByAgentConfigRequest.AgentConfigId,
                    updateWorkflowsByAgentConfigRequest.PublishType,
                    staff.Id.ToString()
                )
            );

        var needEnableWorkflowIds = updateWorkflowsByAgentConfigOutputOutput.Data.NeedEnableWorkflowIds;
        if (needEnableWorkflowIds != null && needEnableWorkflowIds.Any())
        {
            foreach (var needEnableWorkflowVersionedId in needEnableWorkflowIds)
            {
                // copy from EnableWorkflowDraft
                // it's hard to publish AI Workflow in BE V2 because of the dependency, so republish workflow here
                var getVersionedWorkflowOutputOutput = await _workflowsApi.WorkflowsGetVersionedWorkflowPostAsync(
                    getVersionedWorkflowInput: new GetVersionedWorkflowInput(
                        staff.CompanyId,
                        needEnableWorkflowVersionedId));
                if (!getVersionedWorkflowOutputOutput.Success)
                {
                    throw new Exception($"Failed to get versioned workflow {needEnableWorkflowVersionedId}");
                }

                var workflow = getVersionedWorkflowOutputOutput.Data.Workflow;
                var workflowVersionedId = workflow.WorkflowVersionedId;

                if (_aiFlowAutoSetUpService.IsAiFlow(workflow))
                {
                    Dictionary<string, object> schemaful_object = new Dictionary<string, object>();
                    Dictionary<string, object> contact_property = new Dictionary<string, object>();

                    try
                    {
                        var dependencyResult = await _aiFlowAutoSetUpService.CreateDependencyForAiFlow(needEnableWorkflowVersionedId, staff.CompanyId);

                        if (dependencyResult != null && dependencyResult.SchemafulObject != null)
                        {
                            schemaful_object = new Dictionary<string, object>
                            {
                                { "id", dependencyResult.SchemafulObject["id"] },
                                { "properties", dependencyResult.SchemafulObject["properties"] }
                            };
                        }

                        if (dependencyResult != null && dependencyResult.ContactProperty != null)
                        {
                            contact_property = new Dictionary<string, object>
                            {
                                { "id", dependencyResult.ContactProperty.Id },
                                { "label", dependencyResult.ContactProperty.FieldName }
                            };
                        }
                    }
                    catch (Exception ex)
                    {
                        return BadRequest(
                            new ManualEnrollWorkflowResponse
                            {
                                Success = false,
                                Message = "Failed to create dependency for AI flow"
                            });
                    }

                    var input = new UpdateAiAgentWorkflowsByAiNodeInput(
                            sleekflowCompanyId: staff.CompanyId,
                            sleekflowStaffId: staff.Id.ToString(),
                            workflowId: workflow.WorkflowId,
                            schemafulObject: schemaful_object,
                            contactProperty: contact_property
                        );

                    var result = await _workflowsApi.WorkflowsUpdateAiAgentWorkflowsByAiNodePostAsync(updateAiAgentWorkflowsByAiNodeInput: input);

                    if (!result.Success)
                    {
                        return BadRequest(
                            new ManualEnrollWorkflowResponse
                            {
                                Success = false,
                                Message = "Failed to create AI flow"
                            });
                    }

                    var latestWorkflowRequest = await _workflowsApi.WorkflowsGetLatestWorkflowPostAsync(getLatestWorkflowInput: new GetLatestWorkflowInput(
                        staff.CompanyId,
                        workflowId: workflow.WorkflowId));
                    if (!latestWorkflowRequest.Success)
                    {
                        return BadRequest(
                            new ManualEnrollWorkflowResponse
                            {
                                Success = false,
                                Message = "Failed to get latest work flow"
                            });
                    }

                    workflowVersionedId = latestWorkflowRequest.Data.LatestWorkflow.WorkflowVersionedId;
                }

                var enableWorkflowDraftOutputOutput = await _workflowsApi.WorkflowsEnableWorkflowDraftPostAsync(
                    enableWorkflowDraftInput: new EnableWorkflowDraftInput(
                        staff.CompanyId,
                        workflowVersionedId,
                        staff.Id.ToString(),
                        await GetTeamIdsBySleekflowStaffAsync(staff)));

                if (enableWorkflowDraftOutputOutput.Success)
                {
                    _flowHubScheduledWorkflowEnrollmentService.EnqueueScheduledWorkflowEnrollment(enableWorkflowDraftOutputOutput.Data.Workflow);
                }
            }
        }

        return Ok(updateWorkflowsByAgentConfigOutputOutput);
    }

    public class CreateWorkflowWithAssignmentNodeByAgentConfigRequest
    {
        [JsonProperty("agent_config_id")]
        public string AgentConfigId { get; set; }

        [JsonProperty("staff_identity_ids_to_be_assigned")]
        public List<string> StaffIdentityIdsToBeAssigned { get; set; }
    }

    [HttpPost("AiWorkflows/CreateWorkflowWithAssignmentNodeByAgentConfig")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<CreateWorkflowByAgentConfigOutputOutput>> CreateWorkflowWithAssignmentNodeByAgentConfig(
        [FromBody] CreateWorkflowWithAssignmentNodeByAgentConfigRequest request)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff == null)
        {
            return Unauthorized();
        }

        var createWorkflowWithAssignmentNodeByAgentConfigOutputOutput = await _aiWorkflowsApi.AiWorkflowsCreateWorkflowWithAssignmentNodeByAgentConfigPostAsync(
            createWorkflowWithAssignmentNodeByAgentConfigInput: new CreateWorkflowWithAssignmentNodeByAgentConfigInput(
                staff.CompanyId,
                request.AgentConfigId,
                request.StaffIdentityIdsToBeAssigned,
                staff.Id.ToString()));

        return Ok(createWorkflowWithAssignmentNodeByAgentConfigOutputOutput);
    }

    private async Task<GetActionsOutputOutput> GetFeatureEnabledActions(string companyId, GetActionsOutputOutput getActionsOutputOutput)
    {
        if (!getActionsOutputOutput.Success)
        {
            return getActionsOutputOutput;
        }

        var enabledFeatures = await GetEnabledFeaturesForCompany(companyId);
        var enabledShowActionGroups = await GetEnabledActionGroups(enabledFeatures, FlowHubFeatureFlag.ActionGroupMappings);
        var enabledHideActionGroups = await GetEnabledActionGroups(enabledFeatures, FlowHubFeatureFlag.HideActionGroupMappings);

        getActionsOutputOutput.Data.Actions = getActionsOutputOutput.Data.Actions
            .Where(action => ShouldIncludeAction(action, enabledShowActionGroups, enabledHideActionGroups))
            .ToList();

        return getActionsOutputOutput;
    }

    private async Task<List<string>> GetEnabledFeaturesForCompany(string companyId)
    {
        var allEnabledFeaturesForCompany = await _enabledFeaturesService.GetCacheAllEnabledFeaturesForCompany(companyId);
        return allEnabledFeaturesForCompany.Data.EnabledFeatures
            .Select(f => f.FeatureId)
            .ToList();
    }

    private async Task<List<string>> GetEnabledActionGroups(List<string> enabledFeatureIds, Dictionary<string, string> featureFlagMappings)
    {
        var allFeatures = await _featuresService.GetAllFeatures();
        var relevantFeatures = allFeatures.Where(f => featureFlagMappings.ContainsKey(f.Name));

        return relevantFeatures
            .Where(feature => enabledFeatureIds.Contains(feature.Id))
            .Select(feature => featureFlagMappings[feature.Name])
            .ToList();
    }

    private static bool ShouldIncludeAction(
        ActionConfigDto action,
        List<string> enabledShowActionGroups,
        List<string> enabledHideActionGroups)
    {
        var actionGroup = action.ActionGroup;
        var isShowControlled = FlowHubFeatureFlag.ActionGroupMappings.ContainsValue(actionGroup);
        var isHideControlled = FlowHubFeatureFlag.HideActionGroupMappings.ContainsValue(actionGroup);

        return actionGroup switch
        {
            _ when !isShowControlled && !isHideControlled => true, // Not controlled by any feature flags
            _ when isShowControlled => enabledShowActionGroups.Contains(actionGroup), // Show when feature enabled
            _ when isHideControlled => !enabledHideActionGroups.Contains(actionGroup), // Hide when feature enabled
            _ => false
        };
    }

    private async Task<GetTriggersOutputOutput> GetFeatureEnabledTriggers(string companyId, GetTriggersOutputOutput getTriggersOutputOutput)
    {
        if (!getTriggersOutputOutput.Success)
        {
            return getTriggersOutputOutput;
        }

        var flowHubFeatureFlagToTriggerGroupMappings = FlowHubFeatureFlag.TriggerGroupMappings;

        var allFeatures = await _featuresService.GetAllFeatures();
        var flowHubFeatures = allFeatures
            .Where(f => flowHubFeatureFlagToTriggerGroupMappings.Keys.Contains(f.Name))
            .ToList();

        var allEnabledFeaturesForCompany = await _enabledFeaturesService.GetCacheAllEnabledFeaturesForCompany(companyId);
        var flagEnabledFlowHubActionGroups = allEnabledFeaturesForCompany.Data.EnabledFeatures
            .Where(f => flowHubFeatures.Select(x => x.Id).Contains(f.FeatureId))
            .Select(f => flowHubFeatureFlagToTriggerGroupMappings[flowHubFeatures.First(x => x.Id == f.FeatureId).Name])
            .ToList();

        getTriggersOutputOutput.Data.Triggers = getTriggersOutputOutput.Data.Triggers
            .Where(a => !flowHubFeatureFlagToTriggerGroupMappings.Values.Contains(a.TriggerGroup) || flagEnabledFlowHubActionGroups.Contains(a.TriggerGroup))
            .ToList();

        return getTriggersOutputOutput;
    }

    // Helper method to get the preserved IsDynamicVariableEnabled value
    private async Task<bool> GetPreservedDynamicVariableEnabledValue(
        string companyId,
        string workflowId)
    {
        try
        {
            // Get the latest workflow to check existing isDynamicVariableEnabled value
            var latestWorkflowResult = await _workflowsApi.WorkflowsGetLatestWorkflowPostAsync(
                getLatestWorkflowInput: new GetLatestWorkflowInput(
                    companyId,
                    workflowId: workflowId));

            if (latestWorkflowResult.Success &&
                latestWorkflowResult.Data.LatestWorkflow != null)
            {
                // Return the existing IsDynamicVariableEnabled value directly, default to false if null
                return latestWorkflowResult.Data.LatestWorkflow.IsDynamicVariableEnabled ?? false;
            }
        }
        catch (Exception ex)
        {
            // Log exception but continue with default value
            _logger.LogError(ex, "Failed to retrieve existing isDynamicVariableEnabled value");
        }

        return false;
    }
}
