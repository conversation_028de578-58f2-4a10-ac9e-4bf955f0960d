﻿using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Serilog.Context;
using Travis_backend.Helpers;

namespace Travis_backend.Middlewares;

public static class LoggingMiddlewareExtensions
{
    public static IApplicationBuilder UseLoggingMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<LoggingMiddleware>();
    }
}

internal sealed class LoggingMiddleware
{
    private readonly RequestDelegate _next;
    public const string UserIdClaimName = "https://app.sleekflow.io/user_id";
    public const string UserEmailClaimName = "https://app.sleekflow.io/email";

    public LoggingMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public Task InvokeAsync(HttpContext context)
    {
        var entryDomain = context.Request.Host;
        var entryPoint = $"{context.Request.Method} {context.Request.Path}";
        var clientIp = ClientIpHelper.GetClientIp(context);
        var userAgent = context.Request.Headers["User-Agent"].ToString();

        using (LogContext.PushProperty("LogContextClientIp", !string.IsNullOrEmpty(clientIp) ? clientIp : "N/A"))
        using (LogContext.PushProperty("LogContextUserAgent", !string.IsNullOrEmpty(userAgent) ? userAgent : "N/A"))
        using (LogContext.PushProperty("LogContextEntryPoint", entryPoint))
        using (LogContext.PushProperty("LogContextEntryDomain", entryDomain))
        {
            if (context.User.Identity?.IsAuthenticated ?? false)
            {
                var userId = (context.User.FindFirst(c => c.Type == UserIdClaimName) ??
                              context.User.FindFirst(c => c.Type == JwtRegisteredClaimNames.Sub))?.Value;
                var userEmail = context.User.FindFirst(c => c.Type == UserEmailClaimName)?.Value;

                using (LogContext.PushProperty("LogContextCurrentUserId", userId ?? "N/A"))
                using (LogContext.PushProperty("LogContextCurrentUserEmail", userEmail ?? "N/A"))
                {
                    return _next(context);
                }
            }

            return _next(context);
        }
    }
}