﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Sleekflow.Core.DataMigrator.Migrations;

namespace Sleekflow.Core.DataMigrator.MigrationScripts;

public class AspNetUserRolesMigration : BaseMigration
{
    public AspNetUserRolesMigration(Configurations configurations)
        : base(configurations)
    {
    }

    public override async Task<int> ExecuteAsync(string companyId)
    {
        var results = new List<IdentityUserRole<string>>();
        var originUserRoles = await OriginalContext.UserRoles.ToListAsync();

        var existedUserIds = MigrationContext.Users.Select(x => x.Id).ToHashSet();

        var migratedUserIds = MigrationContext.UserRoles.Select(x => x.UserId).ToHashSet();
        foreach (var originItem in originUserRoles)
        {
            if (!migratedUserIds.Contains(originItem.UserId))
            {
                results.Add(originItem);
            }
        }

        foreach (var userRole in results)
        {
            if (existedUserIds.Contains(userRole.UserId))
            {
                await MigrationContext.UserRoles.AddRangeAsync(userRole);
            }

            var entityEntries = MigrationContext.ChangeTracker.Entries().ToList();
            foreach (var entityEntry in entityEntries.Where(
                         entityEntry => entityEntry.Entity.GetType() !=
                                        OriginalContext.UserRoles.FirstOrDefault()?.GetType()))
            {
                entityEntry.State = EntityState.Unchanged;
            }
        }

        return await MigrationContext.SaveChangesAsync();
    }
}