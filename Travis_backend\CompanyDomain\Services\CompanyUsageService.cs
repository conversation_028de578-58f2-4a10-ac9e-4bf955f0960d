﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.ChannelDomain.Exceptions;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs;
using Travis_backend.Infrastructures.Options;
using Travis_backend.InternalDomain.Services;
using Travis_backend.ResellerDomain.Services;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Services;
using Twilio;
using Twilio.Rest.Api.V2010;
using Twilio.Rest.Api.V2010.Account.Usage;

namespace Travis_backend.CompanyDomain.Services
{
    public interface ICompanyUsageService
    {
        Task<BillRecord> GetCompanySubscriptionPlan(string companyId);

        Task<CompanyUsage> GetCompanyUsage(string companyId, bool allowCache = true);

        Task<int> GetCompanyPurchasedWhatsappNumberCount(string companyId);

        Task<APIUsage> GetAPIUsage(string companyId, string apiKey, bool isMarkInCalls = false);

        Task<TwilioUsageRecord> GetTwilioUsage(string companyId, string twilioAccountId);

        Task FetchAllTwilioUsage();

        Task<List<BillingPeriodUsage>> GetBillingPeriodUsages(
            string companyId,
            bool isCountTotalMessages = true);

        Task<CompanyUsageLimitOffsetProfile> GetCompanyUsageLimitOffsetProfile(string companyId);

        /// <summary>
        /// Set enable or disable usage limit offset profile.
        /// </summary>
        /// <param name="companyId">CompanyId.</param>
        /// <param name="isEnable">Is enable.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task EnableUsageLimitOffsetProfileAsync(string companyId, bool isEnable);

        /// <summary>
        /// Add Company's CompanyUsageLimitOffsetProfile.
        /// </summary>
        /// <param name="companyId">CompanyId.</param>
        /// <param name="profile">CompanyUsageLimitOffsetProfile to update.</param>
        /// <returns>Affected Rows.</returns>
        Task<int> AddCompanyUsageLimitOffsetProfileAsync(string companyId, CompanyUsageLimitOffsetProfile profile);

        /// <summary>
        /// Update Company's CompanyUsageLimitOffsetProfile.
        /// </summary>
        /// <param name="companyId">CompanyId.</param>
        /// <param name="profile">CompanyUsageLimitOffsetProfile to update.</param>
        /// <returns>Affected Rows.</returns>
        Task<int> UpdateCompanyUsageLimitOffsetProfileAsync(string companyId, CompanyUsageLimitOffsetProfile profile);

        Task EnsureNotExceedingChannelLimitAsync(string companyId, string staffIdentityId);

        Task<CompanyContactUsage> GetCompanyContactUsageAsync(
            string companyId);

        /// <summary>
        /// Get feature usage in current cycle.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="featureId">Feature Id.</param>
        /// <returns>Feature usage in current cycle.</returns>
        Task<decimal> GetCurrentCycleFeatureUsageAsync(string companyId, string featureId);

        /// <summary>
        /// Reset API Keys Usage.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <returns>A Task object can be awaited.</returns>
        Task ResetApiKeyUsageAsync(string companyId);

        /// <summary>
        /// Computes and updates the maximum WhatsApp instance count for a company based on their purchased WhatsApp phone number quantity.
        /// This method ensures the company's MaximumWhatsappInstance field is synchronized with their actual purchased quantity.
        /// </summary>
        /// <param name="companyId">The company identifier.</param>
        /// <returns>A Task representing the asynchronous operation.</returns>
        Task ComputeAndUpdateMaximumWhatsappInstance(string companyId);

    }

    public class CompanyUsageService : ICompanyUsageService
    {
        private readonly ICompanySubscriptionService _companySubscriptionService;
        private readonly ApplicationDbContext _appDbContext;
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;
        private readonly ILogger _logger;
        private readonly ICompanyInfoCacheService _companyInfoCacheService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly IResellerBillingService _resellerBillingService;
        private readonly IFeatureQuantityService _featureQuantityService;
        private readonly IConversationService _conversationService;
        private readonly IUsageCycleCalculator _usageCycleCalculator;
        private readonly IDefaultSubscriptionPlanIdGetter _defaultSubscriptionPlanIdGetter;
        private readonly IFlowHubConfigService _flowHubConfigService;
        private readonly IFlowHubExecutionService _flowHubExecutionService;
        private readonly ICompanyRepository _companyRepository;
        private readonly ICompanyApiKeyRepository _companyApiKeyRepository;
        private readonly FeatureFlagsOptions _featureFlagsOptions;
        private readonly IExecutionsApi _executionsApi;

        private readonly bool _isEnableTenantHubLogic;

        public CompanyUsageService(
            ICompanySubscriptionService companySubscriptionService,
            ApplicationDbContext appDbContext,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<CompanyUsageService> logger,
            ICompanyInfoCacheService companyInfoCacheService,
            ICacheManagerService cacheManagerService,
            IResellerBillingService resellerBillingService,
            IFeatureQuantityService featureQuantityService,
            IConversationService conversationService,
            IUsageCycleCalculator usageCycleCalculator,
            IDefaultSubscriptionPlanIdGetter defaultSubscriptionPlanIdGetter,
            IFlowHubConfigService flowHubConfigService,
            IFlowHubExecutionService flowHubExecutionService,
            ICompanyRepository companyRepository,
            ICompanyApiKeyRepository companyApiKeyRepository,
            IOptions<FeatureFlagsOptions> featureFlagsOptions,
            IExecutionsApi executionsApi)
        {
            _companySubscriptionService = companySubscriptionService;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _companyInfoCacheService = companyInfoCacheService;
            _cacheManagerService = cacheManagerService;
            _resellerBillingService = resellerBillingService;
            _featureQuantityService = featureQuantityService;
            _conversationService = conversationService;
            _usageCycleCalculator = usageCycleCalculator;
            _defaultSubscriptionPlanIdGetter = defaultSubscriptionPlanIdGetter;
            _flowHubConfigService = flowHubConfigService;
            _flowHubExecutionService = flowHubExecutionService;
            _companyRepository = companyRepository;
            _companyApiKeyRepository = companyApiKeyRepository;
            _featureFlagsOptions = featureFlagsOptions.Value;
            _executionsApi = executionsApi;

            _isEnableTenantHubLogic = _configuration.GetValue<bool>("TenantHub:IsEnableTenantLogic");
        }

        public async Task FetchAllTwilioUsage()
        {
            var twilioConfigs = await _appDbContext.ConfigWhatsAppConfigs
                .Where(x => x.IsSubaccount)
                .ToListAsync();

            foreach (var twilioConfig in twilioConfigs)
            {
                await GetTwilioUsage(twilioConfig.CompanyId, twilioConfig.TwilioAccountId);
            }

            var unusedTwilio = await _appDbContext.CompanyTwilioUsageRecords
                .Where(
                    x =>
                        !_appDbContext.ConfigWhatsAppConfigs
                            .Select(y => y.TwilioAccountId)
                            .Contains(x.TwilioAccountId)
                        && !_appDbContext.ConfigSMSConfigs
                            .Select(y => y.TwilioAccountId)
                            .Contains(x.TwilioAccountId))
                .ToListAsync();

            var coreTwilio = await _appDbContext.CoreTwilioConfigs.FirstOrDefaultAsync();
            TwilioClient.Init(coreTwilio.AccountSID, coreTwilio.AccountSecret);

            foreach (var twilio in unusedTwilio)
            {
                try
                {
                    if (twilio.TotalPrice > twilio.TotalCreditValue)
                    {
                        var account = await AccountResource.UpdateAsync(
                            status: AccountResource.StatusEnum.Suspended,
                            pathSid: twilio.TwilioAccountId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Fetch all twilio usage error: {ExceptionMessage}",
                        ex.Message);
                }
            }
        }

        public async Task<TwilioUsageRecord> GetTwilioUsage(string companyId, string twilioAccountId)
        {
            try
            {
                var twilioAccount = await _appDbContext.ConfigWhatsAppConfigs
                    .FirstOrDefaultAsync(x => x.TwilioAccountId == twilioAccountId);

                TwilioClient.Init(twilioAccount.TwilioAccountId, twilioAccount.TwilioSecret);
                var records = await RecordResource.ReadAsync(category: RecordResource.CategoryEnum.Totalprice);

                foreach (var record in records)
                {
                    var existingRecord = await _appDbContext.CompanyTwilioUsageRecords
                        .FirstOrDefaultAsync(
                            x =>
                                x.CompanyId == companyId
                                && x.TwilioAccountId == twilioAccountId);

                    if (existingRecord == null)
                    {
                        existingRecord = new TwilioUsageRecord
                        {
                            CompanyId = companyId,
                            TwilioAccountId = twilioAccountId,
                            TotalCreditValue = await _appDbContext.CompanyTwilioUsageRecords
                                .Where(x => x.TwilioAccountId == twilioAccountId)
                                .OrderByDescending(x => x.TotalCreditValue)
                                .Select(x => x.TotalCreditValue)
                                .FirstOrDefaultAsync() ?? 0,
                            Currency = "usd"
                        };

                        _appDbContext.CompanyTwilioUsageRecords.Add(existingRecord);
                    }

                    var price = (double) record.Price.Value * 1.03;

                    existingRecord.Start = record.StartDate;
                    existingRecord.End = record.EndDate;
                    existingRecord.TotalPrice = (decimal) price;
                    existingRecord.Currency = record.PriceUnit;
                    existingRecord.Description = record.Description;

                    // twilioUsages.Add(existingRecord);
                    await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);

                    if (existingRecord.TotalCreditValue + 20 < existingRecord.TotalPrice)
                    {
                        twilioAccount.IsRequireTopup = true;

                        await _appDbContext.ConfigWhatsAppConfigs
                            .Where(x => x.TwilioAccountId == twilioAccountId)
                            .ExecuteUpdateAsync(
                                config =>
                                    config.SetProperty(c => c.IsRequireTopup, true));
                    }

                    if (twilioAccount.IsSubaccount
                        && existingRecord.Balance < 0
                        && existingRecord.TotalCreditValue != 0)
                    {
                        try
                        {
                            // suspend account
                            var coreTwilio = await _appDbContext.CoreTwilioConfigs.FirstOrDefaultAsync();
                            TwilioClient.Init(coreTwilio.AccountSID, coreTwilio.AccountSecret);

                            var account = await AccountResource.UpdateAsync(
                                status: AccountResource.StatusEnum.Suspended,
                                pathSid: twilioAccount.TwilioAccountId);

                            BackgroundJob.Enqueue<EmailNotificationService>(
                                x => x.SendAccountDeletionEmail(
                                    twilioAccount.CompanyId,
                                    NotificationType.TwilioAccountSuspended));
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Update twilio sub-account to suspended error: {ExceptionString}",
                                ex.ToString());
                        }
                    }

                    await _appDbContext.SaveChangesAsync();

                    return existingRecord;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Company {CompanyId} get Twilio {TwilioAccountId} usage error: {ExceptionMessage}",
                    companyId,
                    twilioAccountId,
                    ex.Message);
            }

            var existingUsageRecord = await _appDbContext.CompanyTwilioUsageRecords
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.TwilioAccountId == twilioAccountId);

            return existingUsageRecord;
        }

        public async Task<APIUsage> GetAPIUsage(string companyId, string apiKey, bool isMarkInCalls = false)
        {
            var companyAPIKey = await _appDbContext.CompanyAPIKeys
                .FirstOrDefaultAsync(x => x.APIKey == apiKey);

            if (isMarkInCalls)
            {
                await _appDbContext.CompanyAPIKeys
                    .Where(x => x.APIKey == apiKey)
                    .ExecuteUpdateAsync(
                        key =>
                            key.SetProperty(
                                k => k.Calls,
                                k => k.Calls + 1));
            }

            if (companyAPIKey.CallLimit.HasValue && companyAPIKey.CallLimit != 0)
            {
                return new APIUsage
                {
                    maximumAPICalls = companyAPIKey.CallLimit.Value,
                    totalAPICalls = companyAPIKey.Calls
                };
            }
            else
            {
                var usage = await GetCompanyUsage(companyId);
                companyAPIKey.CallLimit = usage.MaximumAPICalls;

                await _appDbContext.SaveChangesAsync();

                return new APIUsage
                {
                    maximumAPICalls = companyAPIKey.CallLimit.Value,
                    totalAPICalls = companyAPIKey.Calls
                };
            }
        }

        public async Task<int> GetCompanyPurchasedWhatsappNumberCount(string companyId)
        {
            return _isEnableTenantHubLogic
                ? await _featureQuantityService.GetAddOnsFeatureQuantityAsync(companyId, "whatsapp_phone_number")
                : (int) await _appDbContext.CompanyBillRecords
                    .Where(
                        x =>
                            x.CompanyId == companyId
                            && x.Status != BillStatus.Inactive
                            && x.Status != BillStatus.Terminated
                            && ValidSubscriptionPlan.WhatsAppPhoneNumberAddOns.Contains(x.SubscriptionPlanId)
                            && DateTime.UtcNow >= x.PeriodStart
                            && DateTime.UtcNow < x.PeriodEnd)
                    .SumAsync(x => x.quantity);
        }

        private async Task<long> GetCompanyPurchasedAdditionalContactCount(string companyId)
        {
            return await _appDbContext.CompanyBillRecords
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Status != BillStatus.Inactive
                        && x.Status != BillStatus.Terminated
                        && ValidSubscriptionPlan.AdditionalContactAddOns.Contains(x.SubscriptionPlanId)
                        && x.PeriodStart <= DateTime.UtcNow && DateTime.UtcNow < x.PeriodEnd)
                .SumAsync(x => x.SubscriptionPlan.MaximumContact * x.quantity);
        }

        private async Task<long> GetCompanyPurchasedAdditionalAgentCount(string companyId)
        {
            return await _appDbContext.CompanyBillRecords
                .Where(
                    x => x.CompanyId == companyId &&
                         x.Status != BillStatus.Inactive &&
                         x.Status != BillStatus.Terminated &&
                         ValidSubscriptionPlan.AgentPlan.Contains(x.SubscriptionPlanId) &&
                         x.PeriodStart <= DateTime.UtcNow && DateTime.UtcNow < x.PeriodEnd)
                .SumAsync(x => x.SubscriptionPlan.IncludedAgents * x.quantity);
        }

        public async Task<BillRecord> GetCompanySubscriptionPlan(string companyId)
        {
            return await _appDbContext.CompanyBillRecords
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Status != BillStatus.Inactive
                        && x.Status != BillStatus.Terminated
                        && ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                        && x.PeriodStart < DateTime.UtcNow)
                .OrderByDescending(x => x.created)
                .ThenByDescending(x => x.PayAmount)
                .Include(x => x.SubscriptionPlan)
                .Include(x => x.PurchaseStaff)
                .FirstOrDefaultAsync();
        }

        public async Task<CompanyContactUsage> GetCompanyContactUsageAsync(
            string companyId)
        {
            var getCompanyContactUsageCacheKeyPattern = new GetCompanyContactUsageCacheKeyPattern(companyId);

            var data = await _cacheManagerService.GetCacheAsync(getCompanyContactUsageCacheKeyPattern);

            if (!string.IsNullOrEmpty(data))
            {
                return JsonConvert.DeserializeObject<CompanyContactUsage>(data);
            }

            try
            {
                var companyContactUsage = new CompanyContactUsage
                {
                    TotalContactCount = await CountCompanyContactAsync(companyId)
                };

                var company = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == companyId)
                    .Select(
                        x => new
                        {
                            x.MaximumContacts,
                            x.UsageLimitOffsetProfile
                        })
                    .FirstOrDefaultAsync();

                companyContactUsage.UsageLimitOffsetProfile =
                    company.UsageLimitOffsetProfile ?? new CompanyUsageLimitOffsetProfile();

                var billingPeriodUsages = await GetBillingPeriodUsages(companyId, isCountTotalMessages: false);

                companyContactUsage.BaseMaximumContacts = await GetBaseMaximumContactsAsync(companyId, billingPeriodUsages, company.MaximumContacts);

                await _cacheManagerService.SaveCacheAsync(
                    getCompanyContactUsageCacheKeyPattern,
                    companyContactUsage);

                return companyContactUsage;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethondName}] Lightweight contact usage error for company {CompanyId}: {ExceptionMessage}",
                    nameof(CompanyContactUsage),
                    companyId,
                    ex.Message);
                return new CompanyContactUsage();
            }
        }

        private async Task<int> GetBaseMaximumContactsAsync(string companyId, List<BillingPeriodUsage> billingPeriodUsages, int? maximumContacts)
        {
            var purchasedContactCount =
                billingPeriodUsages.FirstOrDefault()?.BillRecord.SubscriptionPlan.MaximumContact +
                (int) await GetCompanyPurchasedAdditionalContactCount(companyId) ?? 0;
            var contactFeatureQuantity = _isEnableTenantHubLogic
                ? await _featureQuantityService.GetFeatureQuantityAsync(companyId, "contacts")
                : purchasedContactCount;

            var baseMaximumContacts = maximumContacts.HasValue
                ? Math.Max(contactFeatureQuantity, maximumContacts.Value)
                : contactFeatureQuantity;

            return baseMaximumContacts;
        }

        private async Task<int> CountCompanyContactAsync(string companyId)
        {
            return await _appDbContext.UserProfiles
                .CountAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.ActiveStatus == ActiveStatus.Active);
        }

        public async Task<CompanyUsage> GetCompanyUsage(string companyId, bool allowCache = true)
        {
            try
            {
                // var key = $"GetCompanyUsage_{companyId}";
                var getCompanyUsageCacheKeyPattern = new GetCompanyUsageCacheKeyPattern(companyId);

                if (allowCache)
                {
                    var data = await _cacheManagerService.GetCacheAsync(getCompanyUsageCacheKeyPattern);

                    if (!string.IsNullOrEmpty(data))
                    {
                        return JsonConvert.DeserializeObject<CompanyUsage>(data);
                    }
                }

                var response = new CompanyUsage();
                var billingPeriodUsages = await GetBillingPeriodUsages(companyId);

                response.billingPeriodUsages = billingPeriodUsages;

                var billRecord = await _appDbContext.CompanyBillRecords
                    .Include(x => x.SubscriptionPlan)
                    .Include(x => x.PurchaseStaff)
                    .OrderByDescending(x => x.created)
                    .ThenByDescending(x => x.PayAmount)
                    .FirstOrDefaultAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.Status != BillStatus.Inactive
                            && x.Status != BillStatus.Terminated
                            && ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                            && x.PeriodStart < DateTime.UtcNow);

                var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == companyId);
                response.UsageLimitOffsetProfile = company.UsageLimitOffsetProfile ?? new CompanyUsageLimitOffsetProfile();

                var agentFeatureQuantity = _isEnableTenantHubLogic
                    ? await _featureQuantityService.GetFeatureQuantityAsync(companyId, "agents")
                    : billRecord?.SubscriptionPlan.IncludedAgents ?? 0;

                var automationFeatureQuantity = _isEnableTenantHubLogic
                    ? await _featureQuantityService.GetFeatureQuantityAsync(companyId, "automations")
                    : billRecord?.SubscriptionPlan.MaximumAutomation ?? 0;

                if (company.MaximumAgents <= 0)
                {
                    company.MaximumAgents = agentFeatureQuantity;
                    await _appDbContext.SaveChangesAsync();
                }

                if (!company.MaximumAutomations.HasValue ||
                    automationFeatureQuantity > company.MaximumAutomations)
                {
                    company.MaximumAutomations = automationFeatureQuantity;
                    await _appDbContext.SaveChangesAsync();
                }

                var purchasedWhatsappNumberCount = await GetCompanyPurchasedWhatsappNumberCount(companyId);
                if (company.MaximumWhatsappInstance < purchasedWhatsappNumberCount)
                {
                    company.MaximumWhatsappInstance = purchasedWhatsappNumberCount;
                    await _appDbContext.SaveChangesAsync();
                }

                response.totalContacts = await CountCompanyContactAsync(companyId);

                var channelCount = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == companyId)
                    .Select(
                        x => new
                        {
                            Count = (x.FacebookConfigs == null ? 0 : x.FacebookConfigs.Count) +
                                    (x.WhatsAppConfigs == null ? 0 : x.WhatsAppConfigs.Count) +
                                    (x.WhatsApp360DialogConfigs == null ? 0 : x.WhatsApp360DialogConfigs.Count) +
                                    (x.LineConfigs == null ? 0 : x.LineConfigs.Count) +
                                    (x.ViberConfigs == null ? 0 : x.ViberConfigs.Count) +
                                    (x.SMSConfigs == null ? 0 : x.SMSConfigs.Count) +
                                    (x.InstagramConfigs == null ? 0 : x.InstagramConfigs.Count) +
                                    (x.TelegramConfigs == null ? 0 : x.TelegramConfigs.Count) +
                                    (x.WeChatConfigId != null ? 1 : 0) +
                                    (x.EmailConfig != null ? 1 : 0),
                            WhatsappCloudApiChannelCount = x.WhatsappCloudApiConfigs.Count
                        })
                    .FirstOrDefaultAsync();

                response.CurrentNumberOfChannels = channelCount.Count + channelCount.WhatsappCloudApiChannelCount;
                response.totalChannelAdded = channelCount.Count + channelCount.WhatsappCloudApiChannelCount;

                // Cloud API
                response.CurrentNumberOfWhatsappCloudApiChannels = channelCount.WhatsappCloudApiChannelCount;
                response.BaseMaximumNumberOfWhatsappCloudApiChannels = company.MaximumWhatsappInstance;

                if (company.CompanyType == CompanyType.ResellerClient)
                {
                    response.totalAgents = await _appDbContext.UserRoleStaffs
                        .AsSplitQuery()
                        .AsNoTracking()
                        .CountAsync(
                            x =>
                                x.CompanyId == companyId
                                && !_appDbContext.ResellerStaffs
                                    .Select(y => y.IdentityId)
                                    .Contains(x.IdentityId));
                }
                else
                {
                    response.totalAgents = await _appDbContext.UserRoleStaffs
                        .CountAsync(
                            x =>
                                x.CompanyId == companyId
                                && x.Id != 1);
                }

                // Agent
                var purchasedAdditionalAgent =
                    billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan.IncludedAgents
                    + (int) await GetCompanyPurchasedAdditionalAgentCount(companyId);

                response.BaseMaximumAgents = Math.Max(
                    _isEnableTenantHubLogic
                        ? agentFeatureQuantity
                        : purchasedAdditionalAgent, company.MaximumAgents);

                var baseMaximumAutomationsFromTenantHubLogic = company.MaximumAutomations.HasValue
                    ? Math.Max(automationFeatureQuantity, company.MaximumAutomations.Value)
                    : automationFeatureQuantity;
                var baseMaximumAutomationsFromTravisBackendLogic = company.MaximumAutomations.HasValue
                    ? company.MaximumAutomations.Value
                    : 0;
                response.BaseMaximumAutomations = _isEnableTenantHubLogic
                    ? baseMaximumAutomationsFromTenantHubLogic
                    : baseMaximumAutomationsFromTravisBackendLogic;

                try
                {
                    var channelFeatureQuantity =
                        await _featureQuantityService.GetFeatureQuantityAsync(companyId, "channels");
                    var baseMaximumNumberOfChannelFromTenantHubLogic = company.MaximumNumberOfChannel.HasValue
                        ? Math.Max(channelFeatureQuantity, company.MaximumNumberOfChannel.Value)
                        : channelFeatureQuantity;

                    var baseMaximumNumberOfChannelFromTravisBackendLogic = company.MaximumNumberOfChannel.HasValue ?
                        ((billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan.SubscriptionTier != SubscriptionTier.Free
                          && billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan.MaximumNumberOfChannel > company.MaximumNumberOfChannel.Value) ?
                            billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan.MaximumNumberOfChannel :
                            company.MaximumNumberOfChannel.Value) :
                        billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan.MaximumNumberOfChannel;

                    response.BaseMaximumNumberOfChannel = _isEnableTenantHubLogic
                        ? baseMaximumNumberOfChannelFromTenantHubLogic
                        : baseMaximumNumberOfChannelFromTravisBackendLogic;
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Unable to get MaximumNumberOfChannel for company {CompanyId}: {ExceptionMessage}",
                        companyId,
                        ex.Message);
                }

                response.LastLogin = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyId)
                    .OrderByDescending(x => x.Identity.LastLoginAt)
                    .Take(1)
                    .Select(x => x.Identity.LastLoginAt)
                    .FirstOrDefaultAsync();

                response.IsExceededWALimit = await _appDbContext.ConversationMessages
                    .AnyAsync(
                        x =>
                            x.CompanyId == companyId
                            && x.Channel == ChannelTypes.WhatsappTwilio
                            && x.ChannelStatusMessage == "63018"
                            && x.CreatedAt.Date == DateTime.UtcNow.Date);

                try
                {

                    response.BaseMaximumContacts = await GetBaseMaximumContactsAsync(
                        companyId,
                        billingPeriodUsages,
                        company.MaximumContacts);
                    response.BaseMaximumAutomatedMessages = await GetBroadcastMessageQuantity(company, billRecord);
                    response.BaseMaximumApiCalls = await GetApiCallQuantity(company, billRecord);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethondName}] Set BaseMaximumContacts, BaseMaximumAutomatedMessages, BaseMaximumApiCalls error for company {CompanyId}: {ExceptionMessage}",
                        nameof(GetCompanyUsage),
                        companyId,
                        ex.Message);
                }

                if (!company.MaximumContacts.HasValue ||
                    response.BaseMaximumContacts > company.MaximumContacts.Value)
                {
                    company.MaximumContacts = response.BaseMaximumContacts;
                    await _appDbContext.SaveChangesAsync();
                }

                var usageCycle = await _companySubscriptionService.GetMonthlyUsageCycleAsync(companyId);
                response.UsageCycleDateTimeRange = usageCycle;

                #region FlowHub Related Usage Limits

                try
                {
                    var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(companyId);
                    var usageLimit = flowHubConfig.UsageLimit;

                    _logger.LogInformation("UsageLimit for Company {CompanyId}: {UsageLimit}", company, JsonConvert.SerializeObject(usageLimit));
                    _logger.LogInformation("UsageLimitOffset for Company {CompanyId}: {UsageLimit}", company, JsonConvert.SerializeObject(flowHubConfig.UsageLimitOffset));

                    response.BaseFlowBuilderFlowEnrolmentUsage = usageLimit.MaximumNumOfMonthlyWorkflowExecutions.GetValueOrDefault(0);
                    response.IsFlowHubUsageLimitOffsetEnabled = flowHubConfig.IsUsageLimitOffsetEnabled;
                    response.FlowHubUsageLimitOffset = flowHubConfig.UsageLimitOffset;

                    var getWorkflowExecutionStatisticsInputFilters = new GetWorkflowExecutionStatisticsInputFilters(
                        null,
                        new DateTimeOffset(usageCycle.From, TimeSpan.Zero),
                        new DateTimeOffset(usageCycle.To, TimeSpan.Zero));

                    var getMonetizedWorkflowExecutionStatisticsOutput = await _executionsApi.ExecutionsGetMonetizedWorkflowExecutionStatisticsPostAsync(
                        getMonetizedWorkflowExecutionStatisticsInput: new GetMonetizedWorkflowExecutionStatisticsInput(
                            companyId, getWorkflowExecutionStatisticsInputFilters));

                    response.CurrentFlowBuilderFlowEnrolmentUsage = getMonetizedWorkflowExecutionStatisticsOutput.Data.NumOfStartedWorkflows
                                                                    - getMonetizedWorkflowExecutionStatisticsOutput.Data.NumOfFailedWorkflows;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error During Setting FlowHub Usage for Company \"{CompanyId}\". ExceptionMessage: {Message}", companyId, ex.Message);
                }

                #endregion

                await _cacheManagerService.SaveCacheAsync(
                    getCompanyUsageCacheKeyPattern,
                    response);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "GetCompanyUsage Error: {CompanyId}, Exception: {ExceptionMessage}",
                    companyId,
                    ex.ToString());

                return new CompanyUsage();
            }
        }


        public async Task<CompanyUsageLimitOffsetProfile> GetCompanyUsageLimitOffsetProfile(string companyId)
        {
            var result = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == companyId)
                .Select(x => x.UsageLimitOffsetProfile)
                .FirstOrDefaultAsync();

            return result;
        }

        /// <inheritdoc />
        public async Task EnableUsageLimitOffsetProfileAsync(string companyId, bool isEnable)
        {
            var companyUsageOffsetProfile = await GetCompanyUsageLimitOffsetProfile(companyId);
            companyUsageOffsetProfile ??= new CompanyUsageLimitOffsetProfile();
            companyUsageOffsetProfile.IsEnabled = isEnable;

            await _flowHubConfigService.EnableFlowHubUsageLimitOffsetAsync(companyId, isEnable);
            await UpdateCompanyUsageLimitOffsetProfileAsync(companyId, companyUsageOffsetProfile);
        }

        /// <inheritdoc />
        public async Task<int> AddCompanyUsageLimitOffsetProfileAsync(string companyId, CompanyUsageLimitOffsetProfile profile)
        {
            var companyUsageOffsetProfile = await GetCompanyUsageLimitOffsetProfile(companyId);

            if (companyUsageOffsetProfile == null)
            {
                return await _companyRepository.UpdateCompanyUsageLimitOffsetProfileAsync(companyId, profile);
            }

            companyUsageOffsetProfile.ApiCallLimitOffSet += profile.ApiCallLimitOffSet;
            companyUsageOffsetProfile.AgentsLimitOffset += profile.AgentsLimitOffset;
            companyUsageOffsetProfile.WhatsappInstanceLimitOffset += profile.WhatsappInstanceLimitOffset;
            companyUsageOffsetProfile.ContactsLimitOffset += profile.ContactsLimitOffset;
            companyUsageOffsetProfile.AutomatedMessagesLimitOffset += profile.AutomatedMessagesLimitOffset;
            companyUsageOffsetProfile.AutomationsLimitOffset += profile.AutomationsLimitOffset;
            companyUsageOffsetProfile.ChannelLimitOffset += profile.ChannelLimitOffset;

            return await _companyRepository.UpdateCompanyUsageLimitOffsetProfileAsync(companyId, companyUsageOffsetProfile);
        }

        /// <inheritdoc />
        public Task<int> UpdateCompanyUsageLimitOffsetProfileAsync(string companyId, CompanyUsageLimitOffsetProfile profile)
        {
            return _companyRepository.UpdateCompanyUsageLimitOffsetProfileAsync(companyId, profile);
        }

        public async Task EnsureNotExceedingChannelLimitAsync(string companyId, string staffIdentityId)
        {
            var companyUsage = await GetCompanyUsage(companyId);

            var exceedingMaximumNumberOfChannels =
                companyUsage.totalChannelAdded >= companyUsage.MaximumNumberOfChannel;

            if (exceedingMaximumNumberOfChannels)
            {
                throw new ExceedingMaximumNumberOfChannelsException(
                    companyId,
                    companyUsage.MaximumNumberOfChannel);
            }
        }

        /// <inheritdoc />
        public async Task<decimal> GetCurrentCycleFeatureUsageAsync(string companyId, string featureId)
        {
            var usageCycle = await _companySubscriptionService.GetMonthlyUsageCycleAsync(companyId);

            if (featureId.EqualsIgnoreCase(FeatureId.FlowBuilderFlowEnrolment))
            {
                return await _flowHubExecutionService.GetExecutionUsageAsync(
                    companyId,
                    new DateTimeOffset(usageCycle.From, TimeSpan.Zero),
                    new DateTimeOffset(usageCycle.To, TimeSpan.Zero));
            }

            return 0;
        }

        /// <inheritdoc />
        public async Task ResetApiKeyUsageAsync(string companyId)
        {
            var currentBaseSubscriptionBillRecord = await _companySubscriptionService.GetCurrentBaseSubscriptionBillRecord(companyId);
            var currentBaseSubscription = await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(x => x.Id == currentBaseSubscriptionBillRecord.SubscriptionPlanId);

            var isUsageCycleApplied = currentBaseSubscriptionBillRecord.IsUsageCycleApplied;
            var isYearlySubscription = currentBaseSubscription.SubscriptionInterval.EqualsIgnoreCase("yearly");
            var subscriptionTier = currentBaseSubscription.SubscriptionTier;

            var apiCallFeatureQuantity = await _featureQuantityService.GetFeatureQuantityAsync(companyId, FeatureId.ApiCalls);
            var quantity = _isEnableTenantHubLogic ? apiCallFeatureQuantity : currentBaseSubscription.MaximumAPICall;
            var limits = quantity;

            //// No division needed for Freemium / Startup plans.
            //// No division needed for Enterprise plan version lower than 10.
            if (isUsageCycleApplied && isYearlySubscription &&
                subscriptionTier != SubscriptionTier.Free &&
                !(subscriptionTier == SubscriptionTier.Enterprise && currentBaseSubscription.Version < 10))
            {
                limits = quantity / 12;
            }

            _logger.LogInformation(
                "Update API Key Usage. CompanyId: {CompanyId}, BillRecordId: {BillRecordId}, SubscriptionPlanId: {SubscriptionPlanId}, OriginalMaxCall: {OriginalMaxCall}, ActualMaxCall: {ActualMaxCall}",
                companyId,
                currentBaseSubscriptionBillRecord.Id,
                currentBaseSubscription.Id,
                quantity,
                limits);

            await _companyApiKeyRepository.UpdateCompanyApiKeysAsync(companyId, limits, 0);
        }

        public async Task<List<BillingPeriodUsage>> GetBillingPeriodUsages(
            string companyId,
            bool isCountTotalMessages = true)
        {
            var now = DateTime.UtcNow;

            if (await _appDbContext.CompanyCompanies
                    .AnyAsync(
                        x =>
                            x.Id == companyId
                            && x.CompanyType == CompanyType.ResellerClient))
            {
                await _resellerBillingService.CheckAndRenewResellerClientBillRecord(companyId);
            }

            var billingPeriodUsages = new List<BillingPeriodUsage>();

            var billRecord = await _appDbContext.CompanyBillRecords
                .Include(x => x.SubscriptionPlan)
                .Include(x => x.PurchaseStaff)
                .OrderByDescending(x => x.created)
                .ThenByDescending(x => x.PayAmount)
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.Status != BillStatus.Inactive
                        && x.Status != BillStatus.Terminated
                        && ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId)
                        && x.PeriodStart < DateTime.UtcNow);

            if (billRecord.PeriodEnd > now && billRecord.IsUsageCycleApplied && billRecord.UsageCycleEnd < now)
            {
                _logger.LogInformation("Reset Usage Cycle for for BillRecord: {BillRecordId}", billRecord.Id);

                await ResetCompanyUsageAsync(companyId, billRecord.SubscriptionPlanId);

                var usageCycle = _usageCycleCalculator.GetUsageCycle(billRecord.PeriodStart, billRecord.PeriodEnd);
                billRecord.UsageCycleStart = usageCycle.From;
                billRecord.UsageCycleEnd = usageCycle.To;
                await _appDbContext.SaveChangesAsync();

                _logger.LogInformation("Calculate Usage Cycle. Start: {Start}, End: {End}", billRecord.UsageCycleStart, billRecord.UsageCycleEnd);
            }

            var broadcastMessageUsageCount = await GetBroadcastMessageCount(companyId, isCountTotalMessages, billRecord);

            billingPeriodUsages.Add(
                new BillingPeriodUsage
                {
                    BillRecord = _mapper.Map<BillRecordResponse>(billRecord),
                    TotalMessagesSentFromSleekflow = broadcastMessageUsageCount
                });

            var isNeededToChangeFreePlan = true;

            var company = await _appDbContext.CompanyCompanies.FirstAsync(x => x.Id == companyId);

            #region Handle for Resller Client

            if (company.CompanyType == CompanyType.ResellerClient)
            {
                var resellerClientResellerCompanyId = await _appDbContext.ResellerClientCompanyProfiles
                    .Include(x => x.ResellerCompanyProfile)
                    .Where(x => x.ClientCompanyId == companyId)
                    .AsNoTracking()
                    .Select(x => x.ResellerCompanyProfile.CompanyId)
                    .FirstOrDefaultAsync();

                var resellerCompany = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == resellerClientResellerCompanyId)
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                // Avoid null reference exception
                if (resellerCompany != null)
                {
                    isNeededToChangeFreePlan = resellerCompany.IsDeleted;
                }
            }

            #endregion

            // Change sleekflow_freemium to sleekflow_free
            if (isNeededToChangeFreePlan &&
                SubscriptionPlansId.FreemiumStartupPlans.ContainsIgnoreCase(billRecord.SubscriptionPlanId))
            {
                var lastLoginAt = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == billRecord.CompanyId)
                    .OrderByDescending(x => x.Identity.LastLoginAt)
                    .Select(x => x.Identity.LastLoginAt)
                    .FirstOrDefaultAsync();

                //// last login is more than 30 days before
                if (lastLoginAt < DateTime.UtcNow.AddDays(-30))
                {
                    billRecord.SubscriptionPlanId = "sleekflow_free";
                    billRecord.PeriodEnd = billRecord.PeriodEnd.AddYears(1);

                    await _appDbContext.SaveChangesAsync();

                    var staffs = await _appDbContext.UserRoleStaffs
                        .Include(x => x.Identity)
                        .Where(
                            x =>
                                x.CompanyId == companyId
                                && !string.IsNullOrEmpty(x.Identity.PhoneNumber))
                        .ToListAsync();

                    foreach (var staff in staffs)
                    {
                        BackgroundJob.Enqueue<ICoreService>(
                            x =>
                                x.UpdateSleekFlowLeadStage(
                                    staff.Identity.PhoneNumber,
                                    "Trial Expired"));
                    }

                    BackgroundJob.Enqueue<IInternalHubSpotService>(
                        x =>
                            x.SyncCompany(companyId, null));
                }
            }

            // If bill Record ended more than 8 hours
            if (billRecord.PeriodEnd.AddHours(8) < DateTime.UtcNow)
            {
                await _companyInfoCacheService.RemoveCompanyInfoCache(companyId);

                var newBillRecord = new BillRecord
                {
                    SubscriptionPlanId = billRecord.SubscriptionPlanId,
                    PayAmount = 0,
                    amount_due = 0,
                    amount_paid = 0,
                    amount_remaining = 0,
                    CompanyId = billRecord.CompanyId,
                    Status = BillStatus.Active,
                    PurchaseStaffId = billRecord.PurchaseStaffId,
                    PaymentStatus = billRecord.PaymentStatus,
                    invoice_Id = null,
                    created = DateTime.UtcNow,
                    PeriodStart = billRecord.PeriodEnd,
                    PeriodEnd = billRecord.PeriodEnd.AddMonths(1),
                };

                #region Downgrade to Freemium / Startup Plan

                //// Downgrade to Startup plan if the meet conditions:
                //// - Company is not VIP
                //// - Current plan is not "sleekflow_free", "sleekflow_freemium", and "sleekflow_v10_startup"
                if (!company.IsVIP
                    && billRecord.SubscriptionPlanId != "sleekflow_free"
                    && !SubscriptionPlansId.FreemiumStartupPlans.ContainsIgnoreCase(billRecord.SubscriptionPlanId))
                {
                    newBillRecord.SubscriptionPlanId = _defaultSubscriptionPlanIdGetter.GetDefaultStartupPlanId();

                    if (billRecord.PurchaseStaff != null)
                    {
                        billRecord.PurchaseStaff = await _appDbContext.UserRoleStaffs
                            .Include(x => x.Identity)
                            .FirstOrDefaultAsync(x => x.Id == billRecord.PurchaseStaff.Id);
                    }
                    else
                    {
                        billRecord.PurchaseStaff = await _appDbContext
                            .UserRoleStaffs
                            .Include(x => x.Identity)
                            .OrderBy(x => x.Id)
                            .FirstOrDefaultAsync(x => x.CompanyId == companyId);
                    }
                }

                #endregion

                #region Downgrade to Free Plan

                //// Downgrade to "sleekflow_free" if the company is in Startup plans, and didn't login for more than 30 days.
                if (SubscriptionPlansId.FreemiumStartupPlans.ContainsIgnoreCase(billRecord.SubscriptionPlanId))
                {
                    var lastLoginAt = await _appDbContext.UserRoleStaffs
                        .Where(x => x.CompanyId == billRecord.CompanyId)
                        .OrderByDescending(x => x.Identity.LastLoginAt)
                        .Select(x => x.Identity.LastLoginAt)
                        .FirstOrDefaultAsync();

                    if (lastLoginAt < DateTime.UtcNow.AddDays(-30))
                    {
                        newBillRecord.SubscriptionPlanId = "sleekflow_free";
                        newBillRecord.PeriodStart = billRecord.PeriodStart;
                        newBillRecord.PeriodEnd = billRecord.PeriodStart.AddYears(1);

                        var staffs = await _appDbContext.UserRoleStaffs
                            .Where(
                                x =>
                                    x.CompanyId == companyId
                                    && !string.IsNullOrEmpty(x.Identity.PhoneNumber))
                            .Include(x => x.Identity)
                            .ToListAsync();

                        foreach (var staff in staffs)
                        {
                            BackgroundJob.Enqueue<ICoreService>(
                                x =>
                                    x.UpdateSleekFlowLeadStage(
                                        staff.Identity.PhoneNumber,
                                        "Trial Expired"));
                        }
                    }
                }

                #endregion

                var usageCycle = _usageCycleCalculator.GetUsageCycle(newBillRecord.PeriodStart, newBillRecord.PeriodEnd);
                newBillRecord.UsageCycleStart = usageCycle.From;
                newBillRecord.UsageCycleEnd = usageCycle.To;

                _appDbContext.CompanyBillRecords.Add(newBillRecord);

                try
                {
                    await _appDbContext.SaveChangesAsync();

                    // Reset Feature Quantity
                    if (ValidSubscriptionPlan.AllPaidPlans.Contains(newBillRecord.SubscriptionPlanId))
                    {
                        //// Calculate feature quantity from BillRecords so that the quantity will update to correct one when database coulumn storing higher value
                        var agentsFeatureQuantity = await _featureQuantityService.GetFeatureQuantityAsync(companyId, FeatureId.Agents);
                        var contactsFeatureQuantity = await _featureQuantityService.GetFeatureQuantityAsync(companyId, FeatureId.Contacts);
                        var automationsFeatureQuantity = await _featureQuantityService.GetFeatureQuantityAsync(companyId, FeatureId.Automations);
                        var broadcastMessagesFeatureQuantity = await _featureQuantityService.GetFeatureQuantityAsync(company.Id, FeatureId.BroadcastMessages);
                        var whatsappPhoneNumberFeatureQuantity = await _featureQuantityService.GetFeatureQuantityAsync(companyId, FeatureId.WhatsAppPhoneNumber);

                        await _companyRepository.UpdateMaximumAgentsAsync(companyId, agentsFeatureQuantity);
                        await _companyRepository.UpdateMaximumContactsAsync(companyId, contactsFeatureQuantity);
                        await _companyRepository.UpdateMaximumAutomationsAsync(companyId, automationsFeatureQuantity);
                        await _companyRepository.UpdateMaximumBroadcastMessageAsync(companyId, broadcastMessagesFeatureQuantity);
                        await _companyRepository.UpdateMaximumWhatsAppInstancesAsync(companyId, whatsappPhoneNumberFeatureQuantity);
                        await ResetApiKeyUsageAsync(companyId);

                        await EnableUsageLimitOffsetProfileAsync(companyId, false);

                        await _appDbContext.SaveChangesAsync();
                    }

                    BackgroundJob.Enqueue<IInternalHubSpotService>(
                        x =>
                            x.SyncCompany(companyId, null));
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Exception in GetBillingPeriodUsages {Message}",
                        ex.Message);
                }

                return await GetBillingPeriodUsages(
                    companyId,
                    isCountTotalMessages);
            }

            return billingPeriodUsages;
        }

        /// <summary>
        /// Reset feature usage for company.
        /// </summary>
        /// <param name="companyId">Company Id.</param>
        /// <param name="subscriptionPlanId">Subscription Plan Id.</param>
        private async Task ResetCompanyUsageAsync(string companyId, string subscriptionPlanId)
        {
            var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(x => x.Id == subscriptionPlanId);
            var isYearlySubscription = subscriptionPlan.SubscriptionInterval.Equals("yearly", StringComparison.OrdinalIgnoreCase);

            _logger.LogInformation(
                "Start Reset Usage for Company. CompanyId: {CompanyId}. SubscriptionPlanId: {SubscriptionPlanId}, IsYearlySubscription: {IsYearlySubscription}",
                companyId,
                subscriptionPlanId,
                isYearlySubscription);

            await ResetApiKeyUsageAsync(companyId);
        }

        private async Task<int> GetBroadcastMessageCount(string companyId, bool isCountTotalMessage, BillRecord billRecord)
        {
            if (!isCountTotalMessage)
            {
                return 0;
            }

            var hasUsageCycle = billRecord.UsageCycleStart.HasValue && billRecord.UsageCycleEnd.HasValue;

            if (hasUsageCycle)
            {
                var (start, end) = (billRecord.UsageCycleStart.Value, billRecord.UsageCycleEnd.Value);
                return await _conversationService.GetBroadcastMessageCountCreatedBetweenDateAsync(companyId, start, end);
            }
            else
            {
                //// Compatible purpose. Could be removed when all client's active base plan has usage cycle.
                var (start, end) = (billRecord.PeriodStart, billRecord.PeriodEnd);
                return await _conversationService.GetBroadcastMessageCountCreatedBetweenDateAsync(companyId, start, end);
            }
        }

        private async Task<int> GetBroadcastMessageQuantity(Company company, BillRecord billRecord)
        {
            var subscriptionPlan = billRecord.SubscriptionPlan;
            var maximumAutomatedMessage = company.MaximumWhAutomatedMessages;
            var campaignFeatureQuantity = await _featureQuantityService.GetFeatureQuantityAsync(company.Id, FeatureId.BroadcastMessages);

            var quantityFromSources = _isEnableTenantHubLogic ? campaignFeatureQuantity : subscriptionPlan.MaximumCampaignSent;
            var quantity = maximumAutomatedMessage.HasValue ? Math.Max(quantityFromSources, maximumAutomatedMessage.Value) : quantityFromSources;

            //// No division needed for Freemium / Premium / Enterprise
            //// Freemium / Startup plans definition quantity is already monthly quantity
            //// For older than V10 Premium / Enterprise has unlimited usage
            //// For V10 only Enterprise has unlimited usage
            var planTier = subscriptionPlan.SubscriptionTier;
            var isYearlySubscription = subscriptionPlan.SubscriptionInterval.EqualsIgnoreCase("yearly");
            var isV9Unlimited = subscriptionPlan.Version <= 9 && (planTier is SubscriptionTier.Premium or SubscriptionTier.Enterprise);
            var isV10Unlimited = subscriptionPlan.Version == 10 && planTier is SubscriptionTier.Enterprise;
            return billRecord.IsUsageCycleApplied && isYearlySubscription && !isV9Unlimited && !isV10Unlimited ? (quantity / 12) : quantity;
        }

        private async Task<int> GetApiCallQuantity(Company company, BillRecord billRecord)
        {
            var subscriptionPlan = billRecord.SubscriptionPlan;

            var apiCallFeatureQuantity = await _featureQuantityService.GetFeatureQuantityAsync(company.Id, FeatureId.ApiCalls);
            var quantity = _isEnableTenantHubLogic ? apiCallFeatureQuantity : subscriptionPlan.MaximumAPICall;

            var isYearlySubscription = subscriptionPlan.SubscriptionInterval.Equals("yearly", StringComparison.OrdinalIgnoreCase);

            return billRecord.IsUsageCycleApplied && isYearlySubscription ? (quantity / 12) : quantity;
        }

        public async Task ComputeAndUpdateMaximumWhatsappInstance(string companyId)
        {
            try
            {
                _logger.LogInformation("Starting ComputeAndUpdateMaximumWhatsappInstance for company {CompanyId}", companyId);
                var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == companyId);
                if (company == null)
                {
                    _logger.LogWarning("Company not found for ComputeAndUpdateMaximumWhatsappInstance. CompanyId: {CompanyId}", companyId);
                    return;
                }

                var whatsappPhoneNumberFeatureQuantity = await GetCompanyPurchasedWhatsappNumberCount(companyId);

                if (whatsappPhoneNumberFeatureQuantity != company.MaximumWhatsappInstance)
                {
                    _logger.LogInformation(
                        "Updating MaximumWhatsappInstance for company {CompanyId} from {OldValue} to {NewValue}",
                        companyId, company.MaximumWhatsappInstance, whatsappPhoneNumberFeatureQuantity);
                    company.MaximumWhatsappInstance = whatsappPhoneNumberFeatureQuantity;
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    _logger.LogInformation(
                        "No update needed for MaximumWhatsappInstance. CompanyId: {CompanyId}, CurrentValue: {CurrentValue}",
                        companyId, company.MaximumWhatsappInstance);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Update MaximumWhatsappInstance error: {ExceptionMessage}",
                    ex.Message);
            }
        }
    }
}