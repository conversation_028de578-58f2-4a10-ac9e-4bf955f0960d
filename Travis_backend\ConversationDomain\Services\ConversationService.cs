using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Repositories;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels.Mappers;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.StripeIntegrationDomain.Services;
using Travis_backend.TenantHubDomain.Services;

namespace Travis_backend.ConversationDomain.Services;

public interface IConversationService
{
    Task<ConversationNoCompanyResponseViewModel> GetConversationDetails(
        string companyId,
        long companyUserId,
        StaffUserRole companyUserRole,
        string conversationId,
        CancellationToken cancellationToken = default);

    Task<HashSet<long>> FilterConversationMessage(
        IQueryable<ConversationMessage> allMessageCandidates,
        List<string> whatsappIds,
        List<long> whatsapp360dialogDefaultChannelIds,
        List<string> whatsappCloudApiDefaultChannelIds,
        List<string> twilioSenderIds);

    Task<bool> GetConversationLastMessage(
        ConversationNoCompanyResponseViewModel conversation,
        bool filterLastMessage,
        List<string> whatsappIds,
        List<long> whatsapp360dialogDefaultChannelIds,
        List<string> channelIdList,
        List<string> channelList,
        List<string> twilioSenderIds,
        bool isUpdatedLastMessageId,
        List<string> whatsappCloudApiDefaultChannelIds,
        CancellationToken cancellationToken = default);

    Task<List<ConversationNoCompanyResponseViewModel>> GetConversationLastMessages(
        List<ConversationNoCompanyResponseViewModel> conversations,
        bool filterLastMessage,
        List<string> whatsappIds,
        List<long> whatsapp360dialogDefaultChannelIds,
        List<string> channelIdList,
        List<string> channelList,
        List<string> twilioSenderIds,
        List<string> whatsappCloudApiDefaultChannelIds,
        CancellationToken cancellationToken = default);

    Task UpdateConversationMetadataAsync(
        string companyId,
        string conversationId,
        string key,
        object valueObject);

    ValueTask<bool> IsStaffAllowedToSendMessage(Staff staff, string conversationId);

    /// <summary>
    /// Get broadcast message count created between two dates.
    /// </summary>
    /// <param name="companyId">Company Id.</param>
    /// <param name="from">Broadcast message created from.</param>
    /// <param name="to">Broadcast message created to.</param>
    /// <returns>Number of broadcast message.</returns>
    Task<int> GetBroadcastMessageCountCreatedBetweenDateAsync(string companyId, DateTime from, DateTime to);
}

public class ConversationService : IConversationService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly ILockService _lockService;
    private readonly ISleekPayService _sleekPayService;
    private readonly IDbContextService _dbContextService;
    private readonly IConversationMessageRepository _conversationMessageRepository;
    private readonly IAccessControlAggregationService _accessControlAggregationService;
    private readonly IRbacConversationPermissionManager _rbacConversationPermissionManager;
    private readonly IServiceProvider _serviceProvider;
    private readonly IConversationNoCompanyResponseViewModelMapper _conversationNoCompanyResponseViewModelMapper;
    private readonly IConversationReadOnlyRepository _conversationReadOnlyRepository;

    private static IReadOnlyList<string> NonAssignableAsTeamDefaultChannels
        = new List<string>()
        {
            ChannelTypes.Note,
            ChannelTypes.LiveChat,
            ChannelTypes.Wechat,
            ChannelTypes.Telegram,
            ChannelTypes.Viber,
            ChannelTypes.Line,
            ChannelTypes.Sms,
            ChannelTypes.Tiktok
        };

    public ConversationService(
        ApplicationDbContext appDbContext,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<ConversationService> logger,
        ILockService lockService,
        ISleekPayService sleekPayService,
        IDbContextService dbContextService,
        IConversationMessageRepository conversationMessageRepository,
        IAccessControlAggregationService accessControlAggregationService,
        IServiceProvider serviceProvider,
        IRbacConversationPermissionManager rbacConversationPermissionManager,
        IConversationNoCompanyResponseViewModelMapper conversationNoCompanyResponseViewModelMapper,
        IConversationReadOnlyRepository conversationReadOnlyRepository)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _lockService = lockService;
        _sleekPayService = sleekPayService;
        _dbContextService = dbContextService;
        _conversationMessageRepository = conversationMessageRepository;
        _serviceProvider = serviceProvider;
        _rbacConversationPermissionManager = rbacConversationPermissionManager;
        _conversationNoCompanyResponseViewModelMapper = conversationNoCompanyResponseViewModelMapper;
        _conversationReadOnlyRepository = conversationReadOnlyRepository;
        _accessControlAggregationService = accessControlAggregationService;
    }

    public async Task<ConversationNoCompanyResponseViewModel> GetConversationDetails(
        string companyId,
        long companyUserId,
        StaffUserRole companyUserRole,
        string conversationId,
        CancellationToken cancellationToken = default)
    {
        var rbacService = _serviceProvider.GetRequiredService<IRbacService>();
        var isRbacEnabled = await rbacService.IsRbacEnabled(companyId);
        if (isRbacEnabled)
        {
            return await RbacGetConversationDetails(
                companyId,
                companyUserId,
                companyUserRole,
                conversationId,
                cancellationToken);
        }

        return await DefaultGetConversationDetails(
            companyId,
            companyUserId,
            companyUserRole,
            conversationId,
            cancellationToken);
    }

    public async Task<ConversationNoCompanyResponseViewModel> DefaultGetConversationDetails(
        string companyId,
        long companyUserId,
        StaffUserRole companyUserRole,
        string conversationId,
        CancellationToken cancellationToken)
    {
        var dbContext = _dbContextService.GetDbContext();
        var conversation = await _conversationReadOnlyRepository.FindConversationByIdAsync(
            conversationId,
            companyId,
            typeof(ConversationNoCompanyResponseViewModel),
            cancellationToken);

        var responseViewModel = await _conversationNoCompanyResponseViewModelMapper.ToViewModelAsync(conversation);

        if (responseViewModel == null)
        {
            throw new EntryPointNotFoundException("no conversation found");
        }

        // Personal bookmark
        responseViewModel.IsBookmarked = await dbContext.ConversationBookmarks
            .AnyAsync(
                x =>
                    x.ConversationId == responseViewModel.ConversationId
                    && x.StaffId == companyUserId,
                cancellationToken: cancellationToken);

        // Personal unread
        if (responseViewModel.UnreadMessageCount == 0)
        {
            responseViewModel.UnreadMessageCount = await dbContext.ConversationUnreadRecords
                .CountAsync(
                    y =>
                        y.CompanyId == companyId
                        && y.StaffId == companyUserId
                        && y.ConversationId == responseViewModel.ConversationId
                        && y.NotificationDeliveryStatus != NotificationDeliveryStatus.Read,
                    cancellationToken: cancellationToken);
        }

        try
        {
            try
            {
                responseViewModel.ConversationChannels = await GetConversationChannels(responseViewModel.ConversationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Error retrieving channels of past messages for conversationId {ConversationId}: {ExceptionMessage}",
                    nameof(GetConversationDetails),
                    conversationId,
                    ex.Message);
            }

            // mappedResult.Company.CompanyHashtags.ForEach(x => x.Count = _appDbContext.ConversationHashtags.Count(y => y.Hashtag.Id == x.Id));
            var rolePermission = await dbContext.CompanyRolePermissions
                .AsNoTracking()
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyId
                        && x.StaffUserRole == companyUserRole,
                    cancellationToken: cancellationToken);

            var filterLastMessage =
                rolePermission != null
                && rolePermission.Permission.IsShowDefaultChannelMessagesOnly;

            var associatedTeams = await dbContext.CompanyStaffTeams
                .Where(x => x.Members.Any(y => y.StaffId == companyUserId))
                .AsNoTracking()
                .ToListAsync(cancellationToken: cancellationToken);

            var channelList = new List<string>();
            var channelIdList = new List<string>();
            var whatsappIds = new List<string>();
            var twilioSenderIds = new List<string>();
            var whatsapp360dialogDefaultChannelIds = new List<long>();
            var whatsappCloudDefaultChannelIds = new List<string>();

            foreach (var associatedTeam in associatedTeams)
            {
                if (associatedTeam.DefaultChannels?.Count > 0)
                {
                    foreach (var defaultChannel in associatedTeam.DefaultChannels
                                 .Where(
                                     x =>
                                         x.channel != ChannelTypes.Whatsapp360Dialog
                                         && x.channel != ChannelTypes.WhatsappCloudApi))
                    {
                        channelList.Add(defaultChannel.channel);

                        if (defaultChannel.ids != null)
                        {
                            foreach (var id in defaultChannel.ids)
                            {
                                var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                whatsappIds.Add(twilioInstance[0]);

                                if (twilioInstance.Count() > 1)
                                {
                                    twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                                }
                            }
                        }
                    }

                    foreach (var defaultChannel in associatedTeam.DefaultChannels
                                 .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                    {
                        foreach (var channelId in defaultChannel.ids)
                        {
                            var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);

                            if (validLong)
                            {
                                whatsapp360dialogDefaultChannelIds.Add(whatsapp360dialogChannelId);
                            }
                        }
                    }

                    foreach (var defaultChannel in associatedTeam.DefaultChannels
                                 .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                    {
                        foreach (var channelId in defaultChannel.ids)
                        {
                            whatsappCloudDefaultChannelIds.Add(channelId);
                        }
                    }
                }
            }

            await GetConversationLastMessage(
                responseViewModel,
                filterLastMessage,
                whatsappIds,
                whatsapp360dialogDefaultChannelIds,
                channelIdList,
                channelList,
                twilioSenderIds,
                false,
                whatsappCloudDefaultChannelIds,
                cancellationToken);

            responseViewModel.FirstMessageId = await dbContext.ConversationMessages
                .Where(y => y.ConversationId == responseViewModel.ConversationId)
                .OrderBy(x => x.CreatedAt)
                .Select(x => x.Id)
                .FirstOrDefaultAsync(cancellationToken: cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Exception for conversation {ConversationId}: {ExceptionMessage}",
                nameof(GetConversationDetails),
                conversationId,
                ex.Message);
        }

        await _sleekPayService.AddSleekPayRecord(responseViewModel.LastMessage);

        responseViewModel.UserProfile.ConversationId = responseViewModel.ConversationId;

        // Attach Contact Listing in the signalR response
        var lists = await _appDbContext.CompanyImportContactHistories
            .Where(x => x.ImportedUserProfiles.Any(y => y.UserProfileId == responseViewModel.UserProfile.Id))
            .OrderByDescending(x => x.IsBookmarked)
            .ThenBy(x => x.Order)
            .ThenByDescending(x => x.CreatedAt)
            .Select(x => new
            {
                x.Id,
                x.ImportName
            })
            .AsNoTracking()
            .ToListAsync(cancellationToken: cancellationToken);

        // Init ContactLists
        responseViewModel.UserProfile.ContactLists = [];

        foreach (var list in lists)
        {
            responseViewModel.UserProfile.ContactLists.Add(
                new ContactJoinedList
                {
                    Id = list.Id,
                    ListName = list.ImportName
                });
        }

        return responseViewModel;
    }

    public async Task<bool> GetConversationLastMessage(
        ConversationNoCompanyResponseViewModel conversation,
        bool filterLastMessage,
        List<string> whatsappIds,
        List<long> whatsapp360dialogDefaultChannelIds,
        List<string> channelIdList,
        List<string> channelList,
        List<string> twilioSenderIds,
        bool isUpdatedLastMessageId,
        List<string> whatsappCloudApiDefaultChannelIds,
        CancellationToken cancellationToken = default)
    {
        var dbContext = _dbContextService.GetDbContext();
        if (conversation.LastMessageId.HasValue
            && (!filterLastMessage
                || (whatsappIds.Count == 0
                    && whatsapp360dialogDefaultChannelIds.Count == 0
                    && whatsappCloudApiDefaultChannelIds.Count == 0)))
        {
            var getLastMessageStopwatch = Stopwatch.StartNew();

            var lastMessage = await dbContext.ConversationMessages
                .Where(x => x.Id == conversation.LastMessageId)
                .Include(x => x.UploadedFiles)
                .Include(x => x.EmailFrom)
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.SenderDevice)
                .Include(x => x.ReceiverDevice)
                .Include(x => x.facebookSender)
                .Include(x => x.facebookReceiver)
                .Include(x => x.whatsappSender)
                .Include(x => x.whatsappReceiver)
                .Include(x => x.WebClientSender)
                .Include(x => x.WebClientReceiver)
                .Include(x => x.MessageAssignee.Identity)
                .Include(x => x.WeChatSender)
                .Include(x => x.WeChatReceiver)
                .Include(x => x.LineSender)
                .Include(x => x.LineReceiver)
                .Include(x => x.SMSSender)
                .Include(x => x.SMSReceiver)
                .Include(x => x.InstagramReceiver)
                .Include(x => x.InstagramSender)
                .Include(x => x.ViberSender)
                .Include(x => x.ViberReceiver)
                .Include(x => x.TelegramSender)
                .Include(x => x.TelegramReceiver)
                .Include(x => x.Whatsapp360DialogSender)
                .Include(x => x.Whatsapp360DialogReceiver)
                .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                .Include(x => x.ExtendedMessagePayload)
                .ToListAsync(cancellationToken: cancellationToken);

            if (lastMessage.Count != 0)
            {
                conversation.LastMessage = _mapper.Map<List<ConversationMessageResponseViewModel>>(lastMessage);
            }
            else
            {
                conversation.LastMessage = new List<ConversationMessageResponseViewModel>();
            }

            getLastMessageStopwatch.Stop();

            _logger.LogDebug(
                $"[PERFORMANCE] getLastMessage query executed in {getLastMessageStopwatch.ElapsedMilliseconds} ms ");

            var sleekPayStopwatch = Stopwatch.StartNew();

            await _sleekPayService.AddSleekPayRecord(conversation.LastMessage);

            sleekPayStopwatch.Stop();

            _logger.LogDebug(
                $"[PERFORMANCE] sleekpay query executed in {sleekPayStopwatch.ElapsedMilliseconds} ms ");

            conversation.ConversationChannels.Add(conversation.LastMessageChannel);
        }
        else
        {
            var filteringLastMessageStopwatch = Stopwatch.StartNew();

            var conversationMessagesQuery = dbContext.ConversationMessages
                .Where(message => message.ConversationId == conversation.ConversationId);

            HashSet<long> filteredMessagedIds;

            if (whatsappIds.Count == 0
                && whatsappCloudApiDefaultChannelIds.Count == 0
                && whatsapp360dialogDefaultChannelIds.Count == 0
                && twilioSenderIds.Count == 0)
            {
                filteredMessagedIds = (await conversationMessagesQuery
                        .Select(x => x.Id)
                        .ToListAsync(cancellationToken: cancellationToken))
                    .ToHashSet();
            }
            else
            {
                filteredMessagedIds = await FilterConversationMessage(
                    conversationMessagesQuery,
                    whatsappIds,
                    whatsapp360dialogDefaultChannelIds,
                    whatsappCloudApiDefaultChannelIds,
                    twilioSenderIds);
            }

            if (channelIdList.Count > 0)
            {
                try
                {
                    var newWhatsappId = new List<string>();
                    var newInstanceSender = new List<string>();
                    var newWhatsapp360dialogDefaultChannelIds = new List<long>();
                    var newWhatsappCloudApiChannelIds = new List<string>();

                    if (channelList.Contains(ChannelTypes.WhatsappTwilio))
                    {
                        foreach (var myChannelId in channelIdList)
                        {
                            var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                            newWhatsappId.Add(twilioInstance[0]);

                            if (twilioInstance.Count() > 1)
                            {
                                newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                            }
                        }
                    }

                    if (channelList.Contains(ChannelTypes.Whatsapp360Dialog))
                    {
                        var whatsapp360DialogChannelId = channelIdList
                            .Select(x => long.TryParse(x, out var id) ? id : 0)
                            .Where(x => x != 0)
                            .Distinct()
                            .ToList();

                        newWhatsapp360dialogDefaultChannelIds.AddRange(whatsapp360DialogChannelId);
                    }

                    if (channelList.Contains(ChannelTypes.WhatsappCloudApi))
                    {
                        var whatsappCloudApiIds = channelIdList
                            .Distinct()
                            .ToList();

                        newWhatsappCloudApiChannelIds.AddRange(whatsappCloudApiIds);
                    }

                    if (channelList.Contains(ChannelTypes.Instagram))
                    {
                        var instagramIds = channelIdList
                            .Distinct()
                            .ToList();

                        newWhatsappId.AddRange(instagramIds);
                    }

                    if (channelList.Contains(ChannelTypes.Facebook))
                    {
                        var facebookIds = channelIdList
                            .Distinct()
                            .ToList();

                        newWhatsappId.AddRange(facebookIds);
                    }

                    var newFilteredMessagedIds = await FilterConversationMessage(
                        conversationMessagesQuery,
                        newWhatsappId,
                        newWhatsapp360dialogDefaultChannelIds,
                        newWhatsappCloudApiChannelIds,
                        newInstanceSender);

                    if (newFilteredMessagedIds.Count > 0)
                    {
                        filteredMessagedIds.IntersectWith(newFilteredMessagedIds);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}] error: {ExceptionMessage}",
                        nameof(GetConversationLastMessage),
                        ex.Message);
                }
            }

            conversation.LastMessage = await dbContext.ConversationMessages
                .Where(x => filteredMessagedIds.Contains(x.Id))
                .OrderByDescending(x => x.Timestamp)
                .AsNoTracking()
                .ProjectTo<ConversationMessageResponseViewModel>(_mapper.ConfigurationProvider)
                .Take(1)
                .ToListAsync(cancellationToken: cancellationToken);

            await _sleekPayService.AddSleekPayRecord(conversation.LastMessage);

            if (conversation.ConversationChannels.Count == 0)
            {
                conversation.ConversationChannels = await GetConversationChannels(conversation.ConversationId, cancellationToken);
            }

            filteringLastMessageStopwatch.Stop();

            _logger.LogDebug(
                $"[PERFORMANCE] GetConversationLastMessage Filtering LastMessage query executed in {filteringLastMessageStopwatch.ElapsedMilliseconds} ms ");
        }

        return isUpdatedLastMessageId;
    }

    public async Task<List<ConversationNoCompanyResponseViewModel>> GetConversationLastMessages(
        List<ConversationNoCompanyResponseViewModel> conversations,
        bool filterLastMessage,
        List<string> whatsappIds,
        List<long> whatsapp360dialogDefaultChannelIds,
        List<string> channelIdList,
        List<string> channelList,
        List<string> twilioSenderIds,
        List<string> whatsappCloudApiDefaultChannelIds,
        CancellationToken cancellationToken = default)
    {
        if (conversations == null || conversations.Count == 0)
        {
            return conversations ?? new List<ConversationNoCompanyResponseViewModel>();
        }

        var hasDefaultChannel = whatsappIds.Count != 0 || whatsapp360dialogDefaultChannelIds.Count != 0 || whatsappCloudApiDefaultChannelIds.Count != 0;

        // Decide which path to take
        var requiresFiltering = filterLastMessage && hasDefaultChannel;

        if (!requiresFiltering)
        {
            // Path 1: No complex filtering needed, use LastMessageId
            await PopulateUnfilteredLastMessagesAsync(conversations);
        }
        else
        {
            // Path 2: Complex filtering based on channels is required
            await PopulateFilteredLastMessagesAsync(
                conversations,
                whatsappIds,
                whatsapp360dialogDefaultChannelIds,
                channelIdList,
                channelList,
                twilioSenderIds,
                whatsappCloudApiDefaultChannelIds);

            // Populate channels only if filtering was applied (matches original logic)
            await PopulateConversationChannelsIfNeededAsync(conversations);
        }

        return conversations;
    }

    private async Task<List<string>> GetConversationChannels(
        string conversationId,
        CancellationToken cancellationToken = default)
    {
        var conversationChannels = await _dbContextService.GetDbContext().ConversationMessages
            .Where(x => x.ConversationId == conversationId)
            .Select(x => x.Channel)
            .Distinct()
            .ToListAsync(cancellationToken: cancellationToken);

        if (conversationChannels.Contains(ChannelTypes.Note))
        {
            conversationChannels.Remove(ChannelTypes.Note);
        }

        return conversationChannels;
    }

    public async ValueTask<bool> IsStaffAllowedToSendMessage(Staff staff, string conversationId)
    {
        var isStaffRole = staff.RoleType == StaffUserRole.Staff;

        // Bug Fix: DEVS-3349 [Travel Expert] Unassigned tab is not working normally
        // This bug is caused by another Bug Fix: DEVS-2429
        // All members can send messages to an unassigned conversation
        var isUnassignedConversation = await _appDbContext.Conversations.AnyAsync(
            x => x.Id == conversationId
                 && x.AssigneeId == null);

        if (isUnassignedConversation)
        {
            return true;
        }

        // non-Staff role members can send messages to an assigned conversation
        if (!isStaffRole)
        {
            return true;
        }

        // In response to Alex Yu's request for better DB performance, we use EF Core's search here.
        // This decreases round-trip fetches for the conversation object by performing checks within the search.
        var isContactOwner = await _appDbContext.Conversations
            .AnyAsync(x =>
                x.Id == conversationId
                && x.AssigneeId == staff.Id);

        var isCollaborator = await _appDbContext.ConversationAdditionalAssignees
            .AnyAsync(x =>
                x.ConversationId == conversationId
                && x.AssigneeId == staff.Id);

        // Staff role members are only permitted to send messages in an assigned conversation if they are the contact owner or collaborator.
        return isCollaborator || isContactOwner;

        // Previous Fix done by Peter - commit number: 52a57907
        // switch (staffInfo.RoleType)
        // {
        //    case StaffUserRole.Staff:
        //        if (_appDbContext.Conversations.Where(y => y.Id == conversationMessageViewModel.ConversationId && (y.AssigneeId == staffInfo.Id || !y.AssigneeId.HasValue)).Count() > 0)
        //            break;
        //        return Unauthorized();
        //    case StaffUserRole.TeamAdmin:
        //        var teams = _appDbContext.CompanyStaffTeams.Where(x => x.Members.Where(y => y.StaffId == staffInfo.Id).Count() > 0);
        //        if (_appDbContext.Conversations.Where(y => y.Id == conversationMessageViewModel.ConversationId && (y.AssigneeId == staffInfo.Id || !y.AssigneeId.HasValue || teams.Where(z => z.Members.Where(a => a.StaffId == y.AssigneeId).Count() > 0).Count() > 0)).Count() > 0 ||
        //            _appDbContext.Conversations.Where(y => y.Id == conversationMessageViewModel.ConversationId && y.AssignedTeam.Members.Where(x => x.StaffId == staffInfo.Id).Count() > 0).Count() > 0)
        //            break;
        //        return Unauthorized();
        // }
    }

    public async Task<HashSet<long>> FilterConversationMessage(
        IQueryable<ConversationMessage> allMessageCandidates,
        List<string> whatsappIds,
        List<long> whatsapp360dialogDefaultChannelIds,
        List<string> whatsappCloudApiDefaultChannelIds,
        List<string> twilioSenderIds)
    {
        var resultQueryables = new List<IQueryable<ConversationMessage>>();

        if (whatsapp360dialogDefaultChannelIds is { Count: > 0 })
        {
            var whatsapp360dialogDefaultMessagesQueryable =
                from conversationMessage in allMessageCandidates
                where whatsapp360dialogDefaultChannelIds.Contains(
                          conversationMessage.Whatsapp360DialogReceiver.ChannelId.Value)
                      || whatsapp360dialogDefaultChannelIds.Contains(
                          conversationMessage.Whatsapp360DialogSender.ChannelId.Value)
                      || NonAssignableAsTeamDefaultChannels.Contains(conversationMessage.Channel)
                select conversationMessage;

            resultQueryables.Add(whatsapp360dialogDefaultMessagesQueryable);
        }

        if (twilioSenderIds is { Count: > 0 })
        {
            var twilioMessagesQueryable = from conversationMessage in allMessageCandidates
                                          where (conversationMessage.Channel == ChannelTypes.WhatsappTwilio
                                                 && conversationMessage.whatsappReceiver.InstaneSender != null
                                                 && whatsappIds.Contains(conversationMessage.whatsappReceiver.InstanceId)
                                                 && twilioSenderIds.Contains(conversationMessage.whatsappReceiver.InstaneSender))
                                                || NonAssignableAsTeamDefaultChannels.Contains(conversationMessage.Channel)
                                          select conversationMessage;

            resultQueryables.Add(twilioMessagesQueryable);
        }

        if (whatsappIds is { Count: > 0 })
        {
            var whatsappMessagesQueryable =
                from conversationMessage in allMessageCandidates
                where whatsappIds.Contains(conversationMessage.facebookReceiver.pageId)
                      || whatsappIds.Contains(conversationMessage.InstagramReceiver.InstagramPageId)
                      || NonAssignableAsTeamDefaultChannels.Contains(conversationMessage.Channel)
                select conversationMessage;

            resultQueryables.Add(whatsappMessagesQueryable);
        }

        if (whatsappCloudApiDefaultChannelIds is { Count: > 0 })
        {
            var whatsappCloudApiMessagesQueryable = allMessageCandidates
                .Where(
                    x =>
                        whatsappCloudApiDefaultChannelIds.Contains(x.ChannelIdentityId)
                        || NonAssignableAsTeamDefaultChannels.Contains(x.Channel));

            resultQueryables.Add(whatsappCloudApiMessagesQueryable);
        }

        if (resultQueryables.Count == 0)
        {
            return new HashSet<long>();
        }

        var queryable = resultQueryables.FirstOrDefault();

        for (var i = 1; i < resultQueryables.Count; i++)
        {
            queryable = queryable!.Union(resultQueryables[i]);
        }

        var filteredMessagedIds = (await queryable!
                .Select(x => x.Id)
                .ToListAsync())
            .ToHashSet();

        return filteredMessagedIds;
    }

    public async Task UpdateConversationMetadataAsync(string companyId, string conversationId, string key, object valueObject)
    {
        ILockService.Lock myLock = null;

        try
        {
            while (true)
            {
                var lockId = $"update_conversation_metadata_{companyId}_{conversationId}";

                myLock = await _lockService.AcquireLockAsync(lockId, TimeSpan.FromSeconds(2));

                if (myLock == null)
                {
                    await Task.Delay(TimeSpan.FromSeconds(2));
                }
                else
                {
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Unable to lock UpdateConversationMetadataAsync for {CompanyId} {ConversationId}: {ExceptionMessage}",
                nameof(UpdateConversationMetadataAsync),
                companyId,
                conversationId,
                ex.Message);
        }

        var conversation = await _appDbContext.Conversations.Where(x => x.CompanyId == companyId && x.Id == conversationId)
            .FirstOrDefaultAsync();

        if (conversation.Metadata == null)
        {
            conversation.Metadata = new Dictionary<string, object>()
            {
                {
                    key, valueObject
                }
            };
        }
        else
        {
            conversation.Metadata[key] = valueObject;
        }

        await _appDbContext.Conversations.Where(x => x.Id == conversationId)
            .ExecuteUpdateAsync(s => s.SetProperty(c => c.Metadata, conversation.Metadata));

        await _lockService.ReleaseLockAsync(myLock);
    }

    /// <inheritdoc />
    public async Task<int> GetBroadcastMessageCountCreatedBetweenDateAsync(string companyId, DateTime from, DateTime to)
    {
        return await _conversationMessageRepository.CountByCreatedBetweenDateTimeAsync(
            companyId,
            DeliveryType.Broadcast,
            from,
            to);
    }

    #region Rbac method

    public async Task<ConversationNoCompanyResponseViewModel> RbacGetConversationDetails(
        string companyId,
        long companyUserId,
        StaffUserRole companyUserRole,
        string conversationId,
        CancellationToken cancellationToken)
    {
        var dbContext = _dbContextService.GetDbContext();
        var conversation = await _conversationReadOnlyRepository.FindConversationByIdAsync(
            conversationId,
            companyId,
            typeof(ConversationNoCompanyResponseViewModel),
            cancellationToken);

        var responseViewModel = await _conversationNoCompanyResponseViewModelMapper.ToViewModelAsync(conversation);

        if (responseViewModel == null)
        {
            throw new EntryPointNotFoundException("no conversation found");
        }

        // Personal bookmark
        responseViewModel.IsBookmarked = await dbContext.ConversationBookmarks
            .AnyAsync(
                x =>
                    x.ConversationId == responseViewModel.ConversationId
                    && x.StaffId == companyUserId,
                cancellationToken: cancellationToken);

        // Personal unread
        if (responseViewModel.UnreadMessageCount == 0)
        {
            responseViewModel.UnreadMessageCount = await dbContext.ConversationUnreadRecords
                .CountAsync(
                    y =>
                        y.CompanyId == companyId
                        && y.StaffId == companyUserId
                        && y.ConversationId == responseViewModel.ConversationId
                        && y.NotificationDeliveryStatus != NotificationDeliveryStatus.Read,
                    cancellationToken: cancellationToken);
        }

        try
        {
            try
            {
                responseViewModel.ConversationChannels =
                    await GetConversationChannels(responseViewModel.ConversationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Error retrieving channels of past messages for conversationId {ConversationId}: {ExceptionMessage}",
                    nameof(GetConversationDetails),
                    conversationId,
                    ex.Message);
            }

            var staff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(
                companyUserId,
                companyId);

            var aggregatedConversation = await _accessControlAggregationService.GetAggregatedConversationAsync(
                conversationId,
                companyId);

            responseViewModel.StaffConversationPermission = _rbacConversationPermissionManager.GetStaffConversationPermission(staff, aggregatedConversation);

            var filterLastMessage = staff.HasDefaultChannelSettingEnabled();

            var associatedTeams = await dbContext.CompanyStaffTeams
                .Where(x => x.Members.Any(y => y.StaffId == companyUserId))
                .AsNoTracking()
                .ToListAsync(cancellationToken: cancellationToken);

            var channelList = new List<string>();
            var channelIdList = new List<string>();
            var whatsappIds = new List<string>();
            var twilioSenderIds = new List<string>();
            var whatsapp360dialogDefaultChannelIds = new List<long>();
            var whatsappCloudDefaultChannelIds = new List<string>();

            foreach (var associatedTeam in associatedTeams)
            {
                if (associatedTeam.DefaultChannels?.Count > 0)
                {
                    foreach (var defaultChannel in associatedTeam.DefaultChannels
                                 .Where(
                                     x =>
                                         x.channel != ChannelTypes.Whatsapp360Dialog
                                         && x.channel != ChannelTypes.WhatsappCloudApi))
                    {
                        channelList.Add(defaultChannel.channel);

                        if (defaultChannel.ids != null)
                        {
                            foreach (var id in defaultChannel.ids)
                            {
                                var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                whatsappIds.Add(twilioInstance[0]);

                                if (twilioInstance.Count() > 1)
                                {
                                    twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                                }
                            }
                        }
                    }

                    foreach (var defaultChannel in associatedTeam.DefaultChannels
                                 .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                    {
                        foreach (var channelId in defaultChannel.ids)
                        {
                            var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);

                            if (validLong)
                            {
                                whatsapp360dialogDefaultChannelIds.Add(whatsapp360dialogChannelId);
                            }
                        }
                    }

                    foreach (var defaultChannel in associatedTeam.DefaultChannels
                                 .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                    {
                        foreach (var channelId in defaultChannel.ids)
                        {
                            whatsappCloudDefaultChannelIds.Add(channelId);
                        }
                    }
                }
            }

            await GetConversationLastMessage(
                responseViewModel,
                filterLastMessage,
                whatsappIds,
                whatsapp360dialogDefaultChannelIds,
                channelIdList,
                channelList,
                twilioSenderIds,
                false,
                whatsappCloudDefaultChannelIds,
                cancellationToken);

            responseViewModel.FirstMessageId = await dbContext.ConversationMessages
                .Where(y => y.ConversationId == responseViewModel.ConversationId)
                .OrderBy(x => x.CreatedAt)
                .Select(x => x.Id)
                .FirstOrDefaultAsync(cancellationToken: cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Exception for conversation {ConversationId}: {ExceptionMessage}",
                nameof(GetConversationDetails),
                conversationId,
                ex.Message);
        }

        await _sleekPayService.AddSleekPayRecord(responseViewModel.LastMessage);
        if (responseViewModel.UserProfile != null)
        {
            responseViewModel.UserProfile.ConversationId = responseViewModel.ConversationId;

            // Attach Contact Listing in the signalR response
            var lists = await _appDbContext.CompanyImportContactHistories
                .Where(x => x.ImportedUserProfiles.Any(y => y.UserProfileId == responseViewModel.UserProfile.Id))
                .OrderByDescending(x => x.IsBookmarked)
                .ThenBy(x => x.Order)
                .ThenByDescending(x => x.CreatedAt)
                .Select(
                    x => new
                    {
                        x.Id,
                        x.ImportName
                    })
                .AsNoTracking()
                .ToListAsync(cancellationToken: cancellationToken);

            // Init ContactLists
            responseViewModel.UserProfile.ContactLists = [];

            foreach (var list in lists)
            {
                responseViewModel.UserProfile.ContactLists.Add(
                    new ContactJoinedList
                    {
                        Id = list.Id,
                        ListName = list.ImportName
                    });
            }
        }

        return responseViewModel;
    }

    #endregion

    #region Batch Get Last Message

    private async Task PopulateUnfilteredLastMessagesAsync(
        List<ConversationNoCompanyResponseViewModel> conversations)
    {
        var dbContext = _dbContextService.GetDbContext();

        var getLastMessageStopwatch = Stopwatch.StartNew();

        // Get the list of LastMessageId values that actually exist
        var conversationsLastMessageIds = conversations
            .Where(c => c.LastMessageId.HasValue)
            .Select(c => c.LastMessageId.Value) // Select the value directly
            .Distinct() // Avoid fetching the same message multiple times if shared
            .ToList();

        if (!conversationsLastMessageIds.Any())
        {
            // No LastMessageIds to fetch
            getLastMessageStopwatch.Stop();
            return;
        }

        // Fetch all unique last messages in one go
        var lastMessages = await dbContext.ConversationMessages
            .Where(x => conversationsLastMessageIds.Contains(x.Id))
            .Include(x => x.UploadedFiles)
            .Include(x => x.EmailFrom)
            .Include(x => x.Sender)
            .Include(x => x.Receiver)
            .Include(x => x.SenderDevice)
            .Include(x => x.ReceiverDevice)
            .Include(x => x.facebookSender)
            .Include(x => x.facebookReceiver)
            .Include(x => x.whatsappSender)
            .Include(x => x.whatsappReceiver)
            .Include(x => x.WebClientSender)
            .Include(x => x.WebClientReceiver)
            .Include(x => x.MessageAssignee.Identity)
            .Include(x => x.WeChatSender)
            .Include(x => x.WeChatReceiver)
            .Include(x => x.LineSender)
            .Include(x => x.LineReceiver)
            .Include(x => x.SMSSender)
            .Include(x => x.SMSReceiver)
            .Include(x => x.InstagramReceiver)
            .Include(x => x.InstagramSender)
            .Include(x => x.ViberSender)
            .Include(x => x.ViberReceiver)
            .Include(x => x.TelegramSender)
            .Include(x => x.TelegramReceiver)
            .Include(x => x.Whatsapp360DialogSender)
            .Include(x => x.Whatsapp360DialogReceiver)
            .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
            .Include(x => x.ExtendedMessagePayload)
            .AsNoTracking() // Good practice if only reading
            .ToListAsync();

        // Map fetched messages
        var lastMessagesVms = _mapper.Map<List<ConversationMessageResponseViewModel>>(lastMessages);

        // Create a lookup dictionary for efficient assignment
        var lastMessageVmById = lastMessagesVms.ToDictionary(vm => vm.Id);

        // Assign the fetched message VM to the corresponding conversation
        foreach (var conversation in conversations)
        {
            if (conversation.LastMessageId.HasValue && lastMessageVmById.TryGetValue(
                    conversation.LastMessageId.Value,
                    out var lastMessageVm))
            {
                // Assuming LastMessage is a List as per original code
                conversation.LastMessage = new List<ConversationMessageResponseViewModel>
                {
                    lastMessageVm
                };
            }
            else
            {
                conversation.LastMessage = []; // Assign empty list if not found or no ID
            }
        }

        getLastMessageStopwatch.Stop();
        _logger.LogDebug(
            "[PERFORMANCE] PopulateUnfilteredLastMessagesAsync query executed in {ElapsedMilliseconds} ms for {MessageCount} messages.",
            getLastMessageStopwatch.ElapsedMilliseconds,
            lastMessages.Count);

        // Handle SleekPay Integration
        await _sleekPayService.AddSleekPayRecord(lastMessagesVms);
    }


    private async Task PopulateFilteredLastMessagesAsync(
        List<ConversationNoCompanyResponseViewModel> conversations,
        List<string> whatsappIds,
        List<long> whatsapp360dialogDefaultChannelIds,
        List<string> channelIdList,
        List<string> channelList,
        List<string> twilioSenderIds,
        List<string> whatsappCloudApiDefaultChannelIds)
    {
        var dbContext = _dbContextService.GetDbContext();

        var filteringLastMessageStopwatch = Stopwatch.StartNew();
        var conversationIds = conversations.Select(c => c.ConversationId).ToList();

        // 1. Calculate the set of message IDs allowed by the filters
        var baseMessagesQuery = dbContext.ConversationMessages
            .Where(msg => conversationIds.Contains(msg.ConversationId));

        var filteredMessageIds = await CalculateFilteredMessageIdsAsync(
            baseMessagesQuery,
            whatsappIds,
            whatsapp360dialogDefaultChannelIds,
            channelIdList,
            channelList,
            twilioSenderIds,
            whatsappCloudApiDefaultChannelIds);

        if (filteredMessageIds.Count == 0)
        {
            // No messages match the filters, ensure LastMessage is empty
            foreach (var conversation in conversations)
            {
                conversation.LastMessage = [];
            }

            filteringLastMessageStopwatch.Stop();
            _logger.LogDebug(
                "[PERFORMANCE] PopulateFilteredLastMessagesAsync: No messages matched filters. Time: {ElapsedMilliseconds} ms",
                filteringLastMessageStopwatch.ElapsedMilliseconds);
            return;
        }

        var fetchRawMessagesStopwatch = Stopwatch.StartNew(); // Start timing
        // 2. Fetch the latest message for each conversation *from the filtered set*
        // Group by ConversationId, order by Timestamp descending, take the first.
        var filteredMessagesRaw = await dbContext.ConversationMessages
            .Where(msg => filteredMessageIds.Contains(msg.Id))
            .Select(msg => new // Select only necessary fields
            {
                msg.Id,
                msg.ConversationId,
                msg.Timestamp
                /*, include other fields needed ONLY for determining the 'latest' if timestamp isn't enough,
                   but avoid fetching full message data here if possible */
            })
            .OrderBy(msg => msg.ConversationId) // Optional, but helps visualize groups
            .ThenByDescending(msg => msg.Timestamp) // Order by Timestamp descending within each potential group
            .AsNoTracking()
            .ToListAsync();

        fetchRawMessagesStopwatch.Stop(); // Stop timing immediately after completion

        _logger.LogDebug(
            "[PERFORMANCE] Fetched {RawMessageCount} raw messages for {ConversationCount} conversations matching {FilteredIdCount} filtered IDs in {ElapsedMilliseconds} ms.",
            filteredMessagesRaw.Count, // Log how many rows were actually retrieved
            conversationIds.Count, // Context: How many conversations were targeted
            filteredMessageIds.Count, // Context: How many specific message IDs were allowed
            fetchRawMessagesStopwatch.ElapsedMilliseconds // Log the duration
        );

        // 3. Perform grouping and select the latest message *in memory*
        var latestFilteredMessages = filteredMessagesRaw
            .GroupBy(msg => msg.ConversationId) // Group by ConversationId in memory
            .Select(g => g.First()) // Since they are ordered descending by Timestamp, the first in each group is the latest
            .ToList(); // Materialize the list of latest messages (containing Id, ConversationId, Timestamp)

        // 3. Create a lookup dictionary from the in-memory result
        var latestMessageIdByConversationId = latestFilteredMessages
            // No need for .Where(msg => msg != null) here as grouping non-empty list won't produce nulls
            .ToDictionary(msg => msg.ConversationId, msg => msg.Id);

        // 3. Fetch full details *only* for the identified latest messages
        var latestMessageIdsToFetch = latestMessageIdByConversationId.Values.ToList();
        List<ConversationMessageResponseViewModel> lastMessageVms = new();

        if (latestMessageIdsToFetch.Count != 0)
        {
            var fullLatestMessages = await dbContext.ConversationMessages
                .Where(msg => latestMessageIdsToFetch.Contains(msg.Id))
                .Include(x => x.UploadedFiles)
                .Include(x => x.EmailFrom)
                .Include(x => x.Sender)
                .Include(x => x.Receiver)
                .Include(x => x.SenderDevice)
                .Include(x => x.ReceiverDevice)
                .Include(x => x.facebookSender)
                .Include(x => x.facebookReceiver)
                .Include(x => x.whatsappSender)
                .Include(x => x.whatsappReceiver)
                .Include(x => x.WebClientSender)
                .Include(x => x.WebClientReceiver)
                .Include(x => x.MessageAssignee.Identity)
                .Include(x => x.WeChatSender)
                .Include(x => x.WeChatReceiver)
                .Include(x => x.LineSender)
                .Include(x => x.LineReceiver)
                .Include(x => x.SMSSender)
                .Include(x => x.SMSReceiver)
                .Include(x => x.InstagramReceiver)
                .Include(x => x.InstagramSender)
                .Include(x => x.ViberSender)
                .Include(x => x.ViberReceiver)
                .Include(x => x.TelegramSender)
                .Include(x => x.TelegramReceiver)
                .Include(x => x.Whatsapp360DialogSender)
                .Include(x => x.Whatsapp360DialogReceiver)
                .Include(x => x.Whatsapp360DialogExtendedMessagePayload)
                .Include(x => x.ExtendedMessagePayload)
                .AsNoTracking()
                .ToListAsync();

            lastMessageVms = _mapper.Map<List<ConversationMessageResponseViewModel>>(fullLatestMessages);
        }

        // Create a lookup for the mapped VMs
        var lastMessageVmById = lastMessageVms.ToDictionary(vm => vm.Id);

        // 4. Assign the filtered last message to each conversation
        foreach (var conversation in conversations)
        {
            if (latestMessageIdByConversationId.TryGetValue(conversation.ConversationId, out var lastMsgId) &&
                lastMessageVmById.TryGetValue(lastMsgId, out var lastMessageVm))
            {
                conversation.LastMessage = new List<ConversationMessageResponseViewModel>
                {
                    lastMessageVm
                };
            }
            else
            {
                conversation.LastMessage = []; // Assign empty list if no message found after filtering
            }
        }

        // 5. Handle SleekPay Integration for the messages actually assigned
        await _sleekPayService.AddSleekPayRecord(lastMessageVms);

        filteringLastMessageStopwatch.Stop();
        _logger.LogDebug(
            "[PERFORMANCE] PopulateFilteredLastMessagesAsync executed in {ElapsedMilliseconds} ms.",
            filteringLastMessageStopwatch.ElapsedMilliseconds);
    }

    private async Task<HashSet<long>> CalculateFilteredMessageIdsAsync(
        IQueryable<ConversationMessage> baseMessagesQuery,
        List<string> whatsappIds,
        List<long> whatsapp360dialogDefaultChannelIds,
        List<string> channelIdList,
        List<string> channelList,
        List<string> twilioSenderIds,
        List<string> whatsappCloudApiDefaultChannelIds)
    {
        HashSet<long> defaultFilteredIds;

        // Check if default channel filtering is needed
        if (whatsappIds.Count != 0 || whatsappCloudApiDefaultChannelIds.Count != 0 || whatsapp360dialogDefaultChannelIds.Count != 0 ||
            twilioSenderIds.Count != 0)
        {
            // Assuming FilterConversationMessage takes IQueryable and returns HashSet<long>
            // Pass dbContext if it's needed internally by FilterConversationMessage
            defaultFilteredIds = await FilterConversationMessage(
                baseMessagesQuery,
                whatsappIds,
                whatsapp360dialogDefaultChannelIds,
                whatsappCloudApiDefaultChannelIds,
                twilioSenderIds);
        }
        else
        {
            // No default filters, initially allow all messages from the base query
            defaultFilteredIds = (await baseMessagesQuery.Select(x => x.Id).ToListAsync()).ToHashSet();
        }


        // Apply specific channel ID / channel List filters if provided
        if (channelIdList.Count <= 0 || channelList.Count <= 0) // Need both for this logic block
        {
            return defaultFilteredIds;
        }

        try
        {
            var specificFilteredIds = await CalculateSpecificChannelFilteredIdsAsync(
                baseMessagesQuery,
                channelIdList,
                channelList);

            // Intersect default results with specific results
            defaultFilteredIds.IntersectWith(specificFilteredIds);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Error calculating specific channel filters: {ExceptionMessage}",
                nameof(CalculateFilteredMessageIdsAsync),
                ex.Message);
        }

        return defaultFilteredIds;
    }

    // Helper to parse specific channel filters and get IDs (Refactored from original code)
    private async Task<HashSet<long>> CalculateSpecificChannelFilteredIdsAsync(
        IQueryable<ConversationMessage> baseMessagesQuery,
        List<string> channelIdList,
        List<string> channelList)
    {
        var newWhatsappId = new List<string>();
        var newInstanceSender = new List<string>();
        var newWhatsapp360dialogDefaultChannelIds = new List<long>();
        var newWhatsappCloudApiChannelIds = new List<string>();
        var newTikTokChannelIds = new List<string>();

        // Parsing logic based on channelList (simplified - adapt based on exact ChannelTypes values)
        if (channelList.Contains(ChannelTypes.WhatsappTwilio)) // Use actual constants/enums
        {
            foreach (var myChannelId in channelIdList)
            {
                var twilioInstance = myChannelId.Split(";", StringSplitOptions.RemoveEmptyEntries);
                if (twilioInstance.Length > 0)
                {
                    newWhatsappId.Add(twilioInstance[0]);
                }

                if (twilioInstance.Length > 1)
                {
                    newInstanceSender.Add(twilioInstance[1].Replace(": ", ":+"));
                }
            }
        }

        if (channelList.Contains(ChannelTypes.Whatsapp360Dialog)) // Use actual constants/enums
        {
            newWhatsapp360dialogDefaultChannelIds.AddRange(
                channelIdList
                    .Select(x => long.TryParse(x, out var id) ? id : 0)
                    .Where(x => x != 0)
                    .Distinct());
        }

        if (channelList.Contains(ChannelTypes.WhatsappCloudApi)) // Use actual constants/enums
        {
            newWhatsappCloudApiChannelIds.AddRange(channelIdList.Distinct());
        }

        // Call the underlying filter method again with the parsed specific filters
        return await FilterConversationMessage(
            baseMessagesQuery,
            newWhatsappId,
            newWhatsapp360dialogDefaultChannelIds,
            newWhatsappCloudApiChannelIds,
            newInstanceSender);
    }

    private async Task PopulateConversationChannelsIfNeededAsync(
        List<ConversationNoCompanyResponseViewModel> conversations)
    {
        var channelsStopwatch = Stopwatch.StartNew();

        // 1. Identify conversations that need their channels populated
        var conversationsToUpdate = conversations
            .Where(c => c.ConversationChannels == null || !c.ConversationChannels.Any())
            .ToList(); // Create a list of the conversation objects needing update

        int updateCount = conversationsToUpdate.Count;

        if (updateCount == 0)
        {
            channelsStopwatch.Stop();
            // No need to log if nothing was done, or maybe a Trace level log
            // _logger.LogInformation("[PERFORMANCE] PopulateConversationChannelsIfNeededAsync: No conversations needed channel population. Time: {ElapsedMilliseconds} ms", channelsStopwatch.ElapsedMilliseconds);
            return; // Nothing to do
        }

        // Extract the IDs for the bulk fetch
        var idsToFetch = conversationsToUpdate
            .Select(c => c.ConversationId)
            .Distinct() // Ensure IDs are unique before fetching
            .ToList();


        // 2. Fetch channels in bulk for the identified conversations
        // Assuming GetBulkConversationChannelsAsync is available and handles DB context
        var channelsById = await GetBulkConversationChannelsAsync(idsToFetch);

        // 3. Assign the fetched channels back to the conversations
        foreach (var conversation in conversationsToUpdate)
        {
            conversation.ConversationChannels = channelsById.GetValueOrDefault(conversation.ConversationId, new List<string>());
        }

        channelsStopwatch.Stop();
        _logger.LogDebug(
            "[PERFORMANCE] PopulateConversationChannelsIfNeededAsync executed in {ElapsedMilliseconds} ms, populated channels for {PopulatedCount} conversations.",
            channelsStopwatch.ElapsedMilliseconds,
            updateCount);
    }

    private async Task<Dictionary<string, List<string>>> GetBulkConversationChannelsAsync(
        List<string> conversationIds,
        CancellationToken cancellationToken = default)
    {
        // Handle empty input list gracefully
        if (conversationIds == null || !conversationIds.Any())
        {
            return new Dictionary<string, List<string>>();
        }

        var dbContext = _dbContextService.GetDbContext();

        // Fetch all relevant ConversationId-Channel pairs in one query
        var conversationChannelPairs = await dbContext.ConversationMessages
            .Where(x => conversationIds.Contains(x.ConversationId)) // Filter by the input conversation IDs
            .Select(x => new { x.ConversationId, x.Channel }) // Select only needed fields
            .Distinct() // Get distinct pairs (avoids processing duplicates later)
            .ToListAsync(cancellationToken); // Fetch the data

        // Group the results by ConversationId in memory
        var channelsByConversationId = conversationChannelPairs
            .GroupBy(pair => pair.ConversationId) // Group by the conversation ID
            .ToDictionary(
                group => group.Key, // Key is the ConversationId
                group => group
                    .Select(pair => pair.Channel) // Select the channel names for this group
                    .Where(channel => channel != ChannelTypes.Note) // Exclude the 'Note' channel type
                    .Distinct() // Ensure channel names within the group are distinct (redundant if DB Distinct worked perfectly, but safe)
                    .ToList() // Convert to a List<string>
            );

        // Ensure all requested conversationIds have an entry in the dictionary, even if they had no messages/channels
        foreach (var requestedId in conversationIds)
        {
            if (!channelsByConversationId.ContainsKey(requestedId))
            {
                channelsByConversationId.Add(requestedId, new List<string>());
            }
        }


        return channelsByConversationId;
    }

    #endregion
}
