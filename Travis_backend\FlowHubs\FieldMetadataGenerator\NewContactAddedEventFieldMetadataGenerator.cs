﻿#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Database.Services;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Models;
using Travis_backend.FlowHubs.Services;
using Travis_backend.Utils;
using Newtonsoft.Json.Linq;

namespace Travis_backend.FlowHubs.FieldMetadataGenerator;

public sealed class NewContactAddedEventFieldMetadataGenerator :
    FlowHubEventFieldMetadataGeneratorBase,
    IFlowHubEventFieldMetadataGenerator
{
    public string EventName => TriggerIds.NewContactAdded;

    public Dictionary<string, string> FieldPathOptionRequestPathDict { get; } = new()
    {
        { "contact.labels[*].LabelValue", "FlowHub/SelectOptions/Labels" },
        { "lists[*].id", "FlowHub/SelectOptions/ContactLists" },
        { "contact.ContactOwner", "FlowHub/SelectOptions/Staffs" },
        { "contactOwner.id", "FlowHub/SelectOptions/Staffs" },
        { "contact.LastChannel", "FlowHub/SelectOptions/ChannelTypes" }
    };

    public NewContactAddedEventFieldMetadataGenerator(
        IDbContextService dbContextService,
        IFieldMetadataVariableVisibilityService fieldMetadataVariableVisibilityService)
        : base(dbContextService, fieldMetadataVariableVisibilityService)
    {
    }

    public async Task<(HashSet<FieldMetadata> FieldMetadataSet, Type EventBodyType, string ContactObjectName)> GenerateAsync(
        string companyId,
        Dictionary<string, object?>? parameters)
    {
        var (userProfileDict, customFieldOptionsRequestPathDict) = await GenerateUserProfileDictAsync(companyId);
        var eventBody = JsonUtils.GenerateSampleObjectFromType<OnContactCreatedEventBody>();

        eventBody.Contact = userProfileDict;
        eventBody.CreatedContact = null!;

        var eventBodyJsonString = eventBody.ToJson();
        var eventBodyJson = JObject.Parse(eventBodyJsonString);
        AddContactExpansionToEventBodyJson(eventBodyJson);

        var fieldMetadataSet = JsonUtils
            .SimplifyJsonData(eventBodyJson.ToString())
            .GetFieldMetadata();

        // Get all available field paths
        var allFieldPaths = fieldMetadataSet.Select(f => f.FieldPath);

        // Get visible field paths including contact custom fields
        var visibleFieldPaths = _fieldMetadataVariableVisibilityService
            .GetVisibleFieldPathsWithDynamicSupport(EventName, allFieldPaths);
        var uiCopyMapping = _fieldMetadataVariableVisibilityService.GetFieldUiCopyMapping(EventName);

        // Filter to only visible fields
        var filteredFieldMetadataSet = fieldMetadataSet
            .Where(field => visibleFieldPaths.Contains(field.FieldPath))
            .ToHashSet();

        foreach (var metadata in filteredFieldMetadataSet)
        {
            if (uiCopyMapping.TryGetValue(metadata.FieldPath, out var uiCopy))
            {
                metadata.UiCopy = uiCopy;
            }
        }

        PopulateOptionRequestPath(
            filteredFieldMetadataSet,
            customFieldOptionsRequestPathDict,
            FieldPathOptionRequestPathDict);

        return (filteredFieldMetadataSet, typeof(OnContactCreatedEventBody), nameof(eventBody.Contact));
    }
}