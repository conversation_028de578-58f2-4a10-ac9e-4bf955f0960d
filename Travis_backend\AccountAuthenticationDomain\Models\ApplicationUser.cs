﻿using System;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace Travis_backend.AccountAuthenticationDomain.Models
{
    [Index(nameof(CompanyId))]
    public class ApplicationUser : IdentityUser
    {
        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string DisplayName { get; set; }

        public long? FacebookId { get; set; }

        public string PictureUrl { get; set; }

        public string UserRole { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public DateTime LastLoginAt { get; set; } = DateTime.UtcNow;

        public bool? IsAgreeMarketingConsent { get; set; }

        [MaxLength(256)]
        public string? InviteToken { get; set; }

        public DateTime? InviteTokenExpireAt { get; set; }

        [MaxLength(50)]
        public string DefaultWebAppVersion { get; set; }

        #region ForMultipleCompany

        [MaxLength(450)]
        public string CompanyId { get; set; }

        #endregion
    }
}