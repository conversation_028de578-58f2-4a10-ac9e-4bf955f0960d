﻿using System.Net.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Apis.TenantHub.Api;
using Travis_backend.Constants;
using Travis_backend.TenantHubDomain.HttpClientHandlers;
using Travis_backend.TenantHubDomain.Services;

namespace Travis_backend.TenantHubDomain;

public static class TenantHubExtensions
{
    public static IServiceCollection RegisterTenantHubServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var tenantHubConfig = new Sleekflow.Apis.TenantHub.Client.Configuration
        {
            BasePath = configuration.GetValue<string>("TenantHub:Endpoint")
        };

        services.AddSingleton(tenantHubConfig);

        services.AddTransient<TenantHubHttpClientHandler>();
        services.AddHttpClient(
            HttpClientHandlerName.TenantHub,
            (sp, client) =>
            {
                var config = sp.GetRequiredService<IConfiguration>();
                client.DefaultRequestHeaders.TryAddWithoutValidation(
                    "X-Sleekflow-Key",
                    config.GetValue<string>("TenantHub:Key"));
            });

        services.AddScoped<IManagementCompaniesApi, ManagementCompaniesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddScoped<IManagementEnabledFeaturesApi, ManagementEnabledFeaturesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddScoped<IManagementFeaturesApi, ManagementFeaturesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddScoped<IManagementRolesApi, ManagementRolesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddScoped<IManagementUsersApi, ManagementUsersApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddTransient<ICompaniesApi, CompaniesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddTransient<IEnabledFeaturesApi, EnabledFeaturesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddTransient<IFeaturesApi, FeaturesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddTransient<IRolesApi, RolesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddTransient<IUsersApi, UsersApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));
        services.AddScoped<IManagementIpWhitelistsApi, ManagementIpWhitelistsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddScoped<IAuthorizedIpWhitelistsApi, AuthorizedIpWhitelistsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddScoped<IManagementRbacApi, ManagementRbacApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddScoped<IRbacService, RbacService>();
        services.AddScoped<IFeaturesService, FeaturesService>();
        services.AddScoped<IEnabledFeaturesService, EnabledFeaturesService>();
        services.AddScoped<ICompaniesService, CompaniesService>();
        services.AddScoped<IManagementImportUserApi, ManagementImportUserApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddScoped<IWebhooksApi, WebhooksApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.TenantHub),
                    sp.GetRequiredService<Sleekflow.Apis.TenantHub.Client.Configuration>(),
                    sp.GetRequiredService<TenantHubHttpClientHandler>()));

        services.AddScoped<IIpWhitelistSettingsProvider, IpWhitelistSettingsProvider>();

        return services;
    }
}