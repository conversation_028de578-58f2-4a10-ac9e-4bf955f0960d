namespace Travis_backend.Cache.Models.CacheKeyPatterns;

public class LiveChatSenderGeoLocationCacheKeyPattern : ICacheKeyPattern
{
    public string IpAddress { get; set; }

    public LiveChatSenderGeoLocationCacheKeyPattern(string ipAddress)
    {
        IpAddress = ipAddress;
    }

    public string GenerateKeyPattern()
    {
        var keyName = CacheHelper.BuildKeyNameFromPattern(
            new object[]
            {
                IpAddress
            });

        return keyName;
    }
}