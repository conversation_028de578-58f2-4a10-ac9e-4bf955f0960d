using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.InternalDomain.Models;

namespace Sleekflow.Core.Tests.IntegrationTests;

/// <summary>
/// End-to-end workflow integration tests that validate the complete MRR calculation
/// flow from API request to final result, ensuring all components work together
/// seamlessly with timezone-aware functionality.
/// </summary>
[TestFixture]
public class EndToEndWorkflowTests
{
    private Mock<ILogger<TimezoneAwareMrrCalculationService>> _mockLogger = null!;
    private TimezoneAwareMrrCalculationService _timezoneService = null!;
    private BillRecordRevenueCalculatorService _billRecordRevenueCalculatorService = null!;
    private Mock<ILogger<BillRecordRevenueCalculatorService>> _mockBillRecordRevenueCalculatorServiceLogger = null!;

    [SetUp]
    public void Setup()
    {
        _mockLogger = new Mock<ILogger<TimezoneAwareMrrCalculationService>>();
        _timezoneService = new TimezoneAwareMrrCalculationService(
            _mockLogger.Object);
        _mockBillRecordRevenueCalculatorServiceLogger = new Mock<ILogger<BillRecordRevenueCalculatorService>>();
        _billRecordRevenueCalculatorService = new BillRecordRevenueCalculatorService(
            _mockBillRecordRevenueCalculatorServiceLogger.Object,
            _timezoneService);
    }

    #region Complete Workflow Tests

    [Test]
    public void CompleteWorkflow_NewSubscription_ShouldCalculateOriginalMrr()
    {
        // Arrange - Simulate new subscription workflow
        var companyId = "new-subscription-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "Asia/Singapore"
        };

        var billRecord = new BillRecord
        {
            Id = 1,
            CompanyId = companyId,
            PayAmount = 2500, // $2500 subscription
            currency = "usd",
            SubscriptionPlanId = "sleekflow_v10_startup",
            created = new DateTime(2023, 6, 15, 0, 0, 0, DateTimeKind.Utc),
            PeriodStart = new DateTime(2023, 6, 15, 16, 0, 0, DateTimeKind.Utc), // Next day in Singapore
            PeriodEnd = new DateTime(2023, 7, 15, 16, 0, 0, DateTimeKind.Utc),
            Status = Travis_backend.Enums.BillStatus.Active,
            PaymentStatus = Travis_backend.Enums.PaymentStatus.Paid,
            CmsSalesPaymentRecords =
            [
                new CmsSalesPaymentRecord
                {
                    Id = 1,
                    SubscriptionFee = 500, // Additional setup fee
                    Currency = "usd"
                }
            ]
        };

        // Act - Execute complete workflow
        var testDateTime = new DateTime(2023, 6, 15, 0, 0, 0, DateTimeKind.Utc);
        var result = ExecuteCompleteWorkflow([billRecord], companyId, company.TimeZoneInfoId, testDateTime);

        // Assert
        result.Should().NotBeNull();
        result.TotalMrr.Should().BeGreaterThan(0);
        result.CalculationMethod.Should().Be("Original");
        result.VariancePercentage.Should().BeLessThan(1.0m);

        // Verify logging occurred
        _mockBillRecordRevenueCalculatorServiceLogger.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Timezone-aware MRR calculation")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    [Test]
    public void CompleteWorkflow_SubscriptionUpgrade_ShouldHandleComplexScenario()
    {
        // Arrange - Simulate subscription upgrade mid-period
        var companyId = "upgrade-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "America/New_York"
        };

        var billRecords = new[]
        {
            // Original subscription (partial month)
            new BillRecord
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 1000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 3, 1, 5, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 3, 1, 5, 0, 0, DateTimeKind.Utc), // EST
                PeriodEnd = new DateTime(2023, 3, 15, 5, 0, 0, DateTimeKind.Utc), // Half month
                Status = Travis_backend.Enums.BillStatus.Active,
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            },
            // Upgraded subscription (partial month)
            new BillRecord
            {
                Id = 2,
                CompanyId = companyId,
                PayAmount = 2000,
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_countrytier1_whatsapp_phone_number_monthly_aed",
                created = new DateTime(2023, 3, 15, 4, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 3, 15, 4, 0, 0, DateTimeKind.Utc), // EDT (after DST)
                PeriodEnd = new DateTime(2023, 4, 1, 4, 0, 0, DateTimeKind.Utc),
                Status = Travis_backend.Enums.BillStatus.Active,
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 3, 15, 4, 0, 0, DateTimeKind.Utc);
        var result = ExecuteCompleteWorkflow(billRecords, companyId, company.TimeZoneInfoId, testDateTime);

        // Assert
        result.Should().NotBeNull();
        result.TotalMrr.Should().BeGreaterThan(0);
        result.BillRecordsProcessed.Should().Be(2);
        result.CalculationMethod.Should().Be("TimezoneAware");

        // Should handle DST transition properly
        result.DstTransitionHandled.Should().BeTrue();
    }

    [Test]
    public void CompleteWorkflow_MultiCurrencyCompany_ShouldConvertAndCalculate()
    {
        // Arrange - Multi-currency scenario
        var companyId = "multi-currency-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "Asia/Hong_Kong"
        };

        var billRecords = new[]
        {
            new BillRecord
            {
                Id = 1,
                CompanyId = companyId,
                PayAmount = 7800, // HKD
                currency = "hkd",
                SubscriptionPlanId = "sleekflow_v10_startup",
                created = new DateTime(2023, 5, 1, 16, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 5, 1, 16, 0, 0, DateTimeKind.Utc),
                PeriodEnd = new DateTime(2023, 6, 1, 16, 0, 0, DateTimeKind.Utc),
                Status = Travis_backend.Enums.BillStatus.Active,
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            },
            new BillRecord
            {
                Id = 2,
                CompanyId = companyId,
                PayAmount = 1200, // USD
                currency = "usd",
                SubscriptionPlanId = "sleekflow_v10_countrytier1_whatsapp_phone_number_monthly_aed",
                created = new DateTime(2023, 5, 1, 16, 0, 0, DateTimeKind.Utc),
                PeriodStart = new DateTime(2023, 5, 1, 16, 0, 0, DateTimeKind.Utc),
                PeriodEnd = new DateTime(2023, 6, 1, 16, 0, 0, DateTimeKind.Utc),
                Status = Travis_backend.Enums.BillStatus.Active,
                CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
            }
        };

        // Act
        var testDateTime = new DateTime(2023, 5, 1, 16, 0, 0, DateTimeKind.Utc);
        var result = ExecuteCompleteWorkflow(billRecords, companyId, company.TimeZoneInfoId, testDateTime);

        // Assert
        result.Should().NotBeNull();
        result.TotalMrr.Should().BeGreaterThan(0);
        result.BillRecordsProcessed.Should().Be(2);
        result.CurrenciesProcessed.Should().Contain("HKD");
        result.CurrenciesProcessed.Should().Contain("USD");
    }

    #endregion

    #region Error Recovery Workflow Tests

    [Test]
    public void CompleteWorkflow_DatabaseUnavailable_ShouldFallbackGracefully()
    {
        // Arrange
        var companyId = "db-error-company";

        var billRecord = new BillRecord
        {
            Id = 1,
            CompanyId = companyId,
            PayAmount = 1500,
            currency = "usd",
            SubscriptionPlanId = "sleekflow_v10_startup",
            created = new DateTime(2023, 4, 1, 0, 0, 0, DateTimeKind.Utc),
            PeriodStart = new DateTime(2023, 4, 1, 0, 0, 0, DateTimeKind.Utc),
            PeriodEnd = new DateTime(2023, 5, 1, 0, 0, 0, DateTimeKind.Utc),
            Status = Travis_backend.Enums.BillStatus.Active,
            CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
        };

        // Act
        var testDateTime = new DateTime(2023, 4, 1, 0, 0, 0, DateTimeKind.Utc);
        var result = ExecuteCompleteWorkflow([billRecord], companyId, string.Empty, testDateTime);

        // Assert
        result.Should().NotBeNull();
        result.TotalMrr.Should().BeGreaterThan(0);
        result.CalculationMethod.Should().Be("Fallback");
        result.ErrorsEncountered.Should().BeGreaterThan(0);
    }

    [Test]
    public void CompleteWorkflow_InvalidTimezoneData_ShouldUseUtcFallback()
    {
        // Arrange
        var companyId = "invalid-timezone-company";
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = "Invalid/Timezone/Data"
        };

        var billRecord = new BillRecord
        {
            Id = 1,
            CompanyId = companyId,
            PayAmount = 800,
            currency = "usd",
            SubscriptionPlanId = "sleekflow_v10_startup",
            created = new DateTime(2023, 7, 1, 0, 0, 0, DateTimeKind.Utc),
            PeriodStart = new DateTime(2023, 7, 1, 0, 0, 0, DateTimeKind.Utc),
            PeriodEnd = new DateTime(2023, 8, 1, 0, 0, 0, DateTimeKind.Utc),
            Status = Travis_backend.Enums.BillStatus.Active,
            CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
        };

        // Act
        var testDateTime = new DateTime(2023, 7, 1, 0, 0, 0, DateTimeKind.Utc);
        var result = ExecuteCompleteWorkflow([billRecord], companyId, company.TimeZoneInfoId, testDateTime);

        // Assert
        result.Should().NotBeNull();
        result.TotalMrr.Should().Be(800); // Should use UTC fallback (exact month)
        result.TimezoneUsed.Should().Be("UTC");
    }

    #endregion

    #region High-Volume Workflow Tests

    [Test]
    public void CompleteWorkflow_HighVolumeProcessing_ShouldMaintainPerformance()
    {
        // Arrange - High volume scenario
        var companies = GenerateTestCompanies(10);
        var billRecords = GenerateTestBillRecords(companies, 50); // 500 total records

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var results = new List<WorkflowResult>();

        foreach (var companyGroup in billRecords.GroupBy(br => br.CompanyId))
        {
            var company = companies.First(c => c.Id == companyGroup.Key);
            var testDateTime = companyGroup.First().PeriodStart;
            var result = ExecuteCompleteWorkflow(
                companyGroup.ToArray(),
                companyGroup.Key,
                company.TimeZoneInfoId,
                testDateTime);
            results.Add(result);
        }

        stopwatch.Stop();

        // Assert
        results.Should().HaveCount(10);
        results.All(r => r.TotalMrr > 0).Should().BeTrue();
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(60000); // Complete within 60 seconds

        var totalRecordsProcessed = results.Sum(r => r.BillRecordsProcessed);
        totalRecordsProcessed.Should().Be(500);

        Console.WriteLine(
            $"Processed {totalRecordsProcessed} records across {results.Count} companies in {stopwatch.ElapsedMilliseconds}ms");
    }

    #endregion

    #region Workflow Execution Helpers

    private WorkflowResult ExecuteCompleteWorkflow(
        BillRecord[] billRecords,
        string companyId,
        string timezone,
        DateTime testDateTime)
    {
        var result = new WorkflowResult
        {
            CompanyId = companyId,
            BillRecordsProcessed = billRecords.Length,
            CurrenciesProcessed = billRecords.Select(br => br.currency.ToUpper()).Distinct().ToList()
        };

        try
        {
            // Step 1: Calculate MRR using enhanced calculation
            var totalMrr = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                billRecords.ToList(),
                timezone,
                testDateTime);
            result.TotalMrr = totalMrr;

            // Step 2: Calculate original MRR for variance comparison
            var modifiedBillRecords = billRecords.Select(br => new BillRecord
            {
                Id = br.Id,
                PayAmount = br.PayAmount, // Keep same amount to avoid artificial variance
                currency = br.currency,
                PeriodStart = br.PeriodStart, // Keep same dates to avoid variance
                PeriodEnd = br.PeriodEnd, // Keep same dates to avoid variance
                Status = br.Status,
                SubscriptionPlanId = br.SubscriptionPlanId,
                CmsSalesPaymentRecords = br.CmsSalesPaymentRecords // Include payment records to avoid variance
            }).ToList();

            var originalMrr =
                _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                    modifiedBillRecords,
                    timezone,
                    testDateTime);

            // Step 3: Calculate variance
            var variance = Math.Abs(totalMrr - originalMrr);
            result.VariancePercentage = originalMrr != 0 ? (variance / originalMrr) * 100 : 0;

            // Step 4: Determine calculation method used
            result.CalculationMethod = totalMrr != originalMrr ? "TimezoneAware" : "Original";

            // Step 5: Check for DST transitions
            result.DstTransitionHandled = billRecords.Any(br =>
                IsDstTransitionPeriod(br.PeriodStart, br.PeriodEnd));

            try
            {
                TimeZoneInfo.FindSystemTimeZoneById(timezone);
                result.TimezoneUsed = timezone;
            }
            catch (TimeZoneNotFoundException)
            {
                result.TimezoneUsed = "UTC";
                result.ErrorsEncountered++;
                result.CalculationMethod = "Fallback";
            }
        }
        catch (Exception ex)
        {
            result.ErrorsEncountered++;
            result.CalculationMethod = "Fallback";
            result.LastError = ex.Message;
        }

        return result;
    }

    private List<Company> GenerateTestCompanies(int count)
    {
        var timezones = new[]
        {
            "UTC",
            "Asia/Singapore",
            "Europe/London",
            "America/New_York",
            "Australia/Sydney",
            "Asia/Tokyo",
            "Europe/Berlin",
            "America/Los_Angeles",
            "Asia/Hong_Kong",
            "Europe/Paris"
        };

        return Enumerable.Range(1, count)
            .Select(i => new Company
            {
                Id = $"test-company-{i}", TimeZoneInfoId = timezones[i % timezones.Length]
            })
            .ToList();
    }

    private List<BillRecord> GenerateTestBillRecords(List<Company> companies, int recordsPerCompany)
    {
        var billRecords = new List<BillRecord>();
        var random = new Random(12345);

        foreach (var company in companies)
        {
            for (int i = 0; i < recordsPerCompany; i++)
            {
                var startDate = new DateTime(2023, 1, 1).AddDays(random.Next(0, 365));
                var endDate = startDate.AddDays(random.Next(28, 35));

                billRecords.Add(
                    new BillRecord
                    {
                        Id = billRecords.Count + 1,
                        CompanyId = company.Id,
                        PayAmount = random.Next(100, 5000),
                        currency = "usd",
                        SubscriptionPlanId = "sleekflow_v10_countrytier1_whatsapp_phone_number_monthly_aed",
                        created = new DateTime(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                        PeriodStart = startDate,
                        PeriodEnd = endDate,
                        Status = Travis_backend.Enums.BillStatus.Active,
                        CmsSalesPaymentRecords = new List<CmsSalesPaymentRecord>()
                    });
            }
        }

        return billRecords;
    }

    private bool IsDstTransitionPeriod(DateTime start, DateTime end)
    {
        // Check if period overlaps with typical DST transition months
        return (start.Month == 3 || start.Month == 11 || end.Month == 3 || end.Month == 11);
    }

    #endregion

    #region Result Classes

    private class WorkflowResult
    {
        public string CompanyId { get; set; } = string.Empty;
        public decimal TotalMrr { get; set; }
        public int BillRecordsProcessed { get; set; }
        public List<string> CurrenciesProcessed { get; set; } = new ();
        public decimal VariancePercentage { get; set; }
        public string CalculationMethod { get; set; } = string.Empty;
        public bool DstTransitionHandled { get; set; }
        public string TimezoneUsed { get; set; } = string.Empty;
        public int ErrorsEncountered { get; set; }
        public string LastError { get; set; } = string.Empty;
    }

    #endregion
}