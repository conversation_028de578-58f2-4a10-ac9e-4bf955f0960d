﻿#nullable enable
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.BackgroundTaskServices;
using Travis_backend.BackgroundTaskServices.Helpers;
using Travis_backend.BroadcastDomain.Constants;
using Travis_backend.BroadcastDomain.Models;
using Travis_backend.BroadcastDomain.Services;
using Travis_backend.BroadcastDomain.ViewModels;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Data.BackgroundTask;
using Travis_backend.Database;
using Travis_backend.**********************;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FileDomain.Services;
using Travis_backend.FlowHubs;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.Repositories;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.Models.BackgroundTask;
using Travis_backend.TenantHubDomain.Services;

namespace Travis_backend.Controllers.MessageControllers
{
    [Authorize]
    [Route("Broadcast")]
    public class BroadcastMessageController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IUploadService _uploadService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly IUserProfileService _userProfileService;
        private readonly IBroadcastService _broadcastService;
        private readonly IBroadcastChannelService _broadcastChannelService;
        private readonly IBackgroundTaskService _backgroundTaskService;
        private readonly ICompanyUsageService _companyUsageService;
        private readonly ILockService _lockService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly ICoreService _coreService;
        private readonly ICompanyTeamService _companyTeamService;
        private readonly IChannelIdentityIdRepository _channelIdentityIdRepository;
        private readonly IBroadcastHooks _broadcastHooks;
        private readonly IServiceProvider _serviceProvider;

        public BroadcastMessageController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            IMapper mapper,
            ILogger<BroadcastMessageController> logger,
            IUploadService uploadService,
            IAzureBlobStorageService azureBlobStorageService,
            IUserProfileService userProfileService,
            IBroadcastService broadcastService,
            IBroadcastChannelService broadcastChannelService,
            IBackgroundTaskService backgroundTaskService,
            ICompanyUsageService companyUsageService,
            ILockService lockService,
            ICacheManagerService cacheManagerService,
            ICoreService coreService,
            ICompanyTeamService companyTeamService,
            IChannelIdentityIdRepository channelIdentityIdRepository,
            IBroadcastHooks broadcastHooks,
            IDistributedInvocationContextService distributedInvocationContextService,
            IServiceProvider serviceProvider)
        {
            _userManager = userManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _logger = logger;
            _uploadService = uploadService;
            _azureBlobStorageService = azureBlobStorageService;
            _userProfileService = userProfileService;
            _broadcastService = broadcastService;
            _broadcastChannelService = broadcastChannelService;
            _backgroundTaskService = backgroundTaskService;
            _companyUsageService = companyUsageService;
            _lockService = lockService;
            _cacheManagerService = cacheManagerService;
            _coreService = coreService;
            _companyTeamService = companyTeamService;
            _channelIdentityIdRepository = channelIdentityIdRepository;
            _broadcastHooks = broadcastHooks;
            _serviceProvider = serviceProvider;
        }

        [HttpPost]
        [Route("")]
        public async Task<ActionResult<CompanyMessageTemplateResponse>> CreateTemplate(
            [FromBody] [FromForm]
            NewTemplateViewModel newTemplateViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "[CreateTemplate] Creating template for Company ID: {CompanyId}, Staff ID: {StaffId}, with Payload {Payload}",
                companyUser.CompanyId,
                companyUser.Id,
                JsonConvert.SerializeObject(newTemplateViewModel));

            var newTemplate = _mapper.Map<CompanyMessageTemplate>(newTemplateViewModel);
            newTemplate.CompanyId = companyUser.CompanyId;
            newTemplate.Status = BroadcastStatus.Draft;
            newTemplate.SavedById = companyUser.Id;
            newTemplate.IsBroadcastOn = true;

            if (newTemplateViewModel.AutomationActions != null)
            {
                foreach (var automation in newTemplateViewModel.AutomationActions)
                {
                    var automationAction = new CampaignAutomationAction()
                    {
                        CompanyId = companyUser.CompanyId,
                        AssignmentType = automation.AssignmentType,
                        AutomatedTriggerType = automation.AutomatedTriggerType,
                        MessageContent = automation.MessageContent,
                        MessageParams = automation.MessageParams,
                        ActionAddConversationHashtags = automation.ActionAddConversationHashtags,
                        ActionAddConversationRemarks = automation.ActionAddConversationRemarks,
                        ActionUpdateCustomFields = automation.ActionUpdateCustomFields,
                        ActionAddedToGroupIds = automation.ActionAddedToGroupIds,
                        ActionRemoveFromGroupIds = automation.ActionRemoveFromGroupIds,
                        ActionWait = automation.ActionWait,
                        ActionWaitDays = automation.ActionWaitDays,
                        Order = automation.Order,
                        TeamAssignmentType = automation.TeamAssignmentType,
                        ChangeConversationStatus = automation.ChangeConversationStatus,
                        TargetedChannelWithIds = automation.TargetedChannelWithIds,
                        AddAdditionalAssigneeIds = automation.AddAdditionalAssigneeIds,
                        WebhookURL = automation.WebhookURL
                    };

                    if (!string.IsNullOrWhiteSpace(automation.StaffId))
                    {
                        automationAction.AssignedStaff = await _appDbContext.UserRoleStaffs
                            .FirstOrDefaultAsync(
                                staff => staff.CompanyId == companyUser.CompanyId &&
                                         staff.IdentityId == automation.StaffId);
                    }

                    if (automation.TeamId.HasValue)
                    {
                        automationAction.AssignedTeam = await _appDbContext.CompanyStaffTeams
                            .Where(
                                team =>
                                    team.Id == automation.TeamId.Value &&
                                    team.CompanyId == companyUser.CompanyId)
                            .FirstOrDefaultAsync();
                    }

                    automationAction.AssignedStaffId = automationAction.AssignedStaff?.Id;
                    automationAction.AssignedTeamId = automationAction.AssignedTeam?.Id;

                    newTemplate.CampaignAutomationActions.Add(automationAction);
                }
            }

            if (newTemplate.TargetedChannelWithIds is { Count: > 0 }
                && newTemplateViewModel.TargetedChannel is null)
            {
                await _broadcastChannelService.MapChannelIdentityIdAsync(newTemplate);
            }

            await _appDbContext.CompanyMessageTemplates.AddAsync(newTemplate);
            await _appDbContext.SaveChangesAsync();

            var messageTemplateResponse = _mapper.Map<CompanyMessageTemplateResponse>(newTemplate);

            _logger.LogInformation(
                "[CreateTemplate] Successfully created template. Template ID: {TemplateId}, Company ID: {CompanyId}",
                newTemplate.Id,
                companyUser.CompanyId);

            return Ok(messageTemplateResponse);
        }

        [HttpGet]
        [Route("channel")]
        public async Task<IActionResult> GetBroadcastableChannel()
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == senderId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var result = new List<BroadcatableChannel>();

            result.Add(
                new BroadcatableChannel
                {
                    channel = ChannelTypes.Sms,
                    IsBroadcastable = true
                });

            result.Add(
                new BroadcatableChannel
                {
                    channel = ChannelTypes.WhatsappTwilio,
                    IsBroadcastable = true
                });

            result.Add(
                new BroadcatableChannel
                {
                    channel = ChannelTypes.Line,
                    IsBroadcastable = true
                });

            result.Add(
                new BroadcatableChannel
                {
                    channel = ChannelTypes.Wechat,
                    IsBroadcastable = false
                });

            result.Add(
                new BroadcatableChannel
                {
                    channel = ChannelTypes.Facebook,
                    IsBroadcastable = true
                });

            result.Add(
                new BroadcatableChannel
                {
                    channel = ChannelTypes.LiveChat,
                    IsBroadcastable = false
                });

            result.Add(
                new BroadcatableChannel
                {
                    channel = "twilio_whatsapp",
                    IsBroadcastable = false
                });

            return Ok(result);
        }

        [HttpPost]
        [Route("Attachment/{messageTemplateId}")]
        public async Task<ActionResult<CompanyMessageTemplateResponse>> AddAttachmentTemplate(
            string messageTemplateId,
            [FromForm]
            CampaignAttachmentViewModel attachmentViewModel)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == senderId).Include(x => x.Company.StorageConfig).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (string.IsNullOrEmpty(companyUser.CompanyId))
                {
                    return Unauthorized();
                }

                companyUser.Company.StorageConfig =
                    await _appDbContext.ConfigStorageConfigs.FirstOrDefaultAsync(
                        x => x.CompanyId == companyUser.CompanyId);

                var messageTemplate = await _appDbContext.CompanyMessageTemplates
                    .Where(
                        x =>
                            x.Id == messageTemplateId
                            && x.CompanyId == companyUser.CompanyId)
                    .Include(x => x.UploadedFiles)
                    .Include(x => x.CampaignChannelMessages)
                    .ThenInclude(x => x.UploadedFiles)
                    .Include(x => x.SavedBy)
                    .FirstOrDefaultAsync();

                foreach (IFormFile file in attachmentViewModel.files)
                {
                    var fileName = BlobUploadPathNameBuilder.GetBroadcastCampaignUploadedFilePath(
                        messageTemplateId,
                        file.FileName);

                    var uploadFileResult = await _uploadService.UploadFile(
                        companyUser.Company.StorageConfig.ContainerName,
                        fileName,
                        file);

                    if (uploadFileResult.Url == null)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = "upload failure"
                            });
                    }

                    var newUploadedFile = new CampaignUploadedFile
                    {
                        Filename = fileName,
                        BlobContainer = companyUser.Company.StorageConfig.ContainerName,
                        Url = uploadFileResult.Url,
                        MIMEType = file.ContentType
                    };

                    if (attachmentViewModel.ChannelMessageId.HasValue)
                    {
                        var channelMessage =
                            messageTemplate.CampaignChannelMessages.FirstOrDefault(
                                x => x.Id == attachmentViewModel.ChannelMessageId);

                        if (channelMessage != null)
                        {
                            channelMessage.UploadedFiles.Add(newUploadedFile);

                            continue;
                        }
                    }

                    messageTemplate.UploadedFiles ??= new List<CampaignUploadedFile>();
                    messageTemplate.UploadedFiles.Add(newUploadedFile);
                }

                messageTemplate.SavedById = companyUser.Id;
                await _appDbContext.SaveChangesAsync();

                var messageTemplateResponse = _mapper.Map<CompanyMessageTemplateResponse>(messageTemplate);

                return Ok(messageTemplateResponse);
            }

            return BadRequest();
        }

        [HttpPost]
        [Route("action/{messageTemplateId}/{broadcastAction}")]
        public async Task<ActionResult<CompanyMessageTemplateResponse>> UpdateTemplate(
            string messageTemplateId,
            string broadcastAction)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var messageTemplate = await _appDbContext.CompanyMessageTemplates
                .Where(x => x.Id == messageTemplateId && x.CompanyId == companyUser.CompanyId)
                .Include(x => x.SavedBy.Identity).FirstOrDefaultAsync();

            _logger.LogInformation(
                "[UpdateTemplate] Updating broadcast {BroadcastId} for Company ID: {CompanyId}, Staff ID: {StaffId}, with Action {Action}",
                messageTemplateId,
                companyUser.CompanyId,
                companyUser.Id,
                broadcastAction);

            switch (broadcastAction.ToLower())
            {
                case "pause":
                    messageTemplate.IsBroadcastOn = false;
                    messageTemplate.Status = BroadcastStatus.Paused;
                    await _appDbContext.SaveChangesAsync();

                    break;
                case "resume":
                    messageTemplate.IsBroadcastOn = true;
                    await _appDbContext.SaveChangesAsync();

                    await BroadcastWithTemplate(
                        new BroadcastViewModel
                        {
                            TempleteId = messageTemplate.Id
                        });

                    break;
            }

            var messageTemplateResponse = _mapper.Map<CompanyMessageTemplateResponse>(messageTemplate);

            messageTemplateResponse.BroadcastHistoryCount =
                await _appDbContext.BroadcastCompaignHistories.CountAsync(
                    x => x.BroadcastCampaignId == messageTemplateId);

            messageTemplateResponse.BroadcastDeliveredCount = await _appDbContext.BroadcastCompaignHistories.CountAsync(
                x => x.BroadcastCampaignId == messageTemplateId && x.ConversationMessages.Any(
                    y => y.Status == MessageStatus.Received || y.Status == MessageStatus.Read));

            return Ok(messageTemplateResponse);
        }

        [HttpPost]
        [Route("{messageTemplateId}")]
        public async Task<ActionResult<CompanyMessageTemplateResponse>> UpdateTemplate(
            string messageTemplateId,
            [FromBody] [FromForm]
            NewTemplateViewModel newTemplateViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "[UpdateTemplate] Updating broadcast {BroadcastId} for Company ID: {CompanyId}, Staff ID: {StaffId}, with Payload {Payload}",
                messageTemplateId,
                companyUser.CompanyId,
                companyUser.Id,
                JsonConvert.SerializeObject(newTemplateViewModel));

            try
            {
                var messageTemplate = await _appDbContext.CompanyMessageTemplates
                    .Where(x => x.Id == messageTemplateId && x.CompanyId == companyUser.CompanyId)
                    .Include(x => x.CampaignChannelMessages).Include(x => x.SavedBy.Identity)
                    .Include(x => x.CampaignAutomationActions)
                    .FirstOrDefaultAsync();

                if (messageTemplate is null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "No broadcast template found"
                        });
                }

                if (!BroadcastConstants.EditableStatuses.Contains(
                        messageTemplate.Status,
                        StringComparer.OrdinalIgnoreCase))
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Only broadcast in draft or scheduled status can be updated"
                        });
                }

                messageTemplate.TemplateName = newTemplateViewModel.TemplateName.Trim();
                messageTemplate.TemplateContent = newTemplateViewModel.TemplateContent;
                messageTemplate.TemplateParams = newTemplateViewModel.TemplateParams;
                messageTemplate.Conditions = newTemplateViewModel.Conditions;
                messageTemplate.TargetedChannel = newTemplateViewModel.TargetedChannel;
                messageTemplate.TargetedChannels = newTemplateViewModel.TargetedChannels;
                messageTemplate.TargetedChannelWithIds = newTemplateViewModel.TargetedChannelWithIds;
                messageTemplate.ScheduledAt = newTemplateViewModel.ScheduledAt;
                messageTemplate.UpdatedAt = DateTime.UtcNow;
                messageTemplate.StripePaymentRequestOption = newTemplateViewModel.StripePaymentRequestOption;
                messageTemplate.Status = BroadcastStatus.Draft;

                if (!string.IsNullOrEmpty(messageTemplate.JobId))
                {
                    BackgroundJob.Delete(messageTemplate.JobId);
                    messageTemplate.JobId = null;
                }

                if (newTemplateViewModel.BroadcastAsNote.HasValue)
                {
                    messageTemplate.BroadcastAsNote = newTemplateViewModel.BroadcastAsNote.Value;
                }

                if (newTemplateViewModel.IsBroadcastOn.HasValue)
                {
                    messageTemplate.IsBroadcastOn = newTemplateViewModel.IsBroadcastOn.Value;
                }

                messageTemplate.SavedById = companyUser.Id;

                foreach (var campaignChannelMessage in newTemplateViewModel.CampaignChannelMessages)
                {
                    if (campaignChannelMessage.Id.HasValue) // update if id exist
                    {
                        var existingRecord =
                            messageTemplate.CampaignChannelMessages.FirstOrDefault(
                                x => x.Id == campaignChannelMessage.Id);

                        if (existingRecord is null)
                        {
                            continue;
                        }

                        existingRecord.TargetedChannel = campaignChannelMessage.TargetedChannel;
                        existingRecord.TargetedChannels = campaignChannelMessage.TargetedChannels;
                        existingRecord.TemplateContent = campaignChannelMessage.TemplateContent;
                        existingRecord.TemplateParams = campaignChannelMessage.TemplateParams;
                        existingRecord.TemplateName = campaignChannelMessage.TemplateName;
                        existingRecord.OfficialTemplateParams = campaignChannelMessage.OfficialTemplateParams;

                        existingRecord.WhatsApp360DialogExtendedCampaignMessage =
                            campaignChannelMessage.WhatsApp360DialogExtendedCampaignMessage;
                        existingRecord.ExtendedMessageType = campaignChannelMessage.ExtendedMessageType;

                        existingRecord.ExtendedMessagePayloadDetail =
                            campaignChannelMessage.ExtendedMessagePayloadDetail;
                        existingRecord.MessageTag = campaignChannelMessage.MessageTag;
                    }
                    else // add if id is null
                    {
                        messageTemplate.CampaignChannelMessages.Add(
                            _mapper.Map<CampaignChannelMessage>(campaignChannelMessage));
                    }
                }

                var list = messageTemplate.CampaignChannelMessages.ToList();

                foreach (var twilio in list)
                {
                    if (twilio.Id != 0)
                    {
                        if (!newTemplateViewModel.CampaignChannelMessages.Any(x => x.Id == twilio.Id))
                        {
                            messageTemplate.CampaignChannelMessages.Remove(twilio);
                        }
                    }
                }

                if (newTemplateViewModel.AutomationActions != null)
                {
                    var removeList = new List<CampaignAutomationAction>();

                    foreach (var automation in messageTemplate.CampaignAutomationActions)
                    {
                        var automationVM = newTemplateViewModel.AutomationActions
                            .FirstOrDefault(x => x.Id == automation.Id);

                        if (automationVM == null)
                        {
                            removeList.Add(automation);
                        }
                        else
                        {
                            automation.AssignmentType = automationVM.AssignmentType;
                            automation.AutomatedTriggerType = automationVM.AutomatedTriggerType;
                            automation.MessageContent = automationVM.MessageContent;
                            automation.MessageParams = automationVM.MessageParams;
                            automation.ActionAddConversationHashtags = automationVM.ActionAddConversationHashtags;
                            automation.ActionAddConversationRemarks = automationVM.ActionAddConversationRemarks;
                            automation.ActionUpdateCustomFields = automationVM.ActionUpdateCustomFields;
                            automation.ActionAddedToGroupIds = automationVM.ActionAddedToGroupIds;
                            automation.ActionRemoveFromGroupIds = automationVM.ActionRemoveFromGroupIds;
                            automation.ActionWait = automationVM.ActionWait;
                            automation.ActionWaitDays = automationVM.ActionWaitDays;
                            automation.Order = automationVM.Order;
                            automation.TeamAssignmentType = automationVM.TeamAssignmentType;
                            automation.ChangeConversationStatus = automationVM.ChangeConversationStatus;
                            automation.TargetedChannelWithIds = automationVM.TargetedChannelWithIds;
                            automation.AddAdditionalAssigneeIds = automationVM.AddAdditionalAssigneeIds;
                            automation.WebhookURL = automationVM.WebhookURL;

                            if (!string.IsNullOrEmpty(automationVM.StaffId))
                            {
                                automation.AssignedStaff = await _appDbContext.UserRoleStaffs
                                    .Where(
                                        x => x.IdentityId == automationVM.StaffId &&
                                             x.CompanyId == companyUser.CompanyId).Include(x => x.Identity)
                                    .FirstOrDefaultAsync();
                            }
                            else
                            {
                                automation.AssignedStaffId = null;
                            }

                            if (automationVM.TeamId.HasValue)
                            {
                                automation.AssignedTeam = await _appDbContext.CompanyStaffTeams
                                    .Where(
                                        x => x.Id == automationVM.TeamId.Value &&
                                             x.CompanyId == companyUser.CompanyId).FirstOrDefaultAsync();
                            }
                            else
                            {
                                automation.AssignedTeamId = null;
                            }
                        }
                    }

                    foreach (var automation in removeList)
                    {
                        messageTemplate.CampaignAutomationActions.Remove(automation);
                    }

                    var newAutomationRule =
                        newTemplateViewModel.AutomationActions.Where(x => x.Id == null).ToList();

                    foreach (var automation in newAutomationRule)
                    {
                        var automationAction = new CampaignAutomationAction()
                        {
                            CompanyId = companyUser.CompanyId,
                            AssignmentType = automation.AssignmentType,
                            AutomatedTriggerType = automation.AutomatedTriggerType,
                            MessageContent = automation.MessageContent,
                            MessageParams = automation.MessageParams,
                            ActionAddConversationHashtags = automation.ActionAddConversationHashtags,
                            ActionAddConversationRemarks = automation.ActionAddConversationRemarks,
                            ActionUpdateCustomFields = automation.ActionUpdateCustomFields,
                            ActionAddedToGroupIds = automation.ActionAddedToGroupIds,
                            ActionRemoveFromGroupIds = automation.ActionRemoveFromGroupIds,
                            ActionWait = automation.ActionWait,
                            ActionWaitDays = automation.ActionWaitDays,
                            Order = automation.Order,
                            TeamAssignmentType = automation.TeamAssignmentType,
                            ChangeConversationStatus = automation.ChangeConversationStatus,
                            TargetedChannelWithIds = automation.TargetedChannelWithIds,
                            AddAdditionalAssigneeIds = automation.AddAdditionalAssigneeIds,
                            WebhookURL = automation.WebhookURL
                        };

                        if (!string.IsNullOrEmpty(automation.StaffId))
                        {
                            automationAction.AssignedStaff = await _appDbContext.UserRoleStaffs
                                .Where(
                                    x => x.IdentityId == automation.StaffId &&
                                         x.CompanyId == companyUser.CompanyId).Include(x => x.Identity)
                                .FirstOrDefaultAsync();
                        }
                        else
                        {
                            automationAction.AssignedStaffId = null;
                        }

                        if (automation.TeamId.HasValue)
                        {
                            automationAction.AssignedTeam = await _appDbContext.CompanyStaffTeams
                                .Where(x => x.Id == automation.TeamId.Value && x.CompanyId == companyUser.CompanyId)
                                .FirstOrDefaultAsync();
                        }
                        else
                        {
                            automationAction.AssignedTeamId = null;
                        }

                        messageTemplate.CampaignAutomationActions.Add(automationAction);
                    }
                }
                else
                {
                    _appDbContext.CampaignAutomationActions.RemoveRange(messageTemplate.CampaignAutomationActions);
                    messageTemplate.CampaignAutomationActions = null;
                }

                if (newTemplateViewModel.TargetedChannelWithIds is { Count: > 0 }
                    && newTemplateViewModel.TargetedChannel is null)
                {
                    await _broadcastChannelService.MapChannelIdentityIdAsync(messageTemplate);
                }

                await _appDbContext.SaveChangesAsync();

                var messageTemplateResponse = _mapper.Map<CompanyMessageTemplateResponse>(messageTemplate);

                messageTemplateResponse.BroadcastHistoryCount = await _appDbContext.BroadcastCompaignHistories
                    .Where(x => x.BroadcastCampaignId == messageTemplateId).CountAsync();

                messageTemplateResponse.BroadcastDeliveredCount = await _appDbContext.BroadcastCompaignHistories
                    .Where(
                        x => x.BroadcastCampaignId == messageTemplateId && x.ConversationMessages.Any(
                            y => y.Status == MessageStatus.Received || y.Status == MessageStatus.Read)).CountAsync();

                return Ok(messageTemplateResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Update broadcast {BroadcastId} error: {ExceptionMessage}. Payload: {Payload}",
                    nameof(UpdateTemplate),
                    messageTemplateId,
                    ex.Message,
                    JsonConvert.SerializeObject(newTemplateViewModel));
            }

            return BadRequest();
        }

        [HttpPost]
        [Route("AutomationAction/Attachment/{automationActionId}")]
        public async Task<ActionResult<AssignmentUploadedFile>> AddAssignmentRule(
            long automationActionId,
            [FromForm]
            AssignmentAttachmentViewModel attachmentViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (string.IsNullOrEmpty(companyUser.CompanyId))
            {
                return Unauthorized();
            }

            companyUser.Company.StorageConfig =
                await _appDbContext.ConfigStorageConfigs.FirstOrDefaultAsync(x => x.CompanyId == companyUser.CompanyId);

            var automationAction = await _appDbContext.CampaignAutomationActions
                .Where(x => x.Id == automationActionId && x.CompanyId == companyUser.CompanyId)
                .Include(x => x.UploadedFiles).FirstOrDefaultAsync();

            foreach (IFormFile file in attachmentViewModel.files)
            {
                var fileName = $"Automation/{automationActionId}/{DateTime.UtcNow.ToString("o")}/{file.FileName}";

                var uploadFileResult = await _uploadService.UploadFile(
                    companyUser.Company.StorageConfig.ContainerName,
                    fileName,
                    file);

                if (uploadFileResult?.Url == null)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "upload failure"
                        });
                }

                var newuUploadedFile = new CampaignAutomationUploadedFile();
                newuUploadedFile.Filename = fileName;
                newuUploadedFile.BlobContainer = companyUser.Company.StorageConfig.ContainerName;
                newuUploadedFile.Url = uploadFileResult.Url;
                newuUploadedFile.MIMEType = file.ContentType;

                if (automationAction.UploadedFiles == null)
                {
                    automationAction.UploadedFiles = new List<CampaignAutomationUploadedFile>();
                }

                automationAction.UploadedFiles.Add(newuUploadedFile);

                await _appDbContext.SaveChangesAsync();
                var response = _mapper.Map<AssignmentUploadedFile>(newuUploadedFile);

                return Ok(response);
            }

            return BadRequest();
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("AutomationAction/Attachment/Private/{filenameId}/{downloadFilename}")]
        [Route("AutomationAction/Attachment/Private/{filenameId}")]
        public async Task<IActionResult> GetPrivateAutomationAzureBlob(string filenameId)
        {
            try
            {
                var file = await _appDbContext.CampaignAutomationUploadedFiles
                    .Where(x => x.CampaignAutomationUploadedFileId == filenameId).FirstOrDefaultAsync();
                var stream = await _azureBlobStorageService.DownloadFromAzureBlob(file.Filename, file.BlobContainer);

                var extension = Path.GetExtension(file.Filename);

                if (string.IsNullOrEmpty(extension))
                {
                    switch (file.MIMEType)
                    {
                        case "video/mp4":
                            extension = "mp4";

                            break;
                        case "image/jpeg":
                            extension = "jpg";

                            break;
                        case "image/png":
                            extension = "png";

                            break;
                    }
                }

                var filename = Path.GetFileNameWithoutExtension(file.Filename);
                var fileType = string.IsNullOrEmpty(file.MIMEType) ? "application/octet-stream" : file.MIMEType;

                var fileDownloadName =
                    $"{filename}{((string.IsNullOrEmpty(extension) && file.MIMEType == "video/mp4") ? ".mp4" : $".{extension}")}";

                // var data = stream.ToArray();
                // string encodeFilename = HttpUtility.UrlEncode(fileDownloadName, Encoding.UTF8);
                // Response.Headers.Add($"Content-Disposition", $"attachment; filename={Uri.EscapeUriString(fileDownloadName)}; filename*=UTF-8\"{encodeFilename}");
                // return new FileStreamResult(new MemoryStream(data), fileType);
                return File(stream.ToArray(), fileType, fileDownloadName);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"file does not exist."
                    });
            }
        }

        [HttpDelete]
        [Route("AutomationAction/Attachment/{filenameId}")]
        public async Task<ActionResult<ResponseViewModel>> DeleteAutomationActionAzureBlob(string filenameId)
        {
            var file = await _appDbContext.CampaignAutomationUploadedFiles
                .Where(x => x.CampaignAutomationUploadedFileId == filenameId).FirstOrDefaultAsync();
            await _azureBlobStorageService.DeleteFromAzureBlob(file.Filename, file.BlobContainer);
            _appDbContext.CampaignAutomationUploadedFiles.RemoveRange(file);
            await _appDbContext.SaveChangesAsync();

            return Ok(
                new ResponseViewModel
                {
                    message = "success"
                });
        }

        [HttpGet]
        [Route("Total")]
        public async Task<ActionResult<TotalCountResponse>> GetBroadcastCount(
            [FromQuery(Name = "name")]
            string? name = null,
            [FromQuery(Name = "statuses")]
            List<string>? statuses = null,
            [FromQuery(Name = "sentAtStartDate")]
            DateTime? sentAtStartDate = null,
            [FromQuery(Name = "sentAtEndDate")]
            DateTime? sentAtEndDate = null,
            [FromQuery(Name = "updatedAtStartDate")]
            DateTime? updatedAtStartDate = null,
            [FromQuery(Name = "updatedAtEndDate")]
            DateTime? updatedAtEndDate = null,
            [FromQuery(Name = "createdBy")]
            List<string>? createdBy = null,
            [FromQuery(Name = "channelIds")]
            List<string>? channelIds = null,
            [FromQuery(Name = "channelTypes")]
            List<string>? channelTypes = null)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (string.IsNullOrEmpty(companyUser.CompanyId))
                {
                    return Unauthorized();
                }

                // Check Default Channel
                var defaultChannelBroadcastIds = await GetDefaultChannelBroadcastIds(companyUser);

                if (defaultChannelBroadcastIds != null)
                {
                    return Ok(
                        new TotalCountResponse
                        {
                            Count = defaultChannelBroadcastIds.Count
                        });
                }

                var broadcastCount = await _appDbContext.CompanyMessageTemplates
                    .AsNoTracking()
                    .Where(x => x.CompanyId == companyUser.CompanyId)
                    .WhereIf(
                        !string.IsNullOrEmpty(name),
                        broadcastTemplate => broadcastTemplate.TemplateName.Contains(name!))
                    .WhereIf(
                        statuses is { Count: > 0 },
                        broadcastTemplate => statuses!.Contains(broadcastTemplate.Status))
                    .WhereIf(
                        sentAtStartDate.HasValue && sentAtEndDate.HasValue,
                        broadcastTemplate => broadcastTemplate.SentAt >= sentAtStartDate &&
                                             broadcastTemplate.SentAt <= sentAtEndDate)
                    .WhereIf(
                        updatedAtStartDate.HasValue && updatedAtEndDate.HasValue,
                        broadcastTemplate => broadcastTemplate.UpdatedAt >= updatedAtStartDate &&
                                             broadcastTemplate.UpdatedAt <= updatedAtEndDate)
                    .WhereIf(
                        createdBy is { Count: > 0 },
                        broadcastTemplate => createdBy!.Contains(broadcastTemplate.SavedBy.IdentityId))
                    .WhereIf(
                        channelIds is { Count: > 0 },
                        broadcastTemplate => channelIds!.Contains(broadcastTemplate.TargetedChannel.ChannelIdentityId))
                    .WhereIf(
                        channelTypes is { Count: > 0 },
                        broadcastTemplate => channelTypes!.Contains(broadcastTemplate.TargetedChannel.ChannelType))
                    .CountAsync();

                return Ok(
                    new TotalCountResponse
                    {
                        Count = broadcastCount
                    });
            }

            return BadRequest(new ResponseViewModel(401));
        }

        /// <summary>
        /// List all broadcast campaign.
        /// </summary>
        /// <param name="offset">For pagination number of skipping items.</param>
        /// <param name="limit">For pagination number of items in the page.</param>
        /// <param name="name">Campaign name.</param>
        /// <param name="channelIds">An array of Channel Id.</param>
        /// <param name="channelTypes">An array of Channel Type.</param>
        /// <param name="statuses">An array of Status of the broadcast.</param>
        /// <param name="sentAtStartDate">sent at start Date.</param>
        /// <param name="sentAtEndDate">sent at end date.</param>
        /// <param name="updatedAtStartDate">updated at start date.</param>
        /// <param name="updatedAtEndDate">updated at end date.</param>
        /// <param name="createdBy">created By a staff id.</param>
        /// <param name="sortBy">sort By a param.</param>
        /// <param name="sortOrder">sort order, either desc or asc.</param>
        /// <returns> </returns>
        [HttpGet]
        [Route("")]
        public async Task<ActionResult<List<CompanyMessageTemplateResponse>>> GetTemplates(
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "name")]
            string? name = null,
            [FromQuery(Name = "statuses")]
            List<string>? statuses = null,
            [FromQuery(Name = "sentAtStartDate")]
            DateTime? sentAtStartDate = null,
            [FromQuery(Name = "sentAtEndDate")]
            DateTime? sentAtEndDate = null,
            [FromQuery(Name = "updatedAtStartDate")]
            DateTime? updatedAtStartDate = null,
            [FromQuery(Name = "updatedAtEndDate")]
            DateTime? updatedAtEndDate = null,
            [FromQuery(Name = "createdBy")]
            List<string>? createdBy = null,
            [FromQuery(Name = "channelIds")]
            List<string>? channelIds = null,
            [FromQuery(Name = "channelTypes")]
            List<string>? channelTypes = null,
            [FromQuery(Name = "sortBy")]
            string sortBy = "updatedAt",
            [FromQuery(Name = "sortOrder")]
            string sortOrder = "desc")
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                var getMessageTemplatesQuery = _appDbContext.CompanyMessageTemplates
                    .AsNoTracking()
                    .AsSingleQuery()
                    .Include(x => x.UploadedFiles)
                    .Include(x => x.CampaignChannelMessages)
                    .ThenInclude(x => x.UploadedFiles)
                    .Include(x => x.SavedBy.Identity)
                    .Include(x => x.LastSentBy.Identity)
                    .Include(x => x.CampaignAutomationActions)
                    .ThenInclude(x => x.UploadedFiles)
                    .Where(broadcastTemplate => broadcastTemplate.CompanyId == companyUser.CompanyId)
                    .WhereIf(
                        !string.IsNullOrEmpty(name),
                        broadcastTemplate => broadcastTemplate.TemplateName.Contains(name!))
                    .WhereIf(
                        !statuses.IsNullOrEmpty(),
                        broadcastTemplate => statuses!.Contains(broadcastTemplate.Status))
                    .WhereIf(
                        sentAtStartDate.HasValue && sentAtEndDate.HasValue,
                        broadcastTemplate => broadcastTemplate.SentAt >= sentAtStartDate &&
                                             broadcastTemplate.SentAt <= sentAtEndDate)
                    .WhereIf(
                        updatedAtStartDate.HasValue && updatedAtEndDate.HasValue,
                        broadcastTemplate => broadcastTemplate.UpdatedAt >= updatedAtStartDate &&
                                             broadcastTemplate.UpdatedAt <= updatedAtEndDate)
                    .WhereIf(
                        createdBy is { Count: > 0 },
                        broadcastTemplate => createdBy!.Contains(broadcastTemplate.SavedBy.IdentityId))
                    .WhereIf(
                        channelIds is { Count: > 0 },
                        broadcastTemplate => channelIds!.Contains(broadcastTemplate.TargetedChannel.ChannelIdentityId))
                    .WhereIf(
                        channelTypes is { Count: > 0 },
                        broadcastTemplate => channelTypes!.Contains(broadcastTemplate.TargetedChannel.ChannelType));

                Expression<Func<CompanyMessageTemplate, object>> orderByExpression = sortBy?.ToLower() switch
                {
                    "sentat" => template => template.SentAt,
                    "updatedat" => template => template.UpdatedAt,
                    "name" => template => template.TemplateName,
                    _ => template => template.UpdatedAt
                };

                getMessageTemplatesQuery = string.Equals(sortOrder, "asc", StringComparison.OrdinalIgnoreCase) ?
                    getMessageTemplatesQuery.OrderBy(orderByExpression) :
                    getMessageTemplatesQuery.OrderByDescending(orderByExpression);

                // Default channel
                var defaultChannelBroadcastIds = await GetDefaultChannelBroadcastIds(companyUser);

                if (defaultChannelBroadcastIds != null)
                {
                    defaultChannelBroadcastIds = defaultChannelBroadcastIds
                        .ToList();

                    getMessageTemplatesQuery = getMessageTemplatesQuery
                        .Where(x => defaultChannelBroadcastIds.Contains(x.Id));
                }

                var messageTemplates = await getMessageTemplatesQuery
                    .Skip(offset)
                    .Take(limit)
                    .ToListAsync(HttpContext.RequestAborted);

                var messageTemplateResponse = _mapper.Map<List<CompanyMessageTemplateResponse>>(messageTemplates);

                return Ok(messageTemplateResponse);
            }

            return BadRequest();
        }

        private async Task<List<string>?> GetDefaultChannelBroadcastIds(Staff companyUser)
        {
            var rbacService = _serviceProvider.GetRequiredService<IRbacService>();
            var isRbacEnabled = rbacService.IsRbacEnabled();
            if (isRbacEnabled)
            {
                return await RbacGetDefaultChannelBroadcastIds(companyUser);
            }

            return await DefaultGetDefaultChannelBroadcastIds(companyUser);
        }

        private async Task<List<string>?> DefaultGetDefaultChannelBroadcastIds(Staff companyUser)
        {
            if (companyUser.RoleType == StaffUserRole.Admin || companyUser.RoleType == StaffUserRole.SuperAdmin)
            {
                // Admins and SuperAdmins can see all broadcasts
                return null;
            }

            var rolePermission = await _appDbContext.CompanyRolePermissions
                .Where(x =>
                    x.CompanyId == companyUser.CompanyId
                    && x.StaffUserRole == companyUser.RoleType)
                .FirstOrDefaultAsync();

            if (rolePermission == null
                || !rolePermission.Permission.IsShowDefaultChannelBroadcastOnly)
            {
                return null;
            }

            var associatedTeams = await _appDbContext.CompanyStaffTeams
                .AsNoTracking()
                .Where(x => x.Members.Any(y => y.StaffId == companyUser.Id))
                .ToListAsync(HttpContext.RequestAborted);

            var defaultChannelList = associatedTeams
                .Where(associatedTeam => associatedTeam.DefaultChannels?.Count > 0)
                .SelectMany(associatedTeam => associatedTeam.DefaultChannels)
                .ToList();

            if (defaultChannelList.Count == 0)
            {
                return null;
            }

            var allMessageTemplates = await _appDbContext.CompanyMessageTemplates
                .AsNoTracking()
                .Where(broadcastMessage => broadcastMessage.CompanyId == companyUser.CompanyId)
                .OrderByDescending(broadcastMessage => broadcastMessage.UpdatedAt)
                .Select(
                    x => new
                    {
                        x.Id,
                        x.TargetedChannel
                    })
                .ToListAsync(HttpContext.RequestAborted);

            defaultChannelList = defaultChannelList
                .GroupBy(x => x.channel)
                .Select(
                    x => new TargetedChannelModel()
                    {
                        channel = x.Key,
                        ids = x.SelectMany(channelModel => channelModel.ids)
                            .Distinct()
                            .ToList()
                    })
                .ToList();

            var defaultChannelIdentityIdDict = new Dictionary<string, List<string>>();

            foreach (var defaultChannel in defaultChannelList)
            {
                var channelIdentityIds = await _channelIdentityIdRepository.GetChannelIdentityIdsByChannelIds(
                    defaultChannel.channel,
                    defaultChannel.ids);

                defaultChannelIdentityIdDict[defaultChannel.channel] = channelIdentityIds;
            }

            // Filter broadcast in default Channels
            var result = allMessageTemplates
                .Where(
                    x =>
                        defaultChannelIdentityIdDict.ContainsKey(x.TargetedChannel.ChannelType)
                        && defaultChannelIdentityIdDict[x.TargetedChannel.ChannelType]
                            .Contains(x.TargetedChannel.ChannelIdentityId))
                .Select(x => x.Id)
                .ToList();

            return result;
        }

        private async Task<List<string>> RbacGetDefaultChannelBroadcastIds(Staff companyUser)
        {
            var accessControlAggregationService = _serviceProvider.GetRequiredService<IAccessControlAggregationService>();

            var staff = await accessControlAggregationService.GetStaffAccessControlAggregateAsync(companyUser);

            if (!staff.HasDefaultChannelSettingEnabled())
            {
                return null;
            }

            var associatedTeams = await _appDbContext.CompanyStaffTeams
                .AsNoTracking()
                .Where(x => x.Members.Any(y => y.StaffId == companyUser.Id))
                .ToListAsync(HttpContext.RequestAborted);

            var defaultChannelList = associatedTeams
                .Where(associatedTeam => associatedTeam.DefaultChannels?.Count > 0)
                .SelectMany(associatedTeam => associatedTeam.DefaultChannels)
                .ToList();

            if (defaultChannelList.Count == 0)
            {
                return null;
            }

            var allMessageTemplates = await _appDbContext.CompanyMessageTemplates
                .AsNoTracking()
                .Where(broadcastMessage => broadcastMessage.CompanyId == companyUser.CompanyId)
                .OrderByDescending(broadcastMessage => broadcastMessage.UpdatedAt)
                .Select(
                    x => new
                    {
                        x.Id,
                        x.TargetedChannel
                    })
                .ToListAsync(HttpContext.RequestAborted);

            defaultChannelList = defaultChannelList
                .GroupBy(x => x.channel)
                .Select(
                    x => new TargetedChannelModel()
                    {
                        channel = x.Key,
                        ids = x.SelectMany(channelModel => channelModel.ids)
                            .Distinct()
                            .ToList()
                    })
                .ToList();

            var defaultChannelIdentityIdDict = new Dictionary<string, List<string>>();

            foreach (var defaultChannel in defaultChannelList)
            {
                var channelIdentityIds = await _channelIdentityIdRepository.GetChannelIdentityIdsByChannelIds(
                    defaultChannel.channel,
                    defaultChannel.ids);

                defaultChannelIdentityIdDict[defaultChannel.channel] = channelIdentityIds;
            }

            // Filter broadcast in default Channels
            var result = allMessageTemplates
                .Where(
                    x =>
                        defaultChannelIdentityIdDict.ContainsKey(x.TargetedChannel.ChannelType)
                        && defaultChannelIdentityIdDict[x.TargetedChannel.ChannelType]
                            .Contains(x.TargetedChannel.ChannelIdentityId))
                .Select(x => x.Id)
                .ToList();

            return result;
        }


        [HttpGet]
        [Route("{broadcastTempleteId}")]
        public async Task<ActionResult<CompanyMessageTemplateResponse>> GetTemplateById(string broadcastTempleteId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var messageTemplates = await _appDbContext.CompanyMessageTemplates.Where(
                    x => x.CompanyId == companyUser.CompanyId && x.Id == broadcastTempleteId)
                .Include(x => x.CampaignAutomationActions).ThenInclude(x => x.UploadedFiles)
                .Include(x => x.CampaignAutomationActions).ThenInclude(x => x.AssignedStaff)
                .ThenInclude(x => x.Identity)
                .Include(x => x.CampaignAutomationActions).ThenInclude(x => x.AssignedTeam)
                .Include(x => x.UploadedFiles)
                .Include(x => x.CampaignChannelMessages).ThenInclude(x => x.UploadedFiles)
                .Include(x => x.SavedBy)
                .Include(x => x.LastSentBy)
                .Include(x => x.SavedBy.Identity)
                .Include(x => x.LastSentBy.Identity)
                .FirstOrDefaultAsync();

            var messageTemplateResponse = _mapper.Map<CompanyMessageTemplateResponse>(messageTemplates);

            return Ok(messageTemplateResponse);
        }

        [HttpPost]
        [Route("duplicate/{broadcastTemplateId}")]
        public async Task<ActionResult<CompanyMessageTemplateResponse>> DuplicateTemplateById(
            string broadcastTemplateId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "[DuplicateTemplateById] Duplicating broadcast {BroadcastId} for Company ID: {CompanyId}, Staff ID: {StaffId}",
                broadcastTemplateId,
                companyUser.CompanyId,
                companyUser.Id);

            var duplicatedMessageTemplate = await _broadcastService.DuplicatedMessageTemplates(
                companyUser,
                new List<string>()
                {
                    broadcastTemplateId
                });

            var messageTemplateResponse =
                _mapper.Map<CompanyMessageTemplateResponse>(duplicatedMessageTemplate.FirstOrDefault());

            return Ok(messageTemplateResponse);
        }

        [HttpPost]
        [Route("duplicate")]
        public async Task<ActionResult<List<CompanyMessageTemplateResponse>>> DuplicateTemplates(
            [FromBody]
            MultipleCampaignViewModel multipleCampaignViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "[DuplicateTemplateById] Duplicating broadcast {BroadcastIds} for Company ID: {CompanyId}, Staff ID: {StaffId}",
                JsonConvert.SerializeObject(multipleCampaignViewModel),
                companyUser.CompanyId,
                companyUser.Id);

            var duplicatedMessageTemplate = await _broadcastService.DuplicatedMessageTemplates(
                companyUser,
                multipleCampaignViewModel.BroadcastIds);

            var messageTemplateResponse = _mapper.Map<List<CompanyMessageTemplateResponse>>(duplicatedMessageTemplate);

            return Ok(messageTemplateResponse);
        }

        [HttpGet]
        [Route("{broadcastTempleteId}/Statistics")]
        public async Task<ActionResult<StatisticsData>> GetStatisticsByBroadcastId(string broadcastTempleteId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var broadcastStatisticsCacheKeyPattern = new BroadcastStatisticsCacheKeyPattern(broadcastTempleteId);

            var data = await _cacheManagerService.GetCacheAsync(broadcastStatisticsCacheKeyPattern);

            if (!string.IsNullOrEmpty(data))
            {
                return Ok(JsonConvert.DeserializeObject<StatisticsData>(data));
            }

            while (true)
            {
                var myLock = await _lockService.AcquireLockAsync(
                    $"lock:broadcastStatisticsCacheKeyPattern_{broadcastStatisticsCacheKeyPattern.GenerateKeyPattern()}",
                    TimeSpan.FromSeconds(60));

                if (myLock == null)
                {
                    var cacheData = await _cacheManagerService.GetCacheAsync(broadcastStatisticsCacheKeyPattern);

                    if (!string.IsNullOrEmpty(cacheData))
                    {
                        return Ok(JsonConvert.DeserializeObject<MissingMessageViewModel>(cacheData));
                    }

                    await Task.Delay(5000);
                }
                else
                {
                    break;
                }
            }

            var messageTemplates = await _appDbContext.CompanyMessageTemplates
                .Where(x => x.CompanyId == companyUser.CompanyId && x.Id == broadcastTempleteId).FirstOrDefaultAsync();

            if (messageTemplates == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = $"Not found"
                    });
            }

            var messageTemplateResponse = new StatisticsData();

            var campaignMessages =
                _appDbContext.BroadcastCompaignHistories.Where(x => x.BroadcastCampaignId == broadcastTempleteId);

            var adminUser = await _appDbContext.UserRoleStaffs
                .Where(X => X.CompanyId == companyUser.CompanyId
                && (X.RoleType == StaffUserRole.Admin || X.RoleType == StaffUserRole.SuperAdmin))
                .FirstOrDefaultAsync();

            var status = await _broadcastService.GetBroadcastStatus(
                adminUser,
                broadcastTempleteId,
                messageTemplates.Status);

            messageTemplateResponse.Sent = status.Sent;
            messageTemplateResponse.Delivered = status.Delivered;
            messageTemplateResponse.Read = status.Read;
            messageTemplateResponse.Replied = status.Replied;
            messageTemplateResponse.Failed = status.Failed;
            messageTemplateResponse.UpdatedAt = DateTime.UtcNow;
            messageTemplates.StatisticsData = messageTemplateResponse;

            await _cacheManagerService.SaveCacheAsync(
                broadcastStatisticsCacheKeyPattern,
                messageTemplateResponse);

            await _appDbContext.SaveChangesAsync();

            return Ok(messageTemplates.StatisticsData);
        }

        [HttpGet]
        [Route("{broadcastTemplateId}/Statistics/Realtime")]
        public async Task<ActionResult<StatisticsData>> GetRealtimeStatisticsByBroadcastId(string broadcastTemplateId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var realtimeBroadcastStatisticsCacheKeyPattern = new RealtimeBroadcastStatisticsCacheKeyPattern(
                broadcastTemplateId,
                companyUser.IdentityId);

            var data = await _cacheManagerService.GetCacheAsync(realtimeBroadcastStatisticsCacheKeyPattern);

            if (!string.IsNullOrEmpty(data))
            {
                return Ok(data);
            }

            var messageTemplates = await _appDbContext.CompanyMessageTemplates
                .Where(x => x.CompanyId == companyUser.CompanyId && x.Id == broadcastTemplateId).FirstOrDefaultAsync();

            if (messageTemplates == null)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = $"Not found"
                    });
            }

            var messageTemplateResponse = new StatisticsData();

            var status = await _broadcastService.GetBroadcastStatus(
                companyUser,
                broadcastTemplateId,
                messageTemplates.Status);

            messageTemplateResponse.Sent = status.Sent;
            messageTemplateResponse.Delivered = status.Delivered;
            messageTemplateResponse.Read = status.Read;
            messageTemplateResponse.Replied = status.Replied;
            messageTemplateResponse.Failed = status.Failed;
            messageTemplateResponse.UpdatedAt = status.UpdatedAt;

            await _cacheManagerService.SaveCacheAsync(
                realtimeBroadcastStatisticsCacheKeyPattern,
                messageTemplateResponse);

            return Ok(messageTemplateResponse);
        }

        [HttpPost]
        [Route("{broadcastTemplateId}/recipient/status")]
        public async Task<ActionResult<List<BroadcastRecipientStatus>>> GetRecipientsBroadcastStatus(
            [FromRoute]
            string broadcastTemplateId,
            [FromBody]
            BroadcastRecipientStatusRequestViewModel request)
        {
            var list = await _appDbContext.BroadcastCompaignHistories
                .AsNoTracking()
                .Include(x => x.Conversation)
                .ThenInclude(x => x.Assignee)
                .ThenInclude(x => x.Identity)
                .Include(x => x.Conversation)
                .ThenInclude(x => x.AssignedTeam)
                .Include(x => x.Conversation)
                .ThenInclude(x => x.UserProfile)
                .Include(x => x.ConversationMessages)
                .Where(
                    x =>
                        request.UserProfileIds.Contains(x.Conversation.UserProfileId)
                        && x.BroadcastCampaignId == broadcastTemplateId
                        && x.Conversation.UserProfile.ActiveStatus == ActiveStatus.Active)
                .ToListAsync();

            var result = new List<BroadcastRecipientStatus>();

            foreach (var broadcastHistory in list)
            {
                var broadcastRecipientStatus = new BroadcastRecipientStatus();

                broadcastRecipientStatus.Conversation =
                    _mapper.Map<ConversationResponseViewModel>(broadcastHistory.Conversation);
                broadcastRecipientStatus.UserProfileId = broadcastHistory.Conversation.UserProfileId;
                broadcastRecipientStatus.ConversationId = broadcastHistory.ConversationId;

                var broadcastMessage = broadcastHistory.ConversationMessages.FirstOrDefault();

                if (broadcastMessage != null)
                {
                    broadcastRecipientStatus.BroadcastMessage =
                        _mapper.Map<ConversationMessageResponseViewModel>(broadcastMessage);

                    switch (request.BroadcastMessageStatus)
                    {
                        case BroadcastMessageStatus.Delivered:
                        case BroadcastMessageStatus.Read:
                            broadcastRecipientStatus.BroadcastMessageStatus = request.BroadcastMessageStatus;

                            break;
                        case BroadcastMessageStatus.Replied:
                            var repliedMessage = await _appDbContext.ConversationMessages
                                .AsNoTracking()
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.ConversationId == broadcastHistory.ConversationId
                                        && x.DeliveryType == DeliveryType.Normal
                                        && x.Id > broadcastMessage.Id
                                        && x.CreatedAt > broadcastHistory.CreatedAt
                                        && x.CreatedAt <= broadcastHistory.CreatedAt.AddDays(3)
                                        && !x.IsSentFromSleekflow);

                            if (repliedMessage != null)
                            {
                                broadcastRecipientStatus.RepliedMessage =
                                    _mapper.Map<ConversationMessageResponseViewModel>(repliedMessage);
                            }

                            broadcastRecipientStatus.BroadcastMessageStatus = BroadcastMessageStatus.Replied;

                            break;
                        case BroadcastMessageStatus.Sent:
                            if (broadcastMessage.Status == MessageStatus.Sent)
                            {
                                broadcastRecipientStatus.BroadcastMessageStatus = BroadcastMessageStatus.Sent;
                            }
                            else if (broadcastMessage.Status == MessageStatus.Received)
                            {
                                broadcastRecipientStatus.BroadcastMessageStatus = BroadcastMessageStatus.Delivered;
                            }
                            else if (broadcastMessage.Status == MessageStatus.Read)
                            {
                                broadcastRecipientStatus.BroadcastMessageStatus = BroadcastMessageStatus.Read;
                            }
                            else if (broadcastMessage.Status is MessageStatus.Failed or MessageStatus.Undelivered)
                            {
                                broadcastRecipientStatus.BroadcastMessageStatus = BroadcastMessageStatus.Failed;
                            }

                            // replied message
                            var repliedMessage2 = await _appDbContext.ConversationMessages
                                .AsNoTracking()
                                .FirstOrDefaultAsync(
                                    message =>
                                        message.ConversationId == broadcastHistory.ConversationId
                                        && message.DeliveryType == DeliveryType.Normal
                                        && message.Id > broadcastMessage.Id
                                        && BroadcastConstants.BroadcastRepliedStatuses.Contains(broadcastMessage.Status)
                                        && message.CreatedAt > broadcastHistory.CreatedAt
                                        && message.CreatedAt <= broadcastHistory.CreatedAt.AddDays(3)
                                        && !message.IsSentFromSleekflow);

                            if (repliedMessage2 != null)
                            {
                                broadcastRecipientStatus.RepliedMessage =
                                    _mapper.Map<ConversationMessageResponseViewModel>(repliedMessage2);
                                broadcastRecipientStatus.BroadcastMessageStatus = BroadcastMessageStatus.Replied;
                            }

                            break;
                        case BroadcastMessageStatus.Failed:
                            broadcastRecipientStatus.BroadcastMessageStatus = BroadcastMessageStatus.Failed;
                            //TODO: We need to standardise the message from here now on.
                            broadcastRecipientStatus.FailedReason = broadcastMessage.ChannelStatusMessage;

                            break;
                        default:
                            throw new ArgumentOutOfRangeException();
                    }
                }
                else if (broadcastHistory.Status != "Sent")
                {
                    broadcastRecipientStatus.BroadcastMessageStatus = BroadcastMessageStatus.Failed;

                    if (broadcastHistory.Status is "Unsubscribed")
                    {
                        broadcastRecipientStatus.FailedReason = broadcastHistory.Status;
                    }
                }

                result.Add(broadcastRecipientStatus);
            }

            return Ok(result);
        }

        [HttpGet]
        [Route("History/{broadcastTempleteId}")]
        public async Task<IActionResult> GetTemplatesHistory(
            string broadcastTempleteId,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == senderId).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                var messageTemplates = await _appDbContext.CompanyMessageTemplates.FirstOrDefaultAsync(
                    x => x.CompanyId == companyUser.CompanyId && x.Id == broadcastTempleteId);

                if (messageTemplates != null)
                {
                    var broadcastHistories = await _appDbContext.BroadcastCompaignHistories
                        .Include(x => x.Conversation.UserProfile).Include(x => x.ConversationMessages)
                        .Where(x => x.BroadcastCampaignId == messageTemplates.Id)
                        .Include(x => x.BroadcastSentBy.Identity).Skip(offset).Take(limit).ToListAsync(HttpContext.RequestAborted);
                    var broadcastHistoryResponse = _mapper.Map<List<BroadcastHistoryResponse>>(broadcastHistories);

                    return Ok(broadcastHistoryResponse);
                }
            }

            return BadRequest();
        }

        [HttpGet]
        [Route("History/{broadcastTempleteId}/export")]
        public async Task<IActionResult> GetTemplatesHistoryCSV(string broadcastTempleteId)
        {
            if (!ModelState.IsValid || !User.Identity.IsAuthenticated)
            {
                return BadRequest();
            }

            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == senderId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var messageTemplates = await _appDbContext.CompanyMessageTemplates.AsNoTracking()
                .FirstOrDefaultAsync(x => x.CompanyId == companyUser.CompanyId && x.Id == broadcastTempleteId);

            if (messageTemplates == null)
            {
                return BadRequest();
            }

            var exportResult =
                await _broadcastService.ExportBroadcastStatus(companyUser.CompanyId, broadcastTempleteId, companyUser.Id);

            return File(
                Encoding.UTF8.GetBytes(exportResult),
                "text/csv",
                $"{messageTemplates.TemplateName.Trim()}: {DateTime.UtcNow}.csv");
        }

        [HttpGet]
        [Route("History/{broadcastTemplateId}/export/background")]
        public async Task<ActionResult<BackgroundTaskViewModel>> GetTemplatesHistoryCSVInBackground(
            string broadcastTemplateId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var messageTemplates = await _appDbContext.CompanyMessageTemplates.AsNoTracking()
                .FirstOrDefaultAsync(x => x.CompanyId == companyUser.CompanyId && x.Id == broadcastTemplateId);

            if (messageTemplates == null)
            {
                return BadRequest();
            }

            var broadcastHistoryCount =
                await _appDbContext.BroadcastCompaignHistories.CountAsync(
                    x => x.BroadcastCampaignId == messageTemplates.Id);

            var backgroundTask = await _backgroundTaskService.EnqueueExportBroadcastStatusToCsvTask(
                companyUser.IdentityId,
                companyUser.CompanyId,
                companyUser.Id,
                broadcastTemplateId,
                broadcastHistoryCount);

            return Ok(backgroundTask.MapToResultViewModel());
        }

        /// <summary>
        /// Bulk export campaign result csv.
        /// </summary>
        /// <param name="multipleCampaignViewModel">Broadcast Ids.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("History/export/background")]
        public async Task<ActionResult<BackgroundTaskViewModel>> GetTemplatesHistoriesCSVInBackground(
            [FromBody]
            MultipleCampaignViewModel multipleCampaignViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "error"
                    });
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            BackgroundTask backgroundTask = null;

            foreach (var broadcastTemplateId in multipleCampaignViewModel.BroadcastIds)
            {
                var messageTemplates = await _appDbContext.CompanyMessageTemplates.AsNoTracking()
                    .FirstOrDefaultAsync(x => x.CompanyId == companyUser.CompanyId && x.Id == broadcastTemplateId);

                if (messageTemplates == null)
                {
                    return BadRequest();
                }

                var broadcastHistoryCount =
                    await _appDbContext.BroadcastCompaignHistories.CountAsync(
                        x => x.BroadcastCampaignId == messageTemplates.Id);

                backgroundTask = await _backgroundTaskService.EnqueueExportBroadcastStatusToCsvTask(
                    companyUser.IdentityId,
                    companyUser.CompanyId,
                    companyUser.Id,
                    broadcastTemplateId,
                    broadcastHistoryCount);
            }

            return Ok(backgroundTask.MapToResultViewModel());
        }

        [HttpDelete]
        [Route("Delete")]
        public async Task<IActionResult> DeleteMessageTemplates([FromBody] RemoveCampaignViewModel messageTemplateIds)
        {
            try
            {
                if (User.Identity.IsAuthenticated)
                {
                    var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                    if (companyUser != null)
                    {
                        _logger.LogInformation(
                            "[DeleteMessageTemplates] Processing deletion for Company ID: {CompanyId}, Staff ID: {StaffId}",
                            companyUser.CompanyId,
                            companyUser.Id);

                        foreach (var messageTemplateId in messageTemplateIds.CampaignIds)
                        {
                            _logger.LogInformation(
                                "[DeleteMessageTemplates] Deleting template {TemplateId} and its associated data",
                                messageTemplateId);

                            var broadcastHistoies = _appDbContext.BroadcastCompaignHistories
                                .Where(x => x.BroadcastCampaign.Id == messageTemplateId)
                                .Include(x => x.ConversationMessages);
                            await broadcastHistoies.ForEachAsync(x => x.ConversationMessages = null);

                            _appDbContext.BroadcastCompaignHistories.RemoveRange(
                                await _appDbContext.BroadcastCompaignHistories
                                    .Where(x => x.BroadcastCampaignId == messageTemplateId)
                                    .ToListAsync());

                            _appDbContext.CampaignUploadedFiles.RemoveRange(
                                await _appDbContext.CampaignUploadedFiles
                                    .Where(x => x.CompanyMessageTemplateId == messageTemplateId)
                                    .ToListAsync());

                            _appDbContext.CampaignChannelMessages.RemoveRange(
                                await _appDbContext.CampaignChannelMessages
                                    .Where(x => x.CompanyMessageTemplateId == messageTemplateId)
                                    .Include(x => x.UploadedFiles)
                                    .ToListAsync());

                            var templates = await _appDbContext.CompanyMessageTemplates
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == companyUser.CompanyId
                                        && x.Id == messageTemplateId);

                            if (string.Equals(templates.Status, BroadcastStatus.Scheduled, StringComparison.OrdinalIgnoreCase))
                            {
                                BackgroundJob.Delete(templates.JobId);
                            }

                            var campaignAutomationActionIdsToRemove =
                                await _appDbContext.CampaignAutomationActions
                                    .Where(x => x.CompanyMessageTemplateId == messageTemplateId)
                                    .Select(x => x.Id)
                                    .ToListAsync();

                            _appDbContext.CampaignAutomationUploadedFiles.RemoveRange(
                                await _appDbContext.CampaignAutomationUploadedFiles
                                    .Where(
                                        x => campaignAutomationActionIdsToRemove
                                            .Contains(x.CampaignAutomationActionId))
                                    .ToListAsync());

                            _appDbContext.CampaignAutomationActions.RemoveRange(
                                await _appDbContext.CampaignAutomationActions
                                    .Where(
                                        x => campaignAutomationActionIdsToRemove
                                            .Contains(x.Id))
                                    .ToListAsync());

                            _appDbContext.CompanyMessageTemplates.Remove(templates);

                            await _appDbContext.SaveChangesAsync();

                            foreach (var broadcastHistory in broadcastHistoies)
                            {
                                await _broadcastHooks.OnBroadcastDeletedAsync(
                                    companyUser.CompanyId,
                                    broadcastHistory);
                            }

                            _logger.LogInformation(
                                "[DeleteMessageTemplates] Successfully deleted template {TemplateId}",
                                messageTemplateId);
                        }

                        return Ok(
                            new ResponseViewModel
                            {
                                message = "success"
                            });
                    }
                }
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "error"
                    });
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "error"
                });
        }

        [HttpPost]
        [Route("BroadcastWithTemplete")]
        public async Task<IActionResult> BroadcastWithTemplate([FromBody] BroadcastViewModel broadcastsViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "error"
                    });
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                _logger.LogWarning("[BroadcastWithTemplate] Unauthorized attempt to broadcast template");
                return Unauthorized();
            }

            _logger.LogInformation(
                "[BroadcastWithTemplate] Broadcast for Company ID: {CompanyId}, Staff ID: {StaffId}, Template ID: {TemplateId}, Payload: {Payload}",
                companyUser.CompanyId,
                companyUser.Id,
                broadcastsViewModel.TempleteId,
                JsonConvert.SerializeObject(broadcastsViewModel));

            var messageTemplate = await _appDbContext.CompanyMessageTemplates
                .Where(x => x.CompanyId == companyUser.CompanyId && x.Id == broadcastsViewModel.TempleteId)
                .Include(x => x.UploadedFiles)
                .Include(x => x.CampaignChannelMessages)
                .FirstOrDefaultAsync();

            _logger.LogInformation(
                "[BroadcastWithTemplate] Retrieved template. Template ID: {TemplateId}, Status: {Status}",
                broadcastsViewModel.TempleteId,
                messageTemplate?.Status ?? "Not Found");

            if (messageTemplate == null)
            {
                _logger.LogWarning(
                    "[BroadcastWithTemplate] Template not found. Template ID: {TemplateId}",
                    broadcastsViewModel.TempleteId);
                return Ok(
                    new ResponseViewModel
                    {
                        message = $"No broadcast template found"
                    });
            }

            if (!broadcastsViewModel.IsTestMessage)
            {
                var filteredUserProfilesQueryable = await GetFilteredBroadcastContacts(
                    companyUser,
                    messageTemplate);

                var targetUserProfilesCount = await filteredUserProfilesQueryable
                    .AsNoTracking()
                    .CountAsync();

                _logger.LogInformation(
                    "[BroadcastWithTemplate] Target audience count: {Count}, CompanyId: {CompanyId}, Staff ID: {StaffId}, Template ID: {TemplateId}",
                    targetUserProfilesCount,
                    companyUser.CompanyId,
                    companyUser.Id,
                    broadcastsViewModel.TempleteId);

                var companyUsage = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);

                if ((companyUsage.billingPeriodUsages.FirstOrDefault()?.TotalMessagesSentFromSleekflow ?? 0) +
                    targetUserProfilesCount > companyUsage.MaximumAutomatedMessages)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = 400,
                            message =
                                $"Company will exceed or has exceeded maximum number of broadcast messages. " +
                                $"CompanyId: {companyUser.CompanyId}",
                            ErrorCode = BroadcastErrorCodeConstants.PublishBroadcastExceedUsageError
                        });
                }
            }

            if (string.Equals(messageTemplate.Status, BroadcastStatus.Sending, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogWarning(
                    "[BroadcastWithTemplate] Template is already sending. CompanyId: {CompanyId}, Staff ID: {StaffId} Template ID: {TemplateId}",
                    companyUser.CompanyId,
                    companyUser.Id,
                    broadcastsViewModel.TempleteId);
                return Ok(
                    new ResponseViewModel
                    {
                        message = $"sending"
                    });
            }

            if (!broadcastsViewModel.IsTestMessage && messageTemplate.ScheduledAt.HasValue)
            {
                _logger.LogInformation(
                    "[BroadcastWithTemplate] Scheduling broadcast. CompanyId: {CompanyId}, Staff ID: {StaffId}, Template ID: {TemplateId}, Scheduled At: {ScheduledAt}",
                    companyUser.CompanyId,
                    companyUser.Id,
                    broadcastsViewModel.TempleteId,
                    messageTemplate.ScheduledAt);

                if (!string.IsNullOrEmpty(messageTemplate.JobId))
                {
                    BackgroundJob.Delete(messageTemplate.JobId);
                }

                var ts = messageTemplate.ScheduledAt.Value - DateTime.UtcNow;

                if (ts.TotalSeconds > 0)
                {
                    var jobId = BackgroundJob.Schedule<IBroadcastService>(
                        x => x.BroadcastMessageAsync(
                            companyUser.Id,
                            broadcastsViewModel,
                            broadcastsViewModel.IsTestMessage,
                            CancellationToken.None),
                        ts);
                    messageTemplate.Status = BroadcastStatus.Scheduled;
                    messageTemplate.JobId = jobId;
                    await _appDbContext.SaveChangesAsync();
                }
                else
                {
                    BackgroundJob.Enqueue<IBroadcastService>(
                        x => x.BroadcastMessageAsync(
                            companyUser.Id,
                            broadcastsViewModel,
                            broadcastsViewModel.IsTestMessage,
                            CancellationToken.None));
                }
            }
            else
            {
                if (string.Equals(messageTemplate.Status, BroadcastStatus.Scheduled, StringComparison.OrdinalIgnoreCase))
                {
                    return Ok(
                        new ResponseViewModel
                        {
                            message = $"scheduled"
                        });
                }

                BackgroundJob.Enqueue<IBroadcastService>(
                    x => x.BroadcastMessageAsync(
                        companyUser.Id,
                        broadcastsViewModel,
                        broadcastsViewModel.IsTestMessage,
                        CancellationToken.None));
            }

            dynamic response = new JObject();
            response.broadcastTemplateId = messageTemplate.Id;
            response.targetAudience = broadcastsViewModel.UserProfileIds?.Count;

            _logger.LogInformation(
                "[BroadcastWithTemplate] Successfully initiated broadcast. Template ID: {TemplateId}, Status: {Status}, Scheduled At: {ScheduledAt}",
                messageTemplate.Id,
                messageTemplate.Status,
                messageTemplate.ScheduledAt);

            return Ok(response);
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("File/Private/{filenameId}/{downloadFilename}")]
        [Route("File/Private/{filenameId}")]
        public async Task<IActionResult> GetPrivateAzureBlob(string filenameId, string downloadFilename)
        {
            // var file = _appDbContext.CampaignUploadedFiles.Where(x => x.CampaignUploadedFileId == filenameId).FirstOrDefault();
            // var stream = await _azureBlobStorageService.DownloadFromAzureBlob(file.Filename, file.BlobContainer);
            // return File(stream.ToArray(), (string.IsNullOrEmpty(file.MIMEType)) ? "application/octet-stream" : file.MIMEType, file.Filename);
            try
            {
                var file = await _appDbContext.CampaignUploadedFiles
                    .FirstOrDefaultAsync(x => x.CampaignUploadedFileId == filenameId);

                var stream = await _azureBlobStorageService.DownloadFromAzureBlob(file.Filename, file.BlobContainer);
                var extension = Path.GetExtension(file.Filename);

                if (string.IsNullOrEmpty(extension))
                {
                    switch (file.MIMEType)
                    {
                        case "video/mp4":
                            extension = "mp4";

                            break;
                        case "image/jpeg":
                            extension = "jpg";

                            break;
                        case "image/png":
                            extension = "png";

                            break;
                    }
                }

                var filename = Path.GetFileNameWithoutExtension(file.Filename);
                var fileType = string.IsNullOrEmpty(file.MIMEType) ? "application/octet-stream" : file.MIMEType;

                var fileDownloadName =
                    $"{filename}{((string.IsNullOrEmpty(extension) && file.MIMEType == "video/mp4") ? ".mp4" : $".{extension}")}";

                // var data = stream.ToArray();
                // string encodeFilename = HttpUtility.UrlEncode(fileDownloadName, Encoding.UTF8);
                // Response.Headers.Add($"Content-Disposition", $"attachment; filename={Uri.EscapeUriString(fileDownloadName)}; filename*=UTF-8\"{encodeFilename}");
                // return new FileStreamResult(new MemoryStream(data), fileType);
                var res = File(stream.ToArray(), fileType, fileDownloadName);
                res.EnableRangeProcessing = true;

                return res;
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "error"
                    });
            }
        }

        [HttpDelete]
        [Route("File/Delete/{filenameId}")]
        public async Task<IActionResult> DeleteAzureBlob(string filenameId)
        {
            try
            {
                var file = await _appDbContext.CampaignUploadedFiles.FirstOrDefaultAsync(
                    x => x.CampaignUploadedFileId == filenameId);
                _appDbContext.CampaignUploadedFiles.RemoveRange(file);

                if (!file.CampaignChannelMessageId.HasValue)
                {
                    await _azureBlobStorageService.DeleteFromAzureBlob(file.Filename, file.BlobContainer);
                }

                await _appDbContext.SaveChangesAsync();

                return Ok(
                    new ResponseViewModel
                    {
                        message = "success"
                    });
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "error"
                    });
            }
        }


        /// <summary>
        /// To get the broadcast contacts count with in 24 hrs, future and past 24 hrs, to have a rough count of daily message usage
        /// </summary>
        /// <param name="queryTime">Current or Scheduled Broadcast Time</param>
        /// <returns>return the sum of broadcast counts</returns>
        [HttpGet]
        [Route("BroadcastContactsCount")]
        public async Task<ActionResult<ResponseViewModel<long>>> GetBroadcastContactsCountAsync([FromQuery] DateTime? queryTime)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                _logger.LogWarning("[GetBroadcastContactsCountAsync] Unauthorized attempt to get broadcast contacts count");
                return Unauthorized();
            }

            var broadcastContactsCount = await _broadcastService.GetBroadcastContactsCountAsync(companyUser.CompanyId, queryTime ?? DateTime.Now);

            return Ok(new ResponseViewModel<long>(broadcastContactsCount, 200));
        }

        private async Task<IQueryable<UserProfile>> GetFilteredBroadcastContacts(Staff companyUser, CompanyMessageTemplate messageTemplate)
        {
            var rbacService = _serviceProvider.GetRequiredService<IRbacService>();
            IQueryable<UserProfile> filteredBroadcastContactsQueryable;

            if (rbacService.IsRbacEnabled())
            {
                filteredBroadcastContactsQueryable = await _userProfileService.GetRbacFilteredBroadcastContacts(
                    companyUser.CompanyId,
                    messageTemplate.Conditions);
            }
            else
            {
                filteredBroadcastContactsQueryable = await _userProfileService.GetIQueryableUserProfilesWithFilter(
                    companyUser.CompanyId,
                    messageTemplate.Conditions,
                    staffUserRole: companyUser.RoleType,
                    assigneeId: companyUser.Id);
            }

            return filteredBroadcastContactsQueryable;
        }
    }
}