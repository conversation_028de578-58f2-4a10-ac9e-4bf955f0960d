namespace Travis_backend.FlowHubs.Constants;

public static class TriggerIds
{
    public const string IncomingMessageReceived = "incoming-message-received";
    public const string OutgoingMessageSent = "outgoing-message-sent";
    public const string ConversationStatusUpdated = "conversation-status-updated";
    public const string NewContactAdded = "new-contact-added";
    public const string LabelAdded = "label-added";
    public const string LabelRemoved = "label-removed";
    public const string ListAdded = "list-added";
    public const string ListRemoved = "list-removed";
    public const string ContactPropertyUpdated = "contact-property-updated";
    public const string TicketCreated = "ticket-created";
    public const string TicketUpdated = "ticket-updated";
    public const string GoogleSheetsRowCreated = "google-sheets-row-created";
    public const string GoogleSheetsRowUpdated = "google-sheets-row-updated";
    public const string SchemafulObjectCreated = "schemaful-object-created";
    public const string SchemafulObjectUpdated = "schemaful-object-updated";
    public const string HubspotObjectCreated = "hubspot-object-created";
    public const string HubspotObjectUpdated = "hubspot-object-updated";
    public const string ZohoObjectCreated = "zoho-object-created";
    public const string ZohoObjectUpdated = "zoho-object-updated";
    public const string ClickToWhatsappAd = "click-to-whatsapp-ad";
    public const string MessageStatusUpdated = "message-status-updated";
    public const string WhatsappFlowSubmissionMessageReceived = "whatsapp-flow-submission-message-received";
    public const string SalesforceObjectCreated = "salesforce-object-created";
    public const string SalesforceObjectUpdated = "salesforce-object-updated";
    public const string VtexOrderCreated = "vtex-order-created";
    public const string VtexOrderStatusChanged = "vtex-order-status-changed";
    public const string VtexOrderEnrolled = "vtex-order-enrolled";
    public const string ScheduledDataAndTimeArrived = "scheduled-date-and-time";
    public const string ContactPropertyDataAndTimeArrived = "contact-property-date-and-time";
    public const string CustomObjectPropertyDateAndTimeArrived = "custom-object-date-and-time";
    public const string FbIgPostCommentReceived = "fb-ig-post-comment-received";
    public const string OnTikTokAdsLeadReceived = "tiktok-ads-lead-received";
    public const string ShopifyOrderCreated = "shopify-order-created";
    public const string ShopifyOrderUpdated = "shopify-order-updated";
    public const string ShopifyCustomerCreated = "shopify-customer-created";
    public const string ShopifyCustomerUpdated = "shopify-customer-updated";
    public const string ShopifyOrderEnrolled = "shopify-order-enrolled";
    public const string ShopifyAbandonedCartCreated = "shopify-abandoned-cart-created";
    public const string ShopifyAbandonedCartUpdated = "shopify-abandoned-cart-updated";
    public const string LiveChtWebsiteUrlDetected = "livechat-website-url-detected";
}