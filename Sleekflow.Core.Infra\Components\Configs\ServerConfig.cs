using Newtonsoft.Json;
using Sleekflow.Core.Infra.Constants;
using Sleekflow.Core.Infra.Helpers;
using YamlDotNet.Serialization;

namespace Sleekflow.Core.Infra.Components.Configs;

public class ServerConfig
{
    public CorsConfig CorsConfig { get; set; }

    public string DefaultSleekflowCoreDomain { get; set; }

    public string DefaultSleekflowCoreFrontDoorDomain { get; set; }

    public List<RegionalConfig> RegionalConfigs { get; set; }

    public string Name { get; }

    public ServerConfig()
    {
        var envWithLocations = new Dictionary<string, List<string>>
        {
            [EnvironmentNames.Dev] = [LocationNames.EastAsia],
            [EnvironmentNames.Staging] = [LocationNames.EastAsia, LocationNames.EastUs],
            [EnvironmentNames.Production] =
            [
                LocationNames.EastAsia,
                LocationNames.EastUs,
                LocationNames.SouthEastAsia,
                LocationNames.UaeNorth,
                LocationNames.WestEurope
            ]
        };

        var config = new Pulumi.Config("sleekflow");
        DefaultSleekflowCoreDomain = config.Require("default_sleekflow_core_domain");
        DefaultSleekflowCoreFrontDoorDomain = config.Require("default_sleekflow_core_front_door_domain");
        CorsConfig = JsonConvert.DeserializeObject<CorsConfig>(config.Require("cors"))!;
        Name = config.Require("name");

        var locations = envWithLocations[Name];
        RegionalConfigs = new List<RegionalConfig>();
        foreach (var location in locations)
        {
            var regionalConfig = ReadRegionalConfigYaml(location);
            RegionalConfigs.Add(regionalConfig);
        }
        Console.WriteLine($"Loaded Regional Config Length: {RegionalConfigs.Count}");
    }

    private RegionalConfig ReadRegionalConfigYaml(string location)
    {
        var projectRootPath = Path.GetFullPath(Path.Combine(AppContext.BaseDirectory, "../../../"));
        var filePath = Path.Combine(projectRootPath, "RegionalConfigs");

        var fileName = $"{Name}.{location}.yaml";

        var yamlContent = File.ReadAllText(Path.Combine(filePath, fileName));

        var deserializer = new DeserializerBuilder().Build();

        var yamlObject = deserializer.Deserialize<object>(yamlContent);

        var jSerializeObject = JsonConvert.SerializeObject(yamlObject, Formatting.Indented);

        var regionalConfig = JsonConvert.DeserializeObject<RegionalConfig>(jSerializeObject)!;

        var encryptionSecretKey = new Pulumi.Config("encryption_secret_key");

        var sleekflowCoreConfig = regionalConfig.SleekflowCoreConfig;
        var sqlDbConfig = regionalConfig.SqlDbConfig;

        var secretString = string.Empty;
        var locationShortName = LocationNames.GetShortName(regionalConfig.LocationName);
        var secretStringOutput = encryptionSecretKey.RequireSecret(locationShortName);
        secretStringOutput.Apply(s => secretString = s);

        if (string.IsNullOrEmpty(secretString))
        {
            throw new Exception("Secret for Server Config is empty");
        }

        // Sql Db
        sqlDbConfig.AdministratorLoginRandomSecret = AesHelper.Decrypt(
            sqlDbConfig.AdministratorLoginRandomSecret,
            secretString);
        sqlDbConfig.AdministratorLoginRandomPasswordSecret = AesHelper.Decrypt(
            sqlDbConfig.AdministratorLoginRandomPasswordSecret,
            secretString);

        // Beamer
        sleekflowCoreConfig.Beamer.ApiKey = AesHelper.Decrypt(sleekflowCoreConfig.Beamer.ApiKey, secretString);
        sleekflowCoreConfig.Beamer.WebhookVerifyKey = AesHelper.Decrypt(
            sleekflowCoreConfig.Beamer.WebhookVerifyKey,
            secretString);

        // Auth0
        sleekflowCoreConfig.Auth0.ClientSecret = AesHelper.Decrypt(
            sleekflowCoreConfig.Auth0.ClientSecret,
            secretString);
        sleekflowCoreConfig.Auth0.HealthCheckConfig.ClientSecret = AesHelper.Decrypt(
            sleekflowCoreConfig.Auth0.HealthCheckConfig.ClientSecret,
            secretString);
        sleekflowCoreConfig.Auth0.TenantHubSecretKey = AesHelper.Decrypt(
            sleekflowCoreConfig.Auth0.TenantHubSecretKey,
            secretString);

        // V2 Hub Config Keys
        sleekflowCoreConfig.AuditHub.Key = AesHelper.Decrypt(sleekflowCoreConfig.AuditHub.Key, secretString);
        sleekflowCoreConfig.CommerceHub.Key = AesHelper.Decrypt(sleekflowCoreConfig.CommerceHub.Key, secretString);
        sleekflowCoreConfig.CrmHub.Key = AesHelper.Decrypt(sleekflowCoreConfig.CrmHub.Key, secretString);
        sleekflowCoreConfig.TenantHub.Key = AesHelper.Decrypt(sleekflowCoreConfig.TenantHub.Key, secretString);
        sleekflowCoreConfig.TicketingHub.Key = AesHelper.Decrypt(
            sleekflowCoreConfig.TicketingHub.Key,
            secretString);
        sleekflowCoreConfig.UserEventHub.Key = AesHelper.Decrypt(
            sleekflowCoreConfig.UserEventHub.Key,
            secretString);
        sleekflowCoreConfig.WebhookHub.AuthSecretKey = AesHelper.Decrypt(
            sleekflowCoreConfig.WebhookHub.AuthSecretKey,
            secretString);
        sleekflowCoreConfig.WebhookHub.Key = AesHelper.Decrypt(sleekflowCoreConfig.WebhookHub.Key, secretString);
        sleekflowCoreConfig.FlowHub.Key = AesHelper.Decrypt(sleekflowCoreConfig.FlowHub.Key, secretString);
        sleekflowCoreConfig.IntelligentHub.Key = AesHelper.Decrypt(
            sleekflowCoreConfig.IntelligentHub.Key,
            secretString);
        sleekflowCoreConfig.InternalIntegrationHub.Key = AesHelper.Decrypt(
            sleekflowCoreConfig.InternalIntegrationHub.Key,
            secretString);
        sleekflowCoreConfig.MessagingHub.Key = AesHelper.Decrypt(
            sleekflowCoreConfig.MessagingHub.Key,
            secretString);
        sleekflowCoreConfig.ShareHub.Key = AesHelper.Decrypt(sleekflowCoreConfig.ShareHub.Key, secretString);

        // Shopify
        sleekflowCoreConfig.Shopify.ShopifyApiKey = AesHelper.Decrypt(
            sleekflowCoreConfig.Shopify.ShopifyApiKey,
            secretString);
        sleekflowCoreConfig.Shopify.ShopifySecretKey = AesHelper.Decrypt(
            sleekflowCoreConfig.Shopify.ShopifySecretKey,
            secretString);

        // Stripe Config Connect Webhook Keys
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.ConnectWebhookSecrets.Gb = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.ConnectWebhookSecrets.Gb,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.ConnectWebhookSecrets.Hk = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.ConnectWebhookSecrets.Hk,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.ConnectWebhookSecrets.My = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.ConnectWebhookSecrets.My,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.ConnectWebhookSecrets.Sg = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.ConnectWebhookSecrets.Sg,
            secretString);

        // Stripe Config Public Keys
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.PublicKeys.Default = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.PublicKeys.Default,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.PublicKeys.Gb = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.PublicKeys.Gb,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.PublicKeys.Hk = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.PublicKeys.Hk,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.PublicKeys.My = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.PublicKeys.My,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.PublicKeys.Sg = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.PublicKeys.Sg,
            secretString);

        // Stripe Config Report Webhook Secrets Keys
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.ReportWebhookSecrets.Gb = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.ReportWebhookSecrets.Gb,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.ReportWebhookSecrets.Hk = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.ReportWebhookSecrets.Hk,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.ReportWebhookSecrets.My = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.ReportWebhookSecrets.My,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.ReportWebhookSecrets.Sg = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.ReportWebhookSecrets.Sg,
            secretString);

        // Stripe Config Secret Keys
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.SecretKeys.Default = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.SecretKeys.Default,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.SecretKeys.Gb = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.SecretKeys.Gb,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.SecretKeys.Hk = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.SecretKeys.Hk,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.SecretKeys.My = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.SecretKeys.My,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.SecretKeys.Sg = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.SecretKeys.Sg,
            secretString);

        // Stripe Config Webhook Secrets Keys
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.WebhookSecrets.Default = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.WebhookSecrets.Default,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.WebhookSecrets.Gb = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.WebhookSecrets.Gb,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.WebhookSecrets.Hk = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.WebhookSecrets.Hk,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.WebhookSecrets.My = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.WebhookSecrets.My,
            secretString);
        sleekflowCoreConfig.SleekPayConfig.StripeConfig.WebhookSecrets.Sg = AesHelper.Decrypt(
            sleekflowCoreConfig.SleekPayConfig.StripeConfig.WebhookSecrets.Sg,
            secretString);

        // Stripe Payment Secrets Keys
        sleekflowCoreConfig.StripePayment.StripePaymentSecretKeyGb = AesHelper.Decrypt(
            sleekflowCoreConfig.StripePayment.StripePaymentSecretKeyGb,
            secretString);
        sleekflowCoreConfig.StripePayment.StripePaymentSecretKeyHk = AesHelper.Decrypt(
            sleekflowCoreConfig.StripePayment.StripePaymentSecretKeyHk,
            secretString);
        sleekflowCoreConfig.StripePayment.StripePaymentSecretKeyMy = AesHelper.Decrypt(
            sleekflowCoreConfig.StripePayment.StripePaymentSecretKeyMy,
            secretString);
        sleekflowCoreConfig.StripePayment.StripePaymentSecretKeySg = AesHelper.Decrypt(
            sleekflowCoreConfig.StripePayment.StripePaymentSecretKeySg,
            secretString);

        // Stripe Report Webhook Secrets Keys
        sleekflowCoreConfig.StripeReport.StripeReportWebhookSecretGb = AesHelper.Decrypt(
            sleekflowCoreConfig.StripeReport.StripeReportWebhookSecretGb,
            secretString);
        sleekflowCoreConfig.StripeReport.StripeReportWebhookSecretHk = AesHelper.Decrypt(
            sleekflowCoreConfig.StripeReport.StripeReportWebhookSecretHk,
            secretString);
        sleekflowCoreConfig.StripeReport.StripeReportWebhookSecretMy = AesHelper.Decrypt(
            sleekflowCoreConfig.StripeReport.StripeReportWebhookSecretMy,
            secretString);
        sleekflowCoreConfig.StripeReport.StripeReportWebhookSecretSg = AesHelper.Decrypt(
            sleekflowCoreConfig.StripeReport.StripeReportWebhookSecretSg,
            secretString);

        // Stripe Keys
        sleekflowCoreConfig.Stripe.StripePublicKey = AesHelper.Decrypt(
            sleekflowCoreConfig.Stripe.StripePublicKey,
            secretString);
        sleekflowCoreConfig.Stripe.StripeReportKey = AesHelper.Decrypt(
            sleekflowCoreConfig.Stripe.StripeReportKey,
            secretString);
        sleekflowCoreConfig.Stripe.StripeSecretKey = AesHelper.Decrypt(
            sleekflowCoreConfig.Stripe.StripeSecretKey,
            secretString);
        sleekflowCoreConfig.Stripe.StripeWebhookSecret = AesHelper.Decrypt(
            sleekflowCoreConfig.Stripe.StripeWebhookSecret,
            secretString);

        // Others
        sleekflowCoreConfig.PartnerStackConfig.PublicKey = AesHelper.Decrypt(
            sleekflowCoreConfig.PartnerStackConfig.PublicKey,
            secretString);
        sleekflowCoreConfig.PartnerStackConfig.SecretKey = AesHelper.Decrypt(
            sleekflowCoreConfig.PartnerStackConfig.SecretKey,
            secretString);
        sleekflowCoreConfig.IntegrationAlertConfig.ApiKey = AesHelper.Decrypt(
            sleekflowCoreConfig.IntegrationAlertConfig.ApiKey,
            secretString);
        sleekflowCoreConfig.HubSpot.InternalHubSpotApiKey = AesHelper.Decrypt(
            sleekflowCoreConfig.HubSpot.InternalHubSpotApiKey,
            secretString);
        sleekflowCoreConfig.IpLookUp.Key = AesHelper.Decrypt(sleekflowCoreConfig.IpLookUp.Key, secretString);
        sleekflowCoreConfig.ChatApi.ApiKey = AesHelper.Decrypt(sleekflowCoreConfig.ChatApi.ApiKey, secretString);
        sleekflowCoreConfig.Facebook.ClientSecret = AesHelper.Decrypt(
            sleekflowCoreConfig.Facebook.ClientSecret,
            secretString);
        sleekflowCoreConfig.PublicApiGateway.Key = AesHelper.Decrypt(
            sleekflowCoreConfig.PublicApiGateway.Key,
            secretString);
        sleekflowCoreConfig.Rewardful.ApiSecret = AesHelper.Decrypt(
            sleekflowCoreConfig.Rewardful.ApiSecret,
            secretString);
        sleekflowCoreConfig.Tokens.Key = AesHelper.Decrypt(sleekflowCoreConfig.Tokens.Key, secretString);

        sleekflowCoreConfig.Values.SleekflowPublicApiKey = AesHelper.Decrypt(
            sleekflowCoreConfig.Values.SleekflowPublicApiKey,
            secretString);
        sleekflowCoreConfig.Azure.MediaService.ClientSecret = AesHelper.Decrypt(
            sleekflowCoreConfig.Azure.MediaService.ClientSecret,
            secretString);

        sleekflowCoreConfig.LiveChatConfig.SecretKey = AesHelper.Decrypt(
            sleekflowCoreConfig.LiveChatConfig.SecretKey,
            secretString);

        return regionalConfig;
    }
}