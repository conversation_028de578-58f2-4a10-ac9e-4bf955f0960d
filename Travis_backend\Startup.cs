﻿using System;
using System.Collections.Generic;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using System.Text;
using Azure.Monitor.OpenTelemetry.Exporter;
using Hangfire;
using Hangfire.Dashboard;
using Hangfire.Pro.Redis;
using Indicia.HubSpot.Api.Companies;
using Indicia.HubSpot.Api.Contacts;
using Indicia.HubSpot.Api.Deals;
using Indicia.HubSpot.Api.Tickets;
using Indicia.HubSpot.Support;
using IPGeolocation;
using Microsoft.ApplicationInsights.Channel;
using Microsoft.ApplicationInsights.DependencyCollector;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.ApplicationInsights.Extensibility.EventCounterCollector;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Azure.SignalR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using OpenTelemetry.Trace;
using StackExchange.Redis;
using Stripe;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.Services;
using Travis_backend.AnalyticsDomain.Services;
using Travis_backend.AuditHubDomain;
using Travis_backend.AutomationDomain.Services;
using Travis_backend.BackgroundTaskServices;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.BroadcastDomain.Services;
using Travis_backend.Cache;
using Travis_backend.CampaignAnalyticsDomain.Services;
using Travis_backend.ChannelDomain.Repositories;
using Travis_backend.ChannelDomain.Services;
using Travis_backend.ChannelDomain.Services.Facebook;
using Travis_backend.ChannelDomain.Services.Instagram;
using Travis_backend.ChannelDomain.Services.LiveChatV2;
using Travis_backend.ClientCustomDomain.Services;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CommonDomain.Repositories;
using Travis_backend.CommonDomain.Services;
using Travis_backend.CompanyDomain.ApiKeyResolvers;
using Travis_backend.CompanyDomain.Repositories;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Helpers;
using Travis_backend.Configuration;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.FieldSetters;
using Travis_backend.ContactDomain.Repositories;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.Services.Cache;
using Travis_backend.ContactDomain.Services.Import;
using Travis_backend.Controllers.ShopifyIntegrationControllers;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.ConversationQueryables;
using Travis_backend.ConversationDomain.ConversationResolver;
using Travis_backend.ConversationDomain.CronJobServices;
using Travis_backend.ConversationDomain.Repositories;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationDomain.ViewModels.Mappers;
using Travis_backend.ConversationServices;
using Travis_backend.ConversationServices.Operations;
using Travis_backend.CustomObjectDomain.Services;
using Travis_backend.Database;
using Travis_backend.Database.DataAccessLayer;
using Travis_backend.Database.Filters;
using Travis_backend.Database.Services;
using Travis_backend.DemoDomain.Services;
using Travis_backend.**********************;
using Travis_backend.Externals;
using Travis_backend.FacebookInstagramIntegrationDomain.Services;
using Travis_backend.FileDomain.Services;
using Travis_backend.FlowHubs;
using Travis_backend.HealthChecks;
using Travis_backend.Infrastructures.Middlewares;
using Travis_backend.Infrastructures.Options;
using Travis_backend.Infrastructures.SleekFlowEnvironments;
using Travis_backend.IntegrationServices;
using Travis_backend.IntelligentHubDomain;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.Services;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.InternalIntegrationHubDomain;
using Travis_backend.InternalIntegrationHubDomain.Services;
using Travis_backend.MessageDomain.ChannelMessageProvider;
using Travis_backend.MessageDomain.ChannelMessageProvider.ChannelMessageHandlers;
using Travis_backend.MessageDomain.ChannelWebhookProvider;
using Travis_backend.MessageDomain.ChannelWebhookProvider.ChannelWebhookHandlers;
using Travis_backend.MessageDomain.Repositories;
using Travis_backend.MessageDomain.Services;
using Travis_backend.MessagingHubDomain;
using Travis_backend.Notifications;
using Travis_backend.OpenTelemetry.Meters;
using Travis_backend.PartnerStackIntegrationDomain.Services;
using Travis_backend.PiiMaskingDomain.Repositories;
using Travis_backend.PiiMaskingDomain.Services;
using Travis_backend.PublicApiGatewayDomain;
using Travis_backend.RateLimiters.Services;
using Travis_backend.ResellerDomain.Services;
using Travis_backend.Services;
using Travis_backend.Services.Internal;
using Travis_backend.Services.JourneyBuilder;
using Travis_backend.ShareHubDomain;
using Travis_backend.ShareInvitationDomain.Services;
using Travis_backend.SignalR;
using Travis_backend.SleekflowCommerceHubDomain;
using Travis_backend.SleekflowCrmHubDomain;
using Travis_backend.StripeIntegrationDomain.Clients;
using Travis_backend.StripeIntegrationDomain.Configs;
using Travis_backend.StripeIntegrationDomain.EventHandlers;
using Travis_backend.StripeIntegrationDomain.EventHandlers.Subscriptions;
using Travis_backend.StripeIntegrationDomain.Services;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Repositories;
using Travis_backend.SubscriptionPlanDomain.Services;
using Travis_backend.Telemetries;
using Travis_backend.Telemetries.TelemetryProcessor;
using Travis_backend.TenantHubDomain;
using Travis_backend.TicketingHubDomain;
using Travis_backend.UserEventHubDomain;
using Travis_backend.WebhookHubDomain;
using Task = System.Threading.Tasks.Task;
using Travis_backend.ContactDomain.Repositories;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.CoreDomain.Services;
using Travis_backend.ConversationDomain.CronJobServices;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels.Mappers;
using Travis_backend.Database.DataAccessLayer;
using Travis_backend.FacebookInstagramIntegrationDomain.Services;
using Travis_backend.Infrastructures.SleekFlowEnvironments;
using Travis_backend.InternalIntegrationHubDomain;
using Travis_backend.InternalIntegrationHubDomain.Services;
using Travis_backend.MessageDomain.ChannelWebhookProvider;
using Travis_backend.MessageDomain.ChannelWebhookProvider.ChannelWebhookHandlers;
using Travis_backend.RateLimiters.Services;
using Travis_backend.StripeIntegrationDomain.EventHandlers;
using Travis_backend.StripeIntegrationDomain.EventHandlers.Subscriptions;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.Telemetries;

using Travis_backend.Telemetries.TelemetryProcessor;
using Travis_backend.TenantHubDomain.Services;
using TokenService = Travis_backend.Services.Internal.TokenService;

namespace Travis_backend
{
    public class Startup
    {
        public Startup(IConfiguration configuration, IWebHostEnvironment environment)
        {
            Environment = environment;
            Configuration = configuration;
        }

        readonly string MyAllowSpecificOrigins = "_myAllowSpecificOrigins";

        public IWebHostEnvironment Environment { get; }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            if (System.Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_CONNECTION_STRING") != null
                && !string.IsNullOrEmpty(
                    System.Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_CONNECTION_STRING")))
            {
                services.AddApplicationInsightsTelemetry(
                    options =>
                    {
                        var isSamplingDisabled = System.Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED");

                        if (isSamplingDisabled != null && string.Equals(isSamplingDisabled, "true", StringComparison.OrdinalIgnoreCase))
                        {
                            options.EnableAdaptiveSampling = false;
                        }
                    });
                services.AddApplicationInsightsTelemetryProcessor<RemoveSucceedSqlCommandTelemetryProcessor>();
                services.AddServiceProfiler();
                services.AddSnapshotCollector(
                    config =>
                    {
                        config.ThresholdForSnapshotting = 50;
                    });
                services.ConfigureTelemetryModule<DependencyTrackingTelemetryModule>(
                    (module, o) => { module.EnableSqlCommandTextInstrumentation = true; });
                services.ConfigureTelemetryModule<EventCounterCollectionModule>(
                    (module, o) =>
                    {
                        // Removes all default counters, if any.
                        module.Counters.Clear();

                        // https://learn.microsoft.com/en-us/dotnet/core/diagnostics/available-counters
                        // @formatter:off
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "time-in-gc"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "alloc-rate"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "cpu-usage"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "exception-count"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gc-heap-size"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gen-0-gc-count"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gen-0-size"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gen-1-gc-count"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gen-1-size"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gen-2-gc-count"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gen-2-size"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "loh-size"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "poh-size"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gc-fragmentation"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "threadpool-completed-items-count"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "threadpool-queue-length"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "threadpool-thread-count"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "working-set"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Runtime", "gc-committed"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Hosting", "current-requests"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Hosting", "failed-requests"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Hosting", "requests-per-second"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Hosting", "total-requests"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Http.Connections", "connections-duration"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Http.Connections", "current-connections"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Http.Connections", "connections-started"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Http.Connections", "connections-stopped"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft.AspNetCore.Http.Connections", "connections-timed-out"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "connection-queue-length"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "connections-per-second"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "current-connections"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "current-tls-handshakes"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "current-upgraded-requests"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "failed-tls-handshakes"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "request-queue-length"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "tls-handshakes-per-second"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "total-connections"));
                        module.Counters.Add(new EventCounterCollectionRequest("Microsoft-AspNetCore-Server-Kestrel", "total-tls-handshakes"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Net.Http", "requests-started"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Net.Http", "requests-started-rate"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Net.Http", "requests-failed"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Net.Http", "requests-failed-rate"));
                        module.Counters.Add(new EventCounterCollectionRequest("System.Net.Http", "current-requests"));
                        // @formatter:on
                    });

                services.AddOpenTelemetry()
                    .WithMetrics(
                        metrics =>
                        {
                            metrics.AddMeter(BaseMeters.SleekflowCoreMeters);
                            metrics.AddAzureMonitorMetricExporter(
                                credential =>
                                {
                                    credential.ConnectionString =
                                        System.Environment.GetEnvironmentVariable(
                                            "APPLICATIONINSIGHTS_CONNECTION_STRING");
                                });
                        })
                    .WithTracing(
                        builder =>
                        {
                            builder.AddHangfireInstrumentation();
                        });
            }

            // Meters to track the usage of the application
            services.AddSingleton<IAutomationMeters, AutomationMeters>();
            services.AddSingleton<IConversationMeters, ConversationMeters>();
            services.AddSingleton<IMetaChannelConnectionMeters, MetaChannelConnectionMeters>();

            services.Configure<NotificationHubConfiguration>(Configuration.GetSection("NotificationHub"));

            services.AddControllersWithViews();
            services.AddRazorPages();

            services.Configure<CookiePolicyOptions>(
                options =>
                {
                    // This lambda determines whether user consent for non-essential cookies is needed for a given request.
                    options.CheckConsentNeeded = context => true;
                    options.MinimumSameSitePolicy = SameSiteMode.None;
                });

            services.ConfigureApplicationCookie(
                options =>
                {
                    options.Cookie.SameSite = SameSiteMode.None;
                });

            services.Configure<DataProtectionTokenProviderOptions>(
                options => options.TokenLifespan = TimeSpan.FromDays(7));

            services.Configure<IdentityOptions>(
                options =>
                {
                    // Default Password settings.
                    options.Password.RequireDigit = false;
                    options.Password.RequireLowercase = false;
                    options.Password.RequireNonAlphanumeric = false;
                    options.Password.RequireUppercase = false;
                    options.Password.RequiredLength = 6;
                });

            services.AddCors(
                options => options.AddPolicy(
                    MyAllowSpecificOrigins,
                    builder =>
                    {
                        // builder.WithOrigins("https://app.sleekflow.io",
                        //                    "https://d2k21dzzygonnm.cloudfront.net",
                        //                    "https://ds3954owgium6.cloudfront.net/",
                        //                    "http://localhost:3000")
                        //                .AllowAnyHeader()
                        //                .AllowAnyMethod();
                        builder.WithOrigins("*")
                            .AllowAnyHeader()
                            .AllowAnyMethod();
                    }));

            // services.AddHangfire(x => x.UseSqlServerStorage(Configuration.GetConnectionString("HangFireConnection"), new SqlServerStorageOptions
            // {
            //    QueuePollInterval = TimeSpan.FromSeconds(30)
            // }));
            var options = new RedisStorageOptions
            {
                Prefix = "hangfire:sleekflow:"
            };

            var hangfireWorkerCount = int.TryParse(
                Configuration["Hangfire:WorkerCount"],
                out var configuredHangfireWorkerCount)
                ? configuredHangfireWorkerCount
                : 20;

            var hangfireQueue = new List<string>()
            {
                HangfireQueues.High, HangfireQueues.Medium, HangfireQueues.Low, HangfireQueues.Default
            };

            if (!string.IsNullOrEmpty(Configuration["HangfireQueues:DisableInstances"]))
            {
                var disableInstances = Configuration["HangfireQueues:DisableInstances"].Split(",").ToList();
                foreach (var disableInstance in disableInstances)
                {
                    hangfireQueue.Remove(disableInstance);
                }
            }

            services.AddSingleton<IHangfireMeters, HangfireMeters>();
            services.AddHangfire(
                (provider, config) =>
                {
                    config.UseRedisStorage(Configuration["redis:connectionString"], options)
                        .WithJobExpirationTimeout(TimeSpan.FromHours(8));

                    config.UseSerializerSettings(
                        new JsonSerializerSettings()
                        {
                            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                            MaxDepth = 80,
                        });
                    config.UseFilter(new BackgroundJobFilterAttribute(provider.GetService<IHangfireMeters>()));
                });
            services.AddHangfireServer(
                x =>
                {
                    x.WorkerCount = hangfireWorkerCount;

                    x.Queues = hangfireQueue.ToArray();
                });

            services.AddDbContext<ApplicationDbContext>(
                dbContextOptionsBuilder =>
                    dbContextOptionsBuilder
                        .UseSqlServer(
                            new SqlConnection(Configuration.GetConnectionString("DefaultConnection")),
                            sqlOptions =>
                                sqlOptions.EnableRetryOnFailure(10, TimeSpan.FromSeconds(30), null)));
            services.AddIdentity<ApplicationUser, IdentityRole>()
                .AddEntityFrameworkStores<ApplicationDbContext>()
                .AddTokenProvider(
                    SleekflowTokenProviderOptions.InviteTokenProviderName,
                    typeof(InvitationTokenProvider))
                .AddDefaultTokenProviders();

            // Read only database context
            services.AddDbContext<ApplicationReadDbContext>(
                optionsAction =>
                    optionsAction.UseSqlServer(new SqlConnection(Configuration.GetConnectionString("ReadConnection"))));
            services.AddScoped<ISleekflowUserService, SleekflowUserService>();
            services.AddScoped<ReadOnlyEndpointAttribute>();
            services.AddScoped<IDbContextService, DbContextService>();
            services.AddScoped<IPersistenceContext, PersistenceContext>();
            services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

            // Analytic DB
            services.AddDbContext<AnalyticDbContext>(
                optionsAction =>
                    optionsAction.UseSqlServer(new SqlConnection(Configuration.GetConnectionString("AnalyticDbConnection"))));

            services
                .AddSignalR()
                .AddNewtonsoftJsonProtocol(
                    options =>
                    {
                        options.PayloadSerializerSettings.DateTimeZoneHandling =
                            Newtonsoft.Json.DateTimeZoneHandling.Utc;
                        options.PayloadSerializerSettings.MaxDepth = 48;
                    })
                .AddAzureSignalR(
                    options =>
                    {
                        options.GracefulShutdown.Mode = GracefulShutdownMode.MigrateClients;
                        options.GracefulShutdown.Timeout = TimeSpan.FromSeconds(30);
                        options.ClaimsProvider = context => context.User.Claims.Where(
                            c => new List<string>
                            {
                                "https://app.sleekflow.io/name",
                                "https://app.sleekflow.io/user_id",
                                "https://app.sleekflow.io/user_name",
                                "https://app.sleekflow.io/email",
                                "https://app.sleekflow.io/login_as_user_id",
                                "https://app.sleekflow.io/login_as_user",
                                "sub",
                            }.Exists(v => v == c.Type));
                    });
            services.Configure<HostOptions>(
                options => options.ShutdownTimeout = TimeSpan.FromSeconds(30));

            services
                .AddHttpClient(HttpClientHandlerName.Default)
                .ConfigurePrimaryHttpMessageHandler(
                    () => new HttpClientHandler
                    {
                        AllowAutoRedirect = false,
                    });

            services
                .AddHttpClient(HttpClientHandlerName.DefaultAllowRedirect);

            services.AddAutoMapper(typeof(Startup));
            services.AddMvc()
                .AddNewtonsoftJson(
                    options =>
                    {
                        options.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                        options.SerializerSettings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
                    });

            services.AddTransient<TokenManagerMiddleware>();
            services.AddTransient<ITokenManager, TokenManager>();
            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            var jwtSection = Configuration.GetSection("Tokens");
            var jwtOptions = new JwtOptions();
            jwtSection.Bind(jwtOptions);

            // var signingKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(Configuration["Tokens:Key"]));
            services
                .AddAuthentication(
                    options =>
                    {
                        options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                        options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                    })
                .AddJwtBearer(
                    config =>
                    {
                        config.RequireHttpsMetadata = false;
                        config.SaveToken = true;
                        config.TokenValidationParameters = new TokenValidationParameters()
                        {
                            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtOptions.Key)),
                            ValidateAudience = true,
                            ValidAudience = jwtOptions.Audience,
                            ValidateIssuer = true,
                            ValidIssuer = jwtOptions.Issuer,
                            ValidateLifetime = true,
                            ValidateIssuerSigningKey = true
                        };
                    });

            services
                .AddResponseCompression(
                    options =>
                    {
                        options.Providers.Add<GzipCompressionProvider>();
                        options.Providers.Add<BrotliCompressionProvider>();
                        options.EnableForHttps = true;
                    })
                .Configure<BrotliCompressionProviderOptions>(options => { options.Level = CompressionLevel.Fastest; })
                .Configure<GzipCompressionProviderOptions>(options => { options.Level = CompressionLevel.Fastest; });

            services.Configure<JwtOptions>(jwtSection);

            services.AddSingleton<ITelemetryInitializer, CustomTelemetryInitializer>();

            ConfigureSleekflowServices(services, Configuration);
        }

        public static void ConfigureSleekflowServices(IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<ISleekFlowEnvironment, SleekFlowEnvironment>();

#if !SWAGGERGEN
            var configOptions = ConfigurationOptions.Parse(configuration["redis:caching:connectionString"]);
            configOptions.SyncTimeout = 10000;
            var connectionMultiplexer = ConnectionMultiplexer.Connect(configOptions);
            services.AddStackExchangeRedisCache(
                r =>
                {
                    r.ConnectionMultiplexerFactory =
                        () => Task.FromResult<IConnectionMultiplexer>(connectionMultiplexer);
                });
            services.AddSingleton<IConnectionMultiplexer>(connectionMultiplexer);
#endif

            services.AddScoped<AuthoriseUserMiddleware>();

            #region UserProfileServices

            services.AddScoped<IContactUpsertService, ContactUpsertService>();
            services.AddScoped<IContactCacheService, ContactCacheService>();

            // Legacy
            services.AddScoped<IDbUserProfileService, DbUserProfileService>();
            services.AddScoped<IUserProfileService, UserProfileService>();
            services.AddScoped<IUserProfileImportService, UserProfileImportService>();
            // User Profile Raw SQL Service/Operations DEPENDENCY INJECTION
            services.AddScoped<IUserProfileSqlService, UserProfileSqlService>();

            #endregion

            services.AddScoped<IConversationMessageService, ConversationMessageService>();
            services.AddScoped<IChannelIdentityIdMigrator, ChannelIdentityIdMigrator>();
            services.AddScoped<IConversationAssigneeService, ConversationAssigneeService>();
            services.AddScoped<IConversationSummaryService, ConversationSummaryService>();

            services.AddScoped<IBroadcastHistoriesOperation, BroadcastHistoriesOperation>();
            services.AddScoped<IChannelOperation, ChannelOperation>();
            services.AddScoped<ICustomFieldOperation, CustomFieldOperation>();
            services.AddScoped<IDateTimeOperation, DateTimeOperation>();
            services.AddScoped<IHashtagOperation, HashtagOperation>();
            services.AddScoped<IManageableOperation, ManageableOperation>();
            services.AddScoped<IShopifyOperation, ShopifyOperation>();
            services.AddScoped<ISignalRService, SignalRService>();
            services.AddScoped<IEmailNotificationService, EmailNotificationService>();
            services.AddScoped<ICompanyService, CompanyService>();
            services.AddScoped<ITimezoneAwareMrrCalculationService, TimezoneAwareMrrCalculationService>();
            services.AddScoped<IBillRecordRevenueCalculatorService, BillRecordRevenueCalculatorService>();
            services.AddScoped<ICompanyRepository, CompanyRepository>();
            services.AddScoped<ICompanyRegionalInfoRepository, CompanyRegionalInfoRepository>();
            services.AddScoped<ICompanyApiKeyRepository, CompanyApiKeyRepository>();
            services.AddScoped<ICancelSubscriptionRecordRepository, CancelSubscriptionRecordRepository>();
            services.AddScoped<ICompanyStaffControllerService, CompanyStaffControllerService>();
            services.AddScoped<IUserRoleStaffRepository, UserRoleStaffRepository>();
            services.AddScoped<ICoreService, CoreService>();
            services.AddScoped<ICountryService, CountryService>();
            services.AddScoped<ICountryRepository, CountryRepository>();
            services.AddScoped<IBroadcastService, BroadcastService>();
            services.AddScoped<IBroadcastChannelService, BroadcastChannelService>();
            services.AddScoped<IBroadcastMigrationService, BroadcastMigrationService>();
            services.AddScoped<ICoreFieldService, CoreFieldService>();
            services.AddScoped<IAutomationService, AutomationService>();
            services.AddScoped<IGoogleAuthService, GoogleAuthService>();
            services.AddScoped<IShopifyAuthService, ShopifyAuthService>();
            services.AddScoped<ILeadAdsServiceService, LeadAdsService>();
            services.AddScoped<ICompanyUsageService, CompanyUsageService>();
            services.AddScoped<ICompanyUsageCycleCalculator, CompanyUsageCycleCalculator>();
            services.AddScoped<IUsageCycleCalculator, UsageCycleCalculator>(_ => new UsageCycleCalculator(TimeProvider.System));
            services.AddScoped<ICompanySubscriptionService, CompanySubscriptionService>();
            services.AddScoped<ICompanyOfferService, CompanyOfferService>();
            services.AddScoped<ICompanyBillRecordService, CompanyBillRecordService>();
            services.AddScoped<ICompanyBillRecordRepository, CompanyBillRecordRepository>();
            services.AddScoped<ICompanyBillRecordCronJobService, CompanyBillRecordCronJobService>();
            services.AddScoped<ICompanyTeamService, CompanyTeamService>();
            services.AddScoped<ICompanyTeamAssignmentQueueService, CompanyTeamAssignmentQueueService>();
            services.AddScoped<ICompanyAssignmentQueueService, CompanyAssignmentQueueService>();
            services.AddScoped<IMediaProcessService, MediaProcessService>();
            services.AddScoped<IMediaEncodingService, MediaEncodingService>();
            services.AddScoped<IGoogleTranscoderService, GoogleTranscoderService>();
            services.AddScoped<IGoogleTranscoderTemplateService, GoogleTranscoderTemplateService>();
            services.AddScoped<IGoogleStorageMediaService, GoogleStorageMediaService>();
            services.AddScoped<ISuzukiService, SuzukiService>();
            services.AddScoped<ISuzukiUtilityService, SuzukiUtilityService>();
            services.AddScoped<ISuzukiUatService, SuzukiUatService>();
            services.AddScoped<IShopifyService, ShopifyService>();
            services.AddScoped<IShopifySqlService, ShopifySqlService>();
            services.AddScoped<IAnalyticsService, AnalyticsService>();
            services.AddScoped<ITwilioService, TwilioService>();
            services.AddScoped<ICacheManagerService, CacheManagerService>();
            services.AddScoped<ICompanyInfoCacheService, CompanyInfoCacheService>();
            services.AddScoped<ILockService, LockService>();
            services.AddScoped<IBackgroundTaskService, BackgroundTaskService>();
            services.AddScoped<IConversationHashtagService, ConversationHashtagService>();
            services.AddScoped<IFacebookService, FacebookService>();
            services.AddScoped<IInstagramService, InstagramService>();
            services.AddScoped<IFacebookInstagramMessageService, FacebookInstagramMessageService>();
            services.AddScoped<IShareLinkService, ShareLinkService>();
            services.AddScoped<ICoreWhatsApp360DialogPartnerAuthService, CoreWhatsApp360DialogPartnerAuthService>();
            services.AddScoped<IWhatsappCloudApiService, WhatsappCloudApiService>();
            services.AddScoped<IInternalWhatsappCloudApiService, InternalWhatsappCloudApiService>();
            services.AddScoped<IAuditHubAuditLogService, AuditHubAuditLogService>();
            services.AddScoped<IExtendedMessageFileService, ExtendedMessageFileService>();
            services.AddScoped<IJourneyBuilderService, JourneyBuilderService>();
            services.AddScoped<IStripeAuthenticationService, StripeAuthenticationService>();
            services.AddScoped<IChannelMessageHandler, WhatsappCloudApiChannelMessageHandler>();
            services.AddScoped<IChannelMessageHandler, Whatsapp360DialogChannelMessageHandler>();
            services.AddScoped<IChannelMessageHandler, WhatsappTwilioChannelMessageHandler>();
            services.AddScoped<IChannelMessageHandler, FacebookChannelMessageHandler>();
            services.AddScoped<IChannelMessageHandler, InstagramChannelMessageHandler>();
            services.AddScoped<IChannelMessageHandler, WechatChannelMessageHandler>();
            services.AddScoped<IChannelMessageHandler, LineChannelMessageHandler>();
            services.AddScoped<IChannelMessageHandler, TelegramChannelMessageHandler>();
            services.AddScoped<IChannelMessageHandler, ViberChannelMessageHandler>();
            services.AddScoped<IChannelMessageHandler, SmsChannelMessageHandler>();
            services.AddScoped<IChannelMessageHandler, EmailChannelMessageHandler>();
            services.AddScoped<IChannelMessageHandler, WebChannelMessageHandler>();
            services.AddScoped<IChannelMessageHandler, TiktokChannelMessageHandler>();
            services.AddScoped<IChannelMessageHandler, LiveChatV2ChannelMessageHandler>();
            services.AddScoped<IChannelMessageProvider, ChannelMessageProvider>();
            services.AddScoped<IMessageChannelSwitcher, MessageChannelSwitcher>();
            services.AddScoped<IConversationMessageV2Service, ConversationMessageV2Service>();
            services.AddScoped<IChannelIdentityIdRepository, ChannelIdentityIdRepository>();
            services.AddScoped<ICompanyStaffOverviewService, CompanyStaffOverviewService>();
            services.AddScoped<IStaffRemovalService, StaffRemovalService>();

            services.AddScoped<IMobilePlatformDetectionService, MobilePlatformDetectionService>();

            // Analytics Turning
            services.AddScoped<IAnalyticsSqlService, AnalyticsSqlService>();
            services.AddScoped<IConversationSqlService, ConversationSqlService>();
            services.AddScoped<IConversationMessageSqlService, ConversationMessageSqlService>();

            services.AddScoped<IContactDeletionConfig, ContactDeletionConfig>();
            services.AddScoped<IUserProfileSafeDeleteService, UserProfileSafeDeleteService>();
            services.AddScoped<IMetaCommentTriggerHandler, MetaCommentTriggerHandler>();

            #region FacebookChannel

            services.AddScoped<IFacebookChannelService, FacebookChannelService>();
            services.AddScoped<IFacebookConfigRepository, FacebookConfigRepository>();
            services.AddScoped<IFacebookChannelCronjobService, FacebookChannelCronjobService>();

            #endregion

            #region InstagramChannel

            services.AddScoped<IInstagramChannelService, InstagramChannelService>();
            services.AddScoped<IInstagramChannelRepository, InstagramChannelRepository>();

            #endregion

            #region LiveChatV2

            services.AddScoped<ILiveChatV2SenderService, LiveChatV2SenderService>();
            services.AddScoped<ILiveChatV2ConfigService, LiveChatV2ConfigService>();
            services.AddScoped<ILiveChatV2MessageService, LiveChatV2MessageService>();
            services.AddScoped<ILiveChatV2TrackingService, LiveChatV2TrackingService>();
            services.AddScoped<ILiveChatV2SenderReadOnlyRepository, LiveChatV2SenderReadOnlyRepository>();
            services.AddScoped<ILiveChatV2AuthManager, LiveChatV2AuthManager>();

            #endregion

            #region CustomModule

            services.AddScoped<IZDotComService, ZDotComService>();

            #endregion

            #region CrmHub

            services.RegisterCrmHubServices(configuration);

            #endregion

            #region CommerceHub

            services.RegisterCommerceHubServices(configuration);

            #endregion

            #region FlowHub

            services.RegisterFlowHubServices(configuration);

            #endregion

            #region IntelligentHub

            services.RegisterIntelligentHubServices(configuration);

            #endregion

            #region MessagingHub

            services.RegisterMessagingHubServices(configuration);

            #endregion

            #region PublicApiGateway

            services.RegisterPublicApiGatewayServices(configuration);

            #endregion

            #region AuditHub

            services.RegisterAuditHubServices(configuration);

            #endregion

            #region TenantHub

            services.RegisterTenantHubServices(configuration);

            #endregion

            #region UserEventHub

            services.RegisterUserEventHubServices(configuration);

            #endregion

            #region ShareHub

            services.RegisterShareHubServices(configuration);

            #endregion

            #region WebhookHub

            services.RegisterWebhookHubServices(configuration);

            #endregion

            #region TicketingHUb

            services.RegisterTicketingHubServices(configuration);

            #endregion

            #region InternalIntegrationHub

            services.RegisterInternalIntegrationHubServices(configuration);

            #endregion

            services.AddSingleton<IExternalFileService, ExternalFileService>();

            services.AddSingleton<IUploadService, UploadService>();
            services.AddSingleton<IAzureBlobStorageService, AzureBlobStorageService>();

            services.AddCustomSwaggerGen();

            services.AddHubSpot(
                hubSpotOptions =>
                {
                    hubSpotOptions.Auth =
                        new InternalHubSpotApiKeyClientAuth(configuration["HubSpot:InternalHubSpotApiKey"]);
                    hubSpotOptions.UseHttpLogging = true;
                });
            services.RegisterHubSpotObjectApi<InternalHubSpotCompany, HubSpotCompanyApi<InternalHubSpotCompany>>();
            services
                .RegisterHubSpotObjectApi<InternalHubSpotAnalyticCompany,
                    HubSpotCompanyApi<InternalHubSpotAnalyticCompany>>();
            services
                .RegisterHubSpotObjectApi<InternalHubSpotCloudApiUsageCompany,
                    HubSpotCompanyApi<InternalHubSpotCloudApiUsageCompany>>();
            services.RegisterHubSpotObjectApi<InternalHubSpotDeal, HubSpotDealApi<InternalHubSpotDeal>>();
            services.RegisterHubSpotObjectApi<InternalHubSpotContact, HubSpotContactApi<InternalHubSpotContact>>();
            services
                .RegisterHubSpotObjectApi<HubSpotWhatsAppApplicationTicket,
                    HubSpotTicketApi<HubSpotWhatsAppApplicationTicket>>();
            services.RegisterHubSpotObjectApi<HubSpotSupportTicket, HubSpotTicketApi<HubSpotSupportTicket>>();
            services.RegisterHubSpotObjectApi<HubSpotCancelPlanTicket, HubSpotTicketApi<HubSpotCancelPlanTicket>>();
            services.RegisterHubSpotObjectApi<HubSpotSalesTicket, HubSpotTicketApi<HubSpotSalesTicket>>();
            services.RegisterHubSpotObjectApi<HubSpotPartnershipTicket, HubSpotTicketApi<HubSpotPartnershipTicket>>();
            services.RegisterHubSpotObjectApi<HubSpotReviewTicket, HubSpotTicketApi<HubSpotReviewTicket>>();
            services
                .RegisterHubSpotObjectApi<HubSpotMigrateTo360DialogTicket,
                    HubSpotTicketApi<HubSpotMigrateTo360DialogTicket>>();
            services.RegisterHubSpotObjectApi<HubSpotPaymentBetaTicket, HubSpotTicketApi<HubSpotPaymentBetaTicket>>();
            services.AddScoped<IInternalHubSpotService, InternalHubSpotService>();
            services.AddScoped<IInternalHubspotRepository, InternalHubspotRepository>();
            services.AddScoped<IInternalDataSnapshotService, InternalDataSnapshotService>();
            services.AddScoped<IInternalCompanyDataRepository, InternalCompanyDataRepository>();
            services.AddScoped<IWhatsApp360DialogService, WhatsApp360DialogService>();
            services.AddScoped<ILineService, LineService>();
            services.AddScoped<ICoreWhatsApp360DialogUsageService, CoreWhatsApp360DialogUsageService>();
            services.AddScoped<IInternalGoogleCloudStorage, InternalGoogleCloudStorage>();

            services.AddScoped<ISubscriptionPlanService, SubscriptionPlanService>();
            services.AddScoped<ISubscriptionControllerService, SubscriptionControllerService>();
            services.AddScoped<ISubscriptionCountryTierDeterminer, SubscriptionCountryTierDeterminer>();
            services.AddScoped<ISubscriptionPlanRepository, SubscriptionPlanRepository>();
            services
                .AddScoped<ICustomSubscriptionPlanTranslationMapRepository,
                    CustomSubscriptionPlanTranslationMapRepository>();
            services.AddScoped<IDefaultSubscriptionPlanIdGetter, DefaultSubscriptionPlanIdGetter>();
            services.AddScoped<IResellerBillingService, ResellerBillingService>();
            services.AddScoped<IResellerBaseService, ResellerBaseService>();
            services.AddScoped<IResellerPortalAuthenticationService, ResellerPortalAuthenticationService>();
            services.AddScoped<IResellerStaffService, ResellerStaffService>();
            services.AddScoped<IResellerAccountService, ResellerAccountService>();
            services.AddScoped<IResellerSubscriptionService, ResellerSubscriptionService>();
            services.AddScoped<IResellerWhatsappCloudApiService, ResellerWhatsappCloudApiService>();
            services.AddScoped<IPowerflowManageResellerRepository, PowerflowManageResellerRepository>();
            services.AddScoped<IResellerPortalRepository, ResellerPortalRepository>();
            services.AddScoped<IInternalAnalyticService, InternalAnalyticService>();
            services.AddScoped<ISleekPayService, SleekPayService>();
            services.AddScoped<IDemoConversationService, DemoConversationService>();

            services.AddScoped<IWhatsappTemplateQuickReplyCallbackService, WhatsappTemplateQuickReplyCallbackService>();
            services.AddScoped<IAutomationRuleService, AutomationRuleRuleService>();
            services.AddScoped<IWhatsappCloudApiProductCatalogService, WhatsappCloudApiProductCatalogService>();
            services.AddScoped<IExtendedMessageFileService, ExtendedMessageFileService>();

            services.AddScoped<IStripeAuthenticationConfig, StripeAuthenticationConfig>();
            services.AddScoped<IStripeClients, StripeClients>();
            services.AddScoped<IStripeServicesFactory, StripeServicesFactory>();
            services.AddScoped<IStripeSubscriptionService, StripeSubscriptionService>();
            services.AddScoped<IStripeInvoiceService, StripeInvoiceService>();
            services.AddScoped<IStripeCustomerService, StripeCustomerService>();
            services.AddScoped<IStripeProductService, StripeProductService>();
            services.AddScoped<IStripeCheckoutSessionService, StripeCheckoutSessionService>();

            services.AddScoped<IMessagingChannelService, MessagingChannelService>();
            services.AddScoped<IChannelMessageValidator, ChannelMessageValidator>();
            services.AddScoped<IConversationValidator, ConversationValidator>();

            services.AddScoped<IConversationService, ConversationService>();
            services.AddScoped<IConversationResolver, ConversationResolver>();
            services.AddScoped<IConversationMessageRepository, ConversationMessageRepository>();
            services.AddScoped<IConversationReadOnlyRepository, ConversationReadOnlyRepository>();

            #region Conversation Permission Control Services

            services.AddScoped<IRbacConversationPermissionManager, RbacConversationPermissionManager>();
            services.AddScoped<IRbacDefaultChannelPermissionManager, RbacDefaultChannelPermissionManager>();
            services.AddScoped<IConversationPermissionControlService, ConversationPermissionControlService>();
            services.AddScoped<IConversationAccessControlManager, ConversationAccessControlManager>();
            services.AddScoped<IAccessControlAggregationService, AccessControlAggregationService>();
            services.AddScoped<IInboxSettingManager, InboxSettingManager>();
            services.AddScoped<IRbacNotificationManager, RbacNotificationManager>();

            #endregion

            services.AddScoped<ITokenService, TokenService>();

            services.AddScoped<IMfaService, MfaService>();

            services.AddSingleton(_ => new IPGeolocationAPI(configuration["IpLookUp:Key"]));
            services.AddScoped<IIpLocationService, IpLocationService>();

            services.AddScoped<ITextAnalyticsService, TextAnalyticsService>();

            services.AddScoped<IFeatureRepository, FeatureRepository>();
            services.AddSingleton<IPlanDefinitionRepository, PlanDefinitionRepository>(_ => new PlanDefinitionRepository(FileDataSourcePaths.PlanDefinitions));

            services.AddScoped<IFeatureService, FeatureService>();
            services.AddScoped<IPlanDefinitionService, PlanDefinitionService>();
            services.AddScoped<ISubscriptionPlanService, SubscriptionPlanService>();
            services.AddScoped<IFeatureQuantityService, FeatureQuantityService>();
            services.AddScoped<IInternalHangfireService, InternalHangfireService>();
            services.AddScoped<IInternalSquadMetricsRepository, InternalSquadMetricsRepository>();

            services.AddScoped<ICustomObjectService, CustomObjectService>();

            services.AddScoped<IImportSpreadsheetService, ImportSpreadsheetService>();

            services.Configure<GlobalPricingOptions>(configuration.GetSection(GlobalPricingOptions.GlobalPricing));
            services.Configure<FeatureFlagsOptions>(configuration.GetSection(FeatureFlagsOptions.FeatureFlags));
            services.Configure<FlowBuilderFlowEnrollmentsIncentivesOptions>(configuration.GetSection(FlowBuilderFlowEnrollmentsIncentivesOptions.FlowBuilderFlowEnrollmentsIncentives));
            services.Configure<LegacyPremiumOptInUpgradeIncentivesOptions>(configuration.GetSection(LegacyPremiumOptInUpgradeIncentivesOptions.LegacyPremiumOptInUpgradeIncentives));

            services.AddScoped<IPiiMaskingConfigRepository, PiiMaskingConfigRepository>();
            services.AddScoped<IPiiMaskingService, PiiMaskingService>();

            services.AddScoped<IUserPreferencesService, UserPreferencesService>();

            services.AddScoped<IUserProfileCronJobService, UserProfileCronJobService>();
            services.AddScoped<IContactWhatsappSenderLockService, ContactWhatsappSenderLockService>();

            services.AddScoped<IUserProfileDuplicationConfigRepository, UserProfileDuplicationConfigRepository>();

            services.AddScoped<IMeasureQueryTimeService, MeasureQueryTimeService>();

            services.AddScoped<IPartnerStackIntegrationService, PartnerStackIntegrationService>();
            services.AddScoped<IInternalPartnerStackService, InternalPartnerStackService>();
            services.AddScoped<IInternalIntegrationService, InternalIntegrationService>();
            services.AddScoped<IIrregularPlanInvoiceCalculator, IrregularPlanInvoiceCalculator>();
            services.AddScoped<IIrregularPlanInvoiceService, IrregularPlanInvoiceService>();

            services.AddScoped<IRateLimiterService, TokenBucketRateLimiterService>();
            services.AddScoped<IRateLimiterOptionService, RateLimiterOptionService>();

            services.AddScoped<IQuickReplyService, QuickReplyService>();

            services
                .AddScoped<IFacebookLeadAdsNotificationConfigRepository, FacebookLeadAdsNotificationConfigRepository>();

            services.AddScoped<IWhatsAppMessageNotificationService, WhatsAppMessageNotificationService>();

            services.AddScoped<IUserProfileDuplicatedLogRepository, UserProfileDuplicatedLogRepository>();

            services.AddScoped<IFacebookLeadAdsFormRepository, FacebookLeadAdsFormRepository>();

            services.AddScoped<IApplicationInsightsTelemetryTracer, ApplicationInsightsTelemetryTracer>();
            services.AddScoped<IUserProfileCustomFieldsDeletionService, UserProfileCustomFieldsDeletionService>();

            services.AddScoped<IIntegrationAlertConfigRepository, IntegrationAlertConfigRepository>();

            services.AddScoped<IChannelWebhookHandlerProvider, ChannelWebhookHandlerProvider>();
            services.AddScoped<IConversationMessageStatusRepository, ConversationMessageStatusRepository>();
            services.AddScoped<IWhatsapp360DialogChannelMessageStatusHandler, Whatsapp360DialogChannelMessageStatusHandler>();
            services.AddScoped<IWhatsappTwilioChannelMessageStatusHandler, WhatsappTwilioChannelMessageStatusHandler>();
            services.AddScoped<IViberMessageStatusHandler, ViberChannelMessageStatusHandler>();

            services.AddScoped<IMentionCronJobService, MentionCronJobService>();
            services.AddScoped<IMentionQueryableResolver, MentionQueryableResolver>();

            #region Analytics

            services.AddScoped<IBusinessHourConfigRepository, BusinessHourConfigRepository>();
            services.AddScoped<IBusinessHourConfigService, BusinessHourConfigService>();
            services.AddScoped<IAnalyticsHooks, AnalyticsHooks>();

            services.AddScoped<IConversationAnalyticsMetricsRepository, ConversationAnalyticsMetricsRepository>();
            services.AddScoped<IConversationAnalyticsMetricsService, ConversationAnalyticsMetricsService>();
            services.AddScoped<ITopicAnalyticsMetricsRepository, TopicAnalyticsMetricsRepository>();
            services.AddScoped<ITopicAnalyticsMetricsService, TopicAnalyticsMetricsService>();
            services.AddScoped<IAnalyticsDataExporter, AnalyticsDataExporter>();

            // campaign analytics
            services.AddScoped<ICampaignAnalyticsService, CampaignAnalyticsService>();
            services.AddScoped<ICampaignAnalyticsRepository, CampaignAnalyticsRepository>();
            services.AddScoped<ICampaignAnalyticsDataExporter, CampaignAnalyticsExporter>();
            #endregion

            #region Rbac

            services.AddScoped<IRbacNotificationService, RbacNotificationService>();

            #endregion

            #region Conversation View Model Mappers

            services
                .AddScoped<IConversationNoCompanyResponseViewModelMapper,
                    ConversationNoCompanyResponseViewModelMapper>();

            #endregion

            services.AddScoped<IContactImportUsageService, ContactImportUsageService>();

            #region Stripe Integrations

            services.AddKeyedScoped<IStripeEventHandler, StripeSubscriptionCustomerCreatedEventHandler>($"StripeSubscription_{Events.CustomerCreated}");
            services.AddKeyedScoped<IStripeEventHandler, StripeSubscriptionCustomerUpdatedEventHandler>($"StripeSubscription_{Events.CustomerUpdated}");
            services.AddKeyedScoped<IStripeEventHandler, StripeSubscriptionInvoicePaymentFailedEventHandler>($"StripeSubscription_{Events.InvoicePaymentFailed}");
            services.AddKeyedScoped<IStripeEventHandler, StripeSubscriptionInvoicePaidEventHandler>($"StripeSubscription_{Events.InvoicePaid}");

            #endregion

            services.AddTravisBackendHealthCheck();
            services.RegisterFieldSetters();
            services.RegisterApiKeyResolvers();
            ConfigureDistributedInvocationContexts(services);
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILogger<Startup> logger)
        {
            app.UseTravisBackendHealthChecks();
            app.UseResponseCompression();
            app.UseExceptionHandler("/error");

            if (!env.IsDevelopment())
            {
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseCustomSwagger();

            app.UseStaticFiles();
            app.UseCookiePolicy();

            app.UseRouting();

            app.UseCors(MyAllowSpecificOrigins);

            app.UseAuthentication();
            app.UseAuthorization();
            app.UseDistributedInvocationMiddleware();
            app.UseMiddleware<TokenManagerMiddleware>();

            app.UseHangfireDashboard(
                "/hangfire",
                new DashboardOptions
                {
                    Authorization = new[]
                    {
                        new MyAuthorizationFilter(),
                    },
                    IgnoreAntiforgeryToken = true
                });

            app.UseAzureSignalR(
                routes =>
                {
                    routes.MapHub<Chat>("/chat");
                });

            app.UseEndpoints(
                endpoints =>
                {
                    endpoints.MapControllerRoute("default", "{controller=Home}/{action=Index}/{id?}");

                    // endpoints.MapControllers();
                });

            ConfigureSleekflowRecurringJobs(env, Configuration);
        }

        public static void ConfigureSleekflowRecurringJobs(IWebHostEnvironment env, IConfiguration configuration)
        {
            var isRecurringJobEnabled = configuration["EnvironmentFeatures:IsRecurringJobEnabled"] ?? "false";
            if (isRecurringJobEnabled != "true")
            {
                return;
            }

            var cronExpressionData = $"{0} {0} * * *";
            var cronExpression = $"{0} {18} * * *";
            var cronExpressionSub = $"{0} {19} * * *";
            var cronExpressionEveryHalfHour = $"*/30 * * * *";
            var cronExpressionEveryFifteenMins = $"*/15 * * * *";
            var cronExpressionEverySixHours = $"0 */6 * * *";

            RecurringJob.AddOrUpdate<CoreService>(x => x.DeleteBlankWebClientSenders(), cronExpression);
            RecurringJob.AddOrUpdate<CoreService>(x => x.DeleteInvalidUserProfileCustomFields(), cronExpression);
            RecurringJob.AddOrUpdate<CoreService>(x => x.CheckSubscriptionExpiration(), cronExpression);
            RecurringJob.AddOrUpdate<ICoreService>(x => x.RemoveAllUnusedCompanyAccounts(), cronExpressionSub);
            // RecurringJob.AddOrUpdate<AnalyticsService>(x => x.GenerateData(), Cron.Daily);
            RecurringJob.AddOrUpdate<CompanyUsageService>(x => x.FetchAllTwilioUsage(), cronExpression);
            RecurringJob.AddOrUpdate<IShopifyService>(
                x => x.CheckAllAbandonedCart(),
                cronExpressionEveryFifteenMins);
            RecurringJob.AddOrUpdate<ICoreService>(x => x.RemoveDuplicatedHashtag(), cronExpressionEveryHalfHour);
            // RecurringJob.AddOrUpdate<ICoreService>(
            //     x => x.RemoveUnusedBlankAutomations(),
            //     cronExpressionEveryFifteenMins);

            // RecurringJob.remo<CoreService>(x => x.RemoveUpdatedNotifications(), cronExpression);
            RecurringJob.AddOrUpdate<ICoreService>(x => x.DailyCleanUp(), cronExpression);
            RecurringJob.AddOrUpdate<IInternalDataSnapshotService>(
                x => x.CreateDailyCompanySnapshotDataAsync(),
                Cron.Daily(22)); // Everyday at 6:00 HKT / 22:00 UTC
            RecurringJob.AddOrUpdate<IInternalDataSnapshotService>(
                x => x.CreateDailySnapshotDataAsync(),
                Cron.Daily(0)); // Everyday at 8:00 am / 200:00 UTC
            RecurringJob.AddOrUpdate<IInternalDataSnapshotService>(
                x => x.UploadInternalMonthlyWhatsappCloudApiConversationUsageAnalyticSnapshot(),
                Cron.Monthly(7, 0));
            RecurringJob.AddOrUpdate<IInternalHubSpotService>(
                x => x.UpdateAndSyncAllCompanies(),
                Cron.Daily(0, 30)); // Everyday at 8:30 am HKT
            RecurringJob.AddOrUpdate<IInternalHubSpotService>(
                x => x.UpdateAndSyncAllCloudApiCredits(),
                Cron.Daily(0)); // Everyday at 8:00 am HKT

            RecurringJob.AddOrUpdate<ICoreWhatsApp360DialogUsageService>(
                x => x.UpdateDirectPaymentAccountBalance(null),
                Cron.Daily(0));
            RecurringJob.AddOrUpdate<ICoreWhatsApp360DialogUsageService>(
                x => x.MonthlyPhoneNumberBillings(null),
                Cron.Monthly(1, 0));
            RecurringJob.AddOrUpdate<ICoreWhatsApp360DialogUsageService>(
                x => x.MonthlyConversationBillings(null),
                Cron.Monthly(1, 1));
            RecurringJob.AddOrUpdate<ICoreWhatsApp360DialogUsageService>(
                x => x.DailyConversationBillings(null),
                Cron.Hourly(0));
            RecurringJob.AddOrUpdate<ICoreWhatsApp360DialogUsageService>(
                x => x.CurrentPhoneNumberBillings(null),
                Cron.Weekly(0));
            RecurringJob.AddOrUpdate<IWhatsApp360DialogService>(x => x.UpdateAllChannelStatus(), Cron.Daily(0));
            RecurringJob.AddOrUpdate<IWhatsappCloudApiService>(
                x => x.RefreshWhatsappCloudApiConfigsDetail(null),
                Cron.Daily(0));
            RecurringJob.AddOrUpdate<IInternalHubSpotService>(
                x => x.SyncInternalContactOwnerMapping(),
                Cron.Daily(0));

            RecurringJob.AddOrUpdate<IResellerBillingService>(x => x.MonthlyResellerBilling(null), Cron.Daily(0));
            RecurringJob.AddOrUpdate<IInternalHubSpotService>(
                x => x.UpdateLastMonthCompanyBreakdownStatuses(null),
                Cron.Monthly(2, 0));
            RecurringJob.AddOrUpdate<ILineService>(x => x.SyncAllCompaniesLineUsageInfo(), Cron.Hourly);

            // Remove failed job with skipping first 10000
            RecurringJob.AddOrUpdate<IInternalHangfireService>(x => x.RemoveFailedJobs(10000), Cron.Daily);

            // BackgroundJob.Enqueue<IAutomationService>(x => x.ScheduledAutomation("aab30bb3-10cd-4c87-9e3c-2d481266c623"));
            RecurringJob.AddOrUpdate<IAnalyticsService>(
                x => x.DailyExportCustomFieldAnalyticCsv(),
                Cron.Daily(16, 01));

            RecurringJob.AddOrUpdate<IFacebookChannelCronjobService>(
                x => x.UpdateAllFacebookChannelStatusesAsync(),
                cronExpressionEverySixHours);

            RecurringJob.AddOrUpdate<ICoreService>(x => x.BackgroundMergeDuplicatedContacts(), Cron.Hourly);

            RecurringJob.AddOrUpdate<IInternalPartnerStackService>(
                x => x.SyncAllMrrToPartnerStack(),
                Cron.Monthly(28, 0));

            RecurringJob.AddOrUpdate<IInternalPartnerStackService>(
                x => x.SendContractEndDateNotifications(),
                Cron.Daily(1));

            RecurringJob.AddOrUpdate<IUserProfileCronJobService>(
                x => x.EnqueueBulkImportContactsTasksAsync(),
                cronExpressionEveryFifteenMins);

            RecurringJob.AddOrUpdate<IMentionCronJobService>(
                x => x.RemoveExpiredMentionRecord(),
                cronExpressionEverySixHours);

            // Process irregular plan invoices daily
            RecurringJob.AddOrUpdate<IIrregularPlanInvoiceService>(
                x => x.ProcessDailyIrregularPlanInvoicesAsync(),
                Cron.Daily(1)); // Run at 1 AM UTC (9 AM HKT) to process invoices

            // Process notice period bill records daily
            RecurringJob.AddOrUpdate<ICompanyBillRecordCronJobService>(
                x => x.ProcessDailyNoticePeriodBillRecordsAsync(),
                Cron.Daily(1)); // Run at 1 AM UTC (9 AM HKT) to process invoices

            ConfigureContactDeleteRecurringJobs(configuration);
        }

        public static void ConfigureContactDeleteRecurringJobs(IConfiguration configuration)
        {
            var cronExpression = "0 18 * * *";

            var contactDeletionStrategyConfig = new ContactDeletionConfig(configuration);

            if (contactDeletionStrategyConfig.IsContactSafeDeleteEnabled)
            {
                RecurringJob.AddOrUpdate<IUserProfileSafeDeleteService>(
                    x => x.CleanUpExceededBufferPeriodUserProfilesAsync(),
                    cronExpression);
                RecurringJob.AddOrUpdate<ICoreService>(x => x.DeleteDeletedUserProfiles(), Cron.Never);
            }
            else
            {
                RecurringJob.AddOrUpdate<ICoreService>(x => x.DeleteDeletedUserProfiles(), cronExpression);
                RecurringJob.AddOrUpdate<IUserProfileSafeDeleteService>(
                    x => x.CleanUpExceededBufferPeriodUserProfilesAsync(),
                    Cron.Never);
            }
        }

        public static void ConfigureDistributedInvocationContexts(IServiceCollection services)
        {
            services.AddScoped<IDistributedInvocationContextService, DistributedInvocationContextService>();
        }

        public class MyAuthorizationFilter : IDashboardAuthorizationFilter
        {
            public bool Authorize(DashboardContext context)
            {
                var httpContext = context.GetHttpContext();
                if (httpContext.Connection.RemoteIpAddress?.ToString() == "127.0.0.1" ||
                    httpContext.Connection.RemoteIpAddress?.ToString() == "::1" ||
                    httpContext.Connection.RemoteIpAddress?.ToString() == "************" ||
                    httpContext.Connection.RemoteIpAddress?.ToString() == "*************" ||

                    // **************
                    httpContext.Connection.RemoteIpAddress?.ToString() == "**************" ||
                    httpContext.Request.Headers["X-Azure-ClientIP"].ToString() == "**************" ||
                    httpContext.Request.Headers["X-Azure-SocketIP"].ToString() == "**************" ||
                    httpContext.Request.Headers["X-Forwarded-For"].ToString() == "**************" ||
                    httpContext.Request.Headers["X-Client-IP"].ToString() == "**************" ||

                    // **************
                    httpContext.Connection.RemoteIpAddress?.ToString() == "**************" ||
                    httpContext.Request.Headers["X-Azure-ClientIP"].ToString() == "**************" ||
                    httpContext.Request.Headers["X-Azure-SocketIP"].ToString() == "**************" ||
                    httpContext.Request.Headers["X-Forwarded-For"].ToString() == "**************" ||
                    httpContext.Request.Headers["X-Client-IP"].ToString() == "**************")
                {
                    // Allow all authenticated users to see the Dashboard (potentially dangerous).
                    // dangerous
                    return true;
                }

                return false;
            }
        }

        public class CustomTelemetryInitializer : ITelemetryInitializer
        {
            private const string ComputerName = "COMPUTERNAME";
            private readonly IConfiguration _configuration;

            public CustomTelemetryInitializer(IConfiguration configuration)
            {
                _configuration = configuration;
            }

            public void Initialize(ITelemetry telemetry)
            {
                if (_configuration[ComputerName] is not null)
                {
                    telemetry.Context.Cloud.RoleInstance = _configuration[ComputerName];
                }
            }
        }
    }
}
