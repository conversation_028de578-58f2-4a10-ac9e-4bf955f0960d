﻿using System.Reflection;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Auth0.ManagementApi.Models;
using Auth0.ManagementApi.Models.Users;
using Auth0.ManagementApi.Paging;
using LinqKit;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.EntityFrameworkCore;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Auth0.Services;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Database.Filters;
using Travis_backend.Database.Services;
using Travis_backend.TenantHubDomain.Exceptions;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Managing the Whatsapp Application
/// Reference to InternalCmsDiveController.cs.
/// </summary>
[Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
[Route("/internal/auth0/[action]")]
public class InternalCmsAuth0Controller : InternalControllerBase
{
    private readonly SleekflowUserManager _userManager;
    private readonly BaseDbContext _appDbContext;

    /// <summary>
    ///
    /// </summary>
    /// <param name="userManager"></param>
    /// <param name="dbContextService"></param>
    public InternalCmsAuth0Controller(
        UserManager<ApplicationUser> userManager,
        IDbContextService dbContextService)
        : base(userManager)
    {
        _appDbContext = dbContextService.GetDbContext();
        _userManager = (SleekflowUserManager) userManager;
    }

    /// <summary>
    /// Get Reset Company Staff Password Url.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    [Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)]
    public async Task<ActionResult<GetResetPasswordResponse>> GetResetStaffPasswordUrl(
        [FromBody]
        GetResetStaffPasswordUrlRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        ApplicationUser userIdentity = null;

        if (!string.IsNullOrEmpty(request.Email))
        {
            userIdentity = await _userManager.FindByEmailAsync(request.Email);
        }
        else if (!string.IsNullOrEmpty(request.UserId))
        {
            userIdentity = await _userManager.FindByIdAsync(request.UserId);
        }

        if (userIdentity == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "User not found"
                });
        }

        var url = await _userManager.GeneratePasswordResetUrlAsync(userIdentity);

            return Ok(
                new GetResetPasswordResponse
                {
                    Url = url
                });
    }


    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<GetAuth0UsersResponse?>> GetAuth0Users(
        [FromBody]
        GetAuth0UsersRequest getAuth0UsersRequest)
    {
        if (!string.IsNullOrEmpty(getAuth0UsersRequest.Query))
        {
            var result = await _userManager.GetAuth0UsersByQuery(getAuth0UsersRequest.Query);

            return Ok(result);
        }

        var searchList = new List<string>();
        if (!string.IsNullOrEmpty(getAuth0UsersRequest.UserId))
        {
            searchList.Add($"app_metadata.sleekflow_id:\"{getAuth0UsersRequest.UserId}\"");
        }

        if (!string.IsNullOrEmpty(getAuth0UsersRequest.Email))
        {
            searchList.Add($"email:*{getAuth0UsersRequest.Email}*");
        }

        if (!string.IsNullOrEmpty(getAuth0UsersRequest.UserName))
        {
            searchList.Add($"username:*{getAuth0UsersRequest.UserName}*");
        }

        if (!string.IsNullOrEmpty(getAuth0UsersRequest.Provider))
        {
            searchList.Add($"identities.provider:\"{getAuth0UsersRequest.Provider}\"");
        }

        if (getAuth0UsersRequest.IsSocial is not null)
        {
            searchList.Add($"identities.isSocial:{getAuth0UsersRequest.IsSocial}");
        }

        if (getAuth0UsersRequest.EmailVerified is not null)
        {
            searchList.Add($"email_verified:{getAuth0UsersRequest.EmailVerified}");
        }

        if (getAuth0UsersRequest.IsBlocked is not null)
        {
            if (!(bool) getAuth0UsersRequest.IsBlocked)
            {
                searchList.Add($"NOT blocked:true");
            }
            else
            {
                searchList.Add($"blocked:{getAuth0UsersRequest.IsBlocked}");
            }
        }

        var searchString = string.Join(" AND ", searchList);
        var searchResult = await _userManager.GetAuth0UsersByQuery(
            searchString,
            getAuth0UsersRequest?.Page ?? 0,
            getAuth0UsersRequest?.ItemPerPage ?? 50);

        return Ok(
            new GetAuth0UsersResponse()
            {
                Users = searchResult.ToList(), PageInfo = searchResult.Paging
            });
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ResponseViewModel>> SetUserEmail(
        [FromBody] SetAuth0UserEmailRequest setUserEmailRequest)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await _userManager.FindByIdAsync(setUserEmailRequest.UserId);

        if (user is null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = $"User not found"
                });
        }

        // Check update value (email) is valid.
        if (!SleekflowUserManager.IsValidEmail(setUserEmailRequest.NewEmail))
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Email not valid."
                });
        }

        var result = await _userManager.SetEmailAsync(user, setUserEmailRequest.NewEmail);
        if (!result.Succeeded)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = result.Errors?.FirstOrDefault()?.Description
                });
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "Email update completed"
            });
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ResponseViewModel>> SetUserName(
        [FromBody] SetAuth0UserNameRequest setUserNamelRequest)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await _userManager.FindByIdAsync(setUserNamelRequest.UserId);

        if (user is null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "User not found"
                });
        }

        var result = await _userManager.SetUserNameAsync(user, setUserNamelRequest.NewUserName);
        if (!result.Succeeded)
        {
            if (result.Errors.Count() > 0)
            {
                var errorMessage = string.Empty;
                foreach (var error in result.Errors)
                {
                    errorMessage = $"{errorMessage}{error.Description};";
                }

                errorMessage = errorMessage != string.Empty
                    ? errorMessage.Remove(errorMessage.Length - 1, 1)
                    : errorMessage;

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = errorMessage
                    });
            }
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "UserName update completed"
            });
    }


    [HttpPost]
    [ProducesResponseType(typeof(GetUserHistoryResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> GetUserLoginHistory([FromBody] GetUserHistoryRequest getUserHistoryRequest)
    {
        var history = await _userManager.GetLogsAsync(
            getUserHistoryRequest.Auth0UserId,
            getUserHistoryRequest.Page,
            getUserHistoryRequest.PerPage);

        var convertedHistory = history.Select(
                userHistory => new UserHistoryDetails()
                {
                    Id = userHistory.Id,
                    Date = userHistory.Date,
                    Description = userHistory.Description,
                    ClientName = userHistory.ClientName,
                    IpAddress = userHistory.IpAddress,
                    Type = userHistory.Type,
                    Strategy = userHistory.Strategy,
                    UserAgent = userHistory.UserAgent
                })
            .ToList();

        return Ok(
            new GetUserHistoryResponse()
            {
                Data = convertedHistory, PageInfo = history.Paging
            });
    }

    [HttpPost]
    [ProducesResponseType(typeof(IPagedList<AuthenticationMethod>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<IPagedList<AuthenticationMethod>>> GetUserMfaList(
        [FromBody] GetUserMfaRequest getUserMfaRequest)
    {
        var result = await _userManager.GetMfaAsync(getUserMfaRequest.Auth0UserId);

        return Ok(result);
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ResetUserMfa([FromBody] ResetUserMfaRequest resetUserMfaRequest)
    {
        var isRevoked = await _userManager.ResetMfaAsync(
            resetUserMfaRequest.Auth0UserId,
            resetUserMfaRequest.MfaId);

        if (!isRevoked)
        {
            return BadRequest("Mfa Reset failed");
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "ok"
            });
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ResponseViewModel>> SetBlockedStatus(
        [FromBody]
        SetAuth0UserBlockStatusRequest setAuth0UserBlockStatus)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Invalid Form"
                });
        }

        var user = await _userManager.FindByIdAsync(setAuth0UserBlockStatus.UserId);

        if (user is null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "User not found"
                });
        }

        var result = await _userManager.SetBlockedStatus(user, setAuth0UserBlockStatus.IsBlocked);
        if (!result)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Blocked status update failed"
                });
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "ok"
            });
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<ResponseViewModel>> SendVerificationEmail(
        [FromBody]
        SendVerificationEmailRequest sendVerificationEmailRequest)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest("Please provide user id");
        }

        var user = await _userManager.FindByIdAsync(sendVerificationEmailRequest.UserId);
        if (user is null)
        {
            return BadRequest("User not found");
        }

        await _userManager.SendVerifyEmailAsync(user);

        return Ok(
            new ResponseViewModel()
            {
                message = "ok"
            });
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<ResponseViewModel>> SetEmailConfirmed(
        [FromBody]
        SetEmailConfirmedRequest setEmailConfirmed)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest("Please provide user id");
        }

        var user = await _userManager.FindByIdAsync(setEmailConfirmed.UserId);
        if (user is null)
        {
            return BadRequest("User not found");
        }

        if (!user.EmailConfirmed)
        {
            await _userManager.SetEmailConfirmedAsync(user, true);
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "ok"
            });
    }


    [HttpPost]
    [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
    [ProducesResponseType(typeof(IsUserHaveCompanyResponse), StatusCodes.Status200OK)]
    public async Task<IsUserHaveCompanyResponse> IsUserHaveCompany([FromBody]UserHaveCompanyRequest request)
    {
        try
        {
            var staff = await _appDbContext.UserRoleStaffs.FirstOrDefaultAsync(u => u.IdentityId == request.UserId);
            return new IsUserHaveCompanyResponse(staff != null);
        }
        catch (Exception err)
        {
            return new IsUserHaveCompanyResponse(false);
        }
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> DeleteUser([FromBody]DeleteUserRequest request)
    {
        try
        {
            var user = await _userManager.FindByIdAsync(request.UserId);
            if (user is null)
            {
                return BadRequest("User not found");
            }

            var result = await _userManager.DeleteAsync(user);

            return Ok(
                new ResponseViewModel()
                {
                    message = result.Succeeded ? "ok" : "Delete failed"
                });
        }
        catch (Exception err)
        {
            return BadRequest(new ResponseViewModel() { message = err.Message});
        }
    }
}