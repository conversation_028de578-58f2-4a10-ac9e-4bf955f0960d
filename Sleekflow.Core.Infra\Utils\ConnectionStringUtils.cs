using Pulumi;
using Pulumi.AzureNative.Resources;
using Pulumi.AzureNative.Storage;
using Sleekflow.Core.Infra.Components.Configs.SleekflowCore;
using Sleekflow.Core.Infra.Components.Models;
using Cache = Pulumi.AzureNative.Cache;
using SignalRService = Pulumi.AzureNative.SignalRService;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;

namespace Sleekflow.Core.Infra.Utils;

public static class ConnectionStringUtils
{
    public static Output<string> GetRedisConnStr(Output<string> resourceGroupName, Cache.Redis redis)
    {
        var listRedisKeysOutput = Output
            .Tuple(resourceGroupName, redis.Name, redis.Id)
            .Apply(
                t => Cache.ListRedisKeys.InvokeAsync(
                    new Cache.ListRedisKeysArgs
                    {
                        ResourceGroupName = t.Item1, Name = t.Item2
                    }));
        return Output
            .Tuple(listRedisKeysOutput, redis.HostName)
            .Apply(
                t =>
                    $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False");
    }

    public static Output<string> GetStorageConnStr(
        Input<string> resourceGroupName,
        Input<string> accountName,
        GeoStorageConfig? geoStorage = null)
    {
        if (geoStorage is not null)
        {
            return Output.Create(geoStorage.ConnectionString);
        }

        // Retrieve the primary storage account key.
        var storageAccountKeys = ListStorageAccountKeys.Invoke(
            new ListStorageAccountKeysInvokeArgs
            {
                ResourceGroupName = resourceGroupName, AccountName = accountName
            });

        return storageAccountKeys.Apply(
            keys =>
            {
                var primaryStorageKey = keys.Keys[0].Value;
                // Build the connection string to the storage account.
                return Output.Format(
                    $"DefaultEndpointsProtocol=https;AccountName={accountName};AccountKey={primaryStorageKey};EndpointSuffix=core.windows.net");
            });
    }

    public static Output<string> GetSqlServerConnStr(
        Output<string> resourceGroupName,
        SqlServerProperties sqlServerProperties,
        GeoSqlDbConfig? geoSqlDbConfig,
        bool isReadOnly = false)
    {
        var databaseName = sqlServerProperties.Database.Name;
        if (geoSqlDbConfig is not null)
        {
            databaseName = Output.Create(geoSqlDbConfig.Name);
        }

        return Output.Tuple(
                sqlServerProperties.Server.Name,
                databaseName,
                sqlServerProperties.AdminUserName,
                sqlServerProperties.AdminPassword)
            .Apply(
                s =>
                {
                    var connectionString =
                        $"Server=tcp:{s.Item1}.database.windows.net,1433;Initial Catalog={s.Item2};Persist Security Info=False;User ID={s.Item3};Password={s.Item4};MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=200;Connection Lifetime=120;";
                    if (isReadOnly)
                    {
                        connectionString += "ApplicationIntent=ReadOnly;";
                    }

                    return connectionString;
                });
    }

    public static Output<string> GetSignalRConnStr(
        Output<string> resourceGroupName,
        SignalRService.SignalR signalR)
    {
        var signalRKey = Output
            .Tuple(resourceGroupName, signalR.Name)
            .Apply(
                i => SignalRService.ListSignalRKeys.InvokeAsync(
                    new SignalRService.ListSignalRKeysArgs
                    {
                        ResourceGroupName = i.Item1, ResourceName = i.Item2
                    }));

        return Output
            .Tuple(signalRKey, signalR.Version)
            .Apply(
                i => i.Item1.PrimaryConnectionString!);
    }

    public static Output<string> GetWorkspaceKey(
        Output<string> resourceGroupName,
        Output<string> logAnalyticsWorkspaceName)
    {
        return Output
            .Tuple(resourceGroupName, logAnalyticsWorkspaceName)
            .Apply(
                items => OperationalInsights.GetSharedKeys.InvokeAsync(
                    new OperationalInsights.GetSharedKeysArgs
                    {
                        ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                    })).Apply(w => w.PrimarySharedKey!);
    }

    public static Output<string> SignedBlobReadUrl(
        Blob blob,
        BlobContainer container,
        StorageAccount account,
        ResourceGroup resourceGroup)
    {
        var serviceSasToken = ListStorageAccountServiceSAS
            .Invoke(
                new ListStorageAccountServiceSASInvokeArgs
                {
                    AccountName = account.Name,
                    Protocols = HttpProtocol.Https,

                    // TODO
                    SharedAccessStartTime = "2021-01-01",

                    // TODO
                    SharedAccessExpiryTime = "2030-01-01",
                    Resource = SignedResource.C,
                    ResourceGroupName = resourceGroup.Name,
                    Permissions = Permissions.R,
                    CanonicalizedResource = Output.Format($"/blob/{account.Name}/{container.Name}"),
                    ContentType = "application/zip",
                    CacheControl = "max-age=5",
                    ContentDisposition = "inline",
                    ContentEncoding = "deflate",
                })
            .Apply(blobSas => blobSas.ServiceSasToken);

        return Output.Format(
            $"https://{account.Name}.blob.core.windows.net/{container.Name}/{blob.Name}?{serviceSasToken}");
    }
}