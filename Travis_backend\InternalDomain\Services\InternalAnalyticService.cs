﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.Configuration.Attributes;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Apis.MessagingHub.Model;
using Stripe.Reporting;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.StripeIntegrationDomain.Clients;
using Travis_backend.StripeIntegrationDomain.Configs;
using Travis_backend.StripeIntegrationDomain.Services;
using Travis_backend.TenantHubDomain.Services;
using Company = Travis_backend.CompanyDomain.Models.Company;
using DateTime = System.DateTime;

namespace Travis_backend.InternalDomain.Services;

public interface IInternalAnalyticService
{
    Task<List<CmsDailyAnalyticDto>> GetCmsAnalytic(
        DateTime start,
        DateTime end,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        List<string> companyIds = null,
        bool excludeMarkupPlan = false);

    Task<List<CmsDailyMonthlyRecurringRevenueAnalyticDto>> GetCmsDailyMonthlyRecurringRevenueAnalyticWithAnalyticDatabase(
        DateTime start,
        DateTime end,
        List<string> companyIds = null);

    Task<List<CmsDailyMonthlyRecurringRevenueAnalyticDto>> GetCmsDailyMonthlyRecurringRevenueAnalytic(
        DateTime start,
        DateTime end,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        List<string> companyIds = null,
        bool excludeMarkupPlan = false);

    Task<List<CmsDailyRevenueAnalyticDto>> GetCmsDailyRevenueAnalytic(
        DateTime start,
        DateTime end,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        List<string> companyIds = null,
        bool excludeMarkupPlan = false);

    Task<List<CmsDailyPlanDistributionAnalyticDto>> GetCmsDailyPlanDistributionAnalytic(
        DateTime start,
        DateTime end,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        List<string> companyIds = null,
        bool excludeMarkupPlan = false);

    Task<List<CmsDailyAccruedRevenueAnalyticDto>> GetCmsDailyAccruedRevenueAnalytic(
        DateTime start,
        DateTime end,
        List<string> companyIds = null,
        bool excludeMarkupPlan = false,
        CompanyType? companyType = null);

    Task<List<CmsDailyDistributionAnalyticDto>> GetCmsDistributionAnalytic(
        DateTime start,
        DateTime end,
        List<string> companyIds = null);

    Task<CmsDailyAnalyticByContactOwnersDto> GetCmsAnalyticByContactOwnerIds(
        CmsContactOwnerType contactOwnerType,
        DateTime start,
        DateTime end,
        List<string> contactOwnerIds,
        List<string> teamNames,
        bool isByOwnerTeams,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        bool excludeMarkupPlan = false);

    Task<CmsDailyMonthlyRecurringRevenueAnalyticByContactOwnersDto>
        GetCmsDailyMonthlyRecurringRevenueAnalyticWithAnalyticDatabaseByContactOwnerIds(
            CmsContactOwnerType contactOwnerType,
            DateTime start,
            DateTime end,
            List<string> contactOwnerIds,
            List<string> teamNames,
            bool isByOwnerTeams);

    Task<CmsDailyMonthlyRecurringRevenueAnalyticByContactOwnersDto>
        GetCmsDailyMonthlyRecurringRevenueAnalyticByContactOwnerIds(
            CmsContactOwnerType contactOwnerType,
            DateTime start,
            DateTime end,
            List<string> contactOwnerIds,
            List<string> teamNames,
            bool isByOwnerTeams,
            string companiesCacheKey,
            bool allowCompaniesCache = true,
            bool excludeMarkupPlan = false);

    Task<CmsDailyRevenueAnalyticByContactOwnersDto> GetCmsDailyRevenueAnalyticByContactOwnerIds(
        CmsContactOwnerType contactOwnerType,
        DateTime start,
        DateTime end,
        List<string> contactOwnerIds,
        List<string> teamNames,
        bool isByOwnerTeams,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        bool excludeMarkupPlan = false);

    Task<CmsDailyPlanDistributionAnalyticByContactOwnersDto>
        GetCmsPlanDistributionAnalyticByContactOwnerIds(
            CmsContactOwnerType contactOwnerType,
            DateTime start,
            DateTime end,
            List<string> contactOwnerIds,
            List<string> teamNames,
            bool isByOwnerTeams,
            string companiesCacheKey,
            bool allowCompaniesCache = true,
            bool excludeMarkupPlan = false);

    Task<CmsDailyAnalyticByPartnerStackGroupNamesDto> GetCmsAnalyticByPartnerStackGroupNames(
        DateTime start,
        DateTime end,
        List<string> partnerStackGroupNames,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        bool excludeMarkupPlan = false);

    Task<CmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNamesDto>
        GetCmsDailyMonthlyRecurringRevenueAnalyticWithAnalyticDatabaseByPartnerStackGroupNames(
            DateTime start,
            DateTime end,
            List<string> partnerStackGroupNames);

    Task<CmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNamesDto>
        GetCmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNames(
            DateTime start,
            DateTime end,
            List<string> partnerStackGroupNames,
            string companiesCacheKey,
            bool allowCompaniesCache = true,
            bool excludeMarkupPlan = false);

    Task<CmsDailyRevenueAnalyticByPartnerStackGroupNamesDto>
        GetCmsDailyRevenueAnalyticByPartnerStackGroupNames(
            DateTime start,
            DateTime end,
            List<string> partnerStackGroupNames,
            string companiesCacheKey,
            bool allowCompaniesCache = true,
            bool excludeMarkupPlan = false);

    Task<CmsDailyPlanDistributionAnalyticByPartnerStackGroupNamesDto>
        GetCmsPlanDistributionAnalyticByPartnerStackGroupNames(
            DateTime start,
            DateTime end,
            List<string> partnerStackGroupNames,
            string companiesCacheKey,
            bool allowCompaniesCache = true,
            bool excludeMarkupPlan = false);

    Task<List<CompanyRevenueStatusBreakdown>> GetCompanyRevenueStatusBreakdowns(
        DateTime dateTo,
        DateTime dateFrom,
        List<string> companyIds = null);

    Task<ReportRun> CreateSleekPayReport(long start, long end, string region);

    Task<string> DownloadSleekPayReportCsv(string fileId, string region);

    Task<List<SleekPayReportData>> DownloadSleekPayReportData(string fileId);

    List<SleekPayReportData> ProcessSleekPayReportCsv(string csv);

    Task<bool> SaveSleekPayReportCsvToDb(string fileId, string csv);

    Task SaveSleekPayReportToDb(string fileId, string csv, string region);

    Task UpdateCompanyAllTimeRevenueAnalyticInfo(List<string> companyIds = null);

    Task<List<CompanyLastPaidSubscriptionDateDto>> GetCompanyLastSubscriptionDate(List<string> companyIds);

    Task<List<CmsFreeTrialDistributionDto>> GetCmsAddOnFreeTrialData();
}

public class InternalAnalyticService : IInternalAnalyticService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly ILogger<InternalAnalyticService> _logger;
    private readonly IMapper _mapper;
    private readonly IStripeClients _stripeClients;
    private readonly IStripeAuthenticationService _stripeAuthenticationService;
    private readonly IStripeAuthenticationConfig _stripeAuthenticationConfig;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IInternalWhatsappCloudApiService _internalWhatsappCloudApiService;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly AnalyticDbContext _analyticDbContext;
    private readonly IConfiguration _configuration;
    private readonly ICompaniesService _companiesService;

    private readonly IBillRecordRevenueCalculatorService _billRecordRevenueCalculatorService;

    public InternalAnalyticService(
        ILogger<InternalAnalyticService> logger,
        ApplicationDbContext appDbContext,
        IMapper mapper,
        IStripeClients stripeClients,
        IStripeAuthenticationService stripeAuthenticationService,
        IStripeAuthenticationConfig stripeAuthenticationConfig,
        IHttpClientFactory httpClientFactory,
        IInternalWhatsappCloudApiService internalWhatsappCloudApiService,
        ICacheManagerService cacheManagerService,
        IDbContextService dbContextService,
        IConfiguration configuration,
        ICompaniesService companiesService,
        IBillRecordRevenueCalculatorService billRecordRevenueCalculatorService)
    {
        _logger = logger;
        _appDbContext = appDbContext;
        _mapper = mapper;
        _stripeClients = stripeClients;
        _stripeAuthenticationService = stripeAuthenticationService;
        _stripeAuthenticationConfig = stripeAuthenticationConfig;
        _httpClientFactory = httpClientFactory;
        _internalWhatsappCloudApiService = internalWhatsappCloudApiService;
        _cacheManagerService = cacheManagerService;
        _analyticDbContext = dbContextService.GetAnalyticDbContext();
        _configuration = configuration;
        _companiesService = companiesService;
        _billRecordRevenueCalculatorService = billRecordRevenueCalculatorService;
    }

    private async Task<List<CmsCompanyAnalyticDto>> GetCompanyAnalyticDataList(
        List<string> companyIds = null,
        bool excludeMarkupPlan = false,
        List<string> companyOwnerIds = null,
        List<string> activationOwnerIds = null,
        List<string> csOwnerIds = null,
        CompanyType? companyType = null,
        bool excludeRefundBillRecords = true,
        Dictionary<string, string> deduplicationCompanyIdServerLocationLookup = null)
    {
        var companiesQuery = _appDbContext.CompanyCompanies
            .AsSplitQuery()
            .AsNoTracking()
            .Include(x => x.BillRecords)
            .ThenInclude(b => b.CmsSalesPaymentRecords)
            .WhereIf(companyIds != null, x => companyIds.Contains(x.Id))
            .WhereIf(companyOwnerIds != null, x => companyOwnerIds.Contains(x.CmsCompanyOwnerId))
            .WhereIf(activationOwnerIds != null, x => activationOwnerIds.Contains(x.CmsActivationOwnerId))
            .WhereIf(csOwnerIds != null, x => csOwnerIds.Contains(x.CmsCsOwnerId))
            .WhereIf(companyType != null, x => x.CompanyType == companyType);

        if (excludeMarkupPlan)
        {
            companiesQuery = companiesQuery
                .Select(
                    x => new Company
                    {
                        Id = x.Id,
                        CompanyName = x.CompanyName,
                        CompanyCountry = x.CompanyCountry,
                        CreatedAt = x.CreatedAt,
                        CmsLeadSource = x.CmsLeadSource,
                        CmsCompanyIndustry = x.CmsCompanyIndustry,
                        CmsCompanyOwnerId = x.CmsCompanyOwnerId,
                        CmsActivationOwnerId = x.CmsActivationOwnerId,
                        CmsCsOwnerId = x.CmsCsOwnerId,
                        BillRecords = x.BillRecords
                            .Where(b => !ValidSubscriptionPlan.MarkupPlans.Contains(b.SubscriptionPlanId))
                            .ToList(),
                        TimeZoneInfoId = x.TimeZoneInfoId ?? string.Empty
                    });
        }
        else
        {
            companiesQuery = companiesQuery.Select(
                x => new Company
                {
                    Id = x.Id,
                    CompanyName = x.CompanyName,
                    CompanyCountry = x.CompanyCountry,
                    CreatedAt = x.CreatedAt,
                    CmsLeadSource = x.CmsLeadSource,
                    CmsCompanyIndustry = x.CmsCompanyIndustry,
                    CmsCompanyOwnerId = x.CmsCompanyOwnerId,
                    CmsActivationOwnerId = x.CmsActivationOwnerId,
                    CmsCsOwnerId = x.CmsCsOwnerId,
                    BillRecords = x.BillRecords.ToList(),
                    TimeZoneInfoId = x.TimeZoneInfoId ?? string.Empty
                });
        }

        var companies = await companiesQuery
            .ProjectTo<CmsCompanyAnalyticDto>(_mapper.ConfigurationProvider)
            .ToListAsync();

        // Filter after projection to improve performance when tenant hub has a lot of companies
        var actualCompanies = new List<CmsCompanyAnalyticDto>();
        var serverLocation = _configuration["SF_REGION"] ?? LocationNames.EastAsia;
        deduplicationCompanyIdServerLocationLookup ??=
            await _companiesService.GetDeduplicationCompanyIdServerLocationLookup(serverLocation);
        actualCompanies.AddRange(
            serverLocation == LocationNames.EastAsia
                ? companies.Where(x => !deduplicationCompanyIdServerLocationLookup.ContainsKey(x.Id))
                : companies.Where(x => deduplicationCompanyIdServerLocationLookup.ContainsKey(x.Id)));

        if (excludeRefundBillRecords)
        {
            actualCompanies.ForEach(
                company =>
                {
                    if (company != null)
                    {
                        company.BillRecords = company.BillRecords
                            .Where(x => x.Status != BillStatus.Inactive)
                            .ToList();
                    }
                });
        }

        return actualCompanies;
    }

    private async Task<List<CmsSleekPayCompanyAnalyticDto>> GetSleekPayCompaniesList(
        DateTime start,
        DateTime end,
        Dictionary<string, string> deduplicationCompanyIdServerLocationLookup)
    {
        var sleekPayCompanies = await _appDbContext.ConfigStripePaymentConfigs
            .OrderBy(x => x.CreatedAt)
            .Select(
                x => new CmsSleekPayCompanyAnalyticDto
                {
                    Id = x.Id,
                    CompanyId = x.CompanyId,
                    CompanyName = _appDbContext.CompanyCompanies.FirstOrDefault(c => c.Id == x.CompanyId).CompanyName,
                    CmsSleekPayReportDatas =
                        _appDbContext.CmsSleekPayReportDatas
                            .Where(
                                c => c.StripeAccountId == x.AccountId
                                     && c.StartActivityDate.Date >= start.Date
                                     && c.EndActivityDate.Date <= end.Date)
                            .ToList()
                })
            .Where(x => x.CmsSleekPayReportDatas.Count > 0)
            .ToListAsync();

        // Deduplication logic
        var serverLocation = _configuration["SF_REGION"] ?? LocationNames.EastAsia;

        sleekPayCompanies = sleekPayCompanies
            .Where(
                x => serverLocation == LocationNames.EastAsia
                    ? !deduplicationCompanyIdServerLocationLookup.ContainsKey(x.CompanyId)
                    : deduplicationCompanyIdServerLocationLookup.ContainsKey(x.CompanyId))
            .ToList();

        return sleekPayCompanies;
    }

    private async Task<List<CmsWhatsApp360DialogUsageTopUpTransactionLogDto>> GetWhatsapp360TopUpLogList(
        DateTime start,
        DateTime end,
        Dictionary<string, string> deduplicationCompanyIdServerLocationLookup)
    {
        var topUps = await _appDbContext
            .CompanyWhatsApp360DialogUsageTransactionLogs
            .Where(
                x => x.TransactionType == WhatsApp360DialogUsageTransactionType.TopUp
                     && x.IsInternalTestingUse == false
                     && x.CreatedAt.Date >= start.Date
                     && x.CreatedAt.Date <= end.Date)
            .Include(x => x.WhatsApp360DialogUsageRecord)
            .Include(x => x.WhatsApp360DialogUsageRecord.Company)
            .Select(
                x => new CmsWhatsApp360DialogUsageTopUpTransactionLogDto
                {
                    Id = x.Id,
                    Total = x.Total,
                    Currency = x.Currency,
                    IsInternalTestingUse = x.IsInternalTestingUse,
                    TransactionType = x.TransactionType,
                    CreatedAt = x.CreatedAt,
                    CompanyName = x.WhatsApp360DialogUsageRecord.Company.CompanyName,
                    CompanyId = x.CompanyId
                })
            .OrderBy(x => x.CreatedAt)
            .ToListAsync();

        // Deduplication logic
        var serverLocation = _configuration["SF_REGION"] ?? LocationNames.EastAsia;

        topUps = topUps
            .Where(
                x => serverLocation == LocationNames.EastAsia
                    ? !deduplicationCompanyIdServerLocationLookup.ContainsKey(x.CompanyId)
                    : deduplicationCompanyIdServerLocationLookup.ContainsKey(x.CompanyId))
            .ToList();

        return topUps;
    }

    private async Task<List<CmsTwilioTopUpLogDto>> GetTwilioTopUpLogList(
        DateTime start,
        DateTime end,
        Dictionary<string, string> deduplicationCompanyIdServerLocationLookup)
    {
        var topUps = await _appDbContext.TwilioTopUpLogs
            .Where(
                x => x.IsInternalTestingUse == false
                     && x.CreatedAt.Date >= start.Date
                     && x.CreatedAt.Date <= end.Date)
            .Select(
                x => new CmsTwilioTopUpLogDto()
                {
                    Id = x.Id,
                    CompanyId = x.CompanyId,
                    CompanyName = _appDbContext.CompanyCompanies.First(c => c.Id == x.CompanyId).CompanyName,
                    TopUpAmount = x.TopUpAmount,
                    IsInternalTestingUse = x.IsInternalTestingUse,
                    Currency = x.Currency,
                    CreatedAt = x.CreatedAt
                })
            .OrderBy(x => x.CreatedAt)
            .ToListAsync();

        // Deduplication logic
        var serverLocation = _configuration["SF_REGION"] ?? LocationNames.EastAsia;

        topUps = topUps
            .Where(
                x => serverLocation == LocationNames.EastAsia
                    ? !deduplicationCompanyIdServerLocationLookup.ContainsKey(x.CompanyId)
                    : deduplicationCompanyIdServerLocationLookup.ContainsKey(x.CompanyId))
            .ToList();

        return topUps;
    }

    private async Task<List<CmsWhatsappCloudApiTopUpDto>> GetWhatsappCloudApiTopUpList(
        DateTime start,
        DateTime end,
        Dictionary<string, string> deduplicationCompanyIdServerLocationLookup)
    {
        var topUps = new List<CmsWhatsappCloudApiTopUpDto>();

        try
        {
            var allWabasResponse = await _internalWhatsappCloudApiService.GetAllWhatsappCloudApiWabas();

            var wabasList = allWabasResponse.Wabas;

            var businessBalanceTransactionLogFilter =
                new BusinessBalanceTransactionLogFilter(
                    transactionType: "TOP_UP",
                    createdAtRange: new DateTimeOffsetRange(start, end),
                    haveTopUpAmount: true,
                    isCalculated: true);

            var getFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs =
                await _internalWhatsappCloudApiService
                    .GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs(
                        businessBalanceTransactionLogFilter,
                        10000);

            var companyNames = await _appDbContext.CompanyCompanies
                .Select(
                    company => new
                    {
                        company.Id,
                        company.CompanyName
                    })
                .AsNoTracking()
                .ToDictionaryAsync(x => x.Id, x => x.CompanyName);

            // Deduplication logic
            var serverLocation = _configuration["SF_REGION"] ?? LocationNames.EastAsia;

            companyNames = companyNames
                .Where(
                    x => serverLocation == LocationNames.EastAsia
                        ? !deduplicationCompanyIdServerLocationLookup.ContainsKey(x.Key)
                        : deduplicationCompanyIdServerLocationLookup.ContainsKey(x.Key))
                .ToDictionary(x => x.Key, x => x.Value);

            if (getFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs.BusinessBalanceTransactionLogs is
                {
                    Count: 0,
                })
            {
                return topUps;
            }

            getFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs.BusinessBalanceTransactionLogs
                .ForEach(
                    log =>
                    {
                        try
                        {
                            var topUpDto = new CmsWhatsappCloudApiTopUpDto
                            {
                                FacebookBusinessId = log.FacebookBusinessId,
                                FacebookBusinessName = wabasList
                                    .Find(w => w.FacebookWabaBusinessId == log.FacebookBusinessId)
                                    ?.FacebookWabaBusinessName ?? string.Empty,
                                Currency = log.Credit.CurrencyIsoCode,
                                CreditAmount = log.Credit.Amount,
                                CreatedAt = log.CreatedAt.UtcDateTime,
                                SleekFlowCompanyIds = wabasList
                                    .Find(w => w.FacebookWabaBusinessId == log.FacebookBusinessId)
                                    ?.SleekflowCompanyIds ?? new List<string>(),
                                IsInternalTestingUse = false
                            };

                            if (log.WabaTopUp is
                                {
                                    PaymentMethod: "INTERNAL"
                                })
                            {
                                log.WabaTopUp.InternalTopUpCreditDetail.Metadata.TryGetValue(
                                    "is_internal_testing_use",
                                    out var isInternalUse);

                                if (isInternalUse is bool use)
                                {
                                    topUpDto.IsInternalTestingUse = use;
                                }
                            }

                            topUpDto.SleekFlowCompanyIds.ForEach(
                                companyId =>
                                {
                                    topUpDto.SleekFlowCompanyNames ??= new List<string>();

                                    topUpDto.SleekFlowCompanyNames.Add(
                                        companyNames.TryGetValue(companyId, out var companyName)
                                            ? companyName
                                            : string.Empty);
                                });

                            topUps.Add(topUpDto);
                        }
                        catch (Exception e)
                        {
                            _logger.LogError(
                                e,
                                "Error while processing Whatsapp Cloud API Top-up log, FacebookBusinessId: {FacebookBusinessId}",
                                log.FacebookBusinessId);
                        }
                    });

            return topUps
                .Where(topUp => topUp.IsInternalTestingUse == false)
                .OrderBy(topUp => topUp.CreatedAt)
                .ToList();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error while getting Whatsapp Cloud API Top-up logs");
        }

        return topUps;
    }

    public async Task<List<CmsDailyAnalyticDto>> GetCmsAnalytic(
        DateTime start,
        DateTime end,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        List<string> companyIds = null,
        bool excludeMarkupPlan = false)
    {
        var companies = await GetCompaniesWithCache(
            companiesCacheKey,
            allowCompaniesCache,
            companyIds,
            excludeMarkupPlan);

        return _billRecordRevenueCalculatorService.GetDailyAnalytics(companies, start, end);
    }

    public async Task<List<CmsDailyMonthlyRecurringRevenueAnalyticDto>> GetCmsDailyMonthlyRecurringRevenueAnalyticWithAnalyticDatabase(
        DateTime start,
        DateTime end,
        List<string> companyIds = null)
    {
        List<CmsDailyMonthlyRecurringRevenueAnalyticDto> cmsDailyRevenueAnalytics = [];

        var startDate = DateOnly.FromDateTime(start);
        var endDate = DateOnly.FromDateTime(end);

        var companyNames = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .WhereIf(companyIds != null, company => companyIds.Contains(company.Id))
            .Select(
                x => new CompanyIdNamePair
                {
                    CompanyId = x.Id,
                    CompanyName = x.CompanyName
                })
            .ToListAsync();

        var mrrByCompanyByDayDataList = await _analyticDbContext.MrrByCompanyByDays
            .AsNoTracking()
            .Where(x => x.Date >= startDate && x.Date <= endDate)
            .WhereIf(companyIds != null, x => companyIds.Contains(x.CompanyId))
            .Select(
                x => new CmsCompanyMrrAnalyticDto
                {
                    Date = x.Date,
                    CompanyId = x.CompanyId,
                    SubscriptionPlanId = x.SubscriptionPlanId,
                    MonthlyRecurringRevenueUsd = x.MonthlyRecurringRevenueUsd
                })
            .ToListAsync();

        // Pre-process data by grouping by date first
        var mrrByDateLookup = mrrByCompanyByDayDataList
            .GroupBy(x => x.Date)
            .ToDictionary(g => g.Key, g => g.ToList());

        // Create a lookup dictionary for company names
        var companyNameLookup = companyNames.ToDictionary(c => c.CompanyId, c => c.CompanyName);

        // Process each day
        for (var date = start; date <= end; date = date.Add(TimeSpan.FromDays(1)))
        {
            var currentDateOnly = DateOnly.FromDateTime(date);
            var dailyAnalytic = new CmsDailyMonthlyRecurringRevenueAnalyticDto
            {
                Date = date.ToString("yyyy-MM-dd"),
                MonthlyRecurringRevenue = 0,
                CompanyRevenueBreakDowns = new List<CmsCompanyRevenueBreakDownDto>()
            };

            // Check if we have data for this date
            if (mrrByDateLookup.TryGetValue(currentDateOnly, out var mrrByCompanyInCurrentDate))
            {
                // Process all companies for this date at once
                var companyRevenueBreakDowns = mrrByCompanyInCurrentDate.Select(mrr => new CmsCompanyRevenueBreakDownDto
                {
                    CompanyId = mrr.CompanyId,
                    CompanyName = companyNameLookup.TryGetValue(mrr.CompanyId, out var name) ? name : null,
                    SubscriptionPlanId = mrr.SubscriptionPlanId,
                    MonthlyRecurringRevenue = mrr.MonthlyRecurringRevenueUsd
                }).ToList();

                dailyAnalytic.MonthlyRecurringRevenue = companyRevenueBreakDowns.Sum(x => x.MonthlyRecurringRevenue);
                dailyAnalytic.CompanyRevenueBreakDowns = companyRevenueBreakDowns;
            }

            cmsDailyRevenueAnalytics.Add(dailyAnalytic);
        }

        return cmsDailyRevenueAnalytics;
    }

    public async Task<List<CmsDailyMonthlyRecurringRevenueAnalyticDto>> GetCmsDailyMonthlyRecurringRevenueAnalytic(
        DateTime start,
        DateTime end,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        List<string> companyIds = null,
        bool excludeMarkupPlan = false)
    {
        var companies = await GetCompaniesWithCache(
            companiesCacheKey,
            allowCompaniesCache,
            companyIds,
            excludeMarkupPlan);

        return _billRecordRevenueCalculatorService.GetDailyMonthlyRecurringRevenueAnalytics(companies, start, end);
    }

    public async Task<List<CmsDailyRevenueAnalyticDto>> GetCmsDailyRevenueAnalytic(
        DateTime start,
        DateTime end,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        List<string> companyIds = null,
        bool excludeMarkupPlan = false)
    {
        var companies = await GetCompaniesWithCache(
            companiesCacheKey,
            allowCompaniesCache,
            companyIds,
            excludeMarkupPlan);

        return _billRecordRevenueCalculatorService.GetDailyRevenueAnalytics(companies, start, end);
    }

    public async Task<List<CmsDailyPlanDistributionAnalyticDto>> GetCmsDailyPlanDistributionAnalytic(
        DateTime start,
        DateTime end,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        List<string> companyIds = null,
        bool excludeMarkupPlan = false)
    {
        var companies = await GetCompaniesWithCache(
            companiesCacheKey,
            allowCompaniesCache,
            companyIds,
            excludeMarkupPlan);

        return _billRecordRevenueCalculatorService.GetDailyPlanDistributionAnalytics(companies, start, end);
    }

    public async Task<List<CmsDailyAccruedRevenueAnalyticDto>> GetCmsDailyAccruedRevenueAnalytic(
        DateTime start,
        DateTime end,
        List<string> companyIds = null,
        bool excludeMarkupPlan = false,
        CompanyType? companyType = null)
    {
        var serverLocation = _configuration["SF_REGION"] ?? LocationNames.EastAsia;

        var deduplicationCompanyIdServerLocationLookup =
            await _companiesService.GetDeduplicationCompanyIdServerLocationLookup(serverLocation);

        var companies = await GetCompanyAnalyticDataList(
            companyIds,
            excludeMarkupPlan,
            companyType: companyType,
            deduplicationCompanyIdServerLocationLookup: deduplicationCompanyIdServerLocationLookup);

        var sleekPayCompanies = await GetSleekPayCompaniesList(start, end, deduplicationCompanyIdServerLocationLookup);

        var whatsapp360DialogTopUpLogs = await GetWhatsapp360TopUpLogList(
            start,
            end,
            deduplicationCompanyIdServerLocationLookup);

        var twilioTopUpLogs = await GetTwilioTopUpLogList(start, end, deduplicationCompanyIdServerLocationLookup);

        var whatsappCloudApiTopUps = await GetWhatsappCloudApiTopUpList(
            start,
            end,
            deduplicationCompanyIdServerLocationLookup);

        return _billRecordRevenueCalculatorService.GetDailyAccruedAnalytics(
            companies,
            sleekPayCompanies,
            whatsapp360DialogTopUpLogs,
            twilioTopUpLogs,
            whatsappCloudApiTopUps,
            start,
            end);
    }

    public async Task<List<CmsDailyDistributionAnalyticDto>> GetCmsDistributionAnalytic(
        DateTime start,
        DateTime end,
        List<string> companyIds = null)
    {
        var companies = await GetCompanyAnalyticDataList(companyIds);

        return _billRecordRevenueCalculatorService.GetDailyDistributionAnalytics(companies, start, end);
    }

    public async Task<CmsDailyAnalyticByContactOwnersDto> GetCmsAnalyticByContactOwnerIds(
        CmsContactOwnerType contactOwnerType,
        DateTime start,
        DateTime end,
        List<string> contactOwnerIds,
        List<string> teamNames,
        bool isByOwnerTeams,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        bool excludeMarkupPlan = false)
    {
        List<string> actualContactOwnerIds;

        if (isByOwnerTeams)
        {
            actualContactOwnerIds = await GetOwnerIdsByTeamNames(teamNames);
        }
        else
        {
            actualContactOwnerIds = contactOwnerIds;
        }

        List<string> companyOwnerIds = null;
        List<string> activationOwnerIds = null;
        List<string> csOwnerIds = null;

        switch (contactOwnerType)
        {
            case CmsContactOwnerType.CompanyOwner:
                companyOwnerIds = actualContactOwnerIds;
                break;
            case CmsContactOwnerType.ActivationOwner:
                activationOwnerIds = actualContactOwnerIds;
                break;
            case CmsContactOwnerType.CsOwner:
                csOwnerIds = actualContactOwnerIds;
                break;
        }

        var companies = await GetCompaniesWithCache(
            companiesCacheKey,
            allowCompaniesCache,
            null,
            excludeMarkupPlan,
            companyOwnerIds: companyOwnerIds,
            activationOwnerIds: activationOwnerIds,
            csOwnerIds: csOwnerIds);

        var owner = await _appDbContext.Users
            .Where(x => actualContactOwnerIds.Contains(x.Id))
            .ToListAsync();

        var analytic = new CmsDailyAnalyticByContactOwnersDto
        {
            ContactOwnerIds = owner.Select(x => x.Id).ToList(),
            ContactOwnerNames = owner.Select(x => x.DisplayName).ToList(),
            TeamNames = isByOwnerTeams ? teamNames : null,
            ContactOwnerType = contactOwnerType.ToString(),
            DailyAnalytics = _billRecordRevenueCalculatorService.GetDailyAnalytics(companies, start, end)
        };

        return analytic;
    }

    public async Task<CmsDailyMonthlyRecurringRevenueAnalyticByContactOwnersDto>
        GetCmsDailyMonthlyRecurringRevenueAnalyticWithAnalyticDatabaseByContactOwnerIds(
            CmsContactOwnerType contactOwnerType,
            DateTime start,
            DateTime end,
            List<string> contactOwnerIds,
            List<string> teamNames,
            bool isByOwnerTeams)
    {
        List<string> actualContactOwnerIds;

        if (isByOwnerTeams)
        {
            actualContactOwnerIds = await GetOwnerIdsByTeamNames(teamNames);
        }
        else
        {
            actualContactOwnerIds = contactOwnerIds;
        }

        List<string> companyIds = null;

        switch (contactOwnerType)
        {
            case CmsContactOwnerType.CompanyOwner:
                companyIds = await _appDbContext.CompanyCompanies
                    .AsNoTracking()
                    .Where(x => actualContactOwnerIds.Contains(x.CmsCompanyOwnerId))
                    .Select(x => x.Id)
                    .ToListAsync();
                break;
            case CmsContactOwnerType.ActivationOwner:
                companyIds = await _appDbContext.CompanyCompanies
                    .AsNoTracking()
                    .Where(x => actualContactOwnerIds.Contains(x.CmsActivationOwnerId))
                    .Select(x => x.Id)
                    .ToListAsync();
                break;
            case CmsContactOwnerType.CsOwner:
                companyIds = await _appDbContext.CompanyCompanies
                    .AsNoTracking()
                    .Where(x => actualContactOwnerIds.Contains(x.CmsCsOwnerId))
                    .Select(x => x.Id)
                    .ToListAsync();
                break;
        }

        var owner = await _appDbContext.Users
            .Where(x => actualContactOwnerIds.Contains(x.Id))
            .ToListAsync();

        var analytic = new CmsDailyMonthlyRecurringRevenueAnalyticByContactOwnersDto
        {
            ContactOwnerIds = owner.Select(x => x.Id).ToList(),
            ContactOwnerNames = owner.Select(x => x.DisplayName).ToList(),
            TeamNames = isByOwnerTeams ? teamNames : null,
            ContactOwnerType = contactOwnerType.ToString(),
            DailyMonthlyRecurringRevenueAnalytics = await GetCmsDailyMonthlyRecurringRevenueAnalyticWithAnalyticDatabase(
                start, end, companyIds)
        };

        return analytic;
    }

    public async Task<CmsDailyMonthlyRecurringRevenueAnalyticByContactOwnersDto>
        GetCmsDailyMonthlyRecurringRevenueAnalyticByContactOwnerIds(
            CmsContactOwnerType contactOwnerType,
            DateTime start,
            DateTime end,
            List<string> contactOwnerIds,
            List<string> teamNames,
            bool isByOwnerTeams,
            string companiesCacheKey,
            bool allowCompaniesCache = true,
            bool excludeMarkupPlan = false)
    {
        List<string> actualContactOwnerIds;

        if (isByOwnerTeams)
        {
            actualContactOwnerIds = await GetOwnerIdsByTeamNames(teamNames);
        }
        else
        {
            actualContactOwnerIds = contactOwnerIds;
        }

        List<string> companyOwnerIds = null;
        List<string> activationOwnerIds = null;
        List<string> csOwnerIds = null;

        switch (contactOwnerType)
        {
            case CmsContactOwnerType.CompanyOwner:
                companyOwnerIds = actualContactOwnerIds;
                break;
            case CmsContactOwnerType.ActivationOwner:
                activationOwnerIds = actualContactOwnerIds;
                break;
            case CmsContactOwnerType.CsOwner:
                csOwnerIds = actualContactOwnerIds;
                break;
        }

        var companies = await GetCompaniesWithCache(
            companiesCacheKey,
            allowCompaniesCache,
            null,
            excludeMarkupPlan,
            companyOwnerIds: companyOwnerIds,
            activationOwnerIds: activationOwnerIds,
            csOwnerIds: csOwnerIds);

        var owner = await _appDbContext.Users
            .Where(x => actualContactOwnerIds.Contains(x.Id))
            .ToListAsync();

        var analytic = new CmsDailyMonthlyRecurringRevenueAnalyticByContactOwnersDto
        {
            ContactOwnerIds = owner.Select(x => x.Id).ToList(),
            ContactOwnerNames = owner.Select(x => x.DisplayName).ToList(),
            TeamNames = isByOwnerTeams ? teamNames : null,
            ContactOwnerType = contactOwnerType.ToString(),
            DailyMonthlyRecurringRevenueAnalytics = _billRecordRevenueCalculatorService.GetDailyMonthlyRecurringRevenueAnalytics(companies, start, end)
        };

        return analytic;
    }

    public async Task<CmsDailyRevenueAnalyticByContactOwnersDto> GetCmsDailyRevenueAnalyticByContactOwnerIds(
        CmsContactOwnerType contactOwnerType,
        DateTime start,
        DateTime end,
        List<string> contactOwnerIds,
        List<string> teamNames,
        bool isByOwnerTeams,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        bool excludeMarkupPlan = false)
    {
        List<string> actualContactOwnerIds;

        if (isByOwnerTeams)
        {
            actualContactOwnerIds = await GetOwnerIdsByTeamNames(teamNames);
        }
        else
        {
            actualContactOwnerIds = contactOwnerIds;
        }

        List<string> companyOwnerIds = null;
        List<string> activationOwnerIds = null;
        List<string> csOwnerIds = null;

        switch (contactOwnerType)
        {
            case CmsContactOwnerType.CompanyOwner:
                companyOwnerIds = actualContactOwnerIds;
                break;
            case CmsContactOwnerType.ActivationOwner:
                activationOwnerIds = actualContactOwnerIds;
                break;
            case CmsContactOwnerType.CsOwner:
                csOwnerIds = actualContactOwnerIds;
                break;
        }

        var companies = await GetCompaniesWithCache(
            companiesCacheKey,
            allowCompaniesCache,
            null,
            excludeMarkupPlan,
            companyOwnerIds: companyOwnerIds,
            activationOwnerIds: activationOwnerIds,
            csOwnerIds: csOwnerIds);

        var owner = await _appDbContext.Users
            .Where(x => actualContactOwnerIds.Contains(x.Id))
            .ToListAsync();

        var analytic = new CmsDailyRevenueAnalyticByContactOwnersDto
        {
            ContactOwnerIds = owner.Select(x => x.Id).ToList(),
            ContactOwnerNames = owner.Select(x => x.DisplayName).ToList(),
            TeamNames = isByOwnerTeams ? teamNames : null,
            ContactOwnerType = contactOwnerType.ToString(),
            DailyRevenueAnalytics = _billRecordRevenueCalculatorService.GetDailyRevenueAnalytics(companies, start, end)
        };

        return analytic;
    }

    public async Task<CmsDailyPlanDistributionAnalyticByContactOwnersDto>
        GetCmsPlanDistributionAnalyticByContactOwnerIds(
            CmsContactOwnerType contactOwnerType,
            DateTime start,
            DateTime end,
            List<string> contactOwnerIds,
            List<string> teamNames,
            bool isByOwnerTeams,
            string companiesCacheKey,
            bool allowCompaniesCache = true,
            bool excludeMarkupPlan = false)
    {
        List<string> actualContactOwnerIds;

        if (isByOwnerTeams)
        {
            actualContactOwnerIds = await GetOwnerIdsByTeamNames(teamNames);
        }
        else
        {
            actualContactOwnerIds = contactOwnerIds;
        }

        List<string> companyOwnerIds = null;
        List<string> activationOwnerIds = null;
        List<string> csOwnerIds = null;

        switch (contactOwnerType)
        {
            case CmsContactOwnerType.CompanyOwner:
                companyOwnerIds = actualContactOwnerIds;
                break;
            case CmsContactOwnerType.ActivationOwner:
                activationOwnerIds = actualContactOwnerIds;
                break;
            case CmsContactOwnerType.CsOwner:
                csOwnerIds = actualContactOwnerIds;
                break;
        }

        var companies = await GetCompaniesWithCache(
            companiesCacheKey,
            allowCompaniesCache,
            null,
            excludeMarkupPlan,
            companyOwnerIds: companyOwnerIds,
            activationOwnerIds: activationOwnerIds,
            csOwnerIds: csOwnerIds);

        var owner = await _appDbContext.Users
            .Where(x => actualContactOwnerIds.Contains(x.Id))
            .ToListAsync();

        var analytic = new CmsDailyPlanDistributionAnalyticByContactOwnersDto
        {
            ContactOwnerIds = owner.Select(x => x.Id).ToList(),
            ContactOwnerNames = owner.Select(x => x.DisplayName).ToList(),
            TeamNames = isByOwnerTeams ? teamNames : null,
            ContactOwnerType = contactOwnerType.ToString(),
            DailyPlanDistributionAnalytics =
                _billRecordRevenueCalculatorService.GetDailyPlanDistributionAnalytics(companies, start, end)
        };

        return analytic;
    }

    public async Task<CmsDailyAnalyticByPartnerStackGroupNamesDto> GetCmsAnalyticByPartnerStackGroupNames(
        DateTime start,
        DateTime end,
        List<string> partnerStackGroupNames,
        string companiesCacheKey,
        bool allowCompaniesCache = true,
        bool excludeMarkupPlan = false)
    {
        var cmsPartnerStackCustomerMaps = await _appDbContext.CmsPartnerStackCustomerMaps
            .Where(
                x => x.PartnerStackPartnerInformation != null)
            .ToListAsync();

        var companyIds = cmsPartnerStackCustomerMaps
            .Where(x => partnerStackGroupNames.Contains(x.PartnerStackPartnerInformation.GroupName))
            .Select(x => x.CompanyId)
            .ToList();

        var companies = await GetCompaniesWithCache(
            companiesCacheKey,
            allowCompaniesCache,
            companyIds,
            excludeMarkupPlan);

        var analytic = new CmsDailyAnalyticByPartnerStackGroupNamesDto
        {
            PartnerStackGroupNames = partnerStackGroupNames,
            DailyAnalytics = _billRecordRevenueCalculatorService.GetDailyAnalytics(companies, start, end)
        };

        return analytic;
    }

    public async Task<CmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNamesDto>
        GetCmsDailyMonthlyRecurringRevenueAnalyticWithAnalyticDatabaseByPartnerStackGroupNames(
            DateTime start,
            DateTime end,
            List<string> partnerStackGroupNames)
    {
        var cmsPartnerStackCustomerMaps = await _appDbContext.CmsPartnerStackCustomerMaps
            .Where(
                x => x.PartnerStackPartnerInformation != null)
            .ToListAsync();

        var companyIds = cmsPartnerStackCustomerMaps
            .Where(x => partnerStackGroupNames.Contains(x.PartnerStackPartnerInformation.GroupName))
            .Select(x => x.CompanyId)
            .ToList();

        var analytic = new CmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNamesDto
        {
            PartnerStackGroupNames = partnerStackGroupNames,
            DailyMonthlyRecurringRevenueAnalytics = await GetCmsDailyMonthlyRecurringRevenueAnalyticWithAnalyticDatabase(
                start, end, companyIds)
        };

        return analytic;
    }

    public async Task<CmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNamesDto>
        GetCmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNames(
            DateTime start,
            DateTime end,
            List<string> partnerStackGroupNames,
            string companiesCacheKey,
            bool allowCompaniesCache = true,
            bool excludeMarkupPlan = false)
    {
        var cmsPartnerStackCustomerMaps = await _appDbContext.CmsPartnerStackCustomerMaps
            .Where(
                x => x.PartnerStackPartnerInformation != null)
            .ToListAsync();

        var companyIds = cmsPartnerStackCustomerMaps
            .Where(x => partnerStackGroupNames.Contains(x.PartnerStackPartnerInformation.GroupName))
            .Select(x => x.CompanyId)
            .ToList();

        var companies = await GetCompaniesWithCache(
            companiesCacheKey,
            allowCompaniesCache,
            companyIds,
            excludeMarkupPlan);

        var analytic = new CmsDailyMonthlyRecurringRevenueAnalyticByPartnerStackGroupNamesDto
        {
            PartnerStackGroupNames = partnerStackGroupNames,
            DailyMonthlyRecurringRevenueAnalytics = _billRecordRevenueCalculatorService.GetDailyMonthlyRecurringRevenueAnalytics(companies, start, end)
        };

        return analytic;
    }

    public async Task<CmsDailyRevenueAnalyticByPartnerStackGroupNamesDto>
        GetCmsDailyRevenueAnalyticByPartnerStackGroupNames(
            DateTime start,
            DateTime end,
            List<string> partnerStackGroupNames,
            string companiesCacheKey,
            bool allowCompaniesCache = true,
            bool excludeMarkupPlan = false)
    {
        var cmsPartnerStackCustomerMaps = await _appDbContext.CmsPartnerStackCustomerMaps
            .Where(
                x => x.PartnerStackPartnerInformation != null)
            .ToListAsync();

        var companyIds = cmsPartnerStackCustomerMaps
            .Where(x => partnerStackGroupNames.Contains(x.PartnerStackPartnerInformation.GroupName))
            .Select(x => x.CompanyId)
            .ToList();

        var companies = await GetCompaniesWithCache(
            companiesCacheKey,
            allowCompaniesCache,
            companyIds,
            excludeMarkupPlan);

        var analytic = new CmsDailyRevenueAnalyticByPartnerStackGroupNamesDto
        {
            PartnerStackGroupNames = partnerStackGroupNames,
            DailyRevenueAnalytics = _billRecordRevenueCalculatorService.GetDailyRevenueAnalytics(companies, start, end)
        };

        return analytic;
    }

    public async Task<CmsDailyPlanDistributionAnalyticByPartnerStackGroupNamesDto>
        GetCmsPlanDistributionAnalyticByPartnerStackGroupNames(
            DateTime start,
            DateTime end,
            List<string> partnerStackGroupNames,
            string companiesCacheKey,
            bool allowCompaniesCache = true,
            bool excludeMarkupPlan = false)
    {
        var cmsPartnerStackCustomerMaps = await _appDbContext.CmsPartnerStackCustomerMaps
            .Where(
                x => x.PartnerStackPartnerInformation != null)
            .ToListAsync();

        var companyIds = cmsPartnerStackCustomerMaps
            .Where(x => partnerStackGroupNames.Contains(x.PartnerStackPartnerInformation.GroupName))
            .Select(x => x.CompanyId)
            .ToList();

        var companies = await GetCompaniesWithCache(
            companiesCacheKey,
            allowCompaniesCache,
            companyIds,
            excludeMarkupPlan);

        var analytic = new CmsDailyPlanDistributionAnalyticByPartnerStackGroupNamesDto
        {
            PartnerStackGroupNames = partnerStackGroupNames,
            DailyPlanDistributionAnalytics = _billRecordRevenueCalculatorService.GetDailyPlanDistributionAnalytics(companies, start, end)
        };

        return analytic;
    }

    public async Task<List<CompanyRevenueStatusBreakdown>> GetCompanyRevenueStatusBreakdowns(
        DateTime dateTo,
        DateTime dateFrom,
        List<string> companyIds = null)
    {
        var result = new List<CompanyRevenueStatusBreakdown>();
        var dailyRevenueAnalytics = await GetCmsAnalytic(
            new DateTime(2020, 1, 1),
            DateTime.UtcNow.Date,
            string.Empty,
            false,
            companyIds: companyIds);

        var to = dateTo.ToString("yyyy-MM-dd");
        var from = dateFrom.ToString("yyyy-MM-dd");

        var dailyRevenueAnalyticToIndex = dailyRevenueAnalytics.FindIndex(x => x.Date == to);
        var dailyRevenueAnalyticFromIndex = dailyRevenueAnalytics.FindIndex(x => x.Date == from);

        var dailyRevenueAnalyticToBreakdowns =
            dailyRevenueAnalytics[dailyRevenueAnalyticToIndex].CompanyRevenueBreakDowns;
        var dailyRevenueAnalyticFromBreakdowns =
            dailyRevenueAnalytics[dailyRevenueAnalyticFromIndex].CompanyRevenueBreakDowns;

        // Calculate Company Revenue Status
        dailyRevenueAnalyticToBreakdowns.ForEach(
            companyBreakdown =>
            {
                var oldCompanyBreakdown = dailyRevenueAnalyticFromBreakdowns
                    .Find(x => x.CompanyId == companyBreakdown.CompanyId);

                if (oldCompanyBreakdown != null)
                {
                    var mrrDiff = companyBreakdown.MonthlyRecurringRevenue -
                                  oldCompanyBreakdown.MonthlyRecurringRevenue;

                    var mrrDiffPercent = Math.Round(mrrDiff / oldCompanyBreakdown.MonthlyRecurringRevenue * 100);

                    // Sustain, Increase, Decrease
                    result.Add(
                        new CompanyRevenueStatusBreakdown()
                        {
                            CompanyId = companyBreakdown.CompanyId,
                            CompanyName = companyBreakdown.CompanyName,
                            SubscriptionPlanId = companyBreakdown.SubscriptionPlanId,
                            MonthlyRecurringRevenue = companyBreakdown.MonthlyRecurringRevenue,
                            MrrDiff = mrrDiff,
                            MrrDiffPercent = mrrDiffPercent,
                            Status = mrrDiff switch
                            {
                                0 => CompanyRevenueStatus.Sustain,
                                > 0 => CompanyRevenueStatus.Increase,
                                < 0 => CompanyRevenueStatus.Decrease
                            }
                        });
                }
                else
                {
                    // New
                    result.Add(
                        new CompanyRevenueStatusBreakdown()
                        {
                            CompanyId = companyBreakdown.CompanyId,
                            CompanyName = companyBreakdown.CompanyName,
                            SubscriptionPlanId = companyBreakdown.SubscriptionPlanId,
                            MonthlyRecurringRevenue = companyBreakdown.MonthlyRecurringRevenue,
                            MrrDiff = companyBreakdown.MonthlyRecurringRevenue,
                            MrrDiffPercent = 0,
                            Status = CompanyRevenueStatus.New
                        });
                }
            });

        // Get Churn
        dailyRevenueAnalyticFromBreakdowns.ForEach(
            companyBreakdown =>
            {
                if (result.All(x => x.CompanyId != companyBreakdown.CompanyId))
                {
                    result.Add(
                        new CompanyRevenueStatusBreakdown()
                        {
                            CompanyId = companyBreakdown.CompanyId,
                            CompanyName = companyBreakdown.CompanyName,
                            SubscriptionPlanId = companyBreakdown.SubscriptionPlanId,
                            MonthlyRecurringRevenue = 0,
                            MrrDiff = -companyBreakdown.MonthlyRecurringRevenue,
                            MrrDiffPercent = -100,
                            Status = CompanyRevenueStatus.Churn
                        });
                }
            });

        // Churned In Middle
        var allAddedCompanyIds = result
            .Select(x => x.CompanyId)
            .ToList();

        for (var i = dailyRevenueAnalyticFromIndex + 1; i < dailyRevenueAnalyticToIndex; i++)
        {
            dailyRevenueAnalytics[i].CompanyRevenueBreakDowns.ForEach(
                companyBreakdown =>
                {
                    if (!allAddedCompanyIds.Contains(companyBreakdown.CompanyId))
                    {
                        result.Add(
                            new CompanyRevenueStatusBreakdown()
                            {
                                CompanyId = companyBreakdown.CompanyId,
                                CompanyName = companyBreakdown.CompanyName,
                                SubscriptionPlanId = companyBreakdown.SubscriptionPlanId,
                                MonthlyRecurringRevenue = 0,
                                MrrDiff = companyBreakdown.MonthlyRecurringRevenue,
                                MrrDiffPercent = -100,
                                Status = CompanyRevenueStatus.ChurnedInMiddle
                            });
                        allAddedCompanyIds.Add(companyBreakdown.CompanyId);
                    }
                });
        }

        // Return
        var markedAsNewCompanyIds = result
            .Where(x => x.Status == CompanyRevenueStatus.New)
            .Select(x => x.CompanyId)
            .ToList();

        for (var i = 0; i < dailyRevenueAnalyticFromIndex; i++)
        {
            dailyRevenueAnalytics[i].CompanyRevenueBreakDowns.ForEach(
                companyBreakdown =>
                {
                    if (markedAsNewCompanyIds.Contains(companyBreakdown.CompanyId))
                    {
                        var markedAsNewCompanyBreakdown = result.First(x => x.CompanyId == companyBreakdown.CompanyId);
                        markedAsNewCompanyBreakdown.Status = CompanyRevenueStatus.Return;

                        markedAsNewCompanyIds.Remove(companyBreakdown.CompanyId);
                    }
                });
        }

        return result;
    }

    public async Task<ReportRun> CreateSleekPayReport(long start, long end, string region)
    {
        var startDateTime = DateTimeOffset.FromUnixTimeSeconds(start).UtcDateTime;
        var endDateTime = DateTimeOffset.FromUnixTimeSeconds(end).UtcDateTime;

        // Default HK for now
        var stripeClient = _stripeClients.GetStripePaymentClient(region);

        var reportTyeService = new ReportTypeService(stripeClient);
        var reportType = await reportTyeService.GetAsync("activity.itemized.2");
        if (reportType.DataAvailableEnd < startDateTime
            || reportType.DataAvailableEnd < endDateTime
            || reportType.DataAvailableStart > startDateTime
            || reportType.DataAvailableStart > endDateTime
            || startDateTime > endDateTime
           )
        {
            return null;
        }

        var options = new ReportRunCreateOptions
        {
            ReportType = "activity.itemized.2",
            Parameters = new ReportRunParametersOptions
            {
                IntervalStart = startDateTime,
                IntervalEnd = endDateTime,
                Columns = new List<string>
                {
                    "balance_transaction_id",
                    "balance_transaction_created_at",
                    "balance_transaction_reporting_category",
                    "balance_transaction_component",
                    "event_type",
                    "activity_at",
                    "activity_interval_type",
                    "activity_start_date",
                    "activity_end_date",
                    "currency",
                    "connected_account_id",
                    "amount",
                    "customer_facing_currency",
                    "customer_facing_amount",
                    "balance_transaction_description",
                    "fee_id",
                    "customer_id",
                    "charge_id",
                    "payment_intent_id",
                    "payment_method_type",
                    "card_brand",
                    "card_funding",
                    "connected_account_name"
                },
            },
        };

        var service = new ReportRunService(stripeClient);
        var report = await service.CreateAsync(options);

        if (report.Status == "succeeded")
        {
            await SaveSleekPayReportToDb(
                report.Result.Id,
                await DownloadSleekPayReportCsv(
                    report.Result.Id,
                    region),
                region);
        }

        return report;
    }

    public async Task<List<SleekPayReportData>> DownloadSleekPayReportData(string fileId)
    {
        var baseAddress = $"https://files.stripe.com/v1/files/{fileId}/contents";

        var result = string.Empty;

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
            "Basic",
            Convert.ToBase64String(
                Encoding.UTF8.GetBytes(
                    _stripeAuthenticationConfig.StripePaymentSecretKeyHk))); // Default HK for now

        using var response = await httpClient.GetAsync(baseAddress);

        result = await response.Content.ReadAsStringAsync();

        List<SleekPayReportData> records = ProcessSleekPayReportCsv(result);

        return records;
    }

    public async Task<string> DownloadSleekPayReportCsv(string fileId, string region)
    {
        var baseAddress = $"https://files.stripe.com/v1/files/{fileId}/contents";

        var result = string.Empty;

        var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
            "Basic",
            Convert.ToBase64String(
                Encoding.UTF8.GetBytes(
                    _stripeAuthenticationService.GetStripeApiKey(region)))); // Default HK for now

        using var response = await httpClient.GetAsync(baseAddress);

        result = await response.Content.ReadAsStringAsync();

        return result;
    }

    public List<SleekPayReportData> ProcessSleekPayReportCsv(string csv)
    {
        var ms = new MemoryStream(Encoding.UTF8.GetBytes(csv));
        List<SleekPayReportData> records = new List<SleekPayReportData>();

        using (var reader = new StreamReader(ms, leaveOpen: true))
        {
            using (var csvReader = new CsvReader(
                       reader,
                       new CsvConfiguration(CultureInfo.InvariantCulture)
                       {
                           Delimiter = ",",
                       }))
            {
                records = csvReader
                    .GetRecords<SleekPayReportData>()
                    .ToList();
            }
        }

        return records;
    }

    public async Task<bool> SaveSleekPayReportCsvToDb(string fileId, string csv)
    {
        try
        {
            if (!await _appDbContext.CmsSleekPayReportCsvs
                    .AnyAsync(x => x.FileId == fileId))
            {
                await _appDbContext.CmsSleekPayReportCsvs.AddAsync(
                    new CmsSleekPayReportCsv
                    {
                        Csv = csv,
                        FileId = fileId
                    });

                await _appDbContext.SaveChangesAsync();
            }

            return true;
        }
        catch (Exception e)
        {
            // ignored
        }

        return false;
    }

    public async Task SaveSleekPayReportToDb(string fileId, string csv, string region)
    {
        try
        {
            List<SleekPayReportData> sleekPayReportDatas = new List<SleekPayReportData>();

            if (await _appDbContext.CmsSleekPayReportCsvs
                    .AnyAsync(x => x.FileId == fileId))
            {
                csv = (await _appDbContext.CmsSleekPayReportCsvs
                        .FirstOrDefaultAsync(x => x.FileId == fileId))?
                    .Csv;
            }
            else
            {
                if (string.IsNullOrEmpty(csv))
                {
                    csv = await DownloadSleekPayReportCsv(fileId, region);
                }

                await SaveSleekPayReportCsvToDb(fileId, csv);
            }

            CmsSleekPayReportCsv cmsSleekPayReportCsv = await _appDbContext.CmsSleekPayReportCsvs
                .FirstOrDefaultAsync(x => x.FileId == fileId);

            sleekPayReportDatas = ProcessSleekPayReportCsv(cmsSleekPayReportCsv?.Csv);

            var startActivityAt = sleekPayReportDatas.MinBy(x => x.ActivityAt)?.ActivityAt;
            var endActivityAt = sleekPayReportDatas.MaxBy(x => x.ActivityAt)?.ActivityAt;

            DateTime startActivityDate = new DateTime();
            DateTime endActivityDate = new DateTime();

            if (startActivityAt != null)
            {
                startActivityDate = DateTime.Parse(startActivityAt);
            }

            if (endActivityAt != null)
            {
                endActivityDate = DateTime.Parse(endActivityAt);
            }

            if (cmsSleekPayReportCsv != null)
            {
                cmsSleekPayReportCsv.StartActivityDate = startActivityDate.Date;
                cmsSleekPayReportCsv.EndActivityDate = endActivityDate.Date;
                await _appDbContext.SaveChangesAsync();
            }

            var accountIds = sleekPayReportDatas
                .Where(x => !string.IsNullOrEmpty(x.ConnectedAccountId))
                .GroupBy(x => x.ConnectedAccountId)
                .Select(x => x.First())
                .Select(x => x.ConnectedAccountId)
                .ToList();

            foreach (var accountId in accountIds)
            {
                var currencies = sleekPayReportDatas
                    .Where(
                        x => x.ConnectedAccountId == accountId
                             && !string.IsNullOrEmpty(x.Currency))
                    .GroupBy(x => x.Currency)
                    .Select(x => x.First())
                    .Select(x => x.Currency)
                    .ToList();

                foreach (var currency in currencies)
                {
                    var dates = sleekPayReportDatas
                        .Where(
                            x =>
                                x.ConnectedAccountId == accountId &&
                                x.Currency == currency &&
                                !string.IsNullOrEmpty(x.ActivityAt))
                        .GroupBy(x => DateTime.Parse(x.ActivityAt).Date)
                        .Select(x => x.First())
                        .Select(x => DateTime.Parse(x.ActivityAt).Date)
                        .ToList();

                    foreach (var date in dates)
                    {
                        var activityDate = date;

                        if (await _appDbContext.CmsSleekPayReportDatas
                                .AnyAsync(
                                    x =>
                                        x.StartActivityDate == date &&
                                        x.StripeAccountId == accountId &&
                                        x.Currency == currency))
                        {
                            continue;
                        }

                        var accountName = sleekPayReportDatas
                            .FirstOrDefault(x => x.ConnectedAccountId == accountId)?
                            .ConnectedAccountName;

                        var volume = sleekPayReportDatas
                            .Where(
                                x =>
                                    x.ConnectedAccountId == accountId &&
                                    x.BalanceTransactionReportingCategory == "transfer" &&
                                    DateTime.Parse(x.ActivityAt).Date == activityDate &&
                                    x.Currency == currency)
                            .Select(x => x.Amount)
                            .Sum(decimal.Parse) * (-1);

                        var earnings = sleekPayReportDatas
                            .Where(
                                x =>
                                    x.ConnectedAccountId == accountId &&
                                    x.BalanceTransactionReportingCategory == "platform_earning" &&
                                    DateTime.Parse(x.ActivityAt).Date == activityDate &&
                                    x.Currency == currency)
                            .Select(x => x.Amount)
                            .Sum(decimal.Parse);
                        var cost = sleekPayReportDatas
                            .Where(
                                x => x.ConnectedAccountId == accountId &&
                                     (x.BalanceTransactionReportingCategory == "network_cost" ||
                                      x.BalanceTransactionReportingCategory == "fee" ||
                                      x.BalanceTransactionComponent == "payments_fee") &&
                                     DateTime.Parse(x.ActivityAt).Date == activityDate &&
                                     x.Currency == currency)
                            .Select(x => x.Amount)
                            .Sum(decimal.Parse) * (-1);

                        var perAuthFees = sleekPayReportDatas
                            .Where(
                                x =>
                                    x.ConnectedAccountId == accountId &&
                                    x.EventType == "charge_authorized" &&
                                    x.BalanceTransactionComponent == "per_auth_fee" &&
                                    DateTime.Parse(x.ActivityAt).Date == activityDate &&
                                    x.Currency == currency)
                            .Select(x => x.Amount)
                            .Sum(decimal.Parse) * (-1);

                        var failedTransactionsNos = sleekPayReportDatas
                            .Where(
                                x =>
                                    x.ConnectedAccountId == accountId &&
                                    DateTime.Parse(x.ActivityAt).Date == activityDate &&
                                    x.Currency == currency &&
                                    x.EventType == "charge_failed")
                            .GroupBy(x => x.PaymentIntentId)
                            .Select(x => x.First())
                            .Count();

                        var orderNos =
                            sleekPayReportDatas
                                .Where(
                                    x =>
                                        x.ConnectedAccountId == accountId &&
                                        DateTime.Parse(x.ActivityAt).Date == activityDate &&
                                        x.Currency == currency)
                                .GroupBy(x => x.PaymentIntentId)
                                .Count()
                            - failedTransactionsNos;

                        var stripeConfig =
                            await _appDbContext.ConfigStripePaymentConfigs
                                .FirstOrDefaultAsync(x => x.AccountId == accountId);

                        var offerRate = stripeConfig?.ApplicationFeeRate ?? 0.034m;

                        var cmsSleekPayReportData = new CmsSleekPayReportData
                        {
                            Cost = cost,
                            OrderNos = orderNos,
                            GrossEarning = earnings,
                            StartActivityDate = activityDate,
                            EndActivityDate = activityDate,
                            Currency = currency,
                            CostRate = volume == 0 ? 0 : cost / volume,
                            AverageOrderValue = orderNos == 0 ? 0 : volume / orderNos,
                            NetEarning = earnings - cost,
                            VariableCostRate = volume == 0 ? 0 : (cost - perAuthFees) / volume,
                            StripeAccountId = accountId,
                            StripeAccountName = accountName,
                            OfferedRate = offerRate,
                            Volume = volume,
                            PerAuthFees = perAuthFees,
                            FailedTransactionsNos = failedTransactionsNos,
                            CmsSleekPayReportCsvId = cmsSleekPayReportCsv?.Id,
                        };

                        await _appDbContext.CmsSleekPayReportDatas.AddAsync(cmsSleekPayReportData);
                        await _appDbContext.SaveChangesAsync();
                    }
                }
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task UpdateCompanyAllTimeRevenueAnalyticInfo(List<string> companyIds = null)
    {
        // Calculate data
        var companies = await GetCompanyAnalyticDataList(companyIds);
        var companyAllTimeRevenueAnalyticInfos = new List<CmsCompanyAllTimeRevenueAnalyticData>();

        companies.ForEach(
            company =>
            {
                try
                {
                    var firstDate = company.CreatedAt;
                    var allBillRecordInitialPaidDate = company.BillRecords
                        .Select(x => x.PeriodStart)
                        .ToList();

                    allBillRecordInitialPaidDate.AddRange(
                        company.BillRecords
                            .SelectMany(x => x.CmsSalesPaymentRecords?.Select(b => b.PaidAt))
                            .Where(x => x != null)
                            .Select(x => x.Value)
                            .ToList());

                    if (allBillRecordInitialPaidDate.MinBy(x => x) < firstDate)
                    {
                        firstDate = allBillRecordInitialPaidDate.MinBy(x => x);
                    }

                    var dailyAnalytics = _billRecordRevenueCalculatorService.GetDailyAnalytics(
                        new List<CmsCompanyAnalyticDto>()
                        {
                            company
                        },
                        firstDate.Date,
                        DateTime.UtcNow);

                    var allTimeRevenueAnalyticData =
                        _billRecordRevenueCalculatorService.GetCompanyAllTimeRevenueAnalyticData(
                            company.Id,
                            dailyAnalytics);

                    companyAllTimeRevenueAnalyticInfos.Add(allTimeRevenueAnalyticData);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}]: Error getting all time revenue analytic data for {CompanyId}",
                        nameof(UpdateCompanyAllTimeRevenueAnalyticInfo),
                        company.Id);
                }
            });

        // Write to database
        foreach (var allTimeRevenueAnalyticInfoChunks in companyAllTimeRevenueAnalyticInfos.Chunk(500))
        {
            var revenueAnalyticInfoChunkIds = allTimeRevenueAnalyticInfoChunks
                .Select(a => a.CompanyId)
                .ToList();

            var companyAdditionalInfos = await _appDbContext.CmsCompanyAdditionalInfos
                .Where(x => revenueAnalyticInfoChunkIds.Contains(x.CompanyId))
                .ToListAsync();

            foreach (var companyId in revenueAnalyticInfoChunkIds)
            {
                try
                {
                    var companyAdditionalInfo = companyAdditionalInfos
                        .FirstOrDefault(x => x.CompanyId == companyId);

                    if (companyAdditionalInfo == null)
                    {
                        // Insert
                        _appDbContext.CmsCompanyAdditionalInfos.Add(
                            new CmsCompanyAdditionalInfo
                            {
                                CompanyId = companyId,
                                AllTimeRevenueAnalyticData = allTimeRevenueAnalyticInfoChunks
                                    .First(x => x.CompanyId == companyId),
                                UpdatedAt = DateTime.UtcNow,
                            });
                    }
                    else
                    {
                        // Update
                        var updatedAllTimeRevenueAnalyticData = allTimeRevenueAnalyticInfoChunks
                            .First(x => x.CompanyId == companyId);

                        if (companyAdditionalInfo.AllTimeRevenueAnalyticData == null ||
                            !updatedAllTimeRevenueAnalyticData.Equals(companyAdditionalInfo.AllTimeRevenueAnalyticData))
                        {
                            // Update when value changed
                            companyAdditionalInfo.AllTimeRevenueAnalyticData = updatedAllTimeRevenueAnalyticData;

                            _appDbContext
                                .Entry(companyAdditionalInfo)
                                .Property(x => x.AllTimeRevenueAnalyticData)
                                .IsModified = true;

                            companyAdditionalInfo.UpdatedAt = DateTime.UtcNow;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName}]: Error updating all time revenue analytic data for {CompanyId}",
                        nameof(UpdateCompanyAllTimeRevenueAnalyticInfo),
                        companyId);
                }
            }

            await _appDbContext.SaveChangesAsync();
        }
    }

    public async Task<List<CompanyLastPaidSubscriptionDateDto>> GetCompanyLastSubscriptionDate(List<string> companyIds)
    {
        var result = new List<CompanyLastPaidSubscriptionDateDto>();

        if (companyIds is not { Count: > 0 })
        {
            return result;
        }

        // Deduplication logic
        var serverLocation = _configuration["SF_REGION"] ?? LocationNames.EastAsia;
        var deduplicationCompanyIdServerLocationLookup =
            await _companiesService.GetDeduplicationCompanyIdServerLocationLookup(serverLocation);

        companyIds = companyIds
            .Where(
                serverLocation == LocationNames.EastAsia
                    ? x => !deduplicationCompanyIdServerLocationLookup.ContainsKey(x)
                    : x => deduplicationCompanyIdServerLocationLookup.ContainsKey(x))
            .ToList();

        var companiesAdditionalInfo = await _appDbContext.CmsCompanyAdditionalInfos
            .AsNoTracking()
            .Where(x => companyIds.Contains(x.CompanyId))
            .Select(
                x => new
                {
                    x.CompanyId,
                    LastPaidSubscriptionPlanId = x.AllTimeRevenueAnalyticData != null
                        ? x.AllTimeRevenueAnalyticData.LastPaidSubscriptionPlanId
                        : string.Empty
                })
            .ToListAsync();

        var companiesBillRecords = await _appDbContext.CompanyBillRecords
            .AsNoTracking()
            .Where(
                x => companyIds.Contains(x.CompanyId) &&
                     ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId) &&
                     !ValidSubscriptionPlan.FreePlans.Contains(x.SubscriptionPlanId))
            .Select(
                x => new
                {
                    x.CompanyId,
                    x.SubscriptionPlanId,
                    x.created,
                    x.PayAmount,
                    x.PeriodStart,
                    x.PeriodEnd
                })
            .ToListAsync();

        var companiesBillRecordsLookUp = companiesBillRecords
            .GroupBy(x => x.CompanyId)
            .ToDictionary(g => g.Key, g => g.ToList());

        foreach (var companyAdditionalInfo in companiesAdditionalInfo)
        {
            var lastPaidSubscriptionId = companyAdditionalInfo.LastPaidSubscriptionPlanId;

            if (string.IsNullOrEmpty(lastPaidSubscriptionId))
            {
                continue;
            }

            if (!companiesBillRecordsLookUp.TryGetValue(companyAdditionalInfo.CompanyId, out var billRecords))
            {
                continue;
            }

            var lastPaidSubscription = billRecords
                .Where(x => x.SubscriptionPlanId == lastPaidSubscriptionId)
                .OrderByDescending(x => x.created)
                .ThenByDescending(x => x.PayAmount)
                .FirstOrDefault();

            if (lastPaidSubscription != null)
            {
                result.Add(
                    new CompanyLastPaidSubscriptionDateDto
                    {
                        CompanyId = companyAdditionalInfo.CompanyId,
                        LastPaidSubscriptionPlanStartDate = lastPaidSubscription.PeriodStart.ToString("yyyy-MM-dd"),
                        LastPaidSubscriptionPlanEndDate = lastPaidSubscription.PeriodEnd.ToString("yyyy-MM-dd")
                    });
            }
        }

        return result;
    }

    public async Task<List<CmsFreeTrialDistributionDto>> GetCmsAddOnFreeTrialData()
    {
        var results = new List<CmsFreeTrialDistributionDto>();
        var salesforceCrmIntegrationAddOns = ValidSubscriptionPlan.SalesforceCrmIntegrationAddOns;
        var hubspotIntegrationAddOns = ValidSubscriptionPlan.HubspotIntegrationAddOns;
        var agentPlan = ValidSubscriptionPlan.AgentPlan;

        // TODO: To be clarified on the way on how to handle the plan names?
        var freeTrialPlans = new List<string>
        {
            "sleekflow_v9_salesforce_integration", "sleekflow_v9_hubspot_integration", "sleekflow_v9_agent_pro_monthly"
        };

        foreach (var freeTrialPlan in freeTrialPlans)
        {
            var companyIds = await _appDbContext.CompanyBillRecords
                .WhereIf(
                    salesforceCrmIntegrationAddOns.Contains(freeTrialPlan),
                    x => salesforceCrmIntegrationAddOns.Contains(x.SubscriptionPlanId) && x.IsFreeTrial)
                .WhereIf(
                    hubspotIntegrationAddOns.Contains(freeTrialPlan),
                    x => hubspotIntegrationAddOns.Contains(x.SubscriptionPlanId) && x.IsFreeTrial)
                .WhereIf(
                    agentPlan.Contains(freeTrialPlan),
                    x => agentPlan.Contains(x.SubscriptionPlanId) && x.IsFreeTrial)
                .Select(x => x.CompanyId)
                .ToListAsync();

            var result = new CmsFreeTrialDistributionDto()
            {
                Plan = freeTrialPlan,
                CompanyIds = companyIds,
                Count = companyIds.Count,
            };

            results.Add(result);
        }

        return results;
    }

    private async Task<List<string>> GetOwnerIdsByTeamNames(List<string> teamNames)
    {
        var ownerIds = new List<string>();

        var contactOwnerIdTeams = await _appDbContext.CmsHubSpotContactOwnerMaps
            .Select(
                x => new
                {
                    x.ContactOwnerId,
                    x.HubspotTeams
                })
            .ToListAsync();

        contactOwnerIdTeams.ForEach(
            x =>
            {
                if (x.HubspotTeams == null || x.HubspotTeams.Count == 0)
                {
                    return;
                }

                if (x.HubspotTeams.Exists(
                        internalHubSpotTeamDto => teamNames.Contains(internalHubSpotTeamDto.Name)))
                {
                    ownerIds.Add(x.ContactOwnerId);
                }
            });

        return ownerIds;
    }

    private async Task<List<CmsCompanyAnalyticDto>> GetCompaniesWithCache(
        string companiesCacheKey,
        bool allowCompaniesCache,
        List<string> companyIds = null,
        bool excludeMarkupPlan = false,
        List<string> companyOwnerIds = null,
        List<string> activationOwnerIds = null,
        List<string> csOwnerIds = null)
    {
        if (allowCompaniesCache)
        {
            var companiesCache = await _cacheManagerService.GetCacheWithConstantKeyAsync(companiesCacheKey);

            if (!string.IsNullOrWhiteSpace(companiesCache))
            {
                return JsonConvert.DeserializeObject<List<CmsCompanyAnalyticDto>>(companiesCache);
            }
        }

        var companies = await GetCompanyAnalyticDataList(
            companyIds,
            excludeMarkupPlan,
            companyOwnerIds,
            activationOwnerIds,
            csOwnerIds);

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            companiesCacheKey,
            companies,
            TimeSpan.FromHours(1));

        return companies;
    }
}

public class SleekPayReportData
{
    [JsonProperty("balance_transaction_id")]
    [Name("balance_transaction_id")]
    [Optional]
    public string BalanceTransactionId { get; set; }

    [JsonProperty("balance_transaction_created_at")]
    [Name("balance_transaction_created_at")]
    [Optional]
    public string BalanceTransactionCreatedAt { get; set; }

    [JsonProperty("balance_transaction_reporting_category")]
    [Name("balance_transaction_reporting_category")]
    [Optional]
    public string BalanceTransactionReportingCategory { get; set; }

    [JsonProperty("balance_transaction_component")]
    [Name("balance_transaction_component")]
    [Optional]
    public string BalanceTransactionComponent { get; set; }

    [JsonProperty("event_type")]
    [Name("event_type")]
    [Optional]
    public string EventType { get; set; }

    [JsonProperty("activity_at")]
    [Name("activity_at")]
    [Optional]
    public string ActivityAt { get; set; }

    [JsonProperty("activity_interval_type")]
    [Name("activity_interval_type")]
    [Optional]
    public string ActivityIntervalType { get; set; }

    [JsonProperty("activity_start_date")]
    [Name("activity_start_date")]
    [Optional]
    public string ActivityStartDate { get; set; }

    [JsonProperty("activity_end_date")]
    [Name("activity_end_date")]
    [Optional]
    public string ActivityEndDate { get; set; }

    [JsonProperty("currency")]
    [Name("currency")]
    [Optional]
    public string Currency { get; set; }

    [JsonProperty("amount")]
    [Name("amount")]
    [Optional]
    public string Amount { get; set; }

    [JsonProperty("customer_facing_currency")]
    [Name("customer_facing_currency")]
    [Optional]
    public string CustomerFacingCurrency { get; set; }

    [JsonProperty("customer_facing_amount")]
    [Name("customer_facing_amount")]
    [Optional]
    public string CustomerFacingAmount { get; set; }

    [JsonProperty("balance_transaction_description")]
    [Name("balance_transaction_description")]
    [Optional]
    public object BalanceTransactionDescription { get; set; }

    [JsonProperty("fee_id")]
    [Name("fee_id")]
    [Optional]
    public object FeeId { get; set; }

    [JsonProperty("customer_id")]
    [Name("customer_id")]
    [Optional]
    public string CustomerId { get; set; }

    [JsonProperty("charge_id")]
    [Name("charge_id")]
    [Optional]
    public string ChargeId { get; set; }

    [JsonProperty("payment_intent_id")]
    [Name("payment_intent_id")]
    [Optional]
    public string PaymentIntentId { get; set; }

    [JsonProperty("payment_method_type")]
    [Name("payment_method_type")]
    [Optional]
    public string PaymentMethodType { get; set; }

    [JsonProperty("card_brand")]
    [Name("card_brand")]
    [Optional]
    public string CardBrand { get; set; }

    [JsonProperty("card_funding")]
    [Name("card_funding")]
    [Optional]
    public string CardFunding { get; set; }

    [JsonProperty("connected_account_name")]
    [Name("connected_account_name")]
    [Optional]
    public string ConnectedAccountName { get; set; }

    [JsonProperty("connected_account_id")]
    [Name("connected_account_id")]
    [Optional]
    public string ConnectedAccountId { get; set; }
}