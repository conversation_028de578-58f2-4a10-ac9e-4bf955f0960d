#nullable enable

using System;
using System.Linq;
using System.Threading.Tasks;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.Database;
using Travis_backend.Helpers;

namespace Travis_backend.InternalIntegrationHubDomain.Services;

public interface IIrregularPlanInvoiceService
{
    Task ProcessDailyIrregularPlanInvoicesAsync();
}

public class IrregularPlanInvoiceService : IIrregularPlanInvoiceService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<IrregularPlanInvoiceService> _logger;
    private readonly IBillRecordRevenueCalculatorService _billRecordRevenueCalculatorService;
    private readonly IIrregularPlanInvoiceCalculator _irregularPlanInvoiceCalculator;

    public IrregularPlanInvoiceService(
        ApplicationDbContext dbContext,
        ILogger<IrregularPlanInvoiceService> logger,
        IBillRecordRevenueCalculatorService billRecordRevenueCalculatorService,
        IIrregularPlanInvoiceCalculator irregularPlanInvoiceCalculator)
    {
        _dbContext = dbContext;
        _logger = logger;
        _billRecordRevenueCalculatorService = billRecordRevenueCalculatorService;
        _irregularPlanInvoiceCalculator = irregularPlanInvoiceCalculator;
    }

    #region Public Methods

    public async Task ProcessDailyIrregularPlanInvoicesAsync()
    {
        var today = DateTime.UtcNow.Date;

        _logger.LogInformation("Starting ProcessDailyIrregularPlanInvoicesAsync for date: {Today}", today);

        try
        {
            // Get eligible bill records
            var eligibleBillRecords = await _dbContext.CompanyBillRecords
                .Include(x => x.CmsSalesPaymentRecords)
                .Where(x => x.IsIrregularPlan &&
                            x.PeriodEnd.Date >= today &&
                            x.PayAmount > 0 &&
                            x.PaymentIntervalType != PaymentIntervalType.OneOff) // Exclude one-off payments
                .ToListAsync();

            _logger.LogInformation("Found {Count} eligible irregular plan bill records", eligibleBillRecords.Count);

            if (eligibleBillRecords.Count == 0)
            {
                return; // Early exit if no records to process
            }

            var invoicesCreated = 0;

            // Process bill records and create invoices as needed
            foreach (var billRecord in eligibleBillRecords)
            {
                try
                {
                    var invoiceSequence = _irregularPlanInvoiceCalculator.GetInvoiceSequenceForDate(billRecord, today);

                    // Skip invalid sequences (0 or negative)
                    if (invoiceSequence <= 0)
                    {
                        continue;
                    }

                    // For non-custom plans, skip sequence 1 (first invoice already created)
                    // For custom plans; sequence 1 represents the first legitimate invoice
                    if (invoiceSequence == 1 && billRecord.PaymentIntervalType != PaymentIntervalType.Custom)
                    {
                        continue;
                    }

                    CreateInvoiceForIrregularPlan(billRecord, invoiceSequence, today);

                    invoicesCreated++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing bill record {BillRecordId}: {ErrorMessage}", billRecord.Id, ex.Message);

                    // Continue processing other records even if one fails
                }
            }

            _logger.LogInformation(
                "Completed ProcessDailyIrregularPlanInvoicesAsync. Created {InvoicesCreated} invoices",
                invoicesCreated);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in ProcessDailyIrregularPlanInvoicesAsync: {ErrorMessage}", ex.Message);
            throw new InvalidOperationException("Failed to process daily irregular plan invoices", ex);
        }
    }

    #endregion

    #region Core Business Logic

    // Core calculation moved into IrregularPlanInvoiceCalculator for testability.
    private void CreateInvoiceForIrregularPlan(BillRecord billRecord, int invoiceSequence, DateTime today)
    {
        _logger.LogInformation("Creating invoice for BillRecord {BillRecordId}", billRecord.Id);

        // Calculate the correct invoice amount based on the payment interval type
        var invoiceAmount = _billRecordRevenueCalculatorService.CalculateInvoiceAmount(billRecord, today);

        // Use the pre-calculated invoice sequence
        var invoiceNumber = $"INV-{billRecord.Id}-{invoiceSequence}";

        var actualPeriodEnd = BillRecordRevenueCalculatorHelper.GetActualPeriodEndFromMetadata(billRecord);
        var jobId = BackgroundJob.Enqueue<IInternalIntegrationService>(x => x.CreateInvoiceAsync(
                invoiceNumber,
                billRecord.CompanyId,
                invoiceAmount,
                0,
                0,
                billRecord.PeriodStart,
                actualPeriodEnd,
                billRecord.PaymentTerms,
                billRecord.currency,
                null,
                today));

        _logger.LogInformation(
            "Successfully queued NetSuite invoice {InvoiceNumber} for BillRecord {BillRecordId} ({PaymentType}: {InvoiceSequence}) with amount {InvoiceAmount} as background job. JobId: {JobId}",
            invoiceNumber,
            billRecord.Id,
            billRecord.PaymentIntervalType,
            invoiceSequence,
            invoiceAmount,
            jobId);
    }

    #endregion
}
