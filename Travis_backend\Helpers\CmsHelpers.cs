﻿using System;

namespace Travis_backend.Helpers
{
    /// <summary>
    /// Static utility class for currency conversion operations.
    /// </summary>
    public static class CurrencyConverter
    {
        /// <summary>
        /// Converts an amount from the specified currency to USD.
        /// </summary>
        /// <param name="amount">The amount to convert.</param>
        /// <param name="currency">The source currency code.</param>
        /// <returns>The amount converted to USD.</returns>
        public static decimal ConvertToUsd(decimal amount, string currency)
        {
            if (string.IsNullOrWhiteSpace(currency))
            {
                return amount;
            }

            return currency.ToLower() switch
            {
                "hkd" => amount / 7.85M,
                "sgd" => amount / 1.34M,
                "myr" => amount / 4.38M,
                "cny" => amount / 7.05M,
                "idr" => amount / 15000M,
                "eur" => amount / 1.00M,
                "gbp" => amount / 0.81M,
                "cad" => amount / 1.26M,
                "aud" => amount / 1.4M,
                "aed" => amount / 3.68M,
                "brl" => amount / 5.15M,
                "inr" => amount / 81.96M,
                _ => amount
            };
        }
    }
}