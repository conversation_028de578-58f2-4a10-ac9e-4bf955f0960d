using System;
using System.Diagnostics.Metrics;
using Microsoft.Extensions.Logging;

namespace Travis_backend.OpenTelemetry.Meters;

public interface IBasicMeters
{
    void IncrementCounter<T>(T name, string? option = null, int count = 1);
}

public class BaseMeters : IBasicMeters
{
    public const string SleekflowCoreMeters = "sleekflow_core.meters";
    private readonly Meter _meter;
    private readonly ILogger _logger;

    protected bool IsMeterEnabled { get; }

    protected BaseMeters(IMeterFactory meterFactory, ILogger logger)
    {
        _logger = logger;
        IsMeterEnabled = false;
        if (string.IsNullOrEmpty(Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_CONNECTION_STRING")))
        {
            return;
        }

        IsMeterEnabled = true;
        _meter = meterFactory.Create(SleekflowCoreMeters);
    }

    protected Counter<T> CreateCounter<T>(string name, string? unit = null, string? description = null)
        where T : struct
    {
        return _meter.CreateCounter<T>(name, unit, description);
    }

    public void IncrementCounter<T>(T name, string? option = null, int count = 1)
    {
        try
        {
            if (IsMeterEnabled)
            {
                GetCounter(name, option).Add(count);
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(exception, "Error occur during increment counter");
        }
    }

    protected virtual Counter<int> GetCounter<T>(T name, string? option = null)
    {
        throw new NotImplementedException();
    }

    protected static string GetComposeKey(string name, string? option)
    {
        return option is null ? name : $"{name}.{option}";
    }
}