﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using GraphApi.Client.Const.WhatsappCloudApi;
using GraphApi.Client.Helpers;
using GraphApi.Client.Helpers.FlowSubmissionPayloadFieldsExtractionHelper;
using GraphApi.Client.Payloads.Models.Flows;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.AuditHub.Api;
using Sleekflow.Apis.AuditHub.Model;
using Sleekflow.Apis.FlowHub.Api;
using Sleekflow.Apis.FlowHub.Model;
using Sleekflow.Apis.MessagingHub.Model;
using Sleekflow.Apis.TicketingHub.Model;
using Travis_backend.BackgroundTaskServices.Attributes;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.**********************;
using Travis_backend.FlowHubs.Mappers;
using Travis_backend.FlowHubs.Models;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.FlowHubs;

public interface IUserProfileHooks
{
    Task OnMessageReceivedAsync(
        string companyId,
        string userProfileId,
        Conversation conversation,
        ConversationMessage conversationMessage);

    Task OnMessageSentAsync(
        string companyId,
        string userProfileId,
        Conversation conversation,
        ConversationMessage conversationMessage);

    Task OnMessageStatusUpdatedAsync(
        string companyId,
        string userProfileId,
        Conversation conversation,
        ConversationMessage conversationMessage);

    /// <summary>
    /// Processes Meta detected outcomes received from a WhatsApp Cloud API webhook.
    /// </summary>
    /// <param name="companyId">The company ID associated with the detected outcome.</param>
    /// <param name="userProfileId">The user profile ID associated with the detected outcome.</param>
    /// <param name="detectedOutcome">The detected outcome data received from Meta.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task OnMetaDetectedOutcomesReceivedAsync(
        string companyId,
        string userProfileId,
        WhatsappCloudApiWebhookMessageDetectedOutcome detectedOutcome);

    Task OnLiveChatWebsiteUrlDetectedAsync(
        string companyId,
        string channelId,
        string userProfileId,
        string url);

    Task OnConversationAssignedTeamChangedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationAssignedTeamChangedData>> dataFunc);

    Task OnConversationAssigneeChangedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationAssigneeChangedData>> dataFunc);

    Task OnConversationCollaboratorAddedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationCollaboratorAddedData>> dataFunc);

    Task OnConversationCollaboratorRemovedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationCollaboratorRemovedData>> dataFunc);

    Task OnConversationLabelsAddedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationLabelsAddedData>> dataFunc);

    Task OnConversationLabelsRemovedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationLabelsRemovedData>> dataFunc);

    Task OnConversationLabelsSetAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationLabelsSetData>> dataFunc);

    Task OnConversationReadAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationReadData>> dataFunc);

    Task OnConversationStatusChangedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationStatusChangedData>> dataFunc);

    Task OnUserProfileCreatedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileCreatedData>> dataFunc);

    Task OnUserProfileDeletedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileDeletedData>> dataFunc);

    Task OnUserProfilesDeletedAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        Func<Task<OnUserProfilesDeletedData>> dataFunc);

    Task OnUserProfileSoftDeletedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileSoftDeletedData>> dataFunc);

    Task OnUserProfilesSoftDeletedAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        Func<Task<OnUserProfilesSoftDeletedData>> dataFunc);

    Task OnUserProfileRecoveredAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileRecoveredData>> dataFunc);

    Task OnUserProfilesRecoveredAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        Func<Task<OnUserProfilesRecoveredData>> dataFunc);

    Task OnUserProfileFieldChangedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileFieldChangedData>> dataFunc);

    Task OnUserProfileAddedToListAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileAddedToListData>> dataFunc);

    Task OnUserProfileRemovedFromListAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileRemovedToListData>> dataFunc);

    Task OnUserProfileEnrolledIntoFlowHubWorkflowAsync(
        string companyId,
        string userProfileId,
        Func<Task<OnUserProfileEnrolledIntoFlowHubWorkflowData>> dataFunc);

    [HangfireMethodConcurrencyLimit("hooks:{0}:{2}", 20, 10)]
    Task OnUserProfileManuallyEnrolledAsync(
        string companyId,
        string userProfileId,
        string workflowId,
        string workflowVersionedId);
}

public class UserProfileHooks : IUserProfileHooks
{
    private readonly IFlowHubService _flowHubService;
    private readonly ILogger<UserProfileHooks> _logger;
    private readonly ICompanyService _companyService;
    private readonly ApplicationDbContext _appDbContext;
    private readonly IConfiguration _configuration;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;
    private readonly IAuditHubAuditLogService _auditHubAuditLogService;
    private readonly ISystemAuditLogsApi _systemAuditLogsApi;

    public UserProfileHooks(
        IFlowHubService flowHubService,
        ILogger<UserProfileHooks> logger,
        ICompanyService companyService,
        ApplicationDbContext appDbContext,
        IConfiguration configuration,
        IDistributedInvocationContextService distributedInvocationContextService,
        IAuditHubAuditLogService auditHubAuditLogService,
        ISystemAuditLogsApi systemAuditLogsApi)
    {
        _flowHubService = flowHubService;
        _logger = logger;
        _companyService = companyService;
        _appDbContext = appDbContext;
        _configuration = configuration;
        _distributedInvocationContextService = distributedInvocationContextService;
        _auditHubAuditLogService = auditHubAuditLogService;
        _systemAuditLogsApi = systemAuditLogsApi;
    }

    public async Task OnMessageReceivedAsync(
        string companyId,
        string userProfileId,
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        try
        {
            if (!await _flowHubService.IsFlowHubEnabledAsync(companyId))
            {
                return;
            }

            if (IsImportedMessage(conversationMessage))
            {
                return;
            }

            if (!IsSupportedMessageTypeForIncomingMessageTrigger(conversationMessage))
            {
                return;
            }

            var channelId = conversationMessage.ChannelIdentityId;
            var conversationId = conversationMessage.ConversationId;
            var messageId = conversationMessage.Id.ToString();

            if (conversation.UserProfileId != userProfileId)
            {
                throw new Exception("Conversation user profile id does not match the user profile id");
            }

            var (messageType, messageBody, messageContent) = GetMessageBody(conversationMessage);

            var userProfileDict = await _flowHubService.GetUserProfileDictAsync(companyId, userProfileId);

            var quotedMessage = await GetQuotedMessageDataAsync(
                companyId,
                conversationMessage.QuotedMsgId);

            switch (messageType)
            {
                case "referral":
                    var referral = conversationMessage.ExtendedMessagePayload
                        .ExtendedMessagePayloadDetail
                        .WhatsappCloudApiReferral;

                    BackgroundJob.Enqueue<IEventsApi>(
                        x => x.EventsOnClickToWhatsAppAdsMessageReceivedEventPostAsync(
                            null,
                            _distributedInvocationContextService.GetSerializedContextHeader(),
                            new OnClickToWhatsAppAdsMessageReceivedEventInput(
                                companyId,
                                userProfileId,
                                new OnClickToWhatsAppAdsMessageReceivedEventBody(
                                    new OnClickToWhatsAppAdsMessageReceivedEventBodyMessage(
                                        messageId,
                                        referral.CtwaClid,
                                        referral.SourceId,
                                        referral.SourceUrl,
                                        referral.Headline,
                                        referral.Body,
                                        conversationMessage.CreatedAt,
                                        conversationMessage.UpdatedAt,
                                        messageBody,
                                        messageType,
                                        messageContent,
                                        conversationMessage.Status.ToString(),
                                        conversationMessage.DeliveryType.ToString()),
                                    conversationMessage.Channel,
                                    channelId,
                                    conversationId,
                                    messageId,
                                    conversationMessage.MessageUniqueID,
                                    userProfileId,
                                    userProfileDict.ToDictionary(
                                        e => e.Key,
                                        e => (object) e.Value),
                                    conversation.IsNewCreatedConversation,
                                    DateTimeOffset.UtcNow)),
                            CancellationToken.None));

                    break;
                case "interactive_reply":
                    var interactiveReplyMessage = messageBody.InteractiveReplyMessage;

                    switch (interactiveReplyMessage.Type)
                    {
                        case WhatsappCloudApiInteractiveReplyTypeConst.nfm_reply:
                            var whatsappFlowSubmissionMessageEventBodyData =
                                WhatsappCloudApiFlowSubmissionHelper
                                    .ResolveOnWhatsappFlowSubmissionMessageReceivedEventBodyData(
                                        conversationMessage,
                                        messageBody,
                                        quotedMessage);

                            BackgroundJob.Enqueue<IEventsApi>(
                                x => x.EventsOnWhatsappFlowSubmissionMessageReceivedEventPostAsync(
                                    null,
                                    _distributedInvocationContextService.GetSerializedContextHeader(),
                                    new OnWhatsappFlowSubmissionMessageReceivedEventInput(
                                        companyId,
                                        userProfileId,
                                        new OnWhatsappFlowSubmissionMessageReceivedEventBody(
                                            new OnWhatsappFlowSubmissionMessageReceivedEventBodyMessage(
                                                messageId,
                                                whatsappFlowSubmissionMessageEventBodyData.WhatsappFlowId,
                                                whatsappFlowSubmissionMessageEventBodyData.WhatsappTemplateId,
                                                whatsappFlowSubmissionMessageEventBodyData.FlowSubmissionData,
                                                conversationMessage.CreatedAt,
                                                conversationMessage.UpdatedAt,
                                                messageBody,
                                                messageType,
                                                messageContent,
                                                conversationMessage.Status.ToString(),
                                                conversationMessage.DeliveryType.ToString()),
                                            conversationMessage.Channel,
                                            channelId,
                                            conversationId,
                                            messageId,
                                            conversationMessage.MessageUniqueID,
                                            userProfileId,
                                            userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                                            conversation.IsNewCreatedConversation,
                                            DateTimeOffset.UtcNow)),
                                    CancellationToken.None));

                            break;
                        case WhatsappCloudApiInteractiveReplyTypeConst.list_reply:
                        case WhatsappCloudApiInteractiveReplyTypeConst.button_reply:
                            BackgroundJob.Enqueue<IEventsApi>(
                                x => x.EventsOnMessageReceivedEventPostAsync(
                                    null,
                                    _distributedInvocationContextService.GetSerializedContextHeader(),
                                    new OnMessageReceivedEventInput(
                                        companyId,
                                        userProfileId,
                                        new OnMessageReceivedEventBody(
                                            new OnMessageReceivedEventBodyMessage(
                                                messageId,
                                                quotedMessage,
                                                conversationMessage.CreatedAt,
                                                conversationMessage.UpdatedAt,
                                                messageBody,
                                                messageType,
                                                messageContent,
                                                conversationMessage.Status.ToString(),
                                                conversationMessage.DeliveryType.ToString()),
                                            conversationMessage.Channel,
                                            channelId,
                                            conversationId,
                                            messageId,
                                            conversationMessage.MessageUniqueID,
                                            userProfileId,
                                            userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                                            conversation.IsNewCreatedConversation,
                                            DateTimeOffset.UtcNow)),
                                    CancellationToken.None));

                            break;
                    }

                    break;
                default:
                    BackgroundJob.Enqueue<IEventsApi>(
                        x => x.EventsOnMessageReceivedEventPostAsync(
                            null,
                            _distributedInvocationContextService.GetSerializedContextHeader(),
                            new OnMessageReceivedEventInput(
                                companyId,
                                userProfileId,
                                new OnMessageReceivedEventBody(
                                    new OnMessageReceivedEventBodyMessage(
                                        messageId,
                                        quotedMessage,
                                        conversationMessage.CreatedAt,
                                        conversationMessage.UpdatedAt,
                                        messageBody,
                                        messageType,
                                        messageContent,
                                        conversationMessage.Status.ToString(),
                                        conversationMessage.DeliveryType.ToString()),
                                    conversationMessage.Channel,
                                    channelId,
                                    conversationId,
                                    messageId,
                                    conversationMessage.MessageUniqueID,
                                    userProfileId,
                                    userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                                    conversation.IsNewCreatedConversation,
                                    DateTimeOffset.UtcNow)),
                            CancellationToken.None));

                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[UserProfileHooks {MethodName}] Company {CompanyId} user profile {UserProfileId} error " +
                "for message id {MessageId} in conversation {ConversationId}. {ExceptionMessage}",
                nameof(OnMessageReceivedAsync),
                companyId,
                userProfileId,
                conversationMessage?.Id,
                conversation?.Id,
                ex.Message);
        }
    }

    private static bool IsImportedMessage(ConversationMessage conversationMessage)
    {
        return conversationMessage.IsFromImport;
    }

    private static bool IsSupportedMessageTypeForIncomingMessageTrigger(ConversationMessage conversationMessage)
    {
        // "request_welcome" message type is temporarily excluded from FlowHub incoming message trigger as per DEVS-5276
        return conversationMessage switch
        {
            { Channel: ChannelTypes.WhatsappCloudApi, MessageType: not "request_welcome" } => true,
            {
                Channel: ChannelTypes.Facebook or ChannelTypes.Instagram or ChannelTypes.LiveChat or ChannelTypes.Wechat
                or ChannelTypes.Telegram or ChannelTypes.Sms or ChannelTypes.Line or ChannelTypes.Viber or ChannelTypes.LiveChatV2
            }

            => true,
            _ => false
        };
    }

    public async Task OnMessageSentAsync(
        string companyId,
        string userProfileId,
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        try
        {
            if (!await _flowHubService.IsFlowHubEnabledAsync(companyId))
            {
                return;
            }

            if (IsImportedMessage(conversationMessage))
            {
                return;
            }

            if (!IsSupportedMessageTypeForOutgoingMessageTrigger(conversationMessage))
            {
                return;
            }

            var channelId = conversationMessage.ChannelIdentityId;
            var conversationId = conversationMessage.ConversationId;
            var messageId = conversationMessage.Id.ToString();

            if (conversation.UserProfileId != userProfileId)
            {
                throw new Exception("Conversation user profile id does not match the user profile id");
            }

            var (messageType, messageBody, messageContent) = GetMessageBody(conversationMessage);

            var userProfileDict = await _flowHubService.GetUserProfileDictAsync(companyId, userProfileId);
            var staffIdentityId = conversationMessage.SenderId;

            var quotedMessage = await GetQuotedMessageDataAsync(
                companyId,
                conversationMessage.QuotedMsgId);

            BackgroundJob.Enqueue<IEventsApi>(
                x => x.EventsOnMessageSentEventPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new OnMessageSentEventInput(
                        companyId,
                        userProfileId,
                        new OnMessageSentEventBody(
                            new OnMessageSentEventBodyMessage(
                                quotedMessage,
                                conversationMessage.CreatedAt,
                                conversationMessage.UpdatedAt,
                                messageBody,
                                messageType,
                                messageContent,
                                conversationMessage.Status.ToString(),
                                conversationMessage.DeliveryType.ToString()),
                            null,
                            null,
                            conversationMessage.Channel,
                            channelId,
                            conversationId,
                            messageId,
                            conversationMessage.MessageUniqueID,
                            userProfileId,
                            userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                            staffIdentityId,
                            DateTimeOffset.UtcNow)),
                    CancellationToken.None));
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[UserProfileHooks {MethodName}] Company {CompanyId} user profile {UserProfileId} error " +
                "for message id {MessageId} in conversation {ConversationId}. {ExceptionMessage}",
                nameof(OnMessageSentAsync),
                companyId,
                userProfileId,
                conversationMessage?.Id,
                conversation?.Id,
                ex.Message);
        }
    }

    private static bool IsSupportedMessageTypeForOutgoingMessageTrigger(ConversationMessage conversationMessage)
    {
        return conversationMessage switch
        {
            {
                Channel: ChannelTypes.Facebook or ChannelTypes.Instagram or ChannelTypes.LiveChat or ChannelTypes.Wechat
                or ChannelTypes.Telegram or ChannelTypes.WhatsappCloudApi or ChannelTypes.Sms or ChannelTypes.Line
                or ChannelTypes.Viber or ChannelTypes.LiveChatV2
            }

            => true,
            _ => false
        };
    }

    public async Task OnMessageStatusUpdatedAsync(
        string companyId,
        string userProfileId,
        Conversation conversation,
        ConversationMessage conversationMessage)
    {
        try
        {
            if (await _flowHubService.IsFlowHubEnabledAsync(companyId))
            {
                if (conversationMessage.Channel != ChannelTypes.WhatsappCloudApi)
                {
                    return;
                }

                if (IsImportedMessage(conversationMessage))
                {
                    return;
                }

                var supportedStatuses = new[]
                {
                    MessageStatus.Sent,
                    MessageStatus.Failed,
                    MessageStatus.Received,
                    MessageStatus.Read
                };

                if (!supportedStatuses.Contains(conversationMessage.Status))
                {
                    return;
                }

                var channelId = conversationMessage.ChannelIdentityId;
                var conversationId = conversationMessage.ConversationId;
                var messageId = conversationMessage.Id.ToString();

                if (conversation.UserProfileId != userProfileId)
                {
                    throw new Exception("Conversation user profile id does not match the user profile id");
                }

                var (messageType, messageBody, messageContent) = GetMessageBody(conversationMessage);

                var userProfileDict = await _flowHubService.GetUserProfileDictAsync(companyId, userProfileId);
                var staffIdentityId = conversationMessage.SenderId;

                var quotedMessage = await GetQuotedMessageDataAsync(
                    companyId,
                    conversationMessage.QuotedMsgId);

                var analyticTags = conversationMessage.AnalyticTags is not null
                    ? string.Join(",", conversationMessage.AnalyticTags)
                    : null;

                BackgroundJob.Enqueue<IEventsApi>(
                    x => x.EventsOnMessageStatusUpdatedEventPostAsync(
                        null,
                        _distributedInvocationContextService.GetSerializedContextHeader(),
                        new OnMessageStatusUpdatedEventInput(
                            companyId,
                            userProfileId,
                            new OnMessageStatusUpdatedEventBody(
                                new OnMessageStatusUpdatedEventBodyMessage(
                                    quotedMessage,
                                    analyticTags,
                                    conversationMessage.CreatedAt,
                                    conversationMessage.UpdatedAt,
                                    messageBody,
                                    messageType,
                                    messageContent,
                                    conversationMessage.Status.ToString(),
                                    conversationMessage.DeliveryType.ToString()),
                                null,
                                null,
                                conversationMessage.Channel,
                                channelId,
                                conversationId,
                                messageId,
                                conversationMessage.MessageUniqueID,
                                userProfileId,
                                userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                                staffIdentityId,
                                DateTimeOffset.UtcNow)),
                        CancellationToken.None));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[UserProfileHooks {MethodName}] Company {CompanyId} user profile {UserProfileId} error " +
                "for message id {MessageId} in conversation {ConversationId}. {ExceptionMessage}",
                nameof(OnMessageStatusUpdatedAsync),
                companyId,
                userProfileId,
                conversationMessage?.Id,
                conversation?.Id,
                ex.Message);
        }
    }

    /// <summary>
    /// Processes Meta detected outcomes received from a WhatsApp Cloud API webhook.
    /// </summary>
    /// <param name="companyId">The company ID associated with the detected outcome.</param>
    /// <param name="userProfileId">The user profile ID associated with the detected outcome.</param>
    /// <param name="detectedOutcome">The detected outcome data received from Meta.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    public async Task OnMetaDetectedOutcomesReceivedAsync(
        string companyId,
        string userProfileId,
        WhatsappCloudApiWebhookMessageDetectedOutcome detectedOutcome)
    {
        try
        {
            if (await _flowHubService.IsFlowHubEnabledAsync(companyId))
            {
                if (detectedOutcome == null)
                {
                    return;
                }

                var customData = new CustomData(
                    detectedOutcome.CustomData?.Currency,
                    detectedOutcome.CustomData?.Value ?? 0);

                BackgroundJob.Enqueue<IEventsApi>(
                    x => x.EventsOnMetaDetectedOutcomeReceivedEventPostAsync(
                        null,
                        _distributedInvocationContextService.GetSerializedContextHeader(),
                        new OnMetaDetectedOutcomeReceivedEventInput(
                            companyId,
                            userProfileId,
                            new OnMetaDetectedOutcomeReceivedEventBody(
                                new OnMetaDetectedOutcomeReceivedEventBodyDetectedOutcome(
                                    detectedOutcome.Id,
                                    detectedOutcome.EventName,
                                    detectedOutcome.Timestamp,
                                    detectedOutcome.CtwaClid,
                                    customData),
                                DateTimeOffset.UtcNow)),
                        CancellationToken.None));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[UserProfileHooks {MethodName}] Company {CompanyId} user profile {UserProfileId} error. {ExceptionMessage}",
                nameof(OnMetaDetectedOutcomesReceivedAsync),
                companyId,
                userProfileId,
                ex.Message);
        }
    }

    public async Task OnLiveChatWebsiteUrlDetectedAsync(
        string companyId,
        string channelId,
        string userProfileId,
        string url)
    {
        try
        {
            if (await _flowHubService.IsFlowHubEnabledAsync(companyId))
            {
                var userProfileDict = await _flowHubService.GetUserProfileDictAsync(companyId, userProfileId);
                BackgroundJob.Enqueue<IEventsApi>(
                    x => x.EventsOnLiveChatWebsiteUrlDetectedEventPostAsync(
                        null,
                        _distributedInvocationContextService.GetSerializedContextHeader(),
                        new OnLiveChatWebsiteUrlDetectedEventInput(
                            companyId,
                            userProfileId,
                            new OnLiveChatWebsiteUrlDetectedEventBody(
                                channelId,
                                url,
                                userProfileId,
                                userProfileDict.ToDictionary(e => e.Key, object (e) => e.Value),
                                DateTimeOffset.UtcNow)),
                        CancellationToken.None));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[UserProfileHooks {MethodName}] Company {CompanyId} user profile {UserProfileId} error. {ExceptionMessage}",
                nameof(OnLiveChatWebsiteUrlDetectedAsync),
                companyId,
                userProfileId,
                ex.Message);
        }
    }

    public async Task OnConversationAssignedTeamChangedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationAssignedTeamChangedData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        var originalTeamLogData = new TeamLogData();

        if (data.OriginalTeam != null)
        {
            originalTeamLogData.TeamId = data.OriginalTeam.TeamId;
            originalTeamLogData.Name = data.OriginalTeam.Name;
        }

        var newTeamLogData = new TeamLogData();

        if (data.NewTeam != null)
        {
            newTeamLogData.TeamId = data.NewTeam.TeamId;
            newTeamLogData.Name = data.NewTeam.Name;
        }

        var staffIdentityId = await GetStaffIdentityIdAsync(companyId, staffId);

        await _auditHubAuditLogService.CreateConversationAssignedTeamChangedLogAsync(
            companyId,
            userProfileId,
            staffIdentityId,
            new AssignedTeamChangedLogData(originalTeamLogData, newTeamLogData));
    }

    public async Task OnConversationAssigneeChangedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationAssigneeChangedData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        var originalAssigneeLogData = new AssigneeLogData();

        if (data.OriginalAssignee != null)
        {
            originalAssigneeLogData.AssigneeId = data.OriginalAssignee.StaffIdentityId;
            originalAssigneeLogData.Name = data.OriginalAssignee.Name;
        }

        var newAssigneeLogData = new AssigneeLogData();

        if (data.NewAssignee != null)
        {
            newAssigneeLogData.AssigneeId = data.NewAssignee.StaffIdentityId;
            newAssigneeLogData.Name = data.NewAssignee.Name;
        }

        var staffIdentityId = await GetStaffIdentityIdAsync(companyId, staffId);

        await _auditHubAuditLogService.CreateConversationAssigneeChangedLogAsync(
            companyId,
            userProfileId,
            staffIdentityId,
            new AssigneeChangedLogData(originalAssigneeLogData, newAssigneeLogData));

        staffId = await GetStaffIdentityIdAsync(companyId, staffId);
        _distributedInvocationContextService.SetContext(companyId, staffId);

        if (data.NewAssignee != null)
        {
            BackgroundJob.Enqueue<ISystemAuditLogsApi>(
                x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new CreateSystemAuditLogInput(
                        userProfileId,
                        "staff-assigned-to-conversation",
                        new StaffAssignedToConversationSystemLogData(
                            data.NewAssignee.StaffId,
                            data.ConversationId,
                            userProfileId)),
                    CancellationToken.None));
        }
        else if (data.OriginalAssignee != null && data.NewAssignee == null)
        {
            BackgroundJob.Enqueue<ISystemAuditLogsApi>(
                x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new CreateSystemAuditLogInput(
                        userProfileId,
                        "staff-unassigned-from-conversation",
                        new StaffUnassignedFromConversationSystemLogData(
                            data.OriginalAssignee.StaffId,
                            data.ConversationId,
                            userProfileId)),
                    CancellationToken.None));
        }
    }

    public async Task OnConversationCollaboratorAddedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationCollaboratorAddedData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        var staffIdentityId = await GetStaffIdentityIdAsync(companyId, staffId);

        await _auditHubAuditLogService.CreateConversationCollaboratorAddedLogAsync(
            companyId,
            userProfileId,
            staffIdentityId,
            new CollaboratorAddedLogData(
                data.AddedCollaborators
                    .Select(s => new AssigneeLogData(s.StaffIdentityId, s.Name))
                    .ToList()));

        staffId = await GetStaffIdAsync(companyId, staffId);
        _distributedInvocationContextService.SetContext(companyId, staffId);

        foreach (var collaborator in data.AddedCollaborators)
        {
            BackgroundJob.Enqueue<ISystemAuditLogsApi>(
                x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new CreateSystemAuditLogInput(
                        userProfileId,
                        "staff-added-as-collaborator",
                        new StaffAddedAsCollaboratorSystemLogData(
                            collaborator.StaffId,
                            data.ConversationId,
                            userProfileId,
                            data.UserProfileFirstName,
                            data.UserProfileLastName)),
                    CancellationToken.None));
        }
    }

    public async Task OnConversationCollaboratorRemovedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationCollaboratorRemovedData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        var staffIdentityId = await GetStaffIdentityIdAsync(companyId, staffId);

        await _auditHubAuditLogService.CreateConversationCollaboratorRemovedLogAsync(
            companyId,
            userProfileId,
            staffIdentityId,
            new CollaboratorRemovedLogData(
                data.RemovedCollaborators
                    .Select(s => new AssigneeLogData(s.StaffIdentityId, s.Name))
                    .ToList()));

        staffId = await GetStaffIdAsync(companyId, staffId);
        _distributedInvocationContextService.SetContext(companyId, staffId);

        foreach (var collaborator in data.RemovedCollaborators)
        {
            BackgroundJob.Enqueue<ISystemAuditLogsApi>(
                x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new CreateSystemAuditLogInput(
                        userProfileId,
                        "staff-removed-as-collaborator",
                        new StaffAddedAsCollaboratorSystemLogData(
                            collaborator.StaffId,
                            data.ConversationId,
                            userProfileId,
                            data.UserProfileFirstName,
                            data.UserProfileLastName)),
                    CancellationToken.None));
        }
    }

    public async Task OnConversationLabelsAddedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationLabelsAddedData>> dataFunc)
    {
        var data = await dataFunc.Invoke();
        var staffIdentityId = await GetStaffIdentityIdAsync(companyId, staffId);

        if (await _flowHubService.IsFlowHubEnabledAsync(companyId))
        {
            var userProfileDict = await _flowHubService.GetUserProfileDictAsync(companyId, userProfileId);

            BackgroundJob.Enqueue<IEventsApi>(
                x => x.EventsOnContactLabelRelationshipsChangedEventPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new OnContactLabelRelationshipsChangedEventInput(
                        companyId,
                        userProfileId,
                        new OnContactLabelRelationshipsChangedEventBody(
                            staffId,
                            null,
                            data.AddedLabels
                                .Select(
                                    l => new OnContactLabelRelationshipsChangedEventBodyContactLabel(
                                        l.LabelValue,
                                        l.LabelColor,
                                        l.LabelType))
                                .ToList(),
                            new List<OnContactLabelRelationshipsChangedEventBodyContactLabel>(),
                            data.CurrentLabels
                                .Select(
                                    l => new OnContactLabelRelationshipsChangedEventBodyContactLabel(
                                        l.LabelValue,
                                        l.LabelColor,
                                        l.LabelType))
                                .ToList(),
                            userProfileId,
                            userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                            staffIdentityId,
                            DateTimeOffset.UtcNow)),
                    CancellationToken.None));
        }

        await _auditHubAuditLogService.CreateConversationLabelAddedLogAsync(
            companyId,
            userProfileId,
            staffIdentityId,
            new LabelAddedLogData(
                data.AddedLabels.Select(l => new LabelLogData(l.LabelValue))
                    .ToList()));
    }

    public async Task OnConversationLabelsRemovedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationLabelsRemovedData>> dataFunc)
    {
        var data = await dataFunc.Invoke();
        var staffIdentityId = await GetStaffIdentityIdAsync(companyId, staffId);

        if (await _flowHubService.IsFlowHubEnabledAsync(companyId))
        {
            var userProfileDict = await _flowHubService.GetUserProfileDictAsync(companyId, userProfileId);

            BackgroundJob.Enqueue<IEventsApi>(
                x => x.EventsOnContactLabelRelationshipsChangedEventPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new OnContactLabelRelationshipsChangedEventInput(
                        companyId,
                        userProfileId,
                        new OnContactLabelRelationshipsChangedEventBody(
                            staffId,
                            null,
                            new List<OnContactLabelRelationshipsChangedEventBodyContactLabel>(),
                            data.RemovedLabels
                                .Select(
                                    l => new OnContactLabelRelationshipsChangedEventBodyContactLabel(
                                        l.LabelValue,
                                        l.LabelColor,
                                        l.LabelType))
                                .ToList(),
                            data.CurrentLabels
                                .Select(
                                    l => new OnContactLabelRelationshipsChangedEventBodyContactLabel(
                                        l.LabelValue,
                                        l.LabelColor,
                                        l.LabelType))
                                .ToList(),
                            userProfileId,
                            userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                            staffIdentityId,
                            DateTimeOffset.UtcNow)),
                    CancellationToken.None));
        }

        await _auditHubAuditLogService.CreateConversationLabelRemovedLogAsync(
            companyId,
            userProfileId,
            staffIdentityId,
            new LabelRemovedLogData(
                data.RemovedLabels.Select(l => new LabelLogData(l.LabelValue))
                    .ToList()));
    }

    public async Task OnConversationLabelsSetAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationLabelsSetData>> dataFunc)
    {
        if (await _flowHubService.IsFlowHubEnabledAsync(companyId))
        {
            var data = await dataFunc.Invoke();

            var userProfileDict = await _flowHubService.GetUserProfileDictAsync(companyId, userProfileId);
            var staffIdentityId = await GetStaffIdentityIdAsync(companyId, staffId);

            BackgroundJob.Enqueue<IEventsApi>(
                x => x.EventsOnContactLabelRelationshipsChangedEventPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new OnContactLabelRelationshipsChangedEventInput(
                        companyId,
                        userProfileId,
                        new OnContactLabelRelationshipsChangedEventBody(
                            staffId,
                            null,
                            data.AddedLabels
                                .Select(
                                    l => new OnContactLabelRelationshipsChangedEventBodyContactLabel(
                                        l.LabelValue,
                                        l.LabelColor,
                                        l.LabelType))
                                .ToList(),
                            data.RemovedLabels
                                .Select(
                                    l => new OnContactLabelRelationshipsChangedEventBodyContactLabel(
                                        l.LabelValue,
                                        l.LabelColor,
                                        l.LabelType))
                                .ToList(),
                            data.CurrentLabels
                                .Select(
                                    l => new OnContactLabelRelationshipsChangedEventBodyContactLabel(
                                        l.LabelValue,
                                        l.LabelColor,
                                        l.LabelType))
                                .ToList(),
                            userProfileId,
                            userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                            staffIdentityId,
                            DateTimeOffset.UtcNow)),
                    CancellationToken.None));
        }
    }

    public async Task OnConversationReadAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationReadData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        await _auditHubAuditLogService.CreateConversationReadLogAsync(
            companyId,
            userProfileId,
            staffId,
            new ConversationReadLogData(new AssigneeLogData(data.Reader.StaffIdentityId, data.Reader.Name)));
    }

    public async Task OnConversationStatusChangedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnConversationStatusChangedData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        if (await _flowHubService.IsFlowHubEnabledAsync(companyId))
        {
            var userProfileDict = await _flowHubService.GetUserProfileDictAsync(companyId, userProfileId);
            var staffIdentityId = await GetStaffIdentityIdAsync(companyId, staffId);

            BackgroundJob.Enqueue<IEventsApi>(
                x => x.EventsOnContactConversationStatusChangedEventPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new OnContactConversationStatusChangedEventInput(
                        companyId,
                        userProfileId,
                        new OnContactConversationStatusChangedEventBody(
                            staffId,
                            null,
                            data.OriginalStatus,
                            data.NewStatus,
                            userProfileId,
                            data.ConversationId,
                            userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                            staffIdentityId,
                            DateTimeOffset.UtcNow)),
                    CancellationToken.None));
        }

        await _auditHubAuditLogService.CreateConversationStatusChangedLogAsync(
            companyId,
            userProfileId,
            staffId,
            new ConversationStatusChangedLogData(
                data.OriginalStatus,
                data.NewStatus,
                data.SnoozeUntil));
    }

    public async Task OnUserProfileCreatedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileCreatedData>> dataFunc)
    {
        var onUserProfileCreatedData = await dataFunc.Invoke();

        var userProfile = onUserProfileCreatedData.UserProfile;

        if (await _flowHubService.IsFlowHubEnabledAsync(companyId))
        {
            var userProfileDict = await _flowHubService.GetUserProfileDictAsync(
                userProfile);

            var staffIdentityId = await GetStaffIdentityIdAsync(companyId, staffId);

            BackgroundJob.Enqueue<IEventsApi>(
                x => x.EventsOnContactCreatedEventPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new OnContactCreatedEventInput(
                        companyId,
                        userProfileId,
                        new OnContactCreatedEventBody(
                            staffId,
                            null,
                            userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                            userProfileId,
                            userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                            staffIdentityId,
                            DateTimeOffset.UtcNow)),
                    CancellationToken.None));
        }

        staffId = await GetStaffIdAsync(companyId, staffId);
        _distributedInvocationContextService.SetContext(companyId, staffId);

        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    userProfileId,
                    "user-profile-created",
                    new UserProfileCreatedSystemLogData(
                        userProfileId,
                        userProfile.FirstName,
                        userProfile.LastName,
                        userProfile.Email)),
                CancellationToken.None));
    }

    private Task<string> GetStaffIdentityIdAsync(string companyId, string staffId)
    {
        // If staffId is already a GUID (Identity ID), return it directly
        if (Guid.TryParse(staffId, out _))
        {
            return Task.FromResult(staffId);
        }

        // If staffId is numeric, convert it to Identity ID
        if (int.TryParse(staffId, out var staffIdInt))
        {
            return GetStaffIdentityIdAsync(companyId, staffIdInt);
        }

        return Task.FromResult<string>(null);
    }

    private Task<string> GetStaffIdAsync(string companyId, string staffId)
    {
        if (staffId == null)
        {
            return Task.FromResult((string) null);
        }

        var isGuid = Guid.TryParse(staffId, out var staffIdGuid);

        if (isGuid)
        {
            var myStaffId = _appDbContext.UserRoleStaffs
                .Where(s => s.CompanyId == companyId && s.IdentityId == staffIdGuid.ToString())
                .Select(s => s.Id)
                .FirstOrDefault();

            staffId = string.Empty + myStaffId;
        }

        return Task.FromResult(staffId);
    }

    private Task<string> GetStaffIdentityIdAsync(string companyId, int staffId)
    {
        return _appDbContext.UserRoleStaffs
            .Where(x => x.Id == staffId && x.CompanyId == companyId)
            .Select(x => x.IdentityId)
            .FirstAsync();
    }

    public async Task OnUserProfileDeletedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileDeletedData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        var dataStaffId = data.Staff?.IdentityId;
        var dataStaffName = data.Staff?.Identity?.DisplayName;

        var staffIdentityId = await GetStaffIdentityIdAsync(companyId, staffId);

        await _auditHubAuditLogService.CreateUserProfileDeletedLogAsync(
            companyId,
            userProfileId,
            staffIdentityId,
            new UserProfileDeletedLogData(
                new UserProfileDeletedTriggerLogData(
                    data.TriggerSource,
                    dataStaffId,
                    dataStaffName)));

        staffId = await GetStaffIdAsync(companyId, staffId);
        _distributedInvocationContextService.SetContext(companyId, staffId);

        BackgroundJob.Enqueue<ISystemAuditLogsApi>(
            x => x.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                _distributedInvocationContextService.GetSerializedContextHeader(),
                new CreateSystemAuditLogInput(
                    userProfileId,
                    "user-profile-deleted",
                    new UserProfileDeletedSystemLogData(
                        userProfileId,
                        data.UserProfile.FirstName,
                        data.UserProfile.LastName,
                        data.UserProfile.Email)),
                CancellationToken.None));
    }

    public async Task OnUserProfilesDeletedAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        Func<Task<OnUserProfilesDeletedData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        var dataStaffId = data.StaffOverview?.StaffIdentityId;
        var dataStaffName = data.StaffOverview?.DisplayName;

        BackgroundJob.Enqueue<IAuditHubAuditLogService>(
            x => x.CreateUserProfileDeletedLogsAsync(
                companyId,
                userProfileIds,
                staffId,
                new UserProfileDeletedLogData(
                    new UserProfileDeletedTriggerLogData(
                        data.TriggerSource,
                        dataStaffId,
                        dataStaffName))));
    }

    public async Task OnUserProfileSoftDeletedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileSoftDeletedData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        var dataStaffId = data.Staff?.IdentityId;
        var dataStaffName = data.Staff?.Identity?.DisplayName;

        await _auditHubAuditLogService.CreateUserProfileSoftDeletedLogAsync(
            companyId,
            userProfileId,
            staffId,
            new UserProfileSoftDeletedLogData(
                new UserProfileSoftDeletedTriggerLogData(
                    data.TriggerSource,
                    dataStaffId,
                    dataStaffName)));
    }

    public async Task OnUserProfilesSoftDeletedAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        Func<Task<OnUserProfilesSoftDeletedData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        var dataStaffId = data.StaffOverview?.StaffIdentityId;
        var dataStaffName = data.StaffOverview?.DisplayName;

        BackgroundJob.Enqueue<IAuditHubAuditLogService>(
            x => x.CreateUserProfileSoftDeletedLogsAsync(
                companyId,
                userProfileIds,
                staffId,
                new UserProfileSoftDeletedLogData(
                    new UserProfileSoftDeletedTriggerLogData(
                        data.TriggerSource,
                        dataStaffId,
                        dataStaffName))));
    }

    public async Task OnUserProfileRecoveredAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileRecoveredData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        var dataStaffId = data.Staff?.IdentityId;
        var dataStaffName = data.Staff?.Identity?.DisplayName;

        await _auditHubAuditLogService.CreateUserProfileRecoveredLogAsync(
            companyId,
            userProfileId,
            staffId,
            new UserProfileRecoveredLogData(
                new UserProfileRecoveredTriggerLogData(
                    data.TriggerSource,
                    dataStaffId,
                    dataStaffName)));
    }

    public async Task OnUserProfilesRecoveredAsync(
        string companyId,
        List<string> userProfileIds,
        string staffId,
        Func<Task<OnUserProfilesRecoveredData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        var dataStaffId = data.StaffOverview?.StaffIdentityId;
        var dataStaffName = data.StaffOverview?.DisplayName;

        BackgroundJob.Enqueue<IAuditHubAuditLogService>(
            x => x.CreateUserProfileRecoveredLogsAsync(
                companyId,
                userProfileIds,
                staffId,
                new UserProfileRecoveredLogData(
                    new UserProfileRecoveredTriggerLogData(
                        data.TriggerSource,
                        dataStaffId,
                        dataStaffName))));
    }

    public async Task OnUserProfileFieldChangedAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileFieldChangedData>> dataFunc)
    {
        var sw = System.Diagnostics.Stopwatch.StartNew();
        _logger.LogDebug("[Watcher] OnUserProfileFieldChangedAsync: dataFunc.Invoke START");
        await WatcherLogger.WriteWatcherLogAsync("[Watcher] OnUserProfileFieldChangedAsync: dataFunc.Invoke START", _logger);
        var data = await dataFunc.Invoke();
        sw.Stop();
        var dataFuncEndMsg = $"[Watcher] OnUserProfileFieldChangedAsync: dataFunc.Invoke END, duration: {sw.ElapsedMilliseconds}ms";
        _logger.LogDebug(dataFuncEndMsg);
        await WatcherLogger.WriteWatcherLogAsync(dataFuncEndMsg, _logger);

        var preUpdatedContact = data.PreUpdatedUserProfile;
        var postUpdatedContact = data.PostUpdatedUserProfile;

        var diffDictionary = DiffDictionaries(data.PreUpdatedUserProfile, data.PostUpdatedUserProfile);

        if (diffDictionary.Count == 0)
        {
            return;
        }

        sw.Restart();
        _logger.LogDebug("[Watcher] OnUserProfileFieldChangedAsync: _companyService.GetStaffAsync START");
        await WatcherLogger.WriteWatcherLogAsync("[Watcher] OnUserProfileFieldChangedAsync: _companyService.GetStaffAsync START", _logger);
        var staff = await _companyService.GetStaffAsync(companyId, staffId);
        sw.Stop();
        var getStaffEndMsg = $"[Watcher] OnUserProfileFieldChangedAsync: _companyService.GetStaffAsync END, duration: {sw.ElapsedMilliseconds}ms";
        _logger.LogDebug(getStaffEndMsg);
        await WatcherLogger.WriteWatcherLogAsync(getStaffEndMsg, _logger);
        var staffIdentityId = staff?.IdentityId;

        sw.Restart();
        _logger.LogDebug("[Watcher] OnUserProfileFieldChangedAsync: _flowHubService.IsFlowHubEnabledAsync START");
        await WatcherLogger.WriteWatcherLogAsync("[Watcher] OnUserProfileFieldChangedAsync: _flowHubService.IsFlowHubEnabledAsync START", _logger);
        var isFlowHubEnabled = await _flowHubService.IsFlowHubEnabledAsync(companyId);
        sw.Stop();
        var isFlowHubEnabledEndMsg = $"[Watcher] OnUserProfileFieldChangedAsync: _flowHubService.IsFlowHubEnabledAsync END, duration: {sw.ElapsedMilliseconds}ms";
        _logger.LogDebug(isFlowHubEnabledEndMsg);
        await WatcherLogger.WriteWatcherLogAsync(isFlowHubEnabledEndMsg, _logger);

        if (isFlowHubEnabled)
        {
            var changeEntries = diffDictionary
                .Select(
                    d => new OnContactUpdatedEventBodyChangeEntry(
                        d.Key,
                        d.Key,
                        d.Value.original,
                        d.Value.modified))
                .ToList();

            sw.Restart();
            _logger.LogDebug("[Watcher] OnUserProfileFieldChangedAsync: BackgroundJob.Enqueue<IEventsApi> START");
            await WatcherLogger.WriteWatcherLogAsync("[Watcher] OnUserProfileFieldChangedAsync: BackgroundJob.Enqueue<IEventsApi> START", _logger);
            BackgroundJob.Enqueue<IEventsApi>(
                x => x.EventsOnContactUpdatedEventPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new OnContactUpdatedEventInput(
                        companyId,
                        userProfileId,
                        new OnContactUpdatedEventBody(
                            staffIdentityId,
                            null,
                            postUpdatedContact.ToDictionary(e => e.Key, e => (object) e.Value),
                            preUpdatedContact.ToDictionary(e => e.Key, e => (object) e.Value),
                            changeEntries,
                            userProfileId,
                            postUpdatedContact.ToDictionary(e => e.Key, e => (object) e.Value),
                            staffIdentityId,
                            DateTimeOffset.UtcNow)),
                    CancellationToken.None));
            sw.Stop();
            var backgroundJobEndMsg = $"[Watcher] OnUserProfileFieldChangedAsync: BackgroundJob.Enqueue<IEventsApi> END, duration: {sw.ElapsedMilliseconds}ms";
            _logger.LogDebug(backgroundJobEndMsg);
            await WatcherLogger.WriteWatcherLogAsync(backgroundJobEndMsg, _logger);
        }

        var changedFieldLogDataList =
            diffDictionary.Select(d => new ChangedFieldLogData(d.Key, d.Value.modified?.ToString()!)).ToList();

        sw.Restart();
        _logger.LogDebug("[Watcher] OnUserProfileFieldChangedAsync: _auditHubAuditLogService.CreateUserProfileFieldChangedLogAsync START");
        await WatcherLogger.WriteWatcherLogAsync("[Watcher] OnUserProfileFieldChangedAsync: _auditHubAuditLogService.CreateUserProfileFieldChangedLogAsync START", _logger);
        await _auditHubAuditLogService.CreateUserProfileFieldChangedLogAsync(
            companyId,
            userProfileId,
            staffIdentityId,
            new UserProfileFieldChangedLogData(changedFieldLogDataList));
        sw.Stop();
        var auditLogEndMsg = $"[Watcher] OnUserProfileFieldChangedAsync: _auditHubAuditLogService.CreateUserProfileFieldChangedLogAsync END, duration: {sw.ElapsedMilliseconds}ms";
        _logger.LogDebug(auditLogEndMsg);
        await WatcherLogger.WriteWatcherLogAsync(auditLogEndMsg, _logger);

        sw.Restart();
        _logger.LogDebug("[Watcher] OnUserProfileFieldChangedAsync: GetStaffIdAsync START");
        await WatcherLogger.WriteWatcherLogAsync("[Watcher] OnUserProfileFieldChangedAsync: GetStaffIdAsync START", _logger);
        staffId = await GetStaffIdAsync(companyId, staffId);
        sw.Stop();
        var getStaffIdEndMsg = $"[Watcher] OnUserProfileFieldChangedAsync: GetStaffIdAsync END, duration: {sw.ElapsedMilliseconds}ms";
        _logger.LogDebug(getStaffIdEndMsg);
        await WatcherLogger.WriteWatcherLogAsync(getStaffIdEndMsg, _logger);
        _distributedInvocationContextService.SetContext(companyId, staffId);

        // Enqueue the audit log creation in Hangfire
        sw.Restart();
        _logger.LogDebug("[Watcher] CreateSystemAuditLogForUserProfileFieldChangeAsync: HangFire Enqueue START");
        await WatcherLogger.WriteWatcherLogAsync("[Watcher] CreateSystemAuditLogForUserProfileFieldChangeAsync: HangFire Enqueue START", _logger);
        var contextHeader = _distributedInvocationContextService.GetSerializedContextHeader();
        BackgroundJob.Enqueue(() => CreateSystemAuditLogForUserProfileFieldChangeAsync(contextHeader, companyId, userProfileId, staffId, diffDictionary));
        sw.Stop();
        var hangfireEnqueueEndMsg = $"[Watcher] CreateSystemAuditLogForUserProfileFieldChangeAsync: HangFire Enqueue END, duration: {sw.ElapsedMilliseconds}ms";
        _logger.LogDebug(hangfireEnqueueEndMsg);
        await WatcherLogger.WriteWatcherLogAsync(hangfireEnqueueEndMsg, _logger);
    }

    public async Task OnUserProfileAddedToListAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileAddedToListData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        var staff = await _companyService.GetStaffAsync(companyId, staffId);
        var staffIdentityId = staff?.IdentityId;

        if (await _flowHubService.IsFlowHubEnabledAsync(companyId))
        {
            var userProfileDict = await _flowHubService.GetUserProfileDictAsync(companyId, userProfileId);

            BackgroundJob.Enqueue<IEventsApi>(
                x => x.EventsOnContactListRelationshipsChangedEventPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new OnContactListRelationshipsChangedEventInput(
                        companyId,
                        userProfileId,
                        new OnContactListRelationshipsChangedEventBody(
                            staffId,
                            null,
                            data.AddedUserProfileLists
                                .Select(
                                    l => new OnContactListRelationshipsChangedEventBodyContactList(
                                        l.ListId,
                                        l.ListName))
                                .ToList(),
                            new List<OnContactListRelationshipsChangedEventBodyContactList>(),
                            data.CurrentUserProfileLists
                                .Select(
                                    l => new OnContactListRelationshipsChangedEventBodyContactList(
                                        l.ListId,
                                        l.ListName))
                                .ToList(),
                            userProfileId,
                            userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                            staffIdentityId,
                            DateTimeOffset.UtcNow)),
                    CancellationToken.None));
        }

        foreach (var userProfileListAdded in data.AddedUserProfileLists)
        {
            await _auditHubAuditLogService.CreateUserProfileAddedToListLogAsync(
                companyId,
                userProfileId,
                staffIdentityId,
                new UserProfileAddedToListLogData(
                    new UserProfileListLogData(
                        long.Parse(userProfileListAdded.ListId),
                        userProfileListAdded.ListName)));
        }
    }

    public async Task OnUserProfileRemovedFromListAsync(
        string companyId,
        string userProfileId,
        string staffId,
        Func<Task<OnUserProfileRemovedToListData>> dataFunc)
    {
        var data = await dataFunc.Invoke();

        var staff = await _companyService.GetStaffAsync(companyId, staffId);
        var staffIdentityId = staff?.IdentityId;

        if (await _flowHubService.IsFlowHubEnabledAsync(companyId))
        {
            var userProfileDict = await _flowHubService.GetUserProfileDictAsync(companyId, userProfileId);

            BackgroundJob.Enqueue<IEventsApi>(
                x => x.EventsOnContactListRelationshipsChangedEventPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new OnContactListRelationshipsChangedEventInput(
                        companyId,
                        userProfileId,
                        new OnContactListRelationshipsChangedEventBody(
                            staffId,
                            null,
                            new List<OnContactListRelationshipsChangedEventBodyContactList>(),
                            data.RemovedUserProfileLists
                                .Select(
                                    l => new OnContactListRelationshipsChangedEventBodyContactList(
                                        l.ListId,
                                        l.ListName))
                                .ToList(),
                            data.CurrentUserProfileLists
                                .Select(
                                    l => new OnContactListRelationshipsChangedEventBodyContactList(
                                        l.ListId,
                                        l.ListName))
                                .ToList(),
                            userProfileId,
                            userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                            staffIdentityId,
                            DateTimeOffset.UtcNow)),
                    CancellationToken.None));
        }

        foreach (var userProfileListAdded in data.RemovedUserProfileLists)
        {
            await _auditHubAuditLogService.CreateUserProfileRemovedFromListLogAsync(
                companyId,
                new List<string>
                {
                    userProfileId
                },
                staffIdentityId,
                new UserProfileRemovedFromListLogData(
                    new UserProfileListLogData(
                        long.Parse(userProfileListAdded.ListId),
                        userProfileListAdded.ListName)));
        }
    }

    public async Task OnUserProfileEnrolledIntoFlowHubWorkflowAsync(
        string companyId,
        string userProfileId,
        Func<Task<OnUserProfileEnrolledIntoFlowHubWorkflowData>> dataFunc)
    {
        var data = await dataFunc();

        await _auditHubAuditLogService.CreateUserProfileEnrolledIntoFlowHubWorkflowLogAsync(
            companyId,
            userProfileId,
            data);
    }

    public async Task OnWebsiteDetectedAsync(string companyId, string userProfileId, Func<Task<OnUserProfileEnrolledIntoFlowHubWorkflowData>> dataFunc)
    {
        if (await _flowHubService.IsFlowHubEnabledAsync(companyId))
        {
            var userProfileDict = await _flowHubService.GetUserProfileDictAsync(companyId, userProfileId);
        }

        throw new NotImplementedException();
    }

    public async Task OnUserProfileManuallyEnrolledAsync(
        string companyId,
        string userProfileId,
        string workflowId,
        string workflowVersionedId)
    {
        if (await _flowHubService.IsFlowHubEnabledAsync(companyId))
        {
            var userProfileDict = await _flowHubService.GetUserProfileDictAsync(companyId, userProfileId);

            BackgroundJob.Enqueue<IEventsApi>(
                x => x.EventsOnContactManuallyEnrolledEventPostAsync(
                    null,
                    _distributedInvocationContextService.GetSerializedContextHeader(),
                    new OnContactManuallyEnrolledEventInput(
                        companyId,
                        userProfileId,
                        new OnContactManuallyEnrolledEventBody(
                            userProfileId,
                            userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value),
                            workflowId,
                            workflowVersionedId,
                            DateTimeOffset.UtcNow)),
                    CancellationToken.None));
        }
    }

    public static Dictionary<string, (JToken original, JToken modified)> DiffDictionaries(
        Dictionary<string, JToken> dict1,
        Dictionary<string, JToken> dict2)
    {
        var diff = new Dictionary<string, (JToken, JToken)>();

        foreach (var key in dict1.Keys)
        {
            if (dict2.ContainsKey(key))
            {
                NormalizeNumericValues(dict1[key]);
                NormalizeNumericValues(dict2[key]);

                if (!JTokenUtils.DeepEquals(dict1[key], dict2[key]))
                {
                    diff[key] = (dict1[key], dict2[key]);
                }
            }
            else
            {
                diff[key] = (dict1[key], null);
            }
        }

        foreach (var key in dict2.Keys)
        {
            if (!dict1.ContainsKey(key))
            {
                diff[key] = (null, dict2[key]);
            }
        }

        return diff;
    }

    private static void NormalizeNumericValues(JToken token)
    {
        if (token == null)
        {
            return;
        }

        if (token.Type == JTokenType.Object)
        {
            foreach (var property in token.Children<JProperty>().ToList())
            {
                NormalizeNumericValues(property.Value);
            }
        }
        else if (token.Type == JTokenType.Array)
        {
            foreach (var child in token.Children().ToList())
            {
                NormalizeNumericValues(child);
            }
        }
        else if (token.Type == JTokenType.Integer || token.Type == JTokenType.Float)
        {
            double value = token.Value<double>();
            token.Replace(new JValue(value));
        }
    }

    private (string MessageType, MessageBody MessageBody, string MessageContent) GetMessageBody(
        ConversationMessage conversationMessage)
    {
        if (conversationMessage.ExtendedMessagePayload == null ||
            conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail == null)
        {
            return MapMessageWithoutExtendedPayload(conversationMessage);
        }

        var (messageType, messageBody) =
            MessageBodyMapper.ToMessageBody(
                conversationMessage.ExtendedMessagePayload.ExtendedMessagePayloadDetail);

        return (messageType, messageBody, conversationMessage.MessageContent);
    }

    private (string MessageType, MessageBody MessageBody, string MessageContent) MapMessageWithoutExtendedPayload(
        ConversationMessage conversationMessage)
    {
        var messageBody = new MessageBody();
        string messageType;

        if (conversationMessage.UploadedFiles is not { Count: > 0 })
        {
            messageType = "text";
            messageBody.TextMessage = new TextMessageObject(conversationMessage.MessageContent);
        }
        else
        {
            messageType = "file";

            var file = conversationMessage.UploadedFiles.First();
            var domain = _configuration.GetValue<string>("Values:DomainName");
            var url = $"{domain}/Message/File/Private/{file.FileId}?mode=redirect";
            var fileName = Path.GetFileName(file.Filename)!;

            switch (file.MIMEType)
            {
                case not null when file.MIMEType.StartsWith("audio"):
                    messageBody.AudioMessage = new AudioMessageObject(
                        id: file.FileId,
                        link: url,
                        filename: fileName);

                    break;
                case not null when file.MIMEType.StartsWith("video"):
                    messageBody.VideoMessage = new VideoMessageObject(
                        id: file.FileId,
                        link: url,
                        filename: fileName);

                    break;
                case not null when file.MIMEType.StartsWith("image"):
                    messageBody.ImageMessage = new ImageMessageObject(
                        id: file.FileId,
                        link: url,
                        filename: fileName);

                    break;
                default:
                    messageBody.DocumentMessage = new DocumentMessageObject(
                        id: file.FileId,
                        link: url,
                        filename: fileName);

                    break;
            }
        }

        return (messageType, messageBody, conversationMessage.MessageContent);
    }

    private async Task<QuotedMessageData> GetQuotedMessageDataAsync(string companyId, string messageUniqueId)
    {
        if (string.IsNullOrWhiteSpace(messageUniqueId))
        {
            return null;
        }

        var message = await _appDbContext.ConversationMessages
            .Where(m => m.CompanyId == companyId && m.MessageUniqueID == messageUniqueId)
            .Include(m => m.ExtendedMessagePayload)
            .FirstOrDefaultAsync();

        return message == null
            ? null
            : new QuotedMessageData(
                message.Id.ToString(),
                message.MessageUniqueID,
                message.MessageContent,
                message.ExtendedMessagePayload);
    }

    public async Task CreateSystemAuditLogForUserProfileFieldChangeAsync(
        string contextHeader,
        string companyId,
        string userProfileId,
        string staffId,
        Dictionary<string, (JToken original, JToken modified)> diffDictionary)
    {
        foreach (var changedFieldLogData in diffDictionary)
        {
            await _systemAuditLogsApi.SystemAuditLogsCreateSystemAuditLogPostAsync(
                null,
                contextHeader,
                new CreateSystemAuditLogInput(
                    userProfileId,
                    "user-profile-property-updated",
                    new UserProfilePropertyUpdatedSystemLogData(
                        userProfileId,
                        changedFieldLogData.Key,
                        changedFieldLogData.Value.original == null
                            ? null
                            : changedFieldLogData.Value.original.ToString(),
                        changedFieldLogData.Value.modified == null
                            ? null
                            : changedFieldLogData.Value.modified.ToString())),
                CancellationToken.None);
        }
    }
}