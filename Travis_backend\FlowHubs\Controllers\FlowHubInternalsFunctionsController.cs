using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PhoneNumbers;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Models.Interfaces;
using Travis_backend.ClientCustomDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.FlowHubs.Attributes;
using Travis_backend.FlowHubs.Exceptions;
using Travis_backend.FlowHubs.Filters;
using Travis_backend.FlowHubs.Models;
using Travis_backend.Infrastructures.Attributes;
using Travis_backend.MessageDomain.Models;
using Travis_backend.Telemetries;

namespace Travis_backend.FlowHubs.Controllers;

[Route("FlowHub/Internals")]
[FlowHubAuthorization]
[SuppressMessage("ReSharper", "ConditionIsAlwaysTrueOrFalse")]
[TypeFilter(typeof(FlowHubExceptionFilter))]
public class FlowHubInternalsFunctionsController : ControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IFlowHubService _flowHubService;
    private readonly ILogger<FlowHubInternalsFunctionsController> _logger;
    private readonly IUserProfileService _userProfileService;
    private readonly IUserProfileHooks _userProfileHooks;
    private readonly ILockService _lockService;
    private readonly IUserProfileSafeDeleteService _userProfileSafeDeleteService;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly IZDotComService _zDotComService;

    public FlowHubInternalsFunctionsController(
        ApplicationDbContext appDbContext,
        IFlowHubService flowHubService,
        IUserProfileService userProfileService,
        IUserProfileHooks userProfileHooks,
        ILockService lockService,
        IUserProfileSafeDeleteService userProfileSafeDeleteService,
        ILogger<FlowHubInternalsFunctionsController> logger,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        IZDotComService zDotComService)
    {
        _appDbContext = appDbContext;
        _flowHubService = flowHubService;
        _userProfileService = userProfileService;
        _userProfileHooks = userProfileHooks;
        _lockService = lockService;
        _userProfileSafeDeleteService = userProfileSafeDeleteService;
        _logger = logger;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _zDotComService = zDotComService;
    }

    [HttpPost("Functions/GetContact")]
    public async Task<ActionResult<GetContactOutput>> GetContact(
        [FromBody]
        GetContactInput getContactInput)
    {
        var companyId = getContactInput.SleekflowCompanyId;
        var contactId = getContactInput.SleekflowContactId;

        var userProfile = await _appDbContext.UserProfiles
            .AsNoTracking()
            .Include(x => x.CustomFields)
            .ThenInclude(x => x.CompanyDefinedField)
            .Where(
                x =>
                    x.CompanyId == companyId
                    && x.Id == contactId)
            .FirstOrDefaultAsync();

        if (userProfile is null
            || userProfile.ActiveStatus == ActiveStatus.Inactive)
        {
            _logger.LogWarning(
                "Unable to find user profile of {UserProfileId} in company {CompanyId}. Status: {ActiveStatus}",
                contactId,
                companyId,
                userProfile?.ActiveStatus);

            throw new ContactNotFoundException(
                companyId,
                contactId);
        }

        var userProfileDict = await _flowHubService.GetUserProfileDictAsync(userProfile);

        return Ok(new GetContactOutput(userProfileDict.ToDictionary(e => e.Key, e => (object) e.Value)));
    }

    [HttpPost("Functions/GetContactDetail")]
    public async Task<ActionResult<GetContactDetailOutput>> GetContactDetail(
            [FromBody]
        GetContactInput input)
    {
        var companyId = input.SleekflowCompanyId;
        var contactId = input.SleekflowContactId;

        var contactDetail = await _flowHubService.GetUserContactAsync(companyId, contactId);

        if (contactDetail == null)
        {
            _logger.LogWarning("Contact detail not found for CompanyId: {CompanyId}, ContactId: {ContactId}", companyId, contactId);
            return NotFound(new ProblemDetails
            {
                Title = "Contact not found",
                Detail = $"Contact with id {contactId} not found for company {companyId}."
            });
        }

        // Convert ContactDetail to Dictionary<string, object>
        var contactDict = contactDetail.Contact;
        var contactOwnerDict = contactDetail.ContactOwner;
        if (contactDetail.ContactOwner != null)
        {
            foreach (var kvp in contactDetail.ContactOwner)
            {
                contactOwnerDict[kvp.Key] = kvp.Value;
            }
        }
        return Ok(new GetContactDetailOutput(contactDict, contactOwnerDict, contactDetail.Lists));
    }

    [HttpPost("Functions/GetContactsByBatch")]
    public async Task<ActionResult<GetContactsByBatchOutput>> GetContactsByBatch(
        [FromBody]
        GetContactsByBatchInput input)
    {
        var companyId = input.SleekflowCompanyId;
        var batchSize = input.BatchSize ?? 500;
        var lastContactCreatedAt = input.LastContactCreatedAt;
        var lastContactId = input.LastContactId;

        // Base query - always apply consistent ordering
        var query = _appDbContext.UserProfiles
            .AsNoTracking()
            .Where(u => u.CompanyId == companyId)
            .Select(
                x =>
                new
                {
                    x.Id,
                    x.CreatedAt,
                })
            .OrderBy(u => u.CreatedAt)
            .ThenBy(u => u.Id)
            .AsQueryable();

        if (lastContactCreatedAt.HasValue && !string.IsNullOrEmpty(lastContactId))
        {
            DateTime cursorUtcDateTime = lastContactCreatedAt.Value.UtcDateTime;
            query = query
                .Where(u => u.CreatedAt > cursorUtcDateTime ||
                            (u.CreatedAt == cursorUtcDateTime && u.Id.CompareTo(lastContactId) > 0));
        }

        var userProfiles = await query.Take(batchSize + 1).ToListAsync();
        var resultUserProfiles = userProfiles.Take(batchSize).ToList();

        if (resultUserProfiles.Count == 0)
        {
            _logger.LogWarning(
                "Unable to find any user profile in company {CompanyId}. With following parameters: {CreatedAt} {LastContactId}",
                companyId,
                lastContactCreatedAt,
                lastContactId);

            return Ok(new GetContactsByBatchOutput(new Dictionary<string, ContactDetail>(), null));
        }

        var lastUser = resultUserProfiles.Last();
        var hasMore = userProfiles.Count > batchSize;

        var nextBatch = hasMore
            ? new NextBatch(DateTime.SpecifyKind(lastUser.CreatedAt, DateTimeKind.Utc), lastUser.Id)
            : null;

        var userProfileDict = await _flowHubService
            .GetUserProfileBatchDictAsync(companyId, resultUserProfiles.Select(x => x.Id).ToList());

        return Ok(new GetContactsByBatchOutput(userProfileDict, nextBatch));
    }

    [HttpPost("Functions/GetStaff")]
    public async Task<ActionResult<GetStaffOutput>> GetStaff(
     [FromBody]
        GetStaffInput getStaffInput)
    {
        var companyId = getStaffInput.SleekflowCompanyId;
        var staffId = getStaffInput.SleekflowStaffId;

        var staffDict = await _flowHubService.GetStaffDictAsync(staffId, companyId);

        return Ok(
            staffDict is null
            ? new GetStaffOutput()
            : new GetStaffOutput(staffDict.ToDictionary(e => e.Key, e => (object) e.Value)));
    }

    [HttpPost("Functions/GetContactLists")]
    public async Task<ActionResult<GetContactListsOutput>> GetContactLists(
        [FromBody]
        GetContactListsInput getContactListsInput)
    {
        var companyId = getContactListsInput.SleekflowCompanyId;
        var contactId = getContactListsInput.SleekflowContactId;

        var allGroupIds = await _appDbContext.CompanyImportedUserProfiles
            .Where(x => x.UserProfileId == contactId)
            .Select(x => x.ImportContactHistoryId)
            .ToListAsync();

        var allGroups = await _appDbContext.CompanyImportContactHistories
            .Where(x => allGroupIds.Contains(x.Id) && x.CompanyId == companyId)
            .Select(
                x => new
                {
                    x.Id,
                    x.ImportName,
                    x.IsImported
                })
            .ToListAsync();

        return Ok(
            new GetContactListsOutput(
                allGroups
                    .Select(
                        x => new ContactList(
                            x.Id.ToString(),
                            x.ImportName,
                            DateTimeOffset.UtcNow,
                            x.IsImported))
                    .ToList()));
    }

    [HttpPost("Functions/GetContactConversation")]
    public async Task<ActionResult<GetContactConversationOutput>> GetContactConversation(
        [FromBody]
        GetContactConversationInput getContactConversationInput)
    {
        var companyId = getContactConversationInput.SleekflowCompanyId;
        var contactId = getContactConversationInput.SleekflowContactId;

        var conversation = await _appDbContext
            .Conversations
            .Where(c => c.CompanyId == companyId && c.UserProfileId == contactId)
            .Select(
                c => new
                {
                    c.Id,
                    c.Status,
                    c.LastMessageChannel,
                    c.LastMessageChannelId,
                    c.LastChannelIdentityId
                })
            .FirstOrDefaultAsync();
        if (conversation == null)
        {
            return Ok(new GetContactConversationOutput());
        }
        return Ok(
            new GetContactConversationOutput(
                new ContactConversation(
                    conversation.Status,
                    conversation.Id,
                    conversation.LastMessageChannel,
                    conversation.LastChannelIdentityId)));
    }

    [HttpPost("Functions/GetContactIdByPhoneNumber")]
    public async Task<ActionResult<GetContactIdOutput>> GetContactIdByPhoneNumber(
        [FromBody] GetContactIdByPhoneNumberInput getContactIdByPhoneNumberInput)
    {
        var companyId = getContactIdByPhoneNumberInput.SleekflowCompanyId;
        var phoneNumber = getContactIdByPhoneNumberInput.PhoneNumber;

        phoneNumber = PhoneNumberUtil.ExtractPossibleNumber(
            PhoneNumberUtil.NormalizeDigitsOnly(phoneNumber));

        if (string.IsNullOrWhiteSpace(phoneNumber))
        {
            _logger.LogWarning(
                "[FlowHubInternals {MethodName} endpoint] Cannot find matching user profile with" +
                " phone number {PhoneNumber} (original: {OriginalPhoneNumber}) in company {CompanyId}",
                nameof(GetContactIdByPhoneNumber),
                phoneNumber,
                getContactIdByPhoneNumberInput.PhoneNumber,
                companyId);

            throw new ContactNotFoundException(
                companyId,
                phoneNumber);
        }

        var userProfileIdStatus = await GetUserProfileIdStatusByPhoneNumberAsync(
            companyId,
            phoneNumber);

        if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
        {
            _logger.LogWarning(
                "[FlowHubInternals {MethodName} endpoint] Cannot find matching user profile with" +
                " phone number {PhoneNumber} (original: {OriginalPhoneNumber}) in company {CompanyId}",
                nameof(GetContactIdByPhoneNumber),
                phoneNumber,
                getContactIdByPhoneNumberInput.PhoneNumber,
                companyId);

            throw new ContactNotFoundException(
                companyId,
                phoneNumber);
        }

        await _userProfileHooks.OnUserProfileEnrolledIntoFlowHubWorkflowAsync(
            companyId,
            userProfileIdStatus.UserProfileId,
            () => Task.FromResult(
                new OnUserProfileEnrolledIntoFlowHubWorkflowData(
                    getContactIdByPhoneNumberInput.WorkflowId,
                    getContactIdByPhoneNumberInput.WorkflowVersionedId,
                    getContactIdByPhoneNumberInput.WorkflowName,
                    getContactIdByPhoneNumberInput.StateId)));

        return Ok(new GetContactIdOutput(userProfileIdStatus.UserProfileId));
    }

    [UpdateUserProfileTriggerSource(UpdateUserProfileTriggerSource.FlowHubAction)]
    [HttpPost("Functions/GetOrCreateContactIdByPhoneNumber")]
    public async Task<ActionResult<GetOrCreateContactIdOutput>> GetOrCreateContactIdByPhoneNumber(
        [FromBody] GetOrCreateContactIdByPhoneNumberInput getOrCreateContactIdByPhoneNumberInput)
    {
        var companyId = getOrCreateContactIdByPhoneNumberInput.SleekflowCompanyId;
        var phoneNumber = getOrCreateContactIdByPhoneNumberInput.PhoneNumber;

        phoneNumber = PhoneNumberUtil.ExtractPossibleNumber(
            PhoneNumberUtil.NormalizeDigitsOnly(phoneNumber));

        if (string.IsNullOrWhiteSpace(phoneNumber))
        {
            _logger.LogWarning(
                "[FlowHubInternals {MethodName} endpoint] Cannot find matching user profile with" +
                " phone number {PhoneNumber} (original: {OriginalPhoneNumber}) in company {CompanyId}",
                nameof(GetOrCreateContactIdByPhoneNumber),
                phoneNumber,
                getOrCreateContactIdByPhoneNumberInput.PhoneNumber,
                companyId);

            throw new ContactNotFoundException(
                companyId,
                phoneNumber);
        }

        var userProfileId = await GetOrCreateUserProfileByPhoneNumberAsync(
            companyId,
            phoneNumber);

        if (string.IsNullOrWhiteSpace(userProfileId))
        {
            _logger.LogWarning(
                "[FlowHubInternals {MethodName} endpoint] Cannot find matching user profile" +
                " phone number {PhoneNumber} in company {CompanyId}",
                nameof(GetOrCreateContactIdByPhoneNumber),
                phoneNumber,
                companyId);

            throw new ContactNotFoundException(
                companyId,
                phoneNumber);
        }

        await _userProfileHooks.OnUserProfileEnrolledIntoFlowHubWorkflowAsync(
            companyId,
            userProfileId,
            () => Task.FromResult(
                new OnUserProfileEnrolledIntoFlowHubWorkflowData(
                    getOrCreateContactIdByPhoneNumberInput.WorkflowId,
                    getOrCreateContactIdByPhoneNumberInput.WorkflowVersionedId,
                    getOrCreateContactIdByPhoneNumberInput.WorkflowName,
                    getOrCreateContactIdByPhoneNumberInput.StateId)));

        return Ok(new GetOrCreateContactIdOutput(userProfileId));
    }

    [HttpPost("Functions/GetContactIdByEmail")]
    public async Task<ActionResult<GetContactIdOutput>> GetContactIdByEmail(
        [FromBody] GetContactIdByEmailInput getContactIdByEmailInput)
    {
        var companyId = getContactIdByEmailInput.SleekflowCompanyId;
        var email = getContactIdByEmailInput.Email;

        if (string.IsNullOrWhiteSpace(email))
        {
            _logger.LogWarning(
                "[FlowHubInternals {MethodName} endpoint] Cannot find matching user profile" +
                " with email {Email} in company {CompanyId}",
                nameof(GetContactIdByEmail),
                email,
                companyId);

            throw new ContactNotFoundException(
                companyId,
                email);
        }

        var userProfileIdStatus = await GetUserProfileIdStatusByEmailAsync(
            companyId,
            email);

        if (userProfileIdStatus.ActiveStatus != ActiveStatus.Active)
        {
            _logger.LogWarning(
                "[FlowHubInternals {MethodName} endpoint] Cannot find matching user profile " +
                "with email {Email} in company {CompanyId}",
                nameof(GetContactIdByEmail),
                email,
                companyId);

            throw new ContactNotFoundException(
                companyId,
                email);
        }

        await _userProfileHooks.OnUserProfileEnrolledIntoFlowHubWorkflowAsync(
            companyId,
            userProfileIdStatus.UserProfileId,
            () => Task.FromResult(
                new OnUserProfileEnrolledIntoFlowHubWorkflowData(
                    getContactIdByEmailInput.WorkflowId,
                    getContactIdByEmailInput.WorkflowVersionedId,
                    getContactIdByEmailInput.WorkflowName,
                    getContactIdByEmailInput.StateId)));

        return Ok(new GetContactIdOutput(userProfileIdStatus.UserProfileId));
    }

    [UpdateUserProfileTriggerSource(UpdateUserProfileTriggerSource.FlowHubAction)]
    [HttpPost("Functions/GetOrCreateContactIdByEmail")]
    public async Task<ActionResult<GetOrCreateContactIdOutput>> GetOrCreateContactIdByEmail(
        [FromBody] GetOrCreateContactIdByEmailInput getOrCreateContactIdByEmailInput)
    {
        var companyId = getOrCreateContactIdByEmailInput.SleekflowCompanyId;
        var email = getOrCreateContactIdByEmailInput.Email;

        if (string.IsNullOrWhiteSpace(email))
        {
            _logger.LogWarning(
                "[FlowHubInternals {MethodName} endpoint] Cannot find matching user profile" +
                " with email {Email} in company {CompanyId}",
                nameof(GetOrCreateContactIdByEmail),
                email,
                companyId);

            throw new ContactNotFoundException(
                companyId,
                email);
        }

        var userProfileId = await GetOrCreateUserProfileByEmailAsync(
            companyId,
            email);

        if (string.IsNullOrWhiteSpace(userProfileId))
        {
            _logger.LogWarning(
                "[FlowHubInternals {MethodName} endpoint] Cannot find matching user profile" +
                " with email {Email} in company {CompanyId}",
                nameof(GetOrCreateContactIdByEmail),
                email,
                companyId);

            throw new ContactNotFoundException(
                companyId,
                email);
        }

        await _userProfileHooks.OnUserProfileEnrolledIntoFlowHubWorkflowAsync(
            companyId,
            userProfileId,
            () => Task.FromResult(
                new OnUserProfileEnrolledIntoFlowHubWorkflowData(
                    getOrCreateContactIdByEmailInput.WorkflowId,
                    getOrCreateContactIdByEmailInput.WorkflowVersionedId,
                    getOrCreateContactIdByEmailInput.WorkflowName,
                    getOrCreateContactIdByEmailInput.StateId)));

        return Ok(new GetOrCreateContactIdOutput(userProfileId));
    }

    [HttpPost("Functions/GetConversationChannelLastMessage")]
    public async Task<ActionResult<GetConversationChannelLastMessageOutput>> GetConversationChannelLastMessage(
        [FromBody] GetConversationChannelLastMessageInput getConversationChannelLastMessageInput)
    {
        var companyId = getConversationChannelLastMessageInput.SleekflowCompanyId;
        var conversationId = getConversationChannelLastMessageInput.ConversationId;
        var channel = getConversationChannelLastMessageInput.Channel;
        var channelId = getConversationChannelLastMessageInput.ChannelId;
        var isSentFromSleekflow = getConversationChannelLastMessageInput.IsSentFromSleekflow;

        var channelLastMessage = await _appDbContext.ConversationMessages
            .OrderByDescending(message => message.CreatedAt)
            .FirstOrDefaultAsync(
                message =>
                    message.CompanyId == companyId
                    && message.ConversationId == conversationId
                    && message.Channel == channel
                    && message.ChannelIdentityId == channelId
                    && message.IsSentFromSleekflow == isSentFromSleekflow
                    && (message.Status == MessageStatus.Sent
                        || message.Status == MessageStatus.Received
                        || message.Status == MessageStatus.Read));

        return Ok(channelLastMessage is null
                ? new GetConversationChannelLastMessageOutput()
                : new GetConversationChannelLastMessageOutput(
                    new ConversationChannelLastMessage(
                        channelLastMessage.ConversationId,
                        channelLastMessage.Id.ToString(),
                        channelLastMessage.MessageUniqueID,
                        channelLastMessage.Status.ToString(),
                        channelLastMessage.MessageType,
                        channelLastMessage.DeliveryType.ToString(),
                        channelLastMessage.Channel,
                        channelLastMessage.ChannelIdentityId,
                        channelLastMessage.MessageContent,
                        new DateTimeOffset(channelLastMessage.CreatedAt))));
    }

    private async Task<string> GetOrCreateUserProfileByPhoneNumberAsync(
        string companyId,
        string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
        {
            return string.Empty;
        }

        ILockService.Lock @lock = null;

        try
        {
            var userProfileIdStatus = await GetUserProfileIdStatusByPhoneNumberAsync(
                companyId,
                phoneNumber);

            // Active contact found
            if (!string.IsNullOrWhiteSpace(userProfileIdStatus.UserProfileId)
                && userProfileIdStatus.ActiveStatus == ActiveStatus.Active)
            {
                return userProfileIdStatus.UserProfileId;
            }

            // Inactive contact found (soft-deleted)
            if (!string.IsNullOrWhiteSpace(userProfileIdStatus.UserProfileId)
                && userProfileIdStatus.ActiveStatus == ActiveStatus.Inactive)
            {
                _logger.LogInformation(
                    "[FlowHub {MethodName}] Recovering soft deleted user profile {UserProfileId} for company {CompanyId}",
                    nameof(GetOrCreateUserProfileByPhoneNumberAsync),
                    userProfileIdStatus.UserProfileId,
                    companyId);

                await _userProfileSafeDeleteService.RecoverSoftDeletedUserProfilesAsync(
                    companyId,
                    new HashSet<string>()
                    {
                        userProfileIdStatus.UserProfileId
                    },
                    new UserProfileRecoveryTriggerContext(
                        UpdateUserProfileTriggerSource.FlowHubAction,
                        null));

                return userProfileIdStatus.UserProfileId;
            }

            @lock = await _lockService.WaitUntilLockAcquiredAsync(
                $"flow-builder-internals-{nameof(GetOrCreateUserProfileByPhoneNumberAsync)}-{companyId}-{phoneNumber}",
                TimeSpan.FromSeconds(10));

            var profiles = await _userProfileService.AddUserProfiles(
                companyId,
                null,
                new List<NewProfileViewModel>()
                {
                    new NewProfileViewModel()
                    {
                        PhoneNumber = phoneNumber
                    }
                },
                userProfileSource: UserProfileSource.FlowHub);

            return profiles.FirstOrDefault()?.Id;
        }
        catch (Exception ex) when (ex is FormatException)
        {
            return (await GetUserProfileIdStatusByPhoneNumberAsync(
                companyId,
                phoneNumber)).UserProfileId;
        }
        finally
        {
            if (@lock is not null)
            {
                await _lockService.ReleaseLockAsync(@lock);
            }
        }
    }

    private async Task<(string UserProfileId, ActiveStatus? ActiveStatus)> GetUserProfileIdStatusByPhoneNumberAsync(
        string companyId,
        string phoneNumber)
    {
        var userProfile = await _appDbContext.UserProfiles
            .Where(
                profile =>
                    profile.CompanyId == companyId
                    && profile.PhoneNumber == phoneNumber)
            .Select(profile => new
            {
                profile.Id,
                profile.ActiveStatus
            })
            .FirstOrDefaultAsync();

        return (userProfile?.Id, userProfile?.ActiveStatus);
    }

    private async Task<string> GetOrCreateUserProfileByEmailAsync(
        string companyId,
        string email)
    {
        if (string.IsNullOrWhiteSpace(email)
            || !Regex.IsMatch(email, @"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase))
        {
            return string.Empty;
        }

        ILockService.Lock @lock = null;

        try
        {
            var userProfileIdStatus = await GetUserProfileIdStatusByEmailAsync(companyId, email);

            // Active contact found
            if (!string.IsNullOrWhiteSpace(userProfileIdStatus.UserProfileId)
                && userProfileIdStatus.ActiveStatus == ActiveStatus.Active)
            {
                return userProfileIdStatus.UserProfileId;
            }

            // Inactive contact found (soft-deleted)
            if (!string.IsNullOrWhiteSpace(userProfileIdStatus.UserProfileId)
                && userProfileIdStatus.ActiveStatus == ActiveStatus.Inactive)
            {
                _logger.LogInformation(
                    "[FlowHub {MethodName}] Recovering soft deleted user profile {UserProfileId} for company {CompanyId}",
                    nameof(GetOrCreateUserProfileByEmailAsync),
                    userProfileIdStatus.UserProfileId,
                    companyId);

                await _userProfileSafeDeleteService.RecoverSoftDeletedUserProfilesAsync(
                    companyId,
                    new HashSet<string>()
                    {
                        userProfileIdStatus.UserProfileId
                    },
                    new UserProfileRecoveryTriggerContext(
                        UpdateUserProfileTriggerSource.FlowHubAction,
                        null));
            }

            @lock = await _lockService.WaitUntilLockAcquiredAsync(
                $"flow-builder-internals-{nameof(GetOrCreateUserProfileByEmailAsync)}-{companyId}-{email}",
                TimeSpan.FromSeconds(10));

            var profiles = await _userProfileService.AddUserProfiles(
                companyId,
                null,
                new List<NewProfileViewModel>()
                {
                    new NewProfileViewModel()
                    {
                        Email = email
                    }
                },
                userProfileSource: UserProfileSource.FlowHub);

            return profiles.FirstOrDefault()?.Id;
        }
        catch (Exception ex) when (ex is FormatException)
        {
            return (await GetUserProfileIdStatusByEmailAsync(
                companyId,
                email)).UserProfileId;
        }
        finally
        {
            if (@lock is not null)
            {
                await _lockService.ReleaseLockAsync(@lock);
            }
        }
    }

    private async Task<(string UserProfileId, ActiveStatus? ActiveStatus)> GetUserProfileIdStatusByEmailAsync(
        string companyId,
        string email)
    {
        var userProfile = await _appDbContext.UserProfiles
            .Where(
                profile =>
                    profile.CompanyId == companyId
                    && profile.Email == email)
            .Select(profile => new
            {
                profile.Id,
                profile.ActiveStatus
            })
            .FirstOrDefaultAsync();

        return (userProfile?.Id, userProfile?.ActiveStatus);
    }

    private Task<string> GetLastMessageChannelIdentityIdAsync(
        string companyId,
        string lastMessageChannel,
        long? lastMessageChannelId)
    {
        if (string.IsNullOrWhiteSpace(lastMessageChannel)
            || !lastMessageChannelId.HasValue)
        {
            return Task.FromResult<string>(null);
        }

        IQueryable<IMessagingChannel> lastMessageChannelQueryable = lastMessageChannel switch
        {
            ChannelTypes.WhatsappTwilio => _appDbContext.ConfigWhatsAppConfigs
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == lastMessageChannelId.Value),
            ChannelTypes.Whatsapp360Dialog => _appDbContext.ConfigWhatsApp360DialogConfigs
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == lastMessageChannelId.Value),
            ChannelTypes.WhatsappCloudApi => _appDbContext.ConfigWhatsappCloudApiConfigs
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == lastMessageChannelId.Value),
            ChannelTypes.Telegram => _appDbContext.ConfigTelegramConfigs
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == lastMessageChannelId.Value),
            ChannelTypes.Viber => _appDbContext.ConfigViberConfigs
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == lastMessageChannelId.Value),
            ChannelTypes.Sms => _appDbContext.ConfigSMSConfigs
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == lastMessageChannelId.Value),
            ChannelTypes.Facebook => _appDbContext.ConfigFacebookConfigs
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == lastMessageChannelId.Value),
            ChannelTypes.Instagram => _appDbContext.ConfigInstagramConfigs
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == lastMessageChannelId.Value),
            ChannelTypes.Email => _appDbContext.ConfigEmailConfigs
                .Where(
                    x =>
                        x.Id == lastMessageChannelId.Value),
            ChannelTypes.Wechat => _appDbContext.ConfigWeChatConfigs
                .Where(
                    x =>
                        x.Id == lastMessageChannelId.Value),
            ChannelTypes.Line => _appDbContext.ConfigLineConfigs
                .Where(
                    x =>
                        x.CompanyId == companyId
                        && x.Id == lastMessageChannelId.Value),
            _ => null
        };

        return lastMessageChannelQueryable?
            .Select(x => x.ChannelIdentityId)
            .FirstOrDefaultAsync();
    }


    [HttpPost("Functions/GetCompanyTimeZoneId")]
    public async Task<ActionResult<string>> GetCompanyTimezoneIdAsync(
        [FromBody]
        GetCompanyTimeZoneIdInput input)
    {
        var companyTimeZoneId = await _zDotComService.GetCompanyTimeZoneIdAsync(input.SleekflowCompanyId);
        return companyTimeZoneId;
    }

}