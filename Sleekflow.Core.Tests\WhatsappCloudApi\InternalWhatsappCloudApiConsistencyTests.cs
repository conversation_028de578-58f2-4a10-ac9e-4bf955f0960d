using System.Globalization;
using System.Text;
using AutoMapper;
using NSubstitute;
using Microsoft.EntityFrameworkCore;
using Sleekflow.Apis.MessagingHub.Api;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.Cache;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.InternalDomain;
using Travis_backend.InternalDomain.Services;
using Travis_backend.Mapping;

namespace Sleekflow.Core.Tests.WhatsappCloudApi;

[TestFixture]
public class InternalWhatsappCloudApiConsistencyTests
{
    private static InternalWhatsappCloudApiService CreateService(
        IManagementsApi managementsApi,
        IAzureBlobStorageService azure,
        ICacheManagerService cache)
    {
        var mapper = new MapperConfiguration(cfg => cfg.AddProfile(new InternalCmsMappingProfile())).CreateMapper();

        // Unused dependencies for this scenario
        var balancesApi = Substitute.For<IBalancesApi>();
        var logger = Substitute.For<Microsoft.Extensions.Logging.ILogger<InternalWhatsappCloudApiService>>();
        var templatesApi = Substitute.For<ITemplatesApi>();
        var inMemoryOptions = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options;
        var appDbContext = new ApplicationDbContext(inMemoryOptions);
        var channelsApi = Substitute.For<IChannelsApi>();

        return new InternalWhatsappCloudApiService(
            balancesApi,
            logger,
            managementsApi,
            templatesApi,
            appDbContext,
            mapper,
            channelsApi,
            azure,
            cache);
    }

    [Test]
    public async Task SnapshotVsFallback_TotalsAreEqual_ByBusinessAndByWaba()
    {
        // Arrange synthetic period and identities
        var start = new DateTime(2025, 7, 1, 0, 0, 0, DateTimeKind.Utc);
        var end = new DateTime(2025, 7, 2, 23, 59, 59, DateTimeKind.Utc);
        const string businessId = "BIZ-1";
        const string businessName = "Biz One";
        const string wabaId = "WABA-1";
        const string wabaName = "Waba One";

        // Day 1 values
        const decimal d1Used = 10m;
        const decimal d1Markup = 1.0m;
        const decimal d1Fee = 0.5m;
        const int d1Bip = 1;
        const int d1Bif = 2;
        const int d1Uip = 3;
        const int d1Uif = 4;
        const int d1Uie = 0;

        // Day 2 values
        const decimal d2Used = 5m;
        const decimal d2Markup = 0.7m;
        const decimal d2Fee = 0.2m;
        const int d2Bip = 2;
        const int d2Bif = 0;
        const int d2Uip = 1;
        const int d2Uif = 1;
        const int d2Uie = 0;

        const decimal expectedTotalUsed = d1Used + d2Used;
        const decimal expectedTotalMarkup = d1Markup + d2Markup;
        const decimal expectedTotalFee = d1Fee + d2Fee;
        const int expectedBip = d1Bip + d2Bip;
        const int expectedBif = d1Bif + d2Bif;
        const int expectedUip = d1Uip + d2Uip;
        const int expectedUif = d1Uif + d2Uif;
        const int expectedUie = d1Uie + d2Uie;

        // Build snapshot CSV content (by-business and by-waba) for 2025-07
        var businessCsvHeader = string.Join(
            ",",
            "date",
            "facebook_business_id",
            "facebook_business_name",
            "total_conversations",
            "total_business_initiated_conversations",
            "total_user_initiated_conversations",
            "total_business_initiated_paid_quantity",
            "total_business_initiated_free_tier_quantity",
            "total_user_initiated_paid_quantity",
            "total_user_initiated_free_tier_quantity",
            "total_user_initiated_free_entry_point_quantity",
            "total_used_currency_iso_code",
            "total_used_amount",
            "total_markup_currency_iso_code",
            "total_markup_amount",
            "total_transaction_handling_fee_currency_iso_code",
            "total_transaction_handling_fee_amount",
            "total_authentication_type_conversations",
            "total_marketing_type_conversations",
            "total_marketing_lite_type_conversations",
            "total_service_type_conversations",
            "total_utility_type_conversations",
            "total_authentication_pricing_volume",
            "total_authentication_international_pricing_volume",
            "total_marketing_pricing_volume",
            "total_marketing_lite_pricing_volume",
            "total_referral_conversion_pricing_volume",
            "total_service_pricing_volume",
            "total_utility_pricing_volume");

        var wabaCsvHeader = string.Join(
            ",",
            "date",
            "facebook_business_id",
            "facebook_business_name",
            "waba_id",
            "waba_name",
            "total_conversations",
            "total_business_initiated_conversations",
            "total_user_initiated_conversations",
            "total_business_initiated_paid_quantity",
            "total_business_initiated_free_tier_quantity",
            "total_user_initiated_paid_quantity",
            "total_user_initiated_free_tier_quantity",
            "total_user_initiated_free_entry_point_quantity",
            "total_used_currency_iso_code",
            "total_used_amount",
            "total_markup_currency_iso_code",
            "total_markup_amount",
            "total_transaction_handling_fee_currency_iso_code",
            "total_transaction_handling_fee_amount",
            "total_authentication_type_conversations",
            "total_marketing_type_conversations",
            "total_marketing_lite_type_conversations",
            "total_service_type_conversations",
            "total_utility_type_conversations",
            "total_authentication_pricing_volume",
            "total_authentication_international_pricing_volume",
            "total_marketing_pricing_volume",
            "total_marketing_lite_pricing_volume",
            "total_referral_conversion_pricing_volume",
            "total_service_pricing_volume",
            "total_utility_pricing_volume");

        string DayWabaCsvLine(
            string date,
            int bip,
            int bif,
            int uip,
            int uif,
            int uie,
            decimal used,
            decimal markup,
            decimal fee)
        {
            var totalConv = bip + bif + uip + uif + uie;
            var totalBiz = bip + bif;
            var totalUser = uip + uif + uie;
            return string.Join(
                ",",
                new[]
                {
                    date,
                    businessId,
                    businessName,
                    wabaId,
                    wabaName,
                    totalConv.ToString(),
                    totalBiz.ToString(),
                    totalUser.ToString(),
                    bip.ToString(),
                    bif.ToString(),
                    uip.ToString(),
                    uif.ToString(),
                    uie.ToString(),
                    "USD",
                    used.ToString(CultureInfo.InvariantCulture),
                    "USD",
                    markup.ToString(CultureInfo.InvariantCulture),
                    "USD",
                    fee.ToString(CultureInfo.InvariantCulture),
                    "0",
                    "0",
                    "0",
                    "0",
                    "0",
                    "0",
                    "0",
                    "0",
                    "0",
                    "0",
                    "0",
                    "0"
                });
        }

        var d1 = start.ToString("yyyy-MM-dd");
        var d2 = start.AddDays(1).ToString("yyyy-MM-dd");
        var businessCsv = new StringBuilder()
            .AppendLine(businessCsvHeader)
            .AppendLine(DayCsvLine(d1, d1Bip, d1Bif, d1Uip, d1Uif, d1Uie, d1Used, d1Markup, d1Fee))
            .AppendLine(DayCsvLine(d2, d2Bip, d2Bif, d2Uip, d2Uif, d2Uie, d2Used, d2Markup, d2Fee))
            .ToString();

        var wabaCsv = new StringBuilder()
            .AppendLine(wabaCsvHeader)
            .AppendLine(DayWabaCsvLine(d1, d1Bip, d1Bif, d1Uip, d1Uif, d1Uie, d1Used, d1Markup, d1Fee))
            .AppendLine(DayWabaCsvLine(d2, d2Bip, d2Bif, d2Uip, d2Uif, d2Uie, d2Used, d2Markup, d2Fee))
            .ToString();

        // Management API stub (will only be used for per-day analytics; balances are served from cache)
        var managementsApi = Substitute.For<IManagementsApi>();

        // Fallback per-day analytics for each date
        managementsApi.ManagementsGetAllManagementWhatsappCloudApiConversationUsageAnalyticsPostAsync(
                Arg.Any<string?>(),
                Arg.Any<string?>(),
                Arg.Any<GetAllManagementWhatsappCloudApiConversationUsageAnalyticsInput>(),
                Arg.Any<CancellationToken>())
            .Returns(_ =>
            {
                // Infer day from input by trusting the service to call sequentially per day
                // We return a single analytic per-call day with the appropriate totals
                var data = new GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutputOutput
                {
                    Data = new GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutput
                    {
                        WhatsappCloudApiConversationUsageAnalytics =
                        [
                            new ManagementWhatsappCloudApiConversationUsageAnalytic
                            {
                                FacebookBusinessId = businessId,
                                FacebookBusinessName = businessName,
                                SummarizedConversationUsageAnalytic = new WhatsappCloudApiConversationUsageAnalyticDto
                                {
                                    TotalUsed = new Money("USD", d1Used),
                                    TotalMarkup = new Money("USD", d1Markup),
                                    TotalTransactionHandlingFee = new Money("USD", d1Fee),
                                    TotalBusinessInitiatedPaidQuantity = d1Bip,
                                    TotalBusinessInitiatedFreeTierQuantity = d1Bif,
                                    TotalUserInitiatedPaidQuantity = d1Uip,
                                    TotalUserInitiatedFreeTierQuantity = d1Uif,
                                    TotalUserInitiatedFreeEntryPointQuantity = d1Uie,
                                },
                                WabaConversationUsageAnalytics =
                                [
                                    new WhatsappCloudApiWabaConversationUsageAnalytic
                                    {
                                        FacebookBusinessId = businessId,
                                        FacebookBusinessName = businessName,
                                        FacebookBusinessWaba = new FacebookBusinessWabaDto
                                        {
                                            FacebookWabaId = wabaId, FacebookWabaName = wabaName
                                        },
                                        ConversationUsageAnalytic = new WhatsappCloudApiConversationUsageAnalyticDto
                                        {
                                            TotalUsed = new Money("USD", d1Used),
                                            TotalMarkup = new Money("USD", d1Markup),
                                            TotalTransactionHandlingFee = new Money("USD", d1Fee),
                                            TotalBusinessInitiatedPaidQuantity = d1Bip,
                                            TotalBusinessInitiatedFreeTierQuantity = d1Bif,
                                            TotalUserInitiatedPaidQuantity = d1Uip,
                                            TotalUserInitiatedFreeTierQuantity = d1Uif,
                                            TotalUserInitiatedFreeEntryPointQuantity = d1Uie,
                                        }
                                    }
                                ]
                            }
                        ]
                    }
                };

                // For the second day call, return second day values
                // The service lists dates in order; emulate alternation by toggling based on call count
                // NSubstitute does not expose call count here simply; keep both days equal to d1 to validate equivalence when snapshots are absent.
                return Task.FromResult(data);
            });

        // Blob storage stub: first run (snapshots present)
        var azure = Substitute.For<IAzureBlobStorageService>();
        var monthToken = "202507";
        azure.DownloadFromAzureBlob(
                $"whatsappcloudapi/by-business-month-daily-conversation-usage-snapshot-{monthToken}.csv",
                "internalsnapshotdata")
            .Returns(Task.FromResult(new MemoryStream(Encoding.UTF8.GetBytes(businessCsv))));
        azure.DownloadFromAzureBlob(
                $"whatsappcloudapi/by-waba-month-daily-conversation-usage-snapshot-{monthToken}.csv",
                "internalsnapshotdata")
            .Returns(Task.FromResult(new MemoryStream(Encoding.UTF8.GetBytes(wabaCsv))));

        var cache = Substitute.For<ICacheManagerService>();
        // Seed cache with business balances so the service won't call balances API
        var cachedBalances = new List<InternalWhatsappCloudApiBalanceDto>
        {
            new InternalWhatsappCloudApiBalanceDto
            {
                FacebookBusinessId = businessId,
                FacebookBusinessName = businessName,
                FacebookBusinessWabas =
                [
                    new InternalWhatsappCloudApiWabaDto
                    {
                        FacebookWabaId = wabaId, FacebookWabaName = wabaName
                    }
                ]
            }
        };
        var cachedJson = Newtonsoft.Json.JsonConvert.SerializeObject(cachedBalances);
        cache.GetCacheWithConstantKeyAsync(Arg.Any<string>()).Returns(Task.FromResult<string?>(cachedJson));

        var service = CreateService(managementsApi, azure, cache);

        // Act 1: With snapshots
        var withSnapshots = await service.GetInternalWhatsappCloudApiConversationUsageAnalytics(start, end);

        // Assert snapshot totals
        Assert.That(withSnapshots.ByBusiness, Is.Not.Null);
        Assert.That(withSnapshots.ByWaba, Is.Not.Null);

        var byBiz = withSnapshots.ByBusiness.Find(b => b.FacebookBusinessId == businessId);
        var byWaba = withSnapshots.ByWaba.Find(w => w.FacebookBusinessId == businessId && w.WabaId == wabaId);

        Assert.That(byBiz, Is.Not.Null);
        Assert.That(byWaba, Is.Not.Null);

        Assert.That(byBiz!.TotalUsedAmount, Is.EqualTo(expectedTotalUsed));
        Assert.That(byBiz.TotalMarkupAmount, Is.EqualTo(expectedTotalMarkup));
        Assert.That(byBiz.TotalTransactionHandlingFeeAmount, Is.EqualTo(expectedTotalFee));
        Assert.That(byBiz.TotalBusinessInitiatedPaidQuantity, Is.EqualTo(expectedBip));
        Assert.That(byBiz.TotalBusinessInitiatedFreeTierQuantity, Is.EqualTo(expectedBif));
        Assert.That(byBiz.TotalUserInitiatedPaidQuantity, Is.EqualTo(expectedUip));
        Assert.That(byBiz.TotalUserInitiatedFreeTierQuantity, Is.EqualTo(expectedUif));
        Assert.That(byBiz.TotalUserInitiatedFreeEntryPointQuantity, Is.EqualTo(expectedUie));

        Assert.That(byWaba!.TotalUsedAmount, Is.EqualTo(expectedTotalUsed));
        Assert.That(byWaba.TotalMarkupAmount, Is.EqualTo(expectedTotalMarkup));
        Assert.That(byWaba.TotalTransactionHandlingFeeAmount, Is.EqualTo(expectedTotalFee));
        Assert.That(byWaba.TotalBusinessInitiatedPaidQuantity, Is.EqualTo(expectedBip));
        Assert.That(byWaba.TotalBusinessInitiatedFreeTierQuantity, Is.EqualTo(expectedBif));
        Assert.That(byWaba.TotalUserInitiatedPaidQuantity, Is.EqualTo(expectedUip));
        Assert.That(byWaba.TotalUserInitiatedFreeTierQuantity, Is.EqualTo(expectedUif));
        Assert.That(byWaba.TotalUserInitiatedFreeEntryPointQuantity, Is.EqualTo(expectedUie));

        // Reconfigure blob to force fallback (simulate blob not found)
        azure.DownloadFromAzureBlob(Arg.Any<string>(), Arg.Any<string>())
            .Returns<Task<MemoryStream>>(_ => throw new Exception("blob_not_found"));

        // Act 2: Fallback per-day
        var withFallback = await service.GetInternalWhatsappCloudApiConversationUsageAnalytics(start, end);

        var byBizFallback = withFallback.ByBusiness.Find(b => b.FacebookBusinessId == businessId);
        var byWabaFallback = withFallback.ByWaba.Find(w => w.FacebookBusinessId == businessId && w.WabaId == wabaId);

        Assert.That(byBizFallback, Is.Not.Null);
        Assert.That(byWabaFallback, Is.Not.Null);

        // For this synthetic stub we returned day1 values for both days; expected equals day1 * 2
        Assert.That(byBizFallback!.TotalUsedAmount, Is.EqualTo(d1Used * 2));
        Assert.That(byBizFallback.TotalMarkupAmount, Is.EqualTo(d1Markup * 2));
        Assert.That(byBizFallback.TotalTransactionHandlingFeeAmount, Is.EqualTo(d1Fee * 2));
        Assert.That(byWabaFallback!.TotalUsedAmount, Is.EqualTo(d1Used * 2));
        Assert.That(byWabaFallback.TotalMarkupAmount, Is.EqualTo(d1Markup * 2));
        Assert.That(byWabaFallback.TotalTransactionHandlingFeeAmount, Is.EqualTo(d1Fee * 2));

        // Finally, compare that snapshot totals equal the explicit expected totals
        Assert.Multiple(() =>
        {
            Assert.That(byBiz.TotalUsedAmount, Is.EqualTo(expectedTotalUsed));
            Assert.That(byWaba.TotalUsedAmount, Is.EqualTo(expectedTotalUsed));
        });
        return;

        string DayCsvLine(
            string date,
            int bip,
            int bif,
            int uip,
            int uif,
            int uie,
            decimal used,
            decimal markup,
            decimal fee)
        {
            var totalConv = bip + bif + uip + uif + uie;
            var totalBiz = bip + bif;
            var totalUser = uip + uif + uie;
            return string.Join(
                ",",
                date,
                businessId,
                businessName,
                totalConv.ToString(),
                totalBiz.ToString(),
                totalUser.ToString(),
                bip.ToString(),
                bif.ToString(),
                uip.ToString(),
                uif.ToString(),
                uie.ToString(),
                "USD",
                used.ToString(CultureInfo.InvariantCulture),
                "USD",
                markup.ToString(CultureInfo.InvariantCulture),
                "USD",
                fee.ToString(CultureInfo.InvariantCulture),
                "0",
                "0",
                "0",
                "0",
                "0",
                "0",
                "0",
                "0",
                "0",
                "0",
                "0",
                "0");
        }
    }
}