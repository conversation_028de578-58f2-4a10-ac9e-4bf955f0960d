using System.Collections.Generic;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Travis_backend.ChannelDomain.Models;

public class Settings
{
    [JsonProperty("branding")]
    public Branding Branding { get; set; }

    [JsonProperty("languages")]
    public Languages Languages { get; set; }

    [JsonProperty("displayLogic")]
    public DisplayLogic DisplayLogic { get; set; }

    [JsonProperty("preChatForm")]
    public PreChatForm PreChatForm { get; set; }

    [JsonProperty("popupMessages")]
    public List<PopupMessage> PopupMessages { get; set; }

    [JsonProperty("channels")]
    public Channels Channels { get; set; }

    [JsonProperty("additionalSettings")]
    public AdditionalSettings AdditionalSettings { get; set; }
}

public class Branding
{
    [JsonProperty("companyLogoUrl")]
    public string CompanyLogoUrl { get; set; }

    [JsonProperty("widgetDisplayName")]
    public string WidgetDisplayName { get; set; }

    [JsonProperty("senderDisplayName")]
    public string SenderDisplayName { get; set; }
}

public class Languages
{
    [JsonProperty("availableLanguage")]
    public Dictionary<string, bool> AvailableLanguage { get; set; }

    [JsonProperty("defaultLanguage")]
    public string DefaultLanguage { get; set; }
}


public class DisplayLogic
{
    [JsonProperty("widgetPosition")]
    [JsonConverter(typeof(StringEnumConverter))]
    public WidgetPosition WidgetPosition { get; set; }

    [JsonProperty("buttonColor")]
    public string ButtonColor { get; set; }

    [JsonProperty("messageBackgroundColor")]
    public string MessageBackgroundColor { get; set; }
}


public class PreChatForm
{
    [JsonProperty("enabled")]
    public bool Enabled { get; set; }

    [JsonProperty("isEmailRequired")]
    public bool IsEmailRequired { get; set; }

    [JsonProperty("isPhoneNumberRequired")]
    public bool IsPhoneNumberRequired { get; set; }
}

public class PopupMessage
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("delaySeconds")]
    public int DelaySeconds { get; set; }

    [JsonProperty("published")]
    public bool Published { get; set; }

    [JsonProperty("conditions")]
    public List<PopupCondition> Conditions { get; set; }
}

public class PopupCondition
{
    [JsonProperty("rule")]
    public string Rule { get; set; }

    [JsonProperty("criteria")]
    public string Criteria { get; set; }
}

public class Channels
{
    [JsonProperty("options")]
    public ChannelOptions Options { get; set; }

    [JsonProperty("availableChannels")]
    public AvailableChannels AvailableChannels { get; set; }
}

public class ChannelOptions
{
    [JsonProperty("whatsapp")]
    public Dictionary<string, object> Whatsapp { get; set; }

    [JsonProperty("facebook")]
    public Dictionary<string, object> Facebook { get; set; }

    [JsonProperty("instagram")]
    public Dictionary<string, object> Instagram { get; set; }

    [JsonProperty("wechat")]
    public Dictionary<string, object> Wechat { get; set; }

    [JsonProperty("line")]
    public Dictionary<string, object> Line { get; set; }

    [JsonProperty("viber")]
    public Dictionary<string, object> Viber { get; set; }

    [JsonProperty("telegram")]
    public Dictionary<string, object> Telegram { get; set; }

    [JsonProperty("livechat")]
    public Dictionary<string, object> Livechat { get; set; }
}

public class AvailableChannels
{
    [JsonProperty("whatsapp")]
    public bool Whatsapp { get; set; }

    [JsonProperty("facebook")]
    public bool Facebook { get; set; }

    [JsonProperty("instagram")]
    public bool Instagram { get; set; }

    [JsonProperty("wechat")]
    public bool Wechat { get; set; }

    [JsonProperty("line")]
    public bool Line { get; set; }

    [JsonProperty("viber")]
    public bool Viber { get; set; }

    [JsonProperty("telegram")]
    public bool Telegram { get; set; }

    [JsonProperty("livechat")]
    public bool Livechat { get; set; }
}

public class AdditionalSettings
{
    [JsonProperty("welcomeMessage")]
    public string WelcomeMessage { get; set; }
}

public enum WidgetPosition
{
    [EnumMember(Value = "bottom-right")]
    BottomRight,
    [EnumMember(Value = "bottom-left")]
    BottomLeft,
    [EnumMember(Value = "top-right")]
    TopRight,
    [EnumMember(Value = "top-left")]
    TopLeft
}