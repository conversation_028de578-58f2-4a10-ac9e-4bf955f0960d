using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Travis_backend.ChannelDomain.Models.LiveChatV2;
using Travis_backend.Constants;

namespace Travis_backend.ChannelDomain.Extensions.LiveChatV2Extensions;

public static class LiveChatMetadataExtension
{
    public static LiveChatV2VisitedPage? GetVisitedPage(this Dictionary<string, object> metadata)
    {
        // 1. Guard against a null sender or a sender without a Metadata dictionary.
        if (metadata is null)
        {
            return null;
        }

        // 2. Use TryGetValue for a safe lookup. This avoids a KeyNotFoundException
        // if the key doesn't exist in the dictionary.
        if (!metadata.TryGetValue(LiveChatMetadataKeys.VisitedPage, out var value) || value == null)
        {
            return null;
        }

        // Optimization: if the object is already the correct type, just return it.
        if (value is LiveChatV2VisitedPage alreadyTypedPage)
        {
            return alreadyTypedPage;
        }

        // The reliable conversion method:
        try
        {
            // 1. Serialize the generic object (whatever it is - JsonElement, etc.) into a standard JSON string.
            var jsonString = JsonConvert.SerializeObject(value);

            // 2. Deserialize that clean JSON string into your specific C# class.
            return JsonConvert.DeserializeObject<LiveChatV2VisitedPage>(jsonString);
        }
        catch (Exception e)
        {
            // If the object's structure doesn't match the class, this prevents a crash.
            return null;
        }
    }
}