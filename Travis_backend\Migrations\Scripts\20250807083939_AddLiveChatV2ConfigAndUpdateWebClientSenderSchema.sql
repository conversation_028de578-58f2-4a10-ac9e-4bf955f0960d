﻿BEGIN TRANSACTION;
GO

ALTER TABLE [SenderWebClientSenders] ADD [ChannelIdentityId] nvarchar(400) NULL;
GO

ALTER TABLE [SenderWebClientSenders] ADD [ConversationId] nvarchar(400) NULL;
GO

ALTER TABLE [SenderWebClientSenders] ADD [Metadata] nvarchar(max) NULL;
GO

ALTER TABLE [SenderWebClientSenders] ADD [UserProfileId] nvarchar(400) NULL;
GO

CREATE TABLE [ConfigLiveChatV2Configs] (
    [Id] bigint NOT NULL IDENTITY,
    [CompanyId] nvarchar(400) NULL,
    [IsDeleted] bit NOT NULL,
    [Settings] nvarchar(max) NULL,
    [ChannelIdentityId] nvarchar(400) NULL,
    [ChannelDisplayName] nvarchar(max) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_ConfigLiveChatV2Configs] PRIMARY KEY ([Id])
);
GO

CREATE INDEX [IX_SenderWebClientSenders_CompanyId_UserProfileId] ON [SenderWebClientSenders] ([CompanyId], [UserProfileId]);
GO

CREATE INDEX [IX_SenderWebClientSenders_ConversationId_CompanyId] ON [SenderWebClientSenders] ([ConversationId], [CompanyId]);
GO

CREATE INDEX [IX_ConfigLiveChatV2Configs_ChannelIdentityId] ON [ConfigLiveChatV2Configs] ([ChannelIdentityId]);
GO

CREATE INDEX [IX_ConfigLiveChatV2Configs_ChannelIdentityId_CompanyId] ON [ConfigLiveChatV2Configs] ([ChannelIdentityId], [CompanyId]);
GO

CREATE INDEX [IX_ConfigLiveChatV2Configs_CompanyId] ON [ConfigLiveChatV2Configs] ([CompanyId]);
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250807083939_AddLiveChatV2ConfigAndUpdateWebClientSenderSchema', N'8.0.7');
GO

COMMIT;
GO

