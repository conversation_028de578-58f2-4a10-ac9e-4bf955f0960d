#nullable enable

using System;
using System.Threading.Tasks;

namespace Travis_backend.CompanyDomain.Services
{
    /// <summary>
    /// Service for timezone-aware MRR calculations.
    /// </summary>
    public interface ITimezoneAwareMrrCalculationService
    {
        /// <summary>
        /// Retrieves timezone information by timezone ID.
        /// </summary>
        /// <param name="timezoneInfoId">The timezone ID to retrieve.</param>
        /// <returns>The timezone information, or UTC if not found or invalid.</returns>
        TimeZoneInfo GetCompanyTimezone(string? timezoneInfoId = null);

        /// <summary>
        /// Converts a period (start and end times) from UTC to the specified timezone.
        /// </summary>
        /// <param name="utcPeriodStart">The UTC period start time.</param>
        /// <param name="utcPeriodEnd">The UTC period end time.</param>
        /// <param name="companyId">The company ID for logging purposes.</param>
        /// <param name="timezoneInfoId">Optional timezone ID to use directly. If not provided, UTC is used.</param>
        /// <returns>A tuple containing the converted period start and end times.</returns>
        (DateTime LocalStart, DateTime LocalEnd) ConvertPeriodToCompanyTimezone(
            DateTime utcPeriodStart,
            DateTime utcPeriodEnd,
            string companyId,
            string? timezoneInfoId = null);

        /// <summary>
        /// Calculates precise month difference with pro-rata logic for partial periods.
        /// </summary>
        /// <param name="periodStart">The period start time.</param>
        /// <param name="periodEnd">The period end time.</param>
        /// <returns>The precise number of months including fractional months for partial periods.</returns>
        decimal GetPreciseMonthDiff(DateTime periodStart, DateTime periodEnd);

        /// <summary>
        /// Calculates precise month difference with timezone awareness and pro-rata logic.
        /// </summary>
        /// <param name="utcPeriodStart">The UTC period start time.</param>
        /// <param name="utcPeriodEnd">The UTC period end time.</param>
        /// <param name="companyId">The company ID for logging purposes.</param>
        /// <param name="timezoneInfoId">Optional timezone ID to use directly. If not provided, UTC is used.</param>
        /// <returns>The precise number of months including fractional months for partial periods.</returns>
        decimal GetPreciseMonthDiff(DateTime utcPeriodStart, DateTime utcPeriodEnd, string companyId, string? timezoneInfoId = null);
    }
}