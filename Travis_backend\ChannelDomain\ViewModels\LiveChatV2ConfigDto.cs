using System.Collections.Generic;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Travis_backend.ChannelDomain.ViewModels.Interfaces;

namespace Travis_backend.ChannelDomain.ViewModels;

public class LiveChatV2ConfigDto : IMessagingChannelDto
{
    public long Id { get; set; }

    public string CompanyId { get; set; }

    public bool IsDeleted { get; set; }

    public Dictionary<string, object> Settings { get; set; } = new Dictionary<string, object>();

    public string ChannelType { get; set; }

    public string ChannelIdentityId { get; set; }

    public string ChannelDisplayName { get; set; }
}