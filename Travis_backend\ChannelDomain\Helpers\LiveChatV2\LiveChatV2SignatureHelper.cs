using System;
using System.Security.Cryptography;
using System.Text;

namespace Travis_backend.ChannelDomain.Helpers.LiveChatV2;

public static class LiveChatV2SignatureHelper
{
    public static string GenerateHmacSignature(string data, string secret)
    {
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secret));
        var hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
        return Convert.ToBase64String(hashBytes);
    }
}