using System;
using System.Collections.Generic;
using Travis_backend.Cache.Models;
using Travis_backend.Cache.Models.CacheKeyPatterns;

namespace Travis_backend.Cache;

public static class KeyPatternConfigMap
{
    private static readonly Dictionary<string, CacheConfig> CacheConfig = new ()
    {
        {
            nameof(AnalyticsRecordCacheKeyPattern), new CacheConfig(
                CachePrefixType.AnalyticsRecordV5,
                TimeSpan.FromDays(30))
        },
        {
            nameof(GetCompanyUsageCacheKeyPattern), new CacheConfig(
                CachePrefixType.GetCompanyUsage,
                TimeSpan.FromMinutes(5))
        },
        {
            nameof(GetCompanyInfoCacheKeyPattern), new CacheConfig(
                CachePrefixType.GetCompanyInfo,
                TimeSpan.FromHours(12))
        },
        {
            nameof(GetUserProfileTotalCacheKeyPattern), new CacheConfig(
                CachePrefixType.GetUserProfileTotal,
                TimeSpan.FromSeconds(10))
        },
        {
            nameof(GetUserProfileCacheKeyPattern), new CacheConfig(
                CachePrefixType.GetUserProfile,
                TimeSpan.FromSeconds(5))
        },
        {
            nameof(SearchUserProfileWithConversationCacheKeyPattern), new CacheConfig(
                CachePrefixType.SearchUserProfileWithConversation,
                TimeSpan.FromMinutes(10))
        },
        {
            nameof(InitWebClientCacheKeyPattern), new CacheConfig(
                CachePrefixType.InitWebClient,
                TimeSpan.FromHours(1))
        },
        {
            nameof(BroadcastStatisticsCacheKeyPattern), new CacheConfig(
                CachePrefixType.BroadcastStatisticsV3,
                TimeSpan.FromMinutes(15))
        },
        {
            nameof(RealtimeBroadcastStatisticsCacheKeyPattern), new CacheConfig(
                CachePrefixType.RealtimeBroadcastStatistics,
                TimeSpan.FromSeconds(10))
        },
        {
            nameof(WhatsApp360DialogGetTemplateCacheKeyPattern), new CacheConfig(
                CachePrefixType.WhatsApp360DialogGetTemplate,
                TimeSpan.FromHours(1))
        },
        {
            nameof(GetShopifyOrderStatisticsCacheKeyPattern), new CacheConfig(
                CachePrefixType.GetShopifyOrderStatistics,
                TimeSpan.FromMinutes(5))
        },
        {
            nameof(OrderStaffStatisticsV3CacheKeyPattern), new CacheConfig(
                CachePrefixType.OrderStaffStatisticsV3,
                TimeSpan.FromMinutes(5))
        },
        {
            nameof(ShopifyProductV1CacheKeyPattern), new CacheConfig(
                CachePrefixType.ShopifyProductV1,
                TimeSpan.FromSeconds(60))
        },
        {
            nameof(ConversationUnreadSummaryCacheKeyPattern), new CacheConfig(
                CachePrefixType.ConversationUnreadSummary,
                TimeSpan.FromSeconds(1))
        },
        {
            nameof(GetCompanyAllSchemasCacheKeyPattern), new CacheConfig(
                string.Empty,
                TimeSpan.FromMinutes(3))
        },
        {
            nameof(PiiMaskingConfigCacheKeyPattern), new CacheConfig(
                CachePrefixType.PiiMaskingConfigs,
                TimeSpan.FromMinutes(5))
        },
        {
            nameof(FlowHubConfigCacheKeyPattern), new CacheConfig(
                CachePrefixType.FlowHubConfig,
                TimeSpan.FromSeconds(60))
        },
        {
            nameof(ConversationDetailsAdminCacheKeyPattern), new CacheConfig(
                CachePrefixType.ConversationDetails,
                TimeSpan.FromSeconds(5))
        },
        {
            nameof(ConversationDetailsPersonalCacheKeyPattern), new CacheConfig(
                CachePrefixType.ConversationDetails,
                TimeSpan.FromSeconds(5))
        },
        {
            nameof(ConversationMessageCacheKeyPattern), new CacheConfig(
                CachePrefixType.Message,
                TimeSpan.FromSeconds(60))
        },
        {
            nameof(ConversationMessageMasterCacheKeyPattern), new CacheConfig(
                CachePrefixType.ConversationMessageMasterCacheKey,
                TimeSpan.FromSeconds(60))
        },
        {
            nameof(ConversationMessageV2MasterCacheKeyPattern), new CacheConfig(
                CachePrefixType.ConversationMessageV2MasterCacheKey,
                TimeSpan.FromSeconds(60))
        },
        {
            nameof(SignalrMessageCacheKeyPattern), new CacheConfig(
                CachePrefixType.SignalrMessage,
                TimeSpan.FromSeconds(60))
        },
        {
            nameof(AssignedConversationV2CacheKeyPattern), new CacheConfig(
                CachePrefixType.Assigned,
                TimeSpan.FromSeconds(30))
        },
        {
            nameof(ConversationAllCacheKeyPattern), new CacheConfig(
                CachePrefixType.All,
                TimeSpan.FromSeconds(30))
        },
        {
            nameof(GetConversationMessagesCacheKeyPattern), new CacheConfig(
                CachePrefixType.GetConversationMessages,
                TimeSpan.FromSeconds(10))
        },
        {
            nameof(GetConversationMessagesMasterCacheKeyPattern), new CacheConfig(
                CachePrefixType.GetConversationMessagesMasterCacheKey,
                TimeSpan.FromMinutes(1))
        },
        {
            nameof(AssignedConversationV3CacheKeyPattern), new CacheConfig(
                CachePrefixType.Assigned,
                TimeSpan.FromSeconds(2))
        },
        {
            nameof(ConversationSummaryV4CacheKeyPattern), new CacheConfig(
                CachePrefixType.Assigned,
                TimeSpan.FromSeconds(8))
        },
        {
            nameof(ConversationHashTagSummaryV4CacheKeyPattern), new CacheConfig(
                CachePrefixType.Assigned,
                TimeSpan.FromSeconds(8))
        },
        {
            nameof(ConnectedFacebookWebhookCacheKeyPattern), new CacheConfig(
                CachePrefixType.ConnectedFacebookWebhookEntryIds,
                TimeSpan.FromMinutes(5))
        },
        {
            nameof(PublicApiSearchMessageCacheKeyPattern), new CacheConfig(
                CachePrefixType.PublicApiSearchMessage,
                TimeSpan.FromMinutes(15))
        },
        {
            nameof(CrmHubServiceCacheKeyPattern), new CacheConfig(
                string.Empty,
                TimeSpan.FromHours(1))
        },
        {
            nameof(SyncContactOwnerMapCacheKeyPattern), new CacheConfig(
                CachePrefixType.SyncContactOwnerMapHourlyLock,
                TimeSpan.FromHours(1))
        },
        {
            nameof(ConversationMessageV2CacheKeyPattern), new CacheConfig(
                CachePrefixType.ConversationsV2,
                TimeSpan.FromSeconds(5))
        },
        {
            nameof(GetUndeliveredMessageCacheKeyPattern), new CacheConfig(
                CachePrefixType.GetUndeliveredMessages,
                TimeSpan.FromSeconds(8))
        },
        {
            nameof(SearchMessageCacheKeyPattern), new CacheConfig(
                CachePrefixType.SearchMessageV2,
                TimeSpan.FromSeconds(60))
        },
        {
            nameof(IpWhitelistCacheKeyPattern), new CacheConfig(
                CachePrefixType.IPWhiteList,
                TimeSpan.MaxValue)
        },
        {
            nameof(GetCompanyContactUsageCacheKeyPattern), new CacheConfig(
                CachePrefixType.GetCompanyContactUsage,
                TimeSpan.FromSeconds(1))
        },
        {
            nameof(TicketingHubTicketUserPattern), new CacheConfig(
                CachePrefixType.TicketingHubTicketUser,
                TimeSpan.FromMinutes(1))
        },
        {
            nameof(GetWhatsappCloudApiFlowsCacheKeyPattern), new CacheConfig(
                CachePrefixType.GetWhatsappCloudApiFlows,
                TimeSpan.FromMinutes(10))
        },
        {
            nameof(GetQuickRepliesCacheKeyPattern), new CacheConfig(
                CachePrefixType.QuickReplies,
                TimeSpan.FromHours(6))
        },
        {
            nameof(GetQuickRepliesMasterCacheKeyPattern), new CacheConfig(
                CachePrefixType.QuickRepliesMaster,
                TimeSpan.FromHours(6))
        },
        {
            nameof(RbacCacheKeyPattern), new CacheConfig(
                CachePrefixType.Rbac,
                TimeSpan.FromMinutes(15))
        },
        {
            nameof(EnabledFeatureCacheKeyPattern), new CacheConfig(
                CachePrefixType.EnabledFeatureFlag,
                TimeSpan.FromHours(1))
        },
        {
            nameof(FeaturesCacheKeyPattern), new CacheConfig(
                CachePrefixType.Features,
                TimeSpan.FromHours(1))
        },
        {
            nameof(ConversationAnalyticsMetricsCacheKeyPattern), new CacheConfig(
                CachePrefixType.AnalyticsRecordV5,
                TimeSpan.FromHours(1))
        },
        {
            nameof(ConversationAnalyticsBroadcastMessageMetricsCacheKeyPattern), new CacheConfig(
                CachePrefixType.AnalyticsRecordV5,
                TimeSpan.FromHours(1))
        },
        {
            nameof(GetCompanyAllStaffRolesWithPermissionsCacheKeyPattern), new CacheConfig(
                CachePrefixType.Rbac,
                TimeSpan.FromHours(1))
        },
        {
            nameof(RbacDefaultRolesCacheKeyPattern), new CacheConfig(
                CachePrefixType.Rbac,
                TimeSpan.FromHours(12))
        },
        {
            nameof(WebClientGetCompanyInfoCacheKeyPattern), new CacheConfig(
                CachePrefixType.WebClientSetting,
                TimeSpan.FromHours(12))
        },
        {
            nameof(WebClientGetStaffAllInfoCacheKeyPattern), new CacheConfig(
                CachePrefixType.WebClientAllStaffInfo,
                TimeSpan.FromHours(12))
        },
        {
            nameof(GetCompanyStaffCacheKeyPattern), new CacheConfig(
                CachePrefixType.IdentifyCompanyStaff,
                TimeSpan.FromMinutes(15))
        },
        {
            nameof(ContactCustomUserProfileFieldsCacheKeyPattern), new CacheConfig(
                CachePrefixType.ContactCustomUserProfileFields,
                TimeSpan.FromHours(1))
        },
        {
            nameof(LiveChatSenderGeoLocationCacheKeyPattern), new CacheConfig(
                CachePrefixType.LiveChatSenderGeoLocation,
                TimeSpan.FromHours(12))
        },
        {
            nameof(StaffDeletionJobStatusKeyPattern), new CacheConfig(
                CachePrefixType.StaffDeletion,
                TimeSpan.FromDays(14))
        },
        {
            nameof(StaffDeletionJobsByCompanyKeyPattern), new CacheConfig(
                CachePrefixType.CompanyStaffDeletion,
                TimeSpan.FromDays(14))
        }
    };

    public static CacheConfig GetCacheConfig(ICacheKeyPattern keyPattern)
    {
        var patternName = keyPattern.GetType().Name;

        if (CacheConfig.TryGetValue(patternName, out var value))
        {
            return value;
        }

        throw new KeyNotFoundException($"CacheConfig for key pattern: {patternName} not found");
    }
}