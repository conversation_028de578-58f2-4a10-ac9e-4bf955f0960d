﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Travis_backend.Database;

#nullable disable

namespace Travis_backend.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250807083939_AddLiveChatV2ConfigAndUpdateWebClientSenderSchema")]
    partial class AddLiveChatV2ConfigAndUpdateWebClientSenderSchema
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Travis_backend.AccountAuthenticationDomain.Models.Admin", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("IdentityId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Locale")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("IdentityId", "CompanyId")
                        .IsUnique()
                        .HasFilter("[IdentityId] IS NOT NULL AND [CompanyId] IS NOT NULL");

                    b.ToTable("UserRoleAdmins");
                });

            modelBuilder.Entity("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("CompanyId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DefaultWebAppVersion")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("DisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<long?>("FacebookId")
                        .HasColumnType("bigint");

                    b.Property<string>("FirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InviteToken")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<DateTime?>("InviteTokenExpireAt")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("IsAgreeMarketingConsent")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastLoginAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("PictureUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("UserRole")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Travis_backend.AccountAuthenticationDomain.Models.Guest", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Gender")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IdentityId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Locale")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("IdentityId", "CompanyId")
                        .IsUnique()
                        .HasFilter("[IdentityId] IS NOT NULL AND [CompanyId] IS NOT NULL");

                    b.ToTable("UserRoleGuests");
                });

            modelBuilder.Entity("Travis_backend.AnalyticsDomain.Models.AnalyticsRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConditionsHash")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Data")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("IsTeamUnassigned")
                        .HasColumnType("bit");

                    b.Property<string>("StaffId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("TeamId")
                        .HasColumnType("bigint");

                    b.Property<int>("Version")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "TeamId", "StaffId", "IsTeamUnassigned", "DateTime", "ConditionsHash", "Version")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [TeamId] IS NOT NULL AND [StaffId] IS NOT NULL AND [IsTeamUnassigned] IS NOT NULL AND [ConditionsHash] IS NOT NULL");

                    b.ToTable("AnalyticsRecords");
                });

            modelBuilder.Entity("Travis_backend.AnalyticsDomain.Models.Segment", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Conditions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("SavedById")
                        .HasColumnType("bigint");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("SavedById");

                    b.ToTable("CompanyAnalyticSegment");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AssignmentQueue", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("StaffIds")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL");

                    b.ToTable("CompanyAssignmentQueues");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AssignmentRule", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long?>("AssignedStaffId")
                        .HasColumnType("bigint");

                    b.Property<long?>("AssignedTeamId")
                        .HasColumnType("bigint");

                    b.Property<string>("AssignmentId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AssignmentRuleName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("AssignmentType")
                        .HasColumnType("int");

                    b.Property<long?>("AssociatedListId")
                        .HasColumnType("bigint");

                    b.Property<int>("AutomationType")
                        .HasColumnType("int");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Conditions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsContinue")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPreview")
                        .HasColumnType("bit");

                    b.Property<int?>("Order")
                        .HasColumnType("int");

                    b.Property<string>("PreviewCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecurringSetting")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("SavedById")
                        .HasColumnType("bigint");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TargetedChannelWithIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TargetedChannels")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TeamAssignmentType")
                        .HasColumnType("int");

                    b.Property<int>("TriggeredCounter")
                        .HasColumnType("int");

                    b.Property<int>("TriggeredFailedCounter")
                        .HasColumnType("int");

                    b.Property<int>("TriggeredSuccessCounter")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("WebhookVariables")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ZaiperSubscriptionId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("AssignedStaffId");

                    b.HasIndex("AssignedTeamId");

                    b.HasIndex("AssignmentId")
                        .IsUnique()
                        .HasFilter("[AssignmentId] IS NOT NULL");

                    b.HasIndex("AssociatedListId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("SavedById");

                    b.HasIndex("Status");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("Status"), false);

                    b.HasIndex("ZaiperSubscriptionId")
                        .IsUnique()
                        .HasFilter("[ZaiperSubscriptionId] IS NOT NULL");

                    b.ToTable("CompanyAssignmentRules");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AssignmentUploadedFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AssignmentUploadedFileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long>("AutomationActionId")
                        .HasColumnType("bigint");

                    b.Property<long?>("AutomationActionRecordId")
                        .HasColumnType("bigint");

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AssignmentUploadedFileId");

                    b.HasIndex("AutomationActionId");

                    b.HasIndex("AutomationActionRecordId");

                    b.ToTable("CompanyAssignmentUploadedFiles");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AutomationAction", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ActionAddConversationHashtags")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ActionAddConversationRemarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ActionAddedToGroupIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ActionRemoveFromGroupIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ActionUpdateCustomFields")
                        .HasColumnType("nvarchar(max)");

                    b.Property<TimeSpan?>("ActionWait")
                        .HasColumnType("time");

                    b.Property<int?>("ActionWaitDays")
                        .HasColumnType("int");

                    b.Property<string>("AddAdditionalAssigneeIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("AssignedStaffId")
                        .HasColumnType("bigint");

                    b.Property<long?>("AssignedTeamId")
                        .HasColumnType("bigint");

                    b.Property<long>("AssignmentRuleId")
                        .HasColumnType("bigint");

                    b.Property<int>("AssignmentType")
                        .HasColumnType("int");

                    b.Property<int>("AutomatedTriggerType")
                        .HasColumnType("int");

                    b.Property<string>("ChangeConversationStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("DialogflowLanguageCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("DialogflowServiceAccountConfigId")
                        .HasColumnType("bigint");

                    b.Property<string>("ExtendedAutomationMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MessageContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MessageParams")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Order")
                        .HasColumnType("int");

                    b.Property<string>("TargetedChannelWithIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TeamAssignmentType")
                        .HasColumnType("int");

                    b.Property<string>("WebhookURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WhatsApp360DialogExtendedAutomationMessages")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AssignedStaffId");

                    b.HasIndex("AssignedTeamId");

                    b.HasIndex("AssignmentRuleId");

                    b.HasIndex("CompanyId");

                    b.ToTable("CompanyAutomationActions");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AutomationActionRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ActionAddConversationHashtags")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ActionAddConversationRemarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ActionAddedToGroupIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ActionRemoveFromGroupIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ActionUpdateCustomFields")
                        .HasColumnType("nvarchar(max)");

                    b.Property<TimeSpan?>("ActionWait")
                        .HasColumnType("time");

                    b.Property<int?>("ActionWaitDays")
                        .HasColumnType("int");

                    b.Property<string>("AddAdditionalAssigneeIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("AssignedStaffId")
                        .HasColumnType("bigint");

                    b.Property<long?>("AssignedTeamId")
                        .HasColumnType("bigint");

                    b.Property<int>("AssignmentType")
                        .HasColumnType("int");

                    b.Property<int>("AutomatedTriggerType")
                        .HasColumnType("int");

                    b.Property<long>("AutomationHistoryId")
                        .HasColumnType("bigint");

                    b.Property<string>("ChangeConversationStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreateAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ExtendedAutomationMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FormattedMessageContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MessageContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MessageParams")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Order")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TargetedChannelWithIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TeamAssignmentType")
                        .HasColumnType("int");

                    b.Property<string>("WebhookPayload")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WebhookURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WhatsApp360DialogExtendedAutomationMessages")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AssignedStaffId");

                    b.HasIndex("AssignedTeamId");

                    b.HasIndex("AutomationHistoryId");

                    b.ToTable("CompanyAutomationActionRecords");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AutomationHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("AssignmentRuleId")
                        .HasColumnType("bigint");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("ConversationMessageId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("IsSendMessage")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RegexValue")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TargetAssignmentRuleId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TargetUserProfileId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AssignmentRuleId");

                    b.HasIndex("TargetAssignmentRuleId", "TargetUserProfileId", "CompanyId", "CreatedAt");

                    b.ToTable("CompanyAutomationHistories");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.FbIgAutoReply", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<int>("AutoAction")
                        .HasColumnType("int");

                    b.Property<long>("AutomationActionId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("FbIgAutoReplyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("LikeComment")
                        .HasColumnType("bit");

                    b.Property<string>("MessageAttachment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MessageContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MessageFormat")
                        .HasColumnType("int");

                    b.Property<string>("PageId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PlatformType")
                        .HasColumnType("int");

                    b.Property<string>("PostId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QuickReplyButtons")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AutomationActionId")
                        .IsUnique();

                    b.ToTable("FbIgAutoReplies");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.FbIgAutoReplyFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("FbIgAutoReplyId")
                        .HasColumnType("bigint");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("FbIgAutoReplyId");

                    b.ToTable("FbIgAutoReplyFiles");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.FbIgAutoReplyHistoryRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AccountId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("AutoAction")
                        .HasColumnType("int");

                    b.Property<string>("CommentId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContactReplyMessages")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("FbIgAutoReplyId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsNewContact")
                        .HasColumnType("bit");

                    b.Property<string>("MessageId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PageId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ParentCommentId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ParentCommentMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PlatformType")
                        .HasColumnType("int");

                    b.Property<string>("PostId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PreviewCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PreviewMessageId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("FbIgAutoReplyId");

                    b.ToTable("FbIgAutoReplyHistoryRecords");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.FbIgIcebreaker", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("AssignmentRuleId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DefaultLanguage")
                        .HasColumnType("int");

                    b.Property<string>("FbIgIcebreakerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PlatformType")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("pageId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AssignmentRuleId")
                        .IsUnique();

                    b.ToTable("FbIgIcebreakers");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.IcebreakerHistoryRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("IcebreakerReplyRuleId")
                        .HasColumnType("bigint");

                    b.Property<string>("MessageId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PageId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Payload")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SenderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("IcebreakerReplyRuleId");

                    b.ToTable("IcebreakerHistoryRecords");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.IcebreakerReplyRule", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AutomationActionIdList")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("FbIgIcebreakerId")
                        .HasColumnType("bigint");

                    b.Property<string>("IcebreakerReplyRuleId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<int?>("Language")
                        .HasColumnType("int");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("PostbackPayload")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PostbackTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("FbIgIcebreakerId");

                    b.ToTable("IcebreakerReplyRules");
                });

            modelBuilder.Entity("Travis_backend.BlastDomain.Models.BlastMessageConfig", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL");

                    b.ToTable("BlastMessageConfigs");
                });

            modelBuilder.Entity("Travis_backend.BlastDomain.Models.BlastMessageTemplate", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("BlastContactsFile")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BlastMessageContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long?>("LastSentById")
                        .HasColumnType("bigint");

                    b.Property<string>("MessageHubRequestId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("MessageHubRequestProgress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MessageHubRequestResultCsvFileUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<long?>("SavedById")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("SentAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("TargetedChannelWithIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TotalContacts")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId"), false);

                    b.HasIndex("LastSentById");

                    b.HasIndex("SavedById");

                    b.ToTable("BlastMessageTemplates");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.Cms360DialogItemCost", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<int>("Cms360DialogCostItemType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,5)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Cms360DialogItemCosts");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.CmsCurrencyExchangeRate", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CurrencyFrom")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CurrencyTo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("ExchangeRate")
                        .HasColumnType("decimal(18,5)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("CmsCurrencyExchangeRates");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.CompanyWhatsapp360DialogTopUpConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ClientId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("PartnerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TopUpMode")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL");

                    b.ToTable("CompanyWhatsapp360DialogTopUpConfigs");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.DialogflowServiceAccountConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DialogflowCredentials")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("ConfigDialogflowServiceAccountConfigs");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.EmailConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<DateTime>("ConnectedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Domain")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsShowInWidget")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SendGridKey")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ConfigEmailConfigs");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.LineConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BasicId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChannelAccessToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChannelID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("ChannelSecert")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("ConnectedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsShowInWidget")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("NumberOfMessagesSentThisMonth")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("NumberOfMessagesSentThisMonthUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long?>("NumberOfTargetLimitForAdditionalMessages")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("NumberOfTargetLimitForAdditionalMessagesUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("TokenExpireAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("ConfigLineConfigs");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.LiveChatV2Config", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ChannelDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("CompanyId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Settings")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ChannelIdentityId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("ChannelIdentityId", "CompanyId");

                    b.ToTable("ConfigLiveChatV2Configs");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.SMSConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("ConnectedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ExpireDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsShowInWidget")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SMSSender")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TwilioAccountId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TwilioSecret")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("TwilioAccountId", "SMSSender")
                        .IsUnique()
                        .HasFilter("[TwilioAccountId] IS NOT NULL AND [SMSSender] IS NOT NULL");

                    b.ToTable("ConfigSMSConfigs");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.TelegramConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<bool>("CanJoinGroups")
                        .HasColumnType("bit");

                    b.Property<bool>("CanReadAllGroupMessages")
                        .HasColumnType("bit");

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("ConnectedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("DisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsShowInWidget")
                        .HasColumnType("bit");

                    b.Property<bool>("SupportsInlineQueries")
                        .HasColumnType("bit");

                    b.Property<string>("TelegramBotDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("TelegramBotId")
                        .HasColumnType("bigint");

                    b.Property<string>("TelegramBotToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TelegramBotUserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("ConfigTelegramConfigs");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.TwilioTemplateBookmarkRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AccountSID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("TemplateId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AccountSID", "TemplateId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("AccountSID", "TemplateId"), false);

                    b.ToTable("TwilioTemplateBookmarkRecords");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.TwilioUsageRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("End")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsVerified")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("Start")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("TotalCreditValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TwilioAccountId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "TwilioAccountId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [TwilioAccountId] IS NOT NULL");

                    b.ToTable("CompanyTwilioUsageRecords");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.ViberConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Category")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("ConnectedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("Country")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IconUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsShowInWidget")
                        .HasColumnType("bit");

                    b.Property<string>("SubCategory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Uri")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ViberBotId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ViberBotName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ViberBotSenderName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ViberBotToken")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("ConfigViberConfigs");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WeChatConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AccessToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AppId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AppSecret")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<DateTime>("ConnectedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsShowInWidget")
                        .HasColumnType("bit");

                    b.Property<string>("Metadata")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("QRCodeId")
                        .HasColumnType("bigint");

                    b.Property<string>("QRCodeURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("TokenExpireAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("WebChatId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("QRCodeId");

                    b.ToTable("ConfigWeChatConfigs");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsApp360DialogConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AccountMode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ApiKey")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ChannelErrorStatus")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ChannelErrorStatusStartAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ChannelId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("ChannelName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChannelStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClientId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CurrentLimit")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CurrentQualityRating")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsClient")
                        .HasColumnType("bit");

                    b.Property<bool>("IsOptInEnable")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSuspended")
                        .HasColumnType("bit");

                    b.Property<string>("LastPartnerWebhookEvent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OptInConfig")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PartnerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("SuspendedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("TemplateNamespace")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("WabaAccountId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WabaAccountName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WabaAccountType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WabaBusinessId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WabaBusinessStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WabaStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WhatsAppChannelSetupName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WhatsAppPhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("ConfigWhatsApp360DialogConfigs");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsApp360DialogMediaFile", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BlobFilePath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("WhatsApp360DialogConfigId")
                        .HasColumnType("bigint");

                    b.Property<string>("WhatsAppMediaType")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("WhatsApp360DialogConfigId");

                    b.ToTable("WhatsApp360DialogMediaFiles");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsApp360DialogTemplateBookmark", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long?>("CreatedByStaffId")
                        .HasColumnType("bigint");

                    b.Property<string>("Namespace")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateLanguage")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("TemplateName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("VisibleToTeamIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("WhatsApp360DialogConfigId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByStaffId");

                    b.HasIndex("WhatsApp360DialogConfigId");

                    b.ToTable("WhatsApp360DialogTemplateBookmarks");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsApp360DialogUsageRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<bool>("CanGenerateMarkupRecord")
                        .HasColumnType("bit");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Credit")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CurrentConversationPeriodUsage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CurrentPhoneNumberPeriodUsage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("DirectPaymentBalance")
                        .HasColumnType("decimal(18,5)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("LastConversationBillingPeriod")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastPhoneNumberBillingPeriod")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("MarkupPercentage")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("PartnerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("PendingCharges")
                        .HasColumnType("decimal(18,5)");

                    b.Property<int>("TopUpMode")
                        .HasColumnType("int");

                    b.Property<decimal>("UpcomingCharges")
                        .HasColumnType("decimal(18,5)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Used")
                        .HasColumnType("decimal(18,5)");

                    b.Property<DateTime>("Waba360DialogClientCreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Waba360DialogClientId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Waba360DialogClientName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("CompanyWhatsApp360DialogUsageRecords");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsApp360DialogUsageTransactionLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BillingPeriod")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("ConversationPeriodUsage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InternalUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("InvoiceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsInternalTestingUse")
                        .HasColumnType("bit");

                    b.Property<bool>("IsMarkedInUsageRecord")
                        .HasColumnType("bit");

                    b.Property<string>("NewPhoneNumberFee")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumberPeriodUsage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("ToUsageCurrencyExchangeRate")
                        .HasColumnType("decimal(18,5)");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,5)");

                    b.Property<int>("TransactionType")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("WhatsApp360DialogUsageRecordId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("WhatsApp360DialogUsageRecordId");

                    b.ToTable("CompanyWhatsApp360DialogUsageTransactionLogs");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsAppConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("ConnectedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CurrentValue")
                        .HasColumnType("int");

                    b.Property<DateTime>("ExpireDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IdempotencyToken")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("IsRequireTopup")
                        .HasColumnType("bit");

                    b.Property<bool>("IsShowInWidget")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSubaccount")
                        .HasColumnType("bit");

                    b.Property<string>("MessagingServiceSid")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReadMoreMessageContentSid")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ReadMoreTemplateId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReadMoreTemplateMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("TriggerValue")
                        .HasColumnType("int");

                    b.Property<string>("TwilioAccountId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TwilioSecret")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WhatsAppSender")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("IdempotencyToken")
                        .IsUnique()
                        .HasFilter("[IdempotencyToken] IS NOT NULL");

                    b.HasIndex("SID");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("SID"), false);

                    b.HasIndex("TwilioAccountId", "WhatsAppSender")
                        .IsUnique()
                        .HasFilter("[TwilioAccountId] IS NOT NULL AND [WhatsAppSender] IS NOT NULL");

                    b.ToTable("ConfigWhatsAppConfigs");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsappTemplateQuickReplyCallback", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ButtonLayout")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CallbackActions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("TemplateName")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TemplateNamespace")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId"), false);

                    b.HasIndex("CompanyId", "TemplateName", "TemplateNamespace")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "TemplateName", "TemplateNamespace"), false);

                    b.ToTable("WhatsappTemplateQuickReplyCallbacks");
                });

            modelBuilder.Entity("Travis_backend.CommonDomain.Models.IntegrationAlertConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Emails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumbers")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique();

                    b.ToTable("IntegrationAlertConfigs");
                });

            modelBuilder.Entity("Travis_backend.CommonDomain.Models.NotificationRegistration", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("Platform")
                        .HasColumnType("int");

                    b.Property<string>("RegistrationId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Tags")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Token")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("NotificationRegistrations");
                });

            modelBuilder.Entity("Travis_backend.CommonDomain.Models.QRcodeRecord", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FacebookPageId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GeneratedURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LineId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WeChatId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WhatsAppPhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("QRcodeRecords");
                });

            modelBuilder.Entity("Travis_backend.CommonDomain.Models.UserDevice", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AppID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AppVersion")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BuildVersion")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("DeviceModel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeviceName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeviceUUID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("DeviceUserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("IP_Address")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NotificationToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OSVersion")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Platform")
                        .HasColumnType("int");

                    b.Property<string>("SignalRConnectionId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("UnreadBadgeNumber")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("created_at")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("last_login")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("updated_at")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("DeviceUUID")
                        .IsUnique()
                        .HasFilter("[DeviceUUID] IS NOT NULL");

                    b.HasIndex("DeviceUserId");

                    b.ToTable("UserUserDevices");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.AnalyticsEmailNotificationConfig", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyCustomUserProfileFieldId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailAddresses")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Enabled")
                        .HasColumnType("bit");

                    b.Property<string>("Remark")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("AnalyticsEmailNotificationConfigs");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.BillRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("DowngradeFromBillRecordId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ExtendFromBillRecordId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsCustomized")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFreeTrial")
                        .HasColumnType("bit");

                    b.Property<bool>("PaidByReseller")
                        .HasColumnType("bit");

                    b.Property<double>("PayAmount")
                        .HasColumnType("float");

                    b.Property<int>("PaymentStatus")
                        .HasColumnType("int");

                    b.Property<DateTime>("PeriodEnd")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("PeriodStart")
                        .HasColumnType("datetime2");

                    b.Property<long?>("PurchaseStaffId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ShopifyChargeId")
                        .HasColumnType("bigint");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime?>("StripeSubscriptionCancelDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("StripeSubscriptionItemId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SubscriptionPlanId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("SubscriptionTier")
                        .HasColumnType("int");

                    b.Property<DateTime?>("TerminatedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long?>("UpgradeFromBillRecordId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UsageCycleEnd")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("UsageCycleStart")
                        .HasColumnType("datetime2");

                    b.Property<long>("amount_due")
                        .HasColumnType("bigint");

                    b.Property<long>("amount_paid")
                        .HasColumnType("bigint");

                    b.Property<long>("amount_remaining")
                        .HasColumnType("bigint");

                    b.Property<string>("chargeId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("created")
                        .HasColumnType("datetime2");

                    b.Property<string>("currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("customerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("customer_email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("hosted_invoice_url")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("invoice_Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("invoice_pdf")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("metadata")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("quantity")
                        .HasColumnType("bigint");

                    b.Property<string>("stripeId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("stripe_subscriptionId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("PurchaseStaffId");

                    b.HasIndex("SubscriptionPlanId");

                    b.HasIndex("invoice_Id");

                    b.HasIndex("stripeId");

                    b.HasIndex("stripe_subscriptionId");

                    b.ToTable("CompanyBillRecords");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.BroadcastHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BroadcastCampaignId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("BroadcastSentById")
                        .HasColumnType("bigint");

                    b.Property<string>("ConversationId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("BroadcastCampaignId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("BroadcastCampaignId"), new[] { "BroadcastSentById", "ConversationId", "CreatedAt", "Status" });

                    b.HasIndex("BroadcastSentById");

                    b.HasIndex("ConversationId", "BroadcastCampaignId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("ConversationId", "BroadcastCampaignId"), false);

                    b.ToTable("BroadcastCompaignHistories");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.BusinessHourConfig", b =>
                {
                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("WeeklyHours")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("CompanyId");

                    b.ToTable("BusinessHourConfigs");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CampaignAutomationAction", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ActionAddConversationHashtags")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ActionAddConversationRemarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ActionAddedToGroupIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ActionRemoveFromGroupIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ActionUpdateCustomFields")
                        .HasColumnType("nvarchar(max)");

                    b.Property<TimeSpan?>("ActionWait")
                        .HasColumnType("time");

                    b.Property<int?>("ActionWaitDays")
                        .HasColumnType("int");

                    b.Property<string>("AddAdditionalAssigneeIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("AssignedStaffId")
                        .HasColumnType("bigint");

                    b.Property<long?>("AssignedTeamId")
                        .HasColumnType("bigint");

                    b.Property<int>("AssignmentType")
                        .HasColumnType("int");

                    b.Property<int>("AutomatedTriggerType")
                        .HasColumnType("int");

                    b.Property<string>("ChangeConversationStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyMessageTemplateId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("MessageContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MessageParams")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Order")
                        .HasColumnType("int");

                    b.Property<string>("TargetedChannelWithIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("TeamAssignmentType")
                        .HasColumnType("int");

                    b.Property<string>("WebhookURL")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AssignedStaffId");

                    b.HasIndex("AssignedTeamId");

                    b.HasIndex("CompanyMessageTemplateId");

                    b.ToTable("CampaignAutomationActions");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CampaignAutomationUploadedFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("CampaignAutomationActionId")
                        .HasColumnType("bigint");

                    b.Property<string>("CampaignAutomationUploadedFileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CampaignAutomationActionId");

                    b.HasIndex("CampaignAutomationUploadedFileId");

                    b.ToTable("CampaignAutomationUploadedFiles");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CampaignChannelMessage", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyMessageTemplateId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ExtendedMessagePayloadDetail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ExtendedMessageType")
                        .HasColumnType("int");

                    b.Property<string>("FacebookOTNTopicId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("MessageTag")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OfficialTemplateParams")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TargetedChannel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TargetedChannels")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateParams")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WhatsApp360DialogExtendedCampaignMessage")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyMessageTemplateId");

                    b.ToTable("CampaignChannelMessages");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CampaignUploadedFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("CampaignChannelMessageId")
                        .HasColumnType("bigint");

                    b.Property<string>("CampaignUploadedFileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyMessageTemplateId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CampaignChannelMessageId");

                    b.HasIndex("CampaignUploadedFileId")
                        .IsUnique()
                        .HasFilter("[CampaignUploadedFileId] IS NOT NULL");

                    b.HasIndex("CompanyMessageTemplateId");

                    b.ToTable("CampaignUploadedFiles");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CancelSubscriptionRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CanncelPlanId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CanncelUserEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("customerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("others")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("resaon")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("stripe_subscriptionId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CoreCancelSubscriptionRecords");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.ChatHistoryBackupConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Emails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("FailToSendAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsLiveChatSupported")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("ChatHistoryBackupConfig");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.Company", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AffiliateCustomerId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CmsActivationOwnerId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CmsCompanyIndustry")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CmsCompanyOwnerId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CmsCsOwnerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CmsLeadSource")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CmsRemark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CommunicationTools")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyCountry")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanySetting")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("CompanyType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DefaultInboxOrder")
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<long?>("EmailConfigId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsEnabledSFCC")
                        .HasColumnType("bit");

                    b.Property<bool>("IsExperimentalImportEnabled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsExpressImportEnabled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFreeTrial")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPaymentFailed")
                        .HasColumnType("bit");

                    b.Property<bool>("IsQRCodeMappingEnabled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRemovedChannels")
                        .HasColumnType("bit");

                    b.Property<bool>("IsReplyWithStaffName")
                        .HasColumnType("bit");

                    b.Property<bool>("IsShopifyAccount")
                        .HasColumnType("bit");

                    b.Property<bool>("IsStripeIntegrationEnabled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVIP")
                        .HasColumnType("bit");

                    b.Property<int>("MaximumAgents")
                        .HasColumnType("int");

                    b.Property<int?>("MaximumAutomations")
                        .HasColumnType("int");

                    b.Property<int?>("MaximumContacts")
                        .HasColumnType("int");

                    b.Property<int?>("MaximumNumberOfChannel")
                        .HasColumnType("int");

                    b.Property<int>("MaximumShopifyStore")
                        .HasColumnType("int");

                    b.Property<int?>("MaximumWhAutomatedMessages")
                        .HasColumnType("int");

                    b.Property<int>("MaximumWhatsappInstance")
                        .HasColumnType("int");

                    b.Property<int?>("NPSScore")
                        .HasColumnType("int");

                    b.Property<bool>("PushAssignedConversationsEmailNotifications")
                        .HasColumnType("bit");

                    b.Property<bool>("PushAssignedConversationsNotifications")
                        .HasColumnType("bit");

                    b.Property<bool>("PushGroupAssignedConversationsEmailNotifications")
                        .HasColumnType("bit");

                    b.Property<bool>("PushGroupAssignedConversationsNotifications")
                        .HasColumnType("bit");

                    b.Property<bool>("PushUnassignedConversationsEmailNotifications")
                        .HasColumnType("bit");

                    b.Property<bool>("PushUnassignedConversationsNotifications")
                        .HasColumnType("bit");

                    b.Property<string>("ReferralCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("RegistrationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ScheduledStripeSubscriptionMigrationId")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<int?>("ShopifyOrderConversion")
                        .HasColumnType("int");

                    b.Property<string>("SignalRGroupName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SubscriptionCountryTier")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.Property<string>("SubscriptionCurrency")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.Property<string>("SubscriptionMigrationStatus")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<int?>("SubscriptionTrialDays")
                        .HasColumnType("int");

                    b.Property<string>("TimeZoneInfoId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UsageLimitOffsetProfile")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("WeChatConfigId")
                        .HasColumnType("bigint");

                    b.Property<string>("lmref")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CmsActivationOwnerId");

                    b.HasIndex("CmsCompanyOwnerId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("EmailConfigId");

                    b.HasIndex("WeChatConfigId");

                    b.ToTable("CompanyCompanies");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyAPIKey", b =>
                {
                    b.Property<string>("APIKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("CallLimit")
                        .HasColumnType("int");

                    b.Property<int>("Calls")
                        .HasColumnType("int");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("KeyType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Permissions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("UsedInZapier")
                        .HasColumnType("bit");

                    b.HasKey("APIKey");

                    b.HasIndex("CompanyId");

                    b.ToTable("CompanyAPIKeys");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyCustomField", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Category")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("FieldName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("IsEditable")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsVisible")
                        .HasColumnType("bit");

                    b.Property<int?>("Order")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("CompanyCompanyCustomFields");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyCustomFieldFieldLingual", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("CompanyCustomFieldId")
                        .HasColumnType("bigint");

                    b.Property<string>("DisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Language")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyCustomFieldId");

                    b.ToTable("CompanyCustomFieldFieldLinguals");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyCustomUserProfileField", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("FieldName")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("FieldsCategory")
                        .HasColumnType("int");

                    b.Property<bool?>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsDeletable")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsEditable")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsVisible")
                        .HasColumnType("bit");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("CompanyId"), new[] { "FieldName", "IsDefault", "IsEditable", "IsVisible", "Order", "Type" });

                    b.HasIndex("CompanyId", "FieldName");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "FieldName"), false);

                    b.HasIndex("FieldName", "Order", "IsVisible", "IsEditable", "IsDefault", "Type");

                    b.ToTable("CompanyCustomUserProfileFields");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyHashtag", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("HashTagColor")
                        .HasColumnType("int");

                    b.Property<int>("HashTagType")
                        .HasColumnType("int");

                    b.Property<string>("Hashtag")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("Hashtag");

                    b.HasIndex("CompanyId", "Hashtag")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [Hashtag] IS NOT NULL");

                    b.ToTable("CompanyDefinedHashtags");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyIconFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfilePictureId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL");

                    b.HasIndex("ProfilePictureId")
                        .IsUnique()
                        .HasFilter("[ProfilePictureId] IS NOT NULL");

                    b.ToTable("CompanyIconFiles");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyMessageTemplate", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("BroadcastAsNote")
                        .HasColumnType("bit");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Conditions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsBroadcastOn")
                        .HasColumnType("bit");

                    b.Property<string>("JobId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("LastSentById")
                        .HasColumnType("bigint");

                    b.Property<string>("MessageTag")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("SavedById")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("ScheduledAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("SentAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("StatisticsData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StripePaymentRequestOption")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TargetedChannelWithIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TargetedChannels")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateParams")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("LastSentById");

                    b.HasIndex("SavedById");

                    b.HasIndex("UpdatedAt");

                    b.ToTable("CompanyMessageTemplates");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyOperator", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("IdentityId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Locale")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Role")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("IdentityId", "CompanyId")
                        .IsUnique()
                        .HasFilter("[IdentityId] IS NOT NULL AND [CompanyId] IS NOT NULL");

                    b.ToTable("UserRoleOperators");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyQuickReply", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<long?>("QuickReplyFileId")
                        .HasColumnType("bigint");

                    b.Property<long?>("SavedById")
                        .HasColumnType("bigint");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("QuickReplyFileId");

                    b.HasIndex("SavedById");

                    b.HasIndex("CompanyId", "Type")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "Type"), false);

                    b.HasIndex("CompanyId", "Value");

                    b.ToTable("CompanyQuickReplies");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyQuickReplyFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QuickReplyFileId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CompanyQuickReplyFiles");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyQuickReplyLingual", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyQuickReplyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("CompanyQuickReplyId1")
                        .HasColumnType("bigint");

                    b.Property<string>("Language")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Params")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WhatsApp360DialogExtendedMessage")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyQuickReplyId1");

                    b.ToTable("CompanyQuickReplyLinguals");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyRegionalInfo", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryIsoCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsSleekFlow")
                        .HasColumnType("bit");

                    b.Property<string>("RegisteredBusinessNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegisteredCompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("CompanyRegionalInfos");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyTeam", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long?>("AssignedStaffId")
                        .HasColumnType("bigint");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("DefaultChannels")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastUpdated")
                        .HasColumnType("datetime2");

                    b.Property<string>("QRCodeAssignmentStaffId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("QRCodeAssignmentType")
                        .HasColumnType("int");

                    b.Property<string>("QRCodeChannel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QRCodeContent")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("QRCodeIdentity")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TeamName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "QRCodeIdentity")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [QRCodeIdentity] IS NOT NULL");

                    b.ToTable("CompanyStaffTeams");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CustomUserProfileFieldLingual", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyCustomUserProfileFieldId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("DisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Language")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyCustomUserProfileFieldId");

                    b.ToTable("CompanyCustomUserProfileFieldLinguals");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CustomUserProfileFieldOption", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyCustomUserProfileFieldId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("Order")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyCustomUserProfileFieldId");

                    b.ToTable("CompanyCustomUserProfileFieldOptions");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CustomUserProfileFieldOptionLingual", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("CustomUserProfileFieldOptionId")
                        .HasColumnType("bigint");

                    b.Property<string>("DisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Language")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CustomUserProfileFieldOptionId");

                    b.ToTable("CompanyCustomUserProfileFieldOptionLinguals");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.ImportContactHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Alias")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("ContactListType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("FailedCount")
                        .HasColumnType("int");

                    b.Property<int>("ImportIndex")
                        .HasColumnType("int");

                    b.Property<string>("ImportName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ImportedCount")
                        .HasColumnType("int");

                    b.Property<long?>("ImportedFromId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsBookmarked")
                        .HasColumnType("bit");

                    b.Property<bool>("IsImported")
                        .HasColumnType("bit");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("UpdatedCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("ImportedFromId");

                    b.ToTable("CompanyImportContactHistories");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.ImportContactToListRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<int>("ImportIndex")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("CompanyImportContactToListRecords");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.ImportedUserProfile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("ImportContactHistoryId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("ImportContactHistoryId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("ImportContactHistoryId"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ImportContactHistoryId"), new[] { "UserProfileId" });

                    b.HasIndex("UserProfileId");

                    b.HasIndex("ImportContactHistoryId", "UserProfileId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("ImportContactHistoryId", "UserProfileId"), false);

                    b.ToTable("CompanyImportedUserProfiles");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.NotificationRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("Count")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("EventName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Payload")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("StaffId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CreatedAt"), false);

                    b.HasIndex("StaffId");

                    b.HasIndex("CompanyId", "StaffId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "StaffId"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("CompanyId", "StaffId"), new[] { "Count", "CreatedAt", "Payload", "EventName" });

                    b.ToTable("CompanyNotificationRecords");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.ProfilePictureFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfilePictureId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("ProfilePictureId")
                        .IsUnique()
                        .HasFilter("[ProfilePictureId] IS NOT NULL");

                    b.ToTable("UserStaffProfilePictures");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.RegisteredSession", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeactivateAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeviceName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("DeviceType")
                        .HasColumnType("int");

                    b.Property<int>("SessionStatus")
                        .HasColumnType("int");

                    b.Property<long>("StaffId")
                        .HasColumnType("bigint");

                    b.Property<string>("UUID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("StaffId");

                    b.HasIndex("Id", "UUID")
                        .IsUnique()
                        .HasFilter("[UUID] IS NOT NULL");

                    b.ToTable("UserRegisteredSessions");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.RequestChannel", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Channel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("CompanyRequestChannels");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.RolePermission", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("StaffUserRole")
                        .HasColumnType("int");

                    b.Property<string>("StoredPermission")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "StaffUserRole")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL");

                    b.ToTable("CompanyRolePermissions");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.Staff", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("DefaultCurrency")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("IdentityId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("IsInboxDemoCompleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsNewlyRegistered")
                        .HasColumnType("bit");

                    b.Property<bool>("IsShowName")
                        .HasColumnType("bit");

                    b.Property<bool>("IsWhatsappConsultationBooked")
                        .HasColumnType("bit");

                    b.Property<string>("Locale")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Message")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("NotificationSettingId")
                        .HasColumnType("bigint");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("Position")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("ProfilePictureId")
                        .HasColumnType("bigint");

                    b.Property<string>("QRCodeChannel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QRCodeIdentity")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Role")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RoleType")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TimeZoneInfoId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("NotificationSettingId");

                    b.HasIndex("ProfilePictureId");

                    b.HasIndex("CompanyId", "QRCodeIdentity")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [QRCodeIdentity] IS NOT NULL");

                    b.HasIndex("IdentityId", "CompanyId")
                        .IsUnique()
                        .HasFilter("[IdentityId] IS NOT NULL AND [CompanyId] IS NOT NULL");

                    b.ToTable("UserRoleStaffs");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.StaffNotificationSetting", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<bool>("EmailNotificationConversationUpdates")
                        .HasColumnType("bit");

                    b.Property<bool>("EmailNotificationNewMessages")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.ToTable("UserStaffNotificationSettings");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.StorageConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ContainerName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL");

                    b.ToTable("ConfigStorageConfigs");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.TeamAssignmentQueue", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("CompanyTeamId")
                        .HasColumnType("bigint");

                    b.Property<string>("StaffIds")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CompanyTeamAssignmentQueues");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.TeamMember", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("CompanyTeamId")
                        .HasColumnType("bigint");

                    b.Property<long>("StaffId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CompanyTeamId");

                    b.HasIndex("StaffId");

                    b.ToTable("CompanyTeamMembers");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.UserPreference", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Preference")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("StaffId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("StaffId")
                        .IsUnique();

                    b.HasIndex("StaffId", "CompanyId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL");

                    b.ToTable("UserPreferences");
                });

            modelBuilder.Entity("Travis_backend.ContactDomain.Models.UserProfile", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("ActiveStatus")
                        .HasColumnType("int");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ContactOwnerId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(350)
                        .HasColumnType("nvarchar(350)");

                    b.Property<long?>("EmailAddressId")
                        .HasColumnType("bigint");

                    b.Property<long?>("FacebookAccountId")
                        .HasColumnType("bigint");

                    b.Property<string>("FirstName")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("FullName")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("InstagramUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsSandbox")
                        .HasColumnType("bit");

                    b.Property<bool>("IsShopifyProfile")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastContact")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("LastContactFromCustomers")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastName")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("LineUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PictureUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("RegisteredUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("SMSUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ShopifyCustomerId")
                        .HasColumnType("bigint");

                    b.Property<string>("StripeCustomerGBId")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("StripeCustomerId")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("StripeCustomerMYId")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("StripeCustomerSGId")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<long?>("TelegramUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long?>("UserDeviceId")
                        .HasColumnType("bigint");

                    b.Property<long?>("UserProfilePictureFileId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ViberUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("WeChatUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("WebClientId")
                        .HasColumnType("bigint");

                    b.Property<long?>("WhatsApp360DialogUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("WhatsAppAccountId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ActiveStatus");

                    b.HasIndex("CompanyId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("CompanyId"), new[] { "ActiveStatus", "CreatedAt", "EmailAddressId", "FacebookAccountId", "FirstName", "IsSandbox", "LastContact", "LastContactFromCustomers", "LastName", "LineUserId", "PictureUrl", "RegisteredUserId", "SMSUserId", "UpdatedAt", "UserDeviceId", "UserProfilePictureFileId", "WebClientId", "WeChatUserId", "WhatsAppAccountId" });

                    b.HasIndex("ContactOwnerId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("ContactOwnerId"), false);

                    b.HasIndex("CreatedAt");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("CreatedAt"), new[] { "EmailAddressId", "FacebookAccountId", "FirstName", "IsSandbox", "LastContact", "LastContactFromCustomers", "LastName", "LineUserId", "RegisteredUserId", "PictureUrl", "UserProfilePictureFileId", "UserDeviceId", "WebClientId", "WeChatUserId", "WhatsAppAccountId", "SMSUserId" });

                    b.HasIndex("Email");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("Email"), false);

                    b.HasIndex("EmailAddressId");

                    b.HasIndex("FacebookAccountId");

                    b.HasIndex("InstagramUserId");

                    b.HasIndex("LineUserId");

                    b.HasIndex("PhoneNumber");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("PhoneNumber"), false);

                    b.HasIndex("RegisteredUserId");

                    b.HasIndex("SMSUserId");

                    b.HasIndex("TelegramUserId");

                    b.HasIndex("UpdatedAt");

                    b.HasIndex("UserDeviceId");

                    b.HasIndex("UserProfilePictureFileId");

                    b.HasIndex("ViberUserId");

                    b.HasIndex("WeChatUserId");

                    b.HasIndex("WebClientId");

                    b.HasIndex("WhatsApp360DialogUserId");

                    b.HasIndex("WhatsAppAccountId");

                    b.HasIndex("ActiveStatus", "CompanyId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("ActiveStatus", "CompanyId"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ActiveStatus", "CompanyId"), new[] { "CreatedAt", "EmailAddressId", "FacebookAccountId", "FirstName", "LastName", "LineUserId", "PictureUrl", "RegisteredUserId", "SMSUserId", "UpdatedAt", "UserDeviceId", "UserProfilePictureFileId", "WebClientId", "WeChatUserId", "WhatsAppAccountId", "IsSandbox" });

                    b.HasIndex("FirstName", "LastName", "CreatedAt");

                    b.ToTable("UserProfiles");
                });

            modelBuilder.Entity("Travis_backend.ContactDomain.Models.UserProfileCustomField", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyDefinedFieldId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyDefinedFieldId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("CompanyDefinedFieldId"), new[] { "UserProfileId", "Value" });

                    b.HasIndex("UserProfileId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("UserProfileId"), new[] { "CompanyDefinedFieldId", "Value" });

                    b.HasIndex("CompanyDefinedFieldId", "UserProfileId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyDefinedFieldId", "UserProfileId"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("CompanyDefinedFieldId", "UserProfileId"), new[] { "Value" });

                    b.HasIndex("CompanyDefinedFieldId", "Value");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyDefinedFieldId", "Value"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("CompanyDefinedFieldId", "Value"), new[] { "UserProfileId" });

                    b.ToTable("UserProfileCustomFields");
                });

            modelBuilder.Entity("Travis_backend.ContactDomain.Models.UserProfileDeletionBuffer", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DeletedAt")
                        .HasColumnType("datetime2");

                    b.Property<long?>("DeletedByStaffId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("ScheduledHardDeleteAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserProfileId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("DeletedByStaffId");

                    b.HasIndex("UserProfileId")
                        .IsUnique();

                    b.HasIndex("CompanyId", "UserProfileId");

                    b.ToTable("UserProfileDeletionBuffers");
                });

            modelBuilder.Entity("Travis_backend.ContactDomain.Models.UserProfileDuplicatedLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Channel")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("KeepUserProfileId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.ToTable("UserProfileDuplicatedLogs");
                });

            modelBuilder.Entity("Travis_backend.ContactDomain.Models.UserProfileDuplicationConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("DuplicationMode")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique();

                    b.ToTable("UserProfileDuplicationConfigs");
                });

            modelBuilder.Entity("Travis_backend.ContactDomain.Models.UserProfilePictureFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfilePictureId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("ProfilePictureId")
                        .IsUnique()
                        .HasFilter("[ProfilePictureId] IS NOT NULL");

                    b.ToTable("UserProfilePictureFiles");
                });

            modelBuilder.Entity("Travis_backend.ConversationDomain.Models.AdditionalAssignee", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long?>("AssigneeId")
                        .HasColumnType("bigint");

                    b.Property<string>("CompanyId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConversationId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("AssigneeId");

                    b.HasIndex("ConversationId", "AssigneeId")
                        .IsUnique()
                        .HasFilter("[ConversationId] IS NOT NULL AND [AssigneeId] IS NOT NULL");

                    b.ToTable("ConversationAdditionalAssignees");
                });

            modelBuilder.Entity("Travis_backend.ConversationDomain.Models.Conversation", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("ActiveStatus")
                        .HasColumnType("int");

                    b.Property<long?>("AssignedTeamId")
                        .HasColumnType("bigint");

                    b.Property<long?>("AssigneeId")
                        .HasColumnType("bigint");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long?>("EmailAddressId")
                        .HasColumnType("bigint");

                    b.Property<long?>("InstagramUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsBookmarked")
                        .HasColumnType("bit");

                    b.Property<bool>("IsNewCreatedConversation")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSandbox")
                        .HasColumnType("bit");

                    b.Property<bool>("IsUnreplied")
                        .HasColumnType("bit");

                    b.Property<string>("LastChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("LastMessageChannel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("LastMessageChannelId")
                        .HasColumnType("bigint");

                    b.Property<long?>("LastMessageId")
                        .HasColumnType("bigint");

                    b.Property<long?>("LineUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("MessageGroupName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Metadata")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ModifiedAt")
                        .HasColumnType("datetime2");

                    b.Property<long?>("NaiveUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("SMSUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("SnoozeUntil")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("StatusChangedFromClosedToOpenAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("StatusChangedToClosedAt")
                        .HasColumnType("datetime2");

                    b.Property<long?>("TelegramUserId")
                        .HasColumnType("bigint");

                    b.Property<int>("UnreadMessageCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedTime")
                        .HasColumnType("datetime2");

                    b.Property<long?>("UserDeviceId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("ViberUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("WeChatUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("WebClientId")
                        .HasColumnType("bigint");

                    b.Property<long?>("WhatsApp360DialogUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("WhatsappUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("facebookUserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ActiveStatus");

                    b.HasIndex("AssignedTeamId");

                    b.HasIndex("AssigneeId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("AssigneeId"), new[] { "UnreadMessageCount" });

                    b.HasIndex("CompanyId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("CompanyId"), new[] { "AssignedTeamId", "ActiveStatus", "AssigneeId", "EmailAddressId", "facebookUserId", "IsBookmarked", "IsSandbox", "LineUserId", "MessageGroupName", "ModifiedAt", "NaiveUserId", "SMSUserId", "SnoozeUntil", "Status", "UnreadMessageCount", "UpdatedTime", "UserDeviceId", "UserProfileId", "WebClientId", "WeChatUserId", "WhatsappUserId" });

                    b.HasIndex("EmailAddressId");

                    b.HasIndex("InstagramUserId");

                    b.HasIndex("IsBookmarked");

                    b.HasIndex("LineUserId");

                    b.HasIndex("ModifiedAt");

                    b.HasIndex("NaiveUserId");

                    b.HasIndex("SMSUserId");

                    b.HasIndex("Status");

                    b.HasIndex("TelegramUserId");

                    b.HasIndex("UpdatedTime");

                    b.HasIndex("UserDeviceId");

                    b.HasIndex("UserProfileId")
                        .IsUnique()
                        .HasFilter("[UserProfileId] IS NOT NULL");

                    b.HasIndex("ViberUserId");

                    b.HasIndex("WeChatUserId");

                    b.HasIndex("WebClientId");

                    b.HasIndex("WhatsApp360DialogUserId");

                    b.HasIndex("WhatsappUserId");

                    b.HasIndex("facebookUserId");

                    b.HasIndex("ActiveStatus", "AssigneeId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("ActiveStatus", "AssigneeId"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ActiveStatus", "AssigneeId"), new[] { "UnreadMessageCount" });

                    b.HasIndex("ActiveStatus", "CompanyId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("ActiveStatus", "CompanyId"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ActiveStatus", "CompanyId"), new[] { "AssigneeId" });

                    b.HasIndex("ActiveStatus", "UpdatedTime");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ActiveStatus", "UpdatedTime"), new[] { "WebClientId" });

                    b.HasIndex("CompanyId", "ActiveStatus");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "ActiveStatus"), false);

                    b.HasIndex("CompanyId", "UserProfileId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [UserProfileId] IS NOT NULL");

                    b.HasIndex("UserProfileId", "ActiveStatus");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("UserProfileId", "ActiveStatus"), false);

                    b.HasIndex("ActiveStatus", "CompanyId", "AssigneeId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("ActiveStatus", "CompanyId", "AssigneeId"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ActiveStatus", "CompanyId", "AssigneeId"), new[] { "AssignedTeamId", "EmailAddressId", "facebookUserId", "IsBookmarked", "LineUserId", "MessageGroupName", "ModifiedAt", "NaiveUserId", "SnoozeUntil", "Status", "UnreadMessageCount", "UpdatedTime", "UserDeviceId", "WebClientId", "WeChatUserId", "WhatsappUserId", "SMSUserId", "IsSandbox", "UserProfileId" });

                    b.HasIndex("CompanyId", "ActiveStatus", "ModifiedAt");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "ActiveStatus", "ModifiedAt"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("CompanyId", "ActiveStatus", "ModifiedAt"), new[] { "AssignedTeamId", "AssigneeId", "EmailAddressId", "facebookUserId", "IsBookmarked", "IsNewCreatedConversation", "IsSandbox", "LineUserId", "MessageGroupName", "NaiveUserId", "SMSUserId", "SnoozeUntil", "Status", "UnreadMessageCount", "UpdatedTime", "UserDeviceId", "UserProfileId", "WebClientId", "WeChatUserId", "WhatsappUserId" });

                    b.HasIndex("Id", "UserProfileId", "CompanyId")
                        .IsUnique()
                        .HasFilter("[UserProfileId] IS NOT NULL AND [CompanyId] IS NOT NULL");

                    b.HasIndex("CompanyId", "ActiveStatus", "AssigneeId", "AssignedTeamId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "ActiveStatus", "AssigneeId", "AssignedTeamId"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("CompanyId", "ActiveStatus", "AssigneeId", "AssignedTeamId"), new[] { "EmailAddressId", "facebookUserId", "IsBookmarked", "IsNewCreatedConversation", "IsSandbox", "LineUserId", "MessageGroupName", "ModifiedAt", "NaiveUserId", "SMSUserId", "SnoozeUntil", "Status", "UnreadMessageCount", "UpdatedTime", "UserDeviceId", "UserProfileId", "WebClientId", "WeChatUserId", "WhatsappUserId" });

                    b.ToTable("Conversations");
                });

            modelBuilder.Entity("Travis_backend.ConversationDomain.Models.ConversationBookmark", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ConversationId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long>("StaffId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ConversationId");

                    b.HasIndex("StaffId");

                    b.ToTable("ConversationBookmarks");
                });

            modelBuilder.Entity("Travis_backend.ConversationDomain.Models.ConversationHashtag", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConversationId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("HashtagId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("ConversationId");

                    b.HasIndex("HashtagId");

                    b.ToTable("ConversationHashtags");
                });

            modelBuilder.Entity("Travis_backend.ConversationDomain.Models.ConversationWhatsappSenderHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ConversationId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("InstanceId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("InstanceSender")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("ConversationId", "InstanceId", "InstanceSender");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("ConversationId", "InstanceId", "InstanceSender"), false);

                    b.ToTable("ConversationWhatsappHistories");
                });

            modelBuilder.Entity("Travis_backend.ConversationDomain.Models.Mention", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ConversationId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("CreatedByStaffId")
                        .HasColumnType("bigint");

                    b.Property<long>("MentionedStaffId")
                        .HasColumnType("bigint");

                    b.Property<string>("MentionedStaffIdentityId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<long>("MessageId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("ConversationId");

                    b.HasIndex("CompanyId", "ConversationId", "CreatedAt");

                    b.HasIndex("CompanyId", "CreatedAt", "MentionedStaffId");

                    b.HasIndex("CompanyId", "ConversationId", "CreatedAt", "MentionedStaffId");

                    b.ToTable("Mentions");
                });

            modelBuilder.Entity("Travis_backend.ConversationServices.Models.FacebookConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BusinessIntegrationSystemUserAccessToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("ConnectedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ExpireDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FacebookBusinessId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsShowInWidget")
                        .HasColumnType("bit");

                    b.Property<bool>("IsV2LeadAdsConnection")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PageAccessToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PageId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("PageName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("SubscribedFields")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("PageId")
                        .IsUnique()
                        .HasFilter("[PageId] IS NOT NULL");

                    b.HasIndex("SubscribedFields");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("SubscribedFields"), false);

                    b.ToTable("ConfigFacebookConfigs");
                });

            modelBuilder.Entity("Travis_backend.ConversationServices.Models.FacebookLeadAdsNotificationConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("FacebookConfigId")
                        .HasColumnType("bigint");

                    b.Property<string>("NotificationEmails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NotificationPhoneNumbers")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "FacebookConfigId")
                        .IsUnique();

                    b.ToTable("FacebookLeadAdsNotificationConfigs");
                });

            modelBuilder.Entity("Travis_backend.ConversationServices.Models.InstagramConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BusinessIntegrationSystemUserAccessToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("ConnectedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ExpireDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FacebookBusinessId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InstagramPageId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("IsShowInWidget")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PageAccessToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PageId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("PageName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("SubscribedFields")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("PageId", "InstagramPageId", "CompanyId")
                        .IsUnique()
                        .HasFilter("[PageId] IS NOT NULL AND [InstagramPageId] IS NOT NULL AND [CompanyId] IS NOT NULL");

                    b.ToTable("ConfigInstagramConfigs");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.CompanyPaymentFailedLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("CompanyPaymentFailedLogs");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.CompanySandbox", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("JoinedCount")
                        .HasColumnType("int");

                    b.Property<string>("MappingIdentity")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL");

                    b.HasIndex("MappingIdentity")
                        .IsUnique()
                        .HasFilter("[MappingIdentity] IS NOT NULL");

                    b.ToTable("CompanySandboxes");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.CoreCustomField", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Category")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FieldName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("IsEditable")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsVisible")
                        .HasColumnType("bit");

                    b.Property<int?>("Order")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CoreCustomFields");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.CoreEmailConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Domain")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SendGridKey")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SenderName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CoreEmailConfigs");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.CoreSandboxTwilioConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TwilioAccountSid")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TwilioSecret")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("PhoneNumber");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("PhoneNumber"), false);

                    b.HasIndex("TwilioAccountSid", "TwilioSecret");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("TwilioAccountSid", "TwilioSecret"), false);

                    b.ToTable("CoreSandboxTwilioConfigs");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.CoreTwilioConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AccountSID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AccountSecret")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CoreTwilioConfigs");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.CoreWhatsApp360DialogPartnerAuthCredential", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("EncryptedAccessToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EncryptedPassword")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EncryptedUsername")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ExpiredAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsInUse")
                        .HasColumnType("bit");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("PartnerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Remark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TopUpMode")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("CoreWhatsApp360DialogPartnerAuthCredentials");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.Country", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("DisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EnglishName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LocalCurrencies")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("SubscriptionCountryTier")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.HasKey("Id");

                    b.ToTable("CoreCountries");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.EmailNotificationTemplate", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("EmailSubject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailTemplate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NotificationName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NotificationType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("CoreEmailNotificationTemplates");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.TwilioTopUpLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long?>("AmountTotal")
                        .HasColumnType("bigint");

                    b.Property<string>("CompanyId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InternalUserId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("InvoiceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsInternalTestingUse")
                        .HasColumnType("bit");

                    b.Property<int>("Method")
                        .HasColumnType("int");

                    b.Property<decimal>("TopUpAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TwilioAccountSid")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<long>("TwilioUsageRecordId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("TwilioTopUpLogs");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.TwilioTopUpPlan", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Price")
                        .HasColumnType("float");

                    b.Property<double>("Value")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.ToTable("CoreTwilioTopupPlans");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.TwilioTopupRecord", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AccountSID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("RedeemedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TopupPlanId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("TopupPlanId", "AccountSID");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("TopupPlanId", "AccountSID"), false);

                    b.ToTable("CoreTwilioTopupRecords");
                });

            modelBuilder.Entity("Travis_backend.DemoDomain.Models.DemoConversation", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Assignee")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DemoUserProfile")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StaffId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UnreadMessageCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("DemoConversations");
                });

            modelBuilder.Entity("Travis_backend.DemoDomain.Models.DemoConversationMessage", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Channel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("DemoConversationId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsFromUser")
                        .HasColumnType("bit");

                    b.Property<string>("MessageContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("DemoConversationId");

                    b.ToTable("DemoConversationMessages");
                });

            modelBuilder.Entity("Travis_backend.FacebookInstagramIntegrationDomain.Models.FacebookLeadAdsForm", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CrmHubSchemaId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("DisplaySetting")
                        .HasColumnType("int");

                    b.Property<long>("FacebookConfigId")
                        .HasColumnType("bigint");

                    b.Property<string>("FacebookFormId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FacebookFormName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SetupStatus")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("FacebookLeadAdsForms");
                });

            modelBuilder.Entity("Travis_backend.FacebookInstagramIntegrationDomain.Models.FacebookLeadAdsFormFieldMapping", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FacebookLeadAdsFormFieldId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("FacebookLeadAdsFormId")
                        .HasColumnType("bigint");

                    b.Property<string>("SleekflowFieldId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SleekflowFieldType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("FacebookLeadAdsFormId");

                    b.ToTable("FacebookLeadAdsFormFieldMappings");
                });

            modelBuilder.Entity("Travis_backend.FileDomain.Models.UploadedFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AssignmentUploadedFileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CampaignUploadedFileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Channel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("ConversationMessageId")
                        .HasColumnType("bigint");

                    b.Property<string>("FileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MessageTopic")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Metadata")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("SenderDeviceId")
                        .HasColumnType("bigint");

                    b.Property<string>("SenderDeviceUUID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SenderId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AssignmentUploadedFileId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("AssignmentUploadedFileId"), false);

                    b.HasIndex("CampaignUploadedFileId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CampaignUploadedFileId"), false);

                    b.HasIndex("ConversationMessageId");

                    b.HasIndex("FileId")
                        .IsUnique()
                        .HasFilter("[FileId] IS NOT NULL");

                    b.HasIndex("SenderDeviceId");

                    b.HasIndex("SenderId");

                    b.ToTable("ConversationMessageUploadedFiles");
                });

            modelBuilder.Entity("Travis_backend.FileDomain.Models.WhatsappRegistrationsFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("FileId")
                        .IsUnique()
                        .HasFilter("[FileId] IS NOT NULL");

                    b.ToTable("CoreWhatsappRegistrationFiles");
                });

            modelBuilder.Entity("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("EncryptedApiKey")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EncryptedToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsEnableSync")
                        .HasColumnType("bit");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("HubSpotIntegrationConfigs");
                });

            modelBuilder.Entity("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationContactOwnerMap", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HubSpotContactOwnerId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<long>("HubSpotIntegrationConfigId")
                        .HasColumnType("bigint");

                    b.Property<long>("HubSpotUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("IdentityId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<long?>("StaffId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("HubSpotIntegrationConfigId");

                    b.ToTable("HubSpotIntegrationContactOwnerMaps");
                });

            modelBuilder.Entity("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationCustomFieldMap", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyCustomUserProfileFieldId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("FieldType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HubSpotCustomFieldName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("HubSpotIntegrationConfigId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsEnableSyncFromHubSpot")
                        .HasColumnType("bit");

                    b.Property<bool>("IsEnableSyncToHubSpot")
                        .HasColumnType("bit")
                        .HasColumnName("IsEnableSyncToSleekFlow");

                    b.Property<bool>("IsOption")
                        .HasColumnType("bit");

                    b.Property<string>("Options")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("HubSpotIntegrationConfigId");

                    b.ToTable("HubSpotIntegrationCustomFieldMaps");
                });

            modelBuilder.Entity("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationSyncTask", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("After")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Completed")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("ErrorCount")
                        .HasColumnType("int");

                    b.Property<long>("HubSpotIntegrationConfigId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsPause")
                        .HasColumnType("bit");

                    b.Property<int?>("Limit")
                        .HasColumnType("int");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("HubSpotIntegrationConfigId");

                    b.ToTable("HubSpotIntegrationSyncTasks");
                });

            modelBuilder.Entity("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationSyncTaskErrorRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExceptionMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HubSpotContactId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("HubSpotIntegrationSyncTaskId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("HubSpotIntegrationSyncTaskId");

                    b.ToTable("HubSpotIntegrationSyncTaskErrorRecords");
                });

            modelBuilder.Entity("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationWebhookErrorRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExceptionMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("HubSpotIntegrationConfigId")
                        .HasColumnType("bigint");

                    b.Property<string>("WebhookRequestBody")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("HubSpotIntegrationWebhookErrorRecords");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyAbandonedCart", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AbandonedURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CartToken")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Currency")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<long?>("CustomerId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("LineItems")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("RecoveredDate")
                        .HasColumnType("datetime2");

                    b.Property<long?>("StaffId")
                        .HasColumnType("bigint");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("SubtotalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long?>("TeamId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalDiscounts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalLineItemsPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalTax")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CartToken", "CompanyId")
                        .IsUnique()
                        .HasFilter("[CartToken] IS NOT NULL AND [CompanyId] IS NOT NULL");

                    b.HasIndex("UserProfileId", "Status");

                    b.ToTable("UserProfileShopifyAbandonedCarts");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyAttribution", b =>
                {
                    b.Property<DateTime?>("From")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TeamId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("To")
                        .HasColumnType("datetime2");

                    b.Property<long>("TotalCount")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.ToTable("ShopifyAttribution", t =>
                        {
                            t.ExcludeFromMigrations();
                        });
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyCollectionProductRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("ProductRecordId")
                        .HasColumnType("bigint");

                    b.Property<long>("ShopifyCollectionRecordId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ProductRecordId");

                    b.HasIndex("ShopifyCollectionRecordId");

                    b.ToTable("ShopifyCollectionProductRecords");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyCollectionRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long?>("CollectionId")
                        .HasColumnType("bigint");

                    b.Property<string>("CollectionPayload")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTimeOffset?>("PublishedAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("PublishedScope")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("ShopifyId")
                        .HasColumnType("bigint");

                    b.Property<string>("SortOrder")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("Title", "CompanyId", "CollectionId", "ShopifyId", "SortOrder");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("Title", "CompanyId", "CollectionId", "ShopifyId", "SortOrder"), false);

                    b.ToTable("ShopifyCollectionRecords");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AccessToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("BillRecordId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ChargeId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("ChargeUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<bool>("IsEnabledDiscounts")
                        .HasColumnType("bit");

                    b.Property<bool>("IsShopifyBillingOwner")
                        .HasColumnType("bit");

                    b.Property<bool>("IsShopifySubscriptionPaid")
                        .HasColumnType("bit");

                    b.Property<bool>("IsShowInInbox")
                        .HasColumnType("bit");

                    b.Property<string>("JobId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastSyncCustomerAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("LastSyncOrderAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PaymentLinkSetting")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("SupportedCountries")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("SyncAllStatus")
                        .HasColumnType("bit");

                    b.Property<bool>("SyncCustomerTags")
                        .HasColumnType("bit");

                    b.Property<bool>("SyncOnlyIfPhoneNumberExist")
                        .HasColumnType("bit");

                    b.Property<bool>("SyncOrderTags")
                        .HasColumnType("bit");

                    b.Property<bool>("SyncProductTags")
                        .HasColumnType("bit");

                    b.Property<string>("SyncShopifyOrderJobId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("UsersMyShopifyUrl")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("BillRecordId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("UsersMyShopifyUrl")
                        .IsUnique()
                        .HasFilter("[UsersMyShopifyUrl] IS NOT NULL");

                    b.ToTable("ConfigShopifyConfigs");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyOrderRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CartToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("ConversionStatus")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<long?>("CustomerId")
                        .HasColumnType("bigint");

                    b.Property<string>("Fulfillment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Fulfillments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LineItems")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("OrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("OrderName")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Payment")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("PlatformCountry")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("StaffId")
                        .HasColumnType("bigint");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("SubtotalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Tags")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("TeamId")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalDiscounts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalLineItemsPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalTax")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("URL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserProfileId");

                    b.HasIndex("CompanyId", "Tags");

                    b.HasIndex("CompanyId", "UserProfileId", "OrderName")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [UserProfileId] IS NOT NULL AND [OrderName] IS NOT NULL");

                    b.HasIndex("CompanyId", "Payment", "ConversionStatus", "PlatformCountry")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "Payment", "ConversionStatus", "PlatformCountry"), false);

                    b.ToTable("UserProfileShopifyOrders");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyProductMessageTemplate", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("MessageBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Params")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("ShopifyConfigId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ShopifyConfigId");

                    b.ToTable("ShopifyProductMessageTemplates");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyProductRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long?>("ProductId")
                        .HasColumnType("bigint");

                    b.Property<string>("ProductPayload")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("ShopifyId")
                        .HasColumnType("bigint");

                    b.Property<string>("Sku")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "ShopifyId", "ProductId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [ShopifyId] IS NOT NULL AND [ProductId] IS NOT NULL");

                    b.HasIndex("Title", "CompanyId", "ShopifyId", "ProductId", "Sku");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("Title", "CompanyId", "ShopifyId", "ProductId", "Sku"), false);

                    b.ToTable("ShopifyProductRecords");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifySalesPerformance", b =>
                {
                    b.Property<int>("LinkSharedClicks")
                        .HasColumnType("int");

                    b.Property<int>("LinkSharedCount")
                        .HasColumnType("int");

                    b.Property<decimal>("PaymentLinkConvertedAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("PaymentLinkSharedClicks")
                        .HasColumnType("int");

                    b.Property<int>("PaymentLinkSharedCount")
                        .HasColumnType("int");

                    b.Property<int>("PaymentLinkSharedPaid")
                        .HasColumnType("int");

                    b.Property<long>("StaffId")
                        .HasColumnType("bigint");

                    b.ToTable("ShopifySalesPerformance", t =>
                        {
                            t.ExcludeFromMigrations();
                        });
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsCompanyAdditionalInfo", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AllTimeRevenueAnalyticData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChurnReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyTier")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ContractEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("HubSpotCompanyIndustry")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LegalTerms")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ManualContractEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL");

                    b.ToTable("CmsCompanyAdditionalInfos");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsCompanyDataSnapshot", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("RecordCount")
                        .HasColumnType("int");

                    b.Property<string>("SnapshotDate")
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("SnapshotDate")
                        .IsUnique()
                        .HasFilter("[SnapshotDate] IS NOT NULL");

                    b.ToTable("CmsCompanyDataSnapshots");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsContactOwnerAssignLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AssignedByUserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("ContactOwnerType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("FromContactOwnerId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ToContactOwnerId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("AssignedByUserId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("FromContactOwnerId");

                    b.HasIndex("ToContactOwnerId");

                    b.ToTable("CmsContactOwnerAssignLogs");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsHubSpotCompanyMap", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("HubSpotCompanyObjectId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("LastSyncAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique();

                    b.HasIndex("CompanyId", "HubSpotCompanyObjectId")
                        .IsUnique();

                    b.ToTable("CmsHubSpotCompanyMaps");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsHubSpotCompanySyncHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("ErrorCount")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StartAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("SucceedCount")
                        .HasColumnType("int");

                    b.Property<int>("TotalCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("CmsHubSpotCompanySyncHistories");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsHubSpotContactOwnerMap", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ContactOwnerId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("HubSpotContactOwnerId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<long>("HubSpotUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("HubspotTeams")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ContactOwnerId");

                    b.ToTable("CmsHubSpotContactOwnerMaps");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsHubSpotUserContactMap", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("HubSpotContactObjectId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("LastSyncAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("Email", "HubSpotContactObjectId")
                        .IsUnique();

                    b.ToTable("CmsHubSpotUserContactMaps");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsLoginAsHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedByUserId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsAdminDive")
                        .HasColumnType("bit");

                    b.Property<long>("LoginToStaffId")
                        .HasColumnType("bigint");

                    b.Property<string>("LoginToStaffIdentityId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CmsLoginAsHistories");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsPartnerStackCustomerMap", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("IndividualCommissionConfig")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PartnerStackCustomerInformation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PartnerStackCustomerKey")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PartnerStackPartnerInformation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique();

                    b.HasIndex("CompanyId", "PartnerStackCustomerKey")
                        .IsUnique();

                    b.ToTable("CmsPartnerStackCustomerMaps");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsSalesPaymentRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("BillRecordId")
                        .HasColumnType("bigint");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreateUserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Discount")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("InvoiceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastModifiedByUserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("LastModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("OneTimeSetupFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("PaidAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("int");

                    b.Property<string>("PaymentTerm")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PaymentTermInt")
                        .HasColumnType("int");

                    b.Property<int?>("PeriodInMonths")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("SubscriptionFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("WhatsappCreditAmount")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("BillRecordId");

                    b.HasIndex("CreateUserId");

                    b.HasIndex("LastModifiedByUserId");

                    b.ToTable("CmsSalesPaymentRecords");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsSalesPaymentRecordFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("CmsSalesPaymentRecordId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CmsSalesPaymentRecordId");

                    b.ToTable("CmsSalesPaymentRecordFiles");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsSleekPayReportCsv", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Csv")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("EndActivityDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StartActivityDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("CmsSleekPayReportCsvs");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsSleekPayReportData", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<decimal>("AverageOrderValue")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("CmsSleekPayReportCsvId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<decimal>("Cost")
                        .HasColumnType("decimal(18,5)");

                    b.Property<decimal>("CostRate")
                        .HasColumnType("decimal(18,5)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("EndActivityDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("FailedTransactionsNos")
                        .HasColumnType("bigint");

                    b.Property<decimal>("GrossEarning")
                        .HasColumnType("decimal(18,5)");

                    b.Property<decimal>("NetEarning")
                        .HasColumnType("decimal(18,5)");

                    b.Property<decimal>("OfferedRate")
                        .HasColumnType("decimal(18,5)");

                    b.Property<long>("OrderNos")
                        .HasColumnType("bigint");

                    b.Property<decimal>("PerAuthFees")
                        .HasColumnType("decimal(18,5)");

                    b.Property<DateTime>("StartActivityDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("StripeAccountId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StripeAccountName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("VariableCostRate")
                        .HasColumnType("decimal(18,5)");

                    b.Property<decimal>("Volume")
                        .HasColumnType("decimal(18,5)");

                    b.HasKey("Id");

                    b.HasIndex("CmsSleekPayReportCsvId");

                    b.ToTable("CmsSleekPayReportDatas");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsSleekPayTransactionRateLog", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("NewApplicationFeeRate")
                        .HasColumnType("decimal(18,5)");

                    b.Property<decimal>("OldApplicationFeeRate")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("StaffId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StripeAccountId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CmsSleekPayTransactionRateLogs");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsWhatsappApplication", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BroughtPhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyWebsite")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("CompletedFacebookVerifications")
                        .HasColumnType("bit");

                    b.Property<string>("ContactOwnerId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ContactPersonEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContactPersonName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContactPersonPhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Country")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedByUserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("FacebookBusinessManagerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Files")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("FormSubmissionDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("GreenTickDate")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("HasToppedUp")
                        .HasColumnType("bit");

                    b.Property<string>("HubSpotTicketId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPinned")
                        .HasColumnType("bit");

                    b.Property<string>("PreferStartWith")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferenceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegulatoryBundleRemark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("RegulatoryBundleSubmissionDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Remark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("SenderProfileCreationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SenderProfileRemark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Step")
                        .HasColumnType("int");

                    b.Property<DateTime?>("TemplateEmailSendDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("TwilioAccountSid")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedByUserId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("ContactOwnerId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("UpdatedByUserId");

                    b.ToTable("CmsWhatsappApplications");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.ImportWhatsappHistoryRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ChannelId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ChannelName")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Filename")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("ImportWhatsappHistoryRecords");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.ConversationMessage", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AnalyticTags")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("BroadcastHistoryId")
                        .HasColumnType("bigint");

                    b.Property<string>("Channel")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("ChannelStatusMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConversationId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("DeliveryType")
                        .HasColumnType("int");

                    b.Property<string>("DynamicChannelSender")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("EmailCC")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("EmailFromId")
                        .HasColumnType("bigint");

                    b.Property<string>("EmailTo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("FrondendTimestamp")
                        .HasColumnType("bigint");

                    b.Property<long?>("InstagramReceiverId")
                        .HasColumnType("bigint");

                    b.Property<long?>("InstagramSenderId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsFromImport")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSandbox")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSentFromSleekflow")
                        .HasColumnType("bit");

                    b.Property<string>("JobId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("LineReceiverId")
                        .HasColumnType("bigint");

                    b.Property<long?>("LineSenderId")
                        .HasColumnType("bigint");

                    b.Property<long?>("LocalTimestamp")
                        .HasColumnType("bigint");

                    b.Property<long?>("MessageAssigneeId")
                        .HasColumnType("bigint");

                    b.Property<string>("MessageChecksum")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("MessageContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MessageTag")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MessageType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MessageUniqueID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Metadata")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QuotedMsgBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QuotedMsgId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("ReceiverDeviceId")
                        .HasColumnType("bigint");

                    b.Property<string>("ReceiverDeviceUUID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReceiverId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("SMSReceiverId")
                        .HasColumnType("bigint");

                    b.Property<long?>("SMSSenderId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("ScheduleSentAt")
                        .HasColumnType("datetime2");

                    b.Property<long?>("SenderDeviceId")
                        .HasColumnType("bigint");

                    b.Property<string>("SenderDeviceUUID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SenderId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("SleekPayRecordId")
                        .HasColumnType("bigint");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StoryURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Subject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("TelegramReceiverId")
                        .HasColumnType("bigint");

                    b.Property<long?>("TelegramSenderId")
                        .HasColumnType("bigint");

                    b.Property<long>("Timestamp")
                        .HasColumnType("bigint");

                    b.Property<string>("TranslationResults")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long?>("ViberReceiverId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ViberSenderId")
                        .HasColumnType("bigint");

                    b.Property<string>("Visibility")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("WeChatReceiverId")
                        .HasColumnType("bigint");

                    b.Property<long?>("WeChatSenderId")
                        .HasColumnType("bigint");

                    b.Property<long?>("WebClientReceiverId")
                        .HasColumnType("bigint");

                    b.Property<long?>("WebClientSenderId")
                        .HasColumnType("bigint");

                    b.Property<long?>("Whatsapp360DialogReceiverId")
                        .HasColumnType("bigint");

                    b.Property<long?>("Whatsapp360DialogSenderId")
                        .HasColumnType("bigint");

                    b.Property<long?>("facebookReceiverId")
                        .HasColumnType("bigint");

                    b.Property<long?>("facebookSenderId")
                        .HasColumnType("bigint");

                    b.Property<long?>("whatsappReceiverId")
                        .HasColumnType("bigint");

                    b.Property<long?>("whatsappSenderId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("BroadcastHistoryId");

                    b.HasIndex("ConversationId");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ConversationId"), new[] { "BroadcastHistoryId", "Channel", "CompanyId", "CreatedAt", "DeliveryType", "EmailCC", "EmailFromId", "EmailTo", "facebookReceiverId", "facebookSenderId", "IsSentFromSleekflow", "LineReceiverId", "LineSenderId", "MessageAssigneeId", "MessageChecksum", "MessageContent", "MessageType", "MessageUniqueID", "ReceiverDeviceUUID", "ReceiverId", "SenderDeviceUUID", "SenderId", "Status", "Subject", "TranslationResults", "UpdatedAt", "Visibility", "WebClientReceiverId", "WebClientSenderId", "WeChatReceiverId", "WeChatSenderId", "whatsappReceiverId", "whatsappSenderId", "SMSReceiverId", "SMSSenderId", "IsSandbox" });

                    b.HasIndex("EmailFromId");

                    b.HasIndex("InstagramReceiverId");

                    b.HasIndex("InstagramSenderId");

                    b.HasIndex("LineReceiverId");

                    b.HasIndex("LineSenderId");

                    b.HasIndex("MessageAssigneeId");

                    b.HasIndex("MessageUniqueID")
                        .IsUnique()
                        .HasFilter("[MessageUniqueID] IS NOT NULL");

                    b.HasIndex("ReceiverDeviceId");

                    b.HasIndex("ReceiverId");

                    b.HasIndex("SMSReceiverId");

                    b.HasIndex("SMSSenderId");

                    b.HasIndex("SenderDeviceId");

                    b.HasIndex("SenderId");

                    b.HasIndex("Status");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("Status"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("Status"), new[] { "BroadcastHistoryId" });

                    b.HasIndex("TelegramReceiverId");

                    b.HasIndex("TelegramSenderId");

                    b.HasIndex("Timestamp");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("Timestamp"), false);

                    b.HasIndex("ViberReceiverId");

                    b.HasIndex("ViberSenderId");

                    b.HasIndex("WeChatReceiverId");

                    b.HasIndex("WeChatSenderId");

                    b.HasIndex("WebClientReceiverId");

                    b.HasIndex("WebClientSenderId");

                    b.HasIndex("Whatsapp360DialogReceiverId");

                    b.HasIndex("Whatsapp360DialogSenderId");

                    b.HasIndex("facebookReceiverId");

                    b.HasIndex("facebookSenderId");

                    b.HasIndex("whatsappReceiverId");

                    b.HasIndex("whatsappSenderId");

                    b.HasIndex("Channel", "Status");

                    b.HasIndex("CompanyId", "MessageChecksum")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [MessageChecksum] IS NOT NULL");

                    b.HasIndex("CompanyId", "SleekPayRecordId")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "SleekPayRecordId"), false);

                    b.HasIndex("ConversationId", "IsSentFromSleekflow");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("ConversationId", "IsSentFromSleekflow"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ConversationId", "IsSentFromSleekflow"), new[] { "BroadcastHistoryId", "Channel", "ChannelStatusMessage", "CompanyId", "CreatedAt", "DeliveryType", "EmailCC", "EmailFromId", "EmailTo", "facebookReceiverId", "facebookSenderId", "IsSandbox", "LineReceiverId", "LineSenderId", "MessageAssigneeId", "MessageChecksum", "MessageContent", "MessageType", "MessageUniqueID", "QuotedMsgBody", "QuotedMsgId", "ReceiverDeviceId", "ReceiverDeviceUUID", "ReceiverId", "SenderDeviceId", "SenderDeviceUUID", "SenderId", "SMSReceiverId", "SMSSenderId", "Status", "Subject", "TranslationResults", "UpdatedAt", "Visibility", "WebClientReceiverId", "WebClientSenderId", "WeChatReceiverId", "WeChatSenderId", "whatsappReceiverId", "whatsappSenderId" });

                    b.HasIndex("ConversationId", "Timestamp");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("ConversationId", "Timestamp"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ConversationId", "Timestamp"), new[] { "BroadcastHistoryId", "Channel", "ChannelStatusMessage", "CompanyId", "CreatedAt", "DeliveryType", "EmailCC", "EmailFromId", "EmailTo", "facebookReceiverId", "facebookSenderId", "IsSandbox", "LineReceiverId", "LineSenderId", "MessageAssigneeId", "MessageChecksum", "MessageContent", "MessageType", "MessageUniqueID", "QuotedMsgBody", "QuotedMsgId", "ReceiverDeviceId", "ReceiverDeviceUUID", "ReceiverId", "SenderDeviceId", "SenderDeviceUUID", "SenderId", "SMSReceiverId", "SMSSenderId", "Status", "Subject", "TranslationResults", "UpdatedAt", "Visibility", "WebClientReceiverId", "WebClientSenderId", "WeChatReceiverId", "WeChatSenderId", "whatsappReceiverId", "whatsappSenderId" });

                    b.HasIndex("ConversationId", "UpdatedAt");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("ConversationId", "UpdatedAt"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ConversationId", "UpdatedAt"), new[] { "BroadcastHistoryId", "Channel", "CompanyId", "CreatedAt", "DeliveryType", "EmailCC", "EmailFromId", "EmailTo", "facebookReceiverId", "facebookSenderId", "IsSentFromSleekflow", "LineReceiverId", "LineSenderId", "MessageAssigneeId", "MessageChecksum", "MessageContent", "MessageType", "MessageUniqueID", "ReceiverDeviceUUID", "ReceiverId", "SenderDeviceUUID", "SenderId", "Status", "Subject", "TranslationResults", "Visibility", "WebClientReceiverId", "WebClientSenderId", "WeChatReceiverId", "WeChatSenderId", "whatsappReceiverId", "whatsappSenderId", "SMSReceiverId", "SMSSenderId", "IsSandbox" });

                    b.HasIndex("CompanyId", "CreatedAt", "DeliveryType");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "CreatedAt", "DeliveryType"), false);

                    b.HasIndex("ConversationId", "DeliveryType", "CreatedAt");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("ConversationId", "DeliveryType", "CreatedAt"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ConversationId", "DeliveryType", "CreatedAt"), new[] { "BroadcastHistoryId", "Channel", "CompanyId", "EmailCC", "EmailFromId", "EmailTo", "facebookReceiverId", "facebookSenderId", "IsSentFromSleekflow", "LineReceiverId", "LineSenderId", "MessageAssigneeId", "MessageChecksum", "MessageContent", "MessageType", "MessageUniqueID", "ReceiverDeviceUUID", "ReceiverId", "SenderDeviceUUID", "SenderId", "Status", "Subject", "TranslationResults", "Visibility", "WebClientReceiverId", "WebClientSenderId", "WeChatReceiverId", "WeChatSenderId", "whatsappReceiverId", "whatsappSenderId", "SMSReceiverId", "SMSSenderId", "IsSandbox", "ChannelStatusMessage" });

                    b.HasIndex("DeliveryType", "CompanyId", "CreatedAt");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("DeliveryType", "CompanyId", "CreatedAt"), new[] { "ConversationId" });

                    b.HasIndex("Channel", "CompanyId", "Status", "CreatedAt");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("Channel", "CompanyId", "Status", "CreatedAt"), false);

                    b.HasIndex("Id", "ConversationId", "CompanyId", "CreatedAt");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("Id", "ConversationId", "CompanyId", "CreatedAt"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("Id", "ConversationId", "CompanyId", "CreatedAt"), new[] { "BroadcastHistoryId", "Channel", "EmailCC", "EmailFromId", "EmailTo", "facebookReceiverId", "facebookSenderId", "IsSentFromSleekflow", "LineReceiverId", "LineSenderId", "MessageAssigneeId", "MessageChecksum", "MessageContent", "MessageType", "MessageUniqueID", "ReceiverDeviceUUID", "ReceiverId", "SenderDeviceUUID", "SenderId", "Status", "Subject", "TranslationResults", "Visibility", "WebClientReceiverId", "WebClientSenderId", "WeChatReceiverId", "WeChatSenderId", "whatsappReceiverId", "whatsappSenderId", "SMSReceiverId", "SMSSenderId", "IsSandbox", "ChannelStatusMessage" });

                    b.ToTable("ConversationMessages");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.ConversationUnreadRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConversationId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeliveredAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("MessageId")
                        .HasColumnType("bigint");

                    b.Property<int>("NotificationDeliveryStatus")
                        .HasColumnType("int");

                    b.Property<int>("NotificationDeliveryType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("SentAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("StaffId")
                        .HasColumnType("bigint");

                    b.Property<string>("StaffIdentityId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "StaffId", "NotificationDeliveryStatus")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "StaffId", "NotificationDeliveryStatus"), false);

                    b.HasIndex("CompanyId", "ConversationId", "StaffId", "Id")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "ConversationId", "StaffId", "Id"), false);

                    b.HasIndex("CompanyId", "ConversationId", "StaffId", "NotificationDeliveryStatus")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "ConversationId", "StaffId", "NotificationDeliveryStatus"), false);

                    b.HasIndex("CompanyId", "StaffId", "MessageId", "NotificationDeliveryStatus")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "StaffId", "MessageId", "NotificationDeliveryStatus"), false);

                    b.HasIndex("CompanyId", "StaffId", "NotificationDeliveryType", "NotificationDeliveryStatus")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "StaffId", "NotificationDeliveryType", "NotificationDeliveryStatus"), false);

                    b.ToTable("ConversationUnreadRecords");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.EmailSender", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CC")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("To")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("locale")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("Email", "CompanyId")
                        .IsUnique()
                        .HasFilter("[Email] IS NOT NULL AND [CompanyId] IS NOT NULL");

                    b.ToTable("SenderEmailSenders");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.ExtendedMessagePayload", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Channel")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<long>("ConversationMessageId")
                        .HasColumnType("bigint");

                    b.Property<string>("ExtendedMessagePayloadDetail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ExtendedMessageType")
                        .HasColumnType("int");

                    b.Property<string>("FacebookOTNTopicId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("ConversationMessageId")
                        .IsUnique();

                    b.ToTable("ExtendedMessagePayloads");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.ExtendedMessagePayloadFile", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("BlobContainer")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("BlobFilePath")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("Channel")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("CompanyId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("ExtendedMessageType")
                        .HasColumnType("int");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("Filename")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("MIMEType")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("MediaType")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Url")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.HasKey("Id");

                    b.ToTable("ExtendedMessagePayloadFiles");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.FacebookOTNTopic", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("HashTagIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PageId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Topic")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("FacebookOtnTopics");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.FacebookSender", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("FacebookId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("first_name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("gender")
                        .HasColumnType("int");

                    b.Property<string>("last_name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("locale")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("pageId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("profile_pic")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("FacebookId", "CompanyId", "pageId")
                        .IsUnique()
                        .HasFilter("[FacebookId] IS NOT NULL AND [CompanyId] IS NOT NULL AND [pageId] IS NOT NULL");

                    b.ToTable("SenderFacebookSenders");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.FacebookUserOneTimeToken", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FacebookOTNTopicId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long>("FacebookUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsTokenRedeemed")
                        .HasColumnType("bit");

                    b.Property<string>("OneTimeToken")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("FacebookOTNTopicId");

                    b.HasIndex("FacebookUserId");

                    b.ToTable("FacebookUserOneTimeTokens");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.FetchedThread", b =>
                {
                    b.Property<string>("ThreadId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("ThreadId");

                    b.ToTable("FacebookFetchedIds");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.IPAddressInfo", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Address")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Area")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BusinessWebsite")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Country")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IPAddressType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ISP")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Locale")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrganisationName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Timezone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("WebClientSenderId")
                        .HasColumnType("bigint");

                    b.Property<string>("WebPath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("region")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("WebClientSenderId");

                    b.ToTable("SenderWebClientIPAddressInfos");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.InstagramSender", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("InstagramId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("InstagramPageId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PageId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProfilePic")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Username")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "InstagramId", "InstagramPageId", "PageId", "UserId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [InstagramId] IS NOT NULL AND [InstagramPageId] IS NOT NULL AND [PageId] IS NOT NULL AND [UserId] IS NOT NULL");

                    b.ToTable("SenderInstagramSenders");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.LineSender", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("displayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("pictureUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("statusMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("userId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("userId", "CompanyId")
                        .IsUnique()
                        .HasFilter("[userId] IS NOT NULL AND [CompanyId] IS NOT NULL");

                    b.ToTable("SenderLineSender");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.SMSSender", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("InstanceId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("SMSId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("locale")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("phone_number")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("profile_pic")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("SMSId", "CompanyId", "InstanceId");

                    b.ToTable("SenderSMSSenders");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.SandboxSender", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long>("CompanySandboxId")
                        .HasColumnType("bigint");

                    b.Property<string>("locale")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("phone_number")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("profile_pic")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CompanySandboxId");

                    b.ToTable("SenderSandboxSenders");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.TelegramSender", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PicturePhotoId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PicturePhotoUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("TelegramBotId")
                        .HasColumnType("bigint");

                    b.Property<long>("TelegramChatId")
                        .HasColumnType("bigint");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SenderTelegramSenders");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.ViberSender", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<int>("ApiVersion")
                        .HasColumnType("int");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Country")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsSubscribed")
                        .HasColumnType("bit");

                    b.Property<string>("Language")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MobileCountryCode")
                        .HasColumnType("int");

                    b.Property<int>("MobileNetworkCode")
                        .HasColumnType("int");

                    b.Property<string>("PictureUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ViberBotId")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ViberUserId")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("ViberVersion")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SenderViberSenders");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.WeChatSender", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("city")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("country")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("groupid")
                        .HasColumnType("int");

                    b.Property<string>("headimgurl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("language")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("nickname")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("openid")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("province")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("qr_scene")
                        .HasColumnType("int");

                    b.Property<string>("qr_scene_str")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("remark")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("sex")
                        .HasColumnType("int");

                    b.Property<int>("subscribe")
                        .HasColumnType("int");

                    b.Property<string>("subscribe_scene")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("subscribe_time")
                        .HasColumnType("int");

                    b.Property<string>("tagid_list")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("openid", "CompanyId")
                        .IsUnique()
                        .HasFilter("[openid] IS NOT NULL AND [CompanyId] IS NOT NULL");

                    b.ToTable("SenderWeChatSenders");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.WebClientSender", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BrowserLocale")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConversationId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Device")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("DeviceModel")
                        .HasColumnType("int");

                    b.Property<string>("IPAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Metadata")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("OnlineStatus")
                        .HasColumnType("int");

                    b.Property<string>("SignalRConnectionId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserProfileId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("WebClientUUID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("locale")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("SignalRConnectionId");

                    b.HasIndex("CompanyId", "UserProfileId");

                    b.HasIndex("ConversationId", "CompanyId");

                    b.HasIndex("WebClientUUID", "CompanyId")
                        .IsUnique()
                        .HasFilter("[WebClientUUID] IS NOT NULL AND [CompanyId] IS NOT NULL");

                    b.ToTable("SenderWebClientSenders");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.WhatsApp360DialogSender", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long?>("ChannelId")
                        .HasColumnType("bigint");

                    b.Property<string>("ChannelWhatsAppPhoneNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ContactStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("LastContactStatusCheckDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("WhatsAppId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "WhatsAppId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "WhatsAppId"), false);

                    b.HasIndex("Id", "CompanyId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("Id", "CompanyId"), false);

                    b.ToTable("SenderWhatsApp360DialogSenders");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.WhatsAppSender", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("InstanceId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("InstaneSender")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("isFetchProfileInfo")
                        .HasColumnType("bit");

                    b.Property<bool>("isVerified")
                        .HasColumnType("bit");

                    b.Property<string>("locale")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("phone_number")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("profile_pic")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("whatsAppId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("InstanceId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("InstanceId"), false);
                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("InstanceId"), new[] { "CompanyId", "isFetchProfileInfo", "locale", "name", "phone_number", "profile_pic", "whatsAppId" });

                    b.HasIndex("InstaneSender");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("InstaneSender"), false);

                    b.HasIndex("whatsAppId", "CompanyId", "InstanceId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("whatsAppId", "CompanyId", "InstanceId"), false);

                    b.HasIndex("whatsAppId", "CompanyId", "InstanceId", "InstaneSender");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("whatsAppId", "CompanyId", "InstanceId", "InstaneSender"), false);

                    b.ToTable("SenderWhatsappSenders");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.Whatsapp360DialogExtendedMessagePayload", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("ConversationMessageId")
                        .HasColumnType("bigint");

                    b.Property<string>("ReplyPayload")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Whatsapp360DialogInteractiveObject")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Whatsapp360DialogTemplateMessage")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ConversationMessageId")
                        .IsUnique();

                    b.ToTable("Whatsapp360DialogExtendedMessagePayload");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.WhatsappCloudApiSender", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ConversationId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("WhatsappChannelPhoneNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("WhatsappId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("WhatsappUserDisplayName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("ConversationId")
                        .IsUnique()
                        .HasFilter("[ConversationId] IS NOT NULL");

                    b.HasIndex("UserProfileId")
                        .IsUnique()
                        .HasFilter("[UserProfileId] IS NOT NULL");

                    b.HasIndex("CompanyId", "WhatsappId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [WhatsappId] IS NOT NULL")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "WhatsappId"), false);

                    b.ToTable("WhatsappCloudApiSenders");
                });

            modelBuilder.Entity("Travis_backend.Models.BackgroundTask.BackgroundTask", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDismissed")
                        .HasColumnType("bit");

                    b.Property<int>("Progress")
                        .HasColumnType("int");

                    b.Property<string>("ResultPayload")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("StaffId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("TargetPayload")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TaskType")
                        .HasColumnType("int");

                    b.Property<int>("Total")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "StaffId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "StaffId"), false);

                    b.HasIndex("CompanyId", "StaffId", "IsDismissed")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "StaffId", "IsDismissed"), false);

                    b.ToTable("BackgroundTasks");
                });

            modelBuilder.Entity("Travis_backend.Models.ChatChannelConfig.WhatsAppCloudApiTemplateBookmark", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("MessagingHubWabaId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TemplateId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TemplateLanguage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "MessagingHubWabaId", "TemplateId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [MessagingHubWabaId] IS NOT NULL AND [TemplateId] IS NOT NULL")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "MessagingHubWabaId", "TemplateId"), false);

                    b.ToTable("WhatsAppCloudApiTemplateBookmarks");
                });

            modelBuilder.Entity("Travis_backend.Models.ChatChannelConfig.WhatsappCloudApiConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ChannelIdentityId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("ChannelName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("FacebookBusinessId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FacebookPhoneNumberId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FacebookWabaBusinessName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FacebookWabaId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FacebookWabaName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsOptInEnable")
                        .HasColumnType("bit");

                    b.Property<string>("MessagingHubWabaId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("MessagingHubWabaPhoneNumberId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("OptInConfig")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("ProductCatalogSetting")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateNamespace")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Waba")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WabaPhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WhatsappDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WhatsappPhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "MessagingHubWabaId", "MessagingHubWabaPhoneNumberId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [MessagingHubWabaId] IS NOT NULL AND [MessagingHubWabaPhoneNumberId] IS NOT NULL")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "MessagingHubWabaId", "MessagingHubWabaPhoneNumberId"), false);

                    b.ToTable("ConfigWhatsappCloudApiConfigs");
                });

            modelBuilder.Entity("Travis_backend.Models.ChatChannelConfig.WhatsappCloudApiWabaConnection", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("FacebookBusinessId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FacebookWabaBusinessName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FacebookWabaId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FacebookWabaName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MessagingHubWabaId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("WhatsappCloudApiWabaConnections");
                });

            modelBuilder.Entity("Travis_backend.PiiMasking.Models.PiiMaskingConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsPlatformApiMasked")
                        .HasColumnType("bit");

                    b.Property<string>("MaskingCustomObjectSchemaIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MaskingLocations")
                        .HasColumnType("int");

                    b.Property<int>("MaskingRoles")
                        .HasColumnType("int");

                    b.Property<string>("RegexPattern")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegexPatterns")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("PiiMaskingConfigs");
                });

            modelBuilder.Entity("Travis_backend.ResellerDomain.Models.ResellerActivityLog", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Action")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Category")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedByUserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ResellerCompanyProfileId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("ResellerCompanyProfileId");

                    b.ToTable("ResellerActivityLogs");
                });

            modelBuilder.Entity("Travis_backend.ResellerDomain.Models.ResellerClientCompanyProfile", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AssigneeIdentityId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClientCompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ContactOwnerIdentityId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ResellerCompanyProfileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ClientCompanyId")
                        .IsUnique()
                        .HasFilter("[ClientCompanyId] IS NOT NULL");

                    b.HasIndex("ResellerCompanyProfileId");

                    b.ToTable("ResellerClientCompanyProfiles");
                });

            modelBuilder.Entity("Travis_backend.ResellerDomain.Models.ResellerCompanyProfile", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<decimal>("BalanceMinimumLimit")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("CountryTier")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Debited")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PartnerStackPartnerKey")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("ResellerDiscount")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("ResellerSubscriptionPlanConfig")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("TopUp")
                        .HasColumnType("decimal(18,5)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL");

                    b.ToTable("ResellerCompanyProfiles");
                });

            modelBuilder.Entity("Travis_backend.ResellerDomain.Models.ResellerProfileLogo", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ResellerCompanyProfileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ResellerCompanyProfileId")
                        .IsUnique()
                        .HasFilter("[ResellerCompanyProfileId] IS NOT NULL");

                    b.ToTable("ResellerProfileLogos");
                });

            modelBuilder.Entity("Travis_backend.ResellerDomain.Models.ResellerStaff", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("IdentityId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Locale")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Position")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("ProfilePictureId")
                        .HasColumnType("bigint");

                    b.Property<string>("ResellerCompanyProfileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TeamNames")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TimeZoneInfoId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ProfilePictureId");

                    b.HasIndex("ResellerCompanyProfileId");

                    b.ToTable("ResellerStaffs");
                });

            modelBuilder.Entity("Travis_backend.ResellerDomain.Models.ResellerTransactionLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,5)");

                    b.Property<long?>("BillRecordId")
                        .HasColumnType("bigint");

                    b.Property<string>("ClientCompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Detail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InvoiceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ResellerCompanyProfileId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("TopUpMethod")
                        .HasColumnType("int");

                    b.Property<int?>("TopupStatus")
                        .HasColumnType("int");

                    b.Property<string>("TransactionAction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TransactionCategory")
                        .HasColumnType("int");

                    b.Property<int>("TransactionMode")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserIdentityId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ResellerCompanyProfileId");

                    b.ToTable("ResellerTransactionLogs");
                });

            modelBuilder.Entity("Travis_backend.ShareInvitationDomain.Models.ShareLinkGenerationRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("PlatformCountry")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("ShareLinkType")
                        .HasColumnType("int");

                    b.Property<long?>("StaffId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("TrackingId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("TrackingUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("TrackingId")
                        .IsUnique()
                        .HasFilter("[TrackingId] IS NOT NULL")
                        .HasAnnotation("SqlServer:Online", true);

                    b.HasIndex("CompanyId", "PlatformCountry")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "PlatformCountry"), false);

                    b.ToTable("CoreShareLinkGenerationRecords");
                });

            modelBuilder.Entity("Travis_backend.ShareInvitationDomain.Models.ShareLinkTrackRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Country")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("IpInfo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PlatformCountry")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("ShareLinkType")
                        .HasColumnType("int");

                    b.Property<long?>("StaffId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("TrackedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("TrackingId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("TrackingUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("StaffId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("StaffId"), false);

                    b.HasIndex("UserProfileId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("UserProfileId"), false);

                    b.HasIndex("CompanyId", "PlatformCountry")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "PlatformCountry"), false);

                    b.ToTable("CoreShareLinkTrackingRecords");
                });

            modelBuilder.Entity("Travis_backend.ShareInvitationDomain.Models.ShareableInvitation", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ExpirationDate")
                        .HasColumnType("datetime2");

                    b.Property<long?>("GeneratedById")
                        .HasColumnType("bigint");

                    b.Property<string>("InvitationId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Quota")
                        .HasColumnType("int");

                    b.Property<int>("Redeemed")
                        .HasColumnType("int");

                    b.Property<int>("Role")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TeamIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("GeneratedById");

                    b.ToTable("CompanyShareableInvitations");
                });

            modelBuilder.Entity("Travis_backend.ShareInvitationDomain.Models.ShareableInvitationRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("InvitedAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("InvitedStaffId")
                        .HasColumnType("bigint");

                    b.Property<long>("ShareableInvitationId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("InvitedStaffId");

                    b.HasIndex("ShareableInvitationId");

                    b.ToTable("CompanyShareableInvitationRecords");
                });

            modelBuilder.Entity("Travis_backend.ShoplineIntegrationDomain.Models.ShoplineConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AccessToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("JobId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastUpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("ConfigShoplineConfigs");
                });

            modelBuilder.Entity("Travis_backend.SleekflowCrmHubDomain.Models.CrmHubEntity", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("EntityId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserProfileId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserProfileId");

                    b.HasIndex("EntityId", "UserProfileId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("EntityId", "UserProfileId"), false);

                    b.ToTable("CrmHubEntities");
                });

            modelBuilder.Entity("Travis_backend.StripeIntegrationDomain.Models.StripeCompanyLogo", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("StripeCompanyLogos");
                });

            modelBuilder.Entity("Travis_backend.StripeIntegrationDomain.Models.StripePaymentConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("AccountId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AccountInfo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("ApplicationFeeRate")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Country")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DefaultCurrency")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<bool>("IsShippingEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("PaymentLinkExpirationOption")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ShippingAllowedCountries")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ShippingOptions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<long?>("StripeCompanyLogoId")
                        .HasColumnType("bigint");

                    b.Property<string>("SupportedCurrencies")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("StripeCompanyLogoId");

                    b.ToTable("ConfigStripePaymentConfigs");
                });

            modelBuilder.Entity("Travis_backend.StripeIntegrationDomain.Models.StripePaymentMessageTemplate", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("MessageBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MessageType")
                        .HasColumnType("int");

                    b.Property<string>("Params")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.ToTable("StripePaymentMessageTemplates");
                });

            modelBuilder.Entity("Travis_backend.StripeIntegrationDomain.Models.StripePaymentRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<decimal>("AmountDue")
                        .HasColumnType("decimal(18,5)");

                    b.Property<decimal>("AmountReceived")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("Billing")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CanceledAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CancellationReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("CustomerEmail")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CustomerId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("ExpiredAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LineItems")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("PaidAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("PayAmount")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("PaymentIntentPayload")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PaymentTrackingUrl")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("PaymentUrl")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("PlatformCountry")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ReceiptUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("ReceivedApplicationFeeAmount")
                        .HasColumnType("decimal(18,5)");

                    b.Property<string>("RefundId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RefundReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("RefundedAmount")
                        .HasColumnType("decimal(18,5)");

                    b.Property<decimal>("RequestedApplicationFeeAmount")
                        .HasColumnType("decimal(18,5)");

                    b.Property<int>("SharedType")
                        .HasColumnType("int");

                    b.Property<string>("Shipping")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long?>("ShopifyDraftOrderId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ShopifyId")
                        .HasColumnType("bigint");

                    b.Property<string>("ShopifyInvoiceUrl")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("ShopifyOrderId")
                        .HasColumnType("bigint");

                    b.Property<string>("ShopifyOrderStatusUrl")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("StaffId")
                        .HasColumnType("bigint");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StripePaymentIntentId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<long?>("TeamId")
                        .HasColumnType("bigint");

                    b.Property<string>("TrackingId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserProfileId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "StripePaymentIntentId");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "StripePaymentIntentId"), false);

                    b.HasIndex("Status", "ExpiredAt");

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("Status", "ExpiredAt"), false);

                    b.HasIndex("CompanyId", "Status", "PlatformCountry")
                        .HasAnnotation("SqlServer:Online", true);

                    SqlServerIndexBuilderExtensions.IsClustered(b.HasIndex("CompanyId", "Status", "PlatformCountry"), false);

                    b.ToTable("StripePaymentRecords");
                });

            modelBuilder.Entity("Travis_backend.StripeIntegrationDomain.Models.StripePaymentReportExportRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("PlatformCountry")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ReportDataEndAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ReportDataStartAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ReportEmailSentAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ReportFailedEmailSentAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("ReportReceiverEmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StripeReportRunId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StripeReportRunRequestId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("StripePaymentReportExportRecords");
                });

            modelBuilder.Entity("Travis_backend.SubscriptionPlanDomain.Models.CustomSubscriptionPlanTranslationMap", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedByUserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("DestinationSubscriptionPlanId")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("SourceSubscriptionPlanId")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedByUserId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("UpdatedByUserId");

                    b.ToTable("CustomSubscriptionPlanTranslationMaps");
                });

            modelBuilder.Entity("Travis_backend.SubscriptionPlanDomain.Models.Promotion", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ExtraAgents")
                        .HasColumnType("int");

                    b.Property<int>("ExtraAutomatedMessages")
                        .HasColumnType("int");

                    b.Property<int>("ExtraContacts")
                        .HasColumnType("int");

                    b.Property<string>("PromotionCode")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("PromotionCode")
                        .IsUnique()
                        .HasFilter("[PromotionCode] IS NOT NULL");

                    b.ToTable("CorePromotions");
                });

            modelBuilder.Entity("Travis_backend.SubscriptionPlanDomain.Models.RedeemPromotionRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("PromotionId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("RedeemedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ReferrerRedeemedAt")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ReferrerStatus")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("PromotionId");

                    b.ToTable("CoreRedeemPromotionRecords");
                });

            modelBuilder.Entity("Travis_backend.SubscriptionPlanDomain.Models.ResellerStripeTopUpOption", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double>("Price")
                        .HasColumnType("float");

                    b.Property<double>("Value")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.ToTable("CoreResellerStripeTopUpOptions");
                });

            modelBuilder.Entity("Travis_backend.SubscriptionPlanDomain.Models.SubscriptionPlan", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<double>("Amount")
                        .HasColumnType("float");

                    b.Property<string>("AvailableFunctions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryTier")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("Currency")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExtraChatAgentPlan")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ExtraChatAgentPrice")
                        .HasColumnType("int");

                    b.Property<int>("IncludedAgents")
                        .HasColumnType("int");

                    b.Property<int>("MaximumAPICall")
                        .HasColumnType("int");

                    b.Property<int?>("MaximumAgentsLimit")
                        .HasColumnType("int");

                    b.Property<int>("MaximumAutomation")
                        .HasColumnType("int");

                    b.Property<int>("MaximumCampaignSent")
                        .HasColumnType("int");

                    b.Property<bool>("MaximumChannel")
                        .HasColumnType("bit");

                    b.Property<int>("MaximumContact")
                        .HasColumnType("int");

                    b.Property<int>("MaximumMessageSent")
                        .HasColumnType("int");

                    b.Property<int>("MaximumNumberOfChannel")
                        .HasColumnType("int");

                    b.Property<string>("PricingMethod")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StripePlanId")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("SubscriptionName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SubscriptionTier")
                        .HasColumnType("int");

                    b.Property<string>("TieredPrices")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Version")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("CoreSubscriptionPlans");
                });

            modelBuilder.Entity("Travis_backend.SupportTicketDomain.Models.SupportTicket", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime?>("ClosedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Details")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HubSpotTicketId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IssueLevel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IssueType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("StaffId")
                        .HasColumnType("bigint");

                    b.Property<string>("Subject")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SupportTickets");
                });

            modelBuilder.Entity("Travis_backend.SupportTicketDomain.Models.SupportTicketFile", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BlobContainer")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("Filename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MIMEType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("SupportTicketId")
                        .HasColumnType("bigint");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SupportTicketId");

                    b.ToTable("SupportTicketFiles");
                });

            modelBuilder.Entity("Travis_backend.TenantHubDomain.Models.SubscriptionTierConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("DefaultFeatureFlags")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SubscriptionTier")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("SubscriptionTierConfigs");
                });

            modelBuilder.Entity("Travis_backend.ZapierIntegrationDomain.Models.ZapierPollingRecord", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CompanyId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("IsWorked")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastTriggeredDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ZaiperTrigger")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId", "ZaiperTrigger")
                        .IsUnique()
                        .HasFilter("[CompanyId] IS NOT NULL AND [ZaiperTrigger] IS NOT NULL");

                    b.ToTable("CoreZapierPollingRecord");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Travis_backend.AccountAuthenticationDomain.Models.Admin", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "Identity")
                        .WithMany()
                        .HasForeignKey("IdentityId");

                    b.Navigation("Company");

                    b.Navigation("Identity");
                });

            modelBuilder.Entity("Travis_backend.AccountAuthenticationDomain.Models.Guest", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("Guests")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "Identity")
                        .WithMany()
                        .HasForeignKey("IdentityId");

                    b.Navigation("Identity");
                });

            modelBuilder.Entity("Travis_backend.AnalyticsDomain.Models.Segment", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("AnalyticSegments")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "SavedBy")
                        .WithMany()
                        .HasForeignKey("SavedById");

                    b.Navigation("SavedBy");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AssignmentQueue", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithOne("ConversationAssignmentQueue")
                        .HasForeignKey("Travis_backend.AutomationDomain.Models.AssignmentQueue", "CompanyId");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AssignmentRule", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "AssignedStaff")
                        .WithMany()
                        .HasForeignKey("AssignedStaffId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyTeam", "AssignedTeam")
                        .WithMany()
                        .HasForeignKey("AssignedTeamId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.ImportContactHistory", "AssociatedList")
                        .WithMany()
                        .HasForeignKey("AssociatedListId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("AssignmentRules")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "SavedBy")
                        .WithMany()
                        .HasForeignKey("SavedById");

                    b.Navigation("AssignedStaff");

                    b.Navigation("AssignedTeam");

                    b.Navigation("AssociatedList");

                    b.Navigation("SavedBy");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AssignmentUploadedFile", b =>
                {
                    b.HasOne("Travis_backend.AutomationDomain.Models.AutomationAction", null)
                        .WithMany("UploadedFiles")
                        .HasForeignKey("AutomationActionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Travis_backend.AutomationDomain.Models.AutomationActionRecord", null)
                        .WithMany("UploadedFiles")
                        .HasForeignKey("AutomationActionRecordId");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AutomationAction", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "AssignedStaff")
                        .WithMany()
                        .HasForeignKey("AssignedStaffId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyTeam", "AssignedTeam")
                        .WithMany()
                        .HasForeignKey("AssignedTeamId");

                    b.HasOne("Travis_backend.AutomationDomain.Models.AssignmentRule", null)
                        .WithMany("AutomationActions")
                        .HasForeignKey("AssignmentRuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("AutomationActions")
                        .HasForeignKey("CompanyId");

                    b.Navigation("AssignedStaff");

                    b.Navigation("AssignedTeam");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AutomationActionRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "AssignedStaff")
                        .WithMany()
                        .HasForeignKey("AssignedStaffId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyTeam", "AssignedTeam")
                        .WithMany()
                        .HasForeignKey("AssignedTeamId");

                    b.HasOne("Travis_backend.AutomationDomain.Models.AutomationHistory", null)
                        .WithMany("AutomationActionRecords")
                        .HasForeignKey("AutomationHistoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssignedStaff");

                    b.Navigation("AssignedTeam");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AutomationHistory", b =>
                {
                    b.HasOne("Travis_backend.AutomationDomain.Models.AssignmentRule", null)
                        .WithMany("AutomationHistories")
                        .HasForeignKey("AssignmentRuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.FbIgAutoReply", b =>
                {
                    b.HasOne("Travis_backend.AutomationDomain.Models.AutomationAction", "AutomationAction")
                        .WithOne("FbIgAutoReply")
                        .HasForeignKey("Travis_backend.AutomationDomain.Models.FbIgAutoReply", "AutomationActionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AutomationAction");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.FbIgAutoReplyFile", b =>
                {
                    b.HasOne("Travis_backend.AutomationDomain.Models.FbIgAutoReply", "FbIgAutoReply")
                        .WithMany("FbIgAutoReplyFiles")
                        .HasForeignKey("FbIgAutoReplyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FbIgAutoReply");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.FbIgAutoReplyHistoryRecord", b =>
                {
                    b.HasOne("Travis_backend.AutomationDomain.Models.FbIgAutoReply", "FbIgAutoReply")
                        .WithMany("FbIgAutoReplyHistoryRecords")
                        .HasForeignKey("FbIgAutoReplyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FbIgAutoReply");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.FbIgIcebreaker", b =>
                {
                    b.HasOne("Travis_backend.AutomationDomain.Models.AssignmentRule", "AssignmentRule")
                        .WithOne("FbIgIcebreaker")
                        .HasForeignKey("Travis_backend.AutomationDomain.Models.FbIgIcebreaker", "AssignmentRuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssignmentRule");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.IcebreakerHistoryRecord", b =>
                {
                    b.HasOne("Travis_backend.AutomationDomain.Models.IcebreakerReplyRule", "IcebreakerReplyRule")
                        .WithMany("IcebreakerHistoryRecords")
                        .HasForeignKey("IcebreakerReplyRuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("IcebreakerReplyRule");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.IcebreakerReplyRule", b =>
                {
                    b.HasOne("Travis_backend.AutomationDomain.Models.FbIgIcebreaker", "FbIgIcebreaker")
                        .WithMany("IcebreakerReplyRules")
                        .HasForeignKey("FbIgIcebreakerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FbIgIcebreaker");
                });

            modelBuilder.Entity("Travis_backend.BlastDomain.Models.BlastMessageConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithOne("BlastMessageConfig")
                        .HasForeignKey("Travis_backend.BlastDomain.Models.BlastMessageConfig", "CompanyId");
                });

            modelBuilder.Entity("Travis_backend.BlastDomain.Models.BlastMessageTemplate", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "LastSentBy")
                        .WithMany()
                        .HasForeignKey("LastSentById");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "SavedBy")
                        .WithMany()
                        .HasForeignKey("SavedById");

                    b.Navigation("LastSentBy");

                    b.Navigation("SavedBy");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.CompanyWhatsapp360DialogTopUpConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithOne("Whatsapp360DialogTopUpConfig")
                        .HasForeignKey("Travis_backend.ChannelDomain.Models.CompanyWhatsapp360DialogTopUpConfig", "CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.DialogflowServiceAccountConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.LineConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("LineConfigs")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.SMSConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("SMSConfigs")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.TelegramConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("TelegramConfigs")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.TwilioUsageRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("TwilioUsageRecords")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.ViberConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("ViberConfigs")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WeChatConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.ProfilePictureFile", "QRCode")
                        .WithMany()
                        .HasForeignKey("QRCodeId");

                    b.Navigation("QRCode");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsApp360DialogConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany("WhatsApp360DialogConfigs")
                        .HasForeignKey("CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsApp360DialogMediaFile", b =>
                {
                    b.HasOne("Travis_backend.ChannelDomain.Models.WhatsApp360DialogConfig", "WhatsApp360DialogConfig")
                        .WithMany("WhatsApp360DialogMediaFiles")
                        .HasForeignKey("WhatsApp360DialogConfigId");

                    b.Navigation("WhatsApp360DialogConfig");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsApp360DialogTemplateBookmark", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "CreatedByStaff")
                        .WithMany()
                        .HasForeignKey("CreatedByStaffId");

                    b.HasOne("Travis_backend.ChannelDomain.Models.WhatsApp360DialogConfig", "WhatsApp360DialogConfig")
                        .WithMany("TemplateBookmarks")
                        .HasForeignKey("WhatsApp360DialogConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedByStaff");

                    b.Navigation("WhatsApp360DialogConfig");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsApp360DialogUsageRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany("WhatsApp360DialogUsageRecords")
                        .HasForeignKey("CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsApp360DialogUsageTransactionLog", b =>
                {
                    b.HasOne("Travis_backend.ChannelDomain.Models.WhatsApp360DialogUsageRecord", "WhatsApp360DialogUsageRecord")
                        .WithMany("TransactionLogs")
                        .HasForeignKey("WhatsApp360DialogUsageRecordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WhatsApp360DialogUsageRecord");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsAppConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("WhatsAppConfigs")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.CommonDomain.Models.UserDevice", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("UserDevices")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "DeviceUser")
                        .WithMany()
                        .HasForeignKey("DeviceUserId");

                    b.Navigation("DeviceUser");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.BillRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("BillRecords")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "PurchaseStaff")
                        .WithMany()
                        .HasForeignKey("PurchaseStaffId");

                    b.HasOne("Travis_backend.SubscriptionPlanDomain.Models.SubscriptionPlan", "SubscriptionPlan")
                        .WithMany()
                        .HasForeignKey("SubscriptionPlanId");

                    b.Navigation("PurchaseStaff");

                    b.Navigation("SubscriptionPlan");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.BroadcastHistory", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyMessageTemplate", "BroadcastCampaign")
                        .WithMany("broadcastHistories")
                        .HasForeignKey("BroadcastCampaignId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "BroadcastSentBy")
                        .WithMany()
                        .HasForeignKey("BroadcastSentById");

                    b.HasOne("Travis_backend.ConversationDomain.Models.Conversation", "Conversation")
                        .WithMany()
                        .HasForeignKey("ConversationId");

                    b.Navigation("BroadcastCampaign");

                    b.Navigation("BroadcastSentBy");

                    b.Navigation("Conversation");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CampaignAutomationAction", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "AssignedStaff")
                        .WithMany()
                        .HasForeignKey("AssignedStaffId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyTeam", "AssignedTeam")
                        .WithMany()
                        .HasForeignKey("AssignedTeamId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyMessageTemplate", null)
                        .WithMany("CampaignAutomationActions")
                        .HasForeignKey("CompanyMessageTemplateId");

                    b.Navigation("AssignedStaff");

                    b.Navigation("AssignedTeam");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CampaignAutomationUploadedFile", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.CampaignAutomationAction", null)
                        .WithMany("UploadedFiles")
                        .HasForeignKey("CampaignAutomationActionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CampaignChannelMessage", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyMessageTemplate", null)
                        .WithMany("CampaignChannelMessages")
                        .HasForeignKey("CompanyMessageTemplateId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CampaignUploadedFile", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.CampaignChannelMessage", null)
                        .WithMany("UploadedFiles")
                        .HasForeignKey("CampaignChannelMessageId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyMessageTemplate", null)
                        .WithMany("UploadedFiles")
                        .HasForeignKey("CompanyMessageTemplateId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.ChatHistoryBackupConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.Company", b =>
                {
                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "CmsActivationOwner")
                        .WithMany()
                        .HasForeignKey("CmsActivationOwnerId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "CmsCompanyOwner")
                        .WithMany()
                        .HasForeignKey("CmsCompanyOwnerId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("SubCompanies")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.ChannelDomain.Models.EmailConfig", "EmailConfig")
                        .WithMany()
                        .HasForeignKey("EmailConfigId");

                    b.HasOne("Travis_backend.ChannelDomain.Models.WeChatConfig", "WeChatConfig")
                        .WithMany()
                        .HasForeignKey("WeChatConfigId");

                    b.Navigation("CmsActivationOwner");

                    b.Navigation("CmsCompanyOwner");

                    b.Navigation("EmailConfig");

                    b.Navigation("WeChatConfig");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyAPIKey", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("CompanyAPIKeys")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyCustomField", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("CompanyCustomFields")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyCustomFieldFieldLingual", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyCustomField", null)
                        .WithMany("CompanyCustomFieldFieldLinguals")
                        .HasForeignKey("CompanyCustomFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyCustomUserProfileField", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("CustomUserProfileFields")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyHashtag", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("CompanyHashtags")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyIconFile", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithOne("CompanyIconFile")
                        .HasForeignKey("Travis_backend.CompanyDomain.Models.CompanyIconFile", "CompanyId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyMessageTemplate", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("CompnayMessageTemplates")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "LastSentBy")
                        .WithMany()
                        .HasForeignKey("LastSentById");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "SavedBy")
                        .WithMany()
                        .HasForeignKey("SavedById");

                    b.OwnsOne("Travis_backend.MessageDomain.Models.TargetedChannel", "TargetedChannel", b1 =>
                        {
                            b1.Property<string>("CompanyMessageTemplateId")
                                .HasColumnType("nvarchar(450)");

                            b1.Property<string>("ChannelIdentityId")
                                .HasColumnType("nvarchar(max)");

                            b1.Property<string>("ChannelType")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("CompanyMessageTemplateId");

                            b1.ToTable("CompanyMessageTemplates");

                            b1.ToJson("TargetedChannel");

                            b1.WithOwner()
                                .HasForeignKey("CompanyMessageTemplateId");
                        });

                    b.Navigation("LastSentBy");

                    b.Navigation("SavedBy");

                    b.Navigation("TargetedChannel");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyOperator", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "Identity")
                        .WithMany()
                        .HasForeignKey("IdentityId");

                    b.Navigation("Company");

                    b.Navigation("Identity");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyQuickReply", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("QuickReplies")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyQuickReplyFile", "QuickReplyFile")
                        .WithMany()
                        .HasForeignKey("QuickReplyFileId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "SavedBy")
                        .WithMany()
                        .HasForeignKey("SavedById");

                    b.Navigation("QuickReplyFile");

                    b.Navigation("SavedBy");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyQuickReplyLingual", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyQuickReply", null)
                        .WithMany("CompanyQuickReplyLinguals")
                        .HasForeignKey("CompanyQuickReplyId1");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyTeam", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("CompanyTeams")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CustomUserProfileFieldLingual", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyCustomUserProfileField", null)
                        .WithMany("CustomUserProfileFieldLinguals")
                        .HasForeignKey("CompanyCustomUserProfileFieldId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CustomUserProfileFieldOption", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyCustomUserProfileField", null)
                        .WithMany("CustomUserProfileFieldOptions")
                        .HasForeignKey("CompanyCustomUserProfileFieldId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CustomUserProfileFieldOptionLingual", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.CustomUserProfileFieldOption", null)
                        .WithMany("CustomUserProfileFieldOptionLinguals")
                        .HasForeignKey("CustomUserProfileFieldOptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.ImportContactHistory", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("ImportContactHistories")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "ImportedFrom")
                        .WithMany()
                        .HasForeignKey("ImportedFromId");

                    b.Navigation("ImportedFrom");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.ImportedUserProfile", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.ImportContactHistory", null)
                        .WithMany("ImportedUserProfiles")
                        .HasForeignKey("ImportContactHistoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Travis_backend.ContactDomain.Models.UserProfile", "UserProfile")
                        .WithMany()
                        .HasForeignKey("UserProfileId");

                    b.Navigation("UserProfile");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.NotificationRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("NotificationRecords")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "NotificationStaff")
                        .WithMany()
                        .HasForeignKey("StaffId");

                    b.Navigation("NotificationStaff");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.ProfilePictureFile", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("StaffProfilePictures")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.RegisteredSession", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", null)
                        .WithMany("RegisteredSessions")
                        .HasForeignKey("StaffId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.RequestChannel", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("RequestChannels")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.RolePermission", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("RolePermission")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.Staff", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany("Staffs")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "Identity")
                        .WithMany()
                        .HasForeignKey("IdentityId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.StaffNotificationSetting", "NotificationSetting")
                        .WithMany()
                        .HasForeignKey("NotificationSettingId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.ProfilePictureFile", "ProfilePicture")
                        .WithMany()
                        .HasForeignKey("ProfilePictureId");

                    b.Navigation("Company");

                    b.Navigation("Identity");

                    b.Navigation("NotificationSetting");

                    b.Navigation("ProfilePicture");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.StorageConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithOne("StorageConfig")
                        .HasForeignKey("Travis_backend.CompanyDomain.Models.StorageConfig", "CompanyId");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.TeamMember", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyTeam", null)
                        .WithMany("Members")
                        .HasForeignKey("CompanyTeamId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "Staff")
                        .WithMany()
                        .HasForeignKey("StaffId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Staff");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.UserPreference", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("UserPreferences")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "Staff")
                        .WithOne("UserPreference")
                        .HasForeignKey("Travis_backend.CompanyDomain.Models.UserPreference", "StaffId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Staff");
                });

            modelBuilder.Entity("Travis_backend.ContactDomain.Models.UserProfile", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("UserProfiles")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.MessageDomain.Models.EmailSender", "EmailAddress")
                        .WithMany()
                        .HasForeignKey("EmailAddressId");

                    b.HasOne("Travis_backend.MessageDomain.Models.FacebookSender", "FacebookAccount")
                        .WithMany()
                        .HasForeignKey("FacebookAccountId");

                    b.HasOne("Travis_backend.MessageDomain.Models.InstagramSender", "InstagramUser")
                        .WithMany()
                        .HasForeignKey("InstagramUserId");

                    b.HasOne("Travis_backend.MessageDomain.Models.LineSender", "LineUser")
                        .WithMany()
                        .HasForeignKey("LineUserId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.Guest", "RegisteredUser")
                        .WithMany()
                        .HasForeignKey("RegisteredUserId");

                    b.HasOne("Travis_backend.MessageDomain.Models.SMSSender", "SMSUser")
                        .WithMany()
                        .HasForeignKey("SMSUserId");

                    b.HasOne("Travis_backend.MessageDomain.Models.TelegramSender", "TelegramUser")
                        .WithMany()
                        .HasForeignKey("TelegramUserId");

                    b.HasOne("Travis_backend.CommonDomain.Models.UserDevice", "UserDevice")
                        .WithMany()
                        .HasForeignKey("UserDeviceId");

                    b.HasOne("Travis_backend.ContactDomain.Models.UserProfilePictureFile", "UserProfilePictureFile")
                        .WithMany()
                        .HasForeignKey("UserProfilePictureFileId");

                    b.HasOne("Travis_backend.MessageDomain.Models.ViberSender", "ViberUser")
                        .WithMany()
                        .HasForeignKey("ViberUserId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WeChatSender", "WeChatUser")
                        .WithMany()
                        .HasForeignKey("WeChatUserId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WebClientSender", "WebClient")
                        .WithMany()
                        .HasForeignKey("WebClientId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WhatsApp360DialogSender", "WhatsApp360DialogUser")
                        .WithMany()
                        .HasForeignKey("WhatsApp360DialogUserId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WhatsAppSender", "WhatsAppAccount")
                        .WithMany()
                        .HasForeignKey("WhatsAppAccountId");

                    b.Navigation("EmailAddress");

                    b.Navigation("FacebookAccount");

                    b.Navigation("InstagramUser");

                    b.Navigation("LineUser");

                    b.Navigation("RegisteredUser");

                    b.Navigation("SMSUser");

                    b.Navigation("TelegramUser");

                    b.Navigation("UserDevice");

                    b.Navigation("UserProfilePictureFile");

                    b.Navigation("ViberUser");

                    b.Navigation("WeChatUser");

                    b.Navigation("WebClient");

                    b.Navigation("WhatsApp360DialogUser");

                    b.Navigation("WhatsAppAccount");
                });

            modelBuilder.Entity("Travis_backend.ContactDomain.Models.UserProfileCustomField", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyCustomUserProfileField", "CompanyDefinedField")
                        .WithMany()
                        .HasForeignKey("CompanyDefinedFieldId");

                    b.HasOne("Travis_backend.ContactDomain.Models.UserProfile", null)
                        .WithMany("CustomFields")
                        .HasForeignKey("UserProfileId");

                    b.Navigation("CompanyDefinedField");
                });

            modelBuilder.Entity("Travis_backend.ContactDomain.Models.UserProfileDeletionBuffer", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "DeletedByStaff")
                        .WithMany()
                        .HasForeignKey("DeletedByStaffId");

                    b.HasOne("Travis_backend.ContactDomain.Models.UserProfile", "UserProfile")
                        .WithMany()
                        .HasForeignKey("UserProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DeletedByStaff");

                    b.Navigation("UserProfile");
                });

            modelBuilder.Entity("Travis_backend.ContactDomain.Models.UserProfilePictureFile", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.ConversationDomain.Models.AdditionalAssignee", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "Assignee")
                        .WithMany()
                        .HasForeignKey("AssigneeId");

                    b.HasOne("Travis_backend.ConversationDomain.Models.Conversation", null)
                        .WithMany("AdditionalAssignees")
                        .HasForeignKey("ConversationId");

                    b.Navigation("Assignee");
                });

            modelBuilder.Entity("Travis_backend.ConversationDomain.Models.Conversation", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyTeam", "AssignedTeam")
                        .WithMany()
                        .HasForeignKey("AssignedTeamId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "Assignee")
                        .WithMany()
                        .HasForeignKey("AssigneeId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("CompanyConversations")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.MessageDomain.Models.EmailSender", "EmailAddress")
                        .WithMany()
                        .HasForeignKey("EmailAddressId");

                    b.HasOne("Travis_backend.MessageDomain.Models.InstagramSender", "InstagramUser")
                        .WithMany()
                        .HasForeignKey("InstagramUserId");

                    b.HasOne("Travis_backend.MessageDomain.Models.LineSender", "LineUser")
                        .WithMany()
                        .HasForeignKey("LineUserId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.Guest", "NaiveUser")
                        .WithMany()
                        .HasForeignKey("NaiveUserId");

                    b.HasOne("Travis_backend.MessageDomain.Models.SMSSender", "SMSUser")
                        .WithMany()
                        .HasForeignKey("SMSUserId");

                    b.HasOne("Travis_backend.MessageDomain.Models.TelegramSender", "TelegramUser")
                        .WithMany()
                        .HasForeignKey("TelegramUserId");

                    b.HasOne("Travis_backend.CommonDomain.Models.UserDevice", "UserDevice")
                        .WithMany()
                        .HasForeignKey("UserDeviceId");

                    b.HasOne("Travis_backend.ContactDomain.Models.UserProfile", "UserProfile")
                        .WithOne("Conversation")
                        .HasForeignKey("Travis_backend.ConversationDomain.Models.Conversation", "UserProfileId");

                    b.HasOne("Travis_backend.MessageDomain.Models.ViberSender", "ViberUser")
                        .WithMany()
                        .HasForeignKey("ViberUserId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WeChatSender", "WeChatUser")
                        .WithMany()
                        .HasForeignKey("WeChatUserId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WebClientSender", "WebClient")
                        .WithMany()
                        .HasForeignKey("WebClientId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WhatsApp360DialogSender", "WhatsApp360DialogUser")
                        .WithMany()
                        .HasForeignKey("WhatsApp360DialogUserId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WhatsAppSender", "WhatsappUser")
                        .WithMany()
                        .HasForeignKey("WhatsappUserId");

                    b.HasOne("Travis_backend.MessageDomain.Models.FacebookSender", "facebookUser")
                        .WithMany()
                        .HasForeignKey("facebookUserId");

                    b.Navigation("AssignedTeam");

                    b.Navigation("Assignee");

                    b.Navigation("EmailAddress");

                    b.Navigation("InstagramUser");

                    b.Navigation("LineUser");

                    b.Navigation("NaiveUser");

                    b.Navigation("SMSUser");

                    b.Navigation("TelegramUser");

                    b.Navigation("UserDevice");

                    b.Navigation("UserProfile");

                    b.Navigation("ViberUser");

                    b.Navigation("WeChatUser");

                    b.Navigation("WebClient");

                    b.Navigation("WhatsApp360DialogUser");

                    b.Navigation("WhatsappUser");

                    b.Navigation("facebookUser");
                });

            modelBuilder.Entity("Travis_backend.ConversationDomain.Models.ConversationBookmark", b =>
                {
                    b.HasOne("Travis_backend.ConversationDomain.Models.Conversation", null)
                        .WithMany("ConversationBookmarks")
                        .HasForeignKey("ConversationId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "Staff")
                        .WithMany()
                        .HasForeignKey("StaffId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Staff");
                });

            modelBuilder.Entity("Travis_backend.ConversationDomain.Models.ConversationHashtag", b =>
                {
                    b.HasOne("Travis_backend.ConversationDomain.Models.Conversation", null)
                        .WithMany("conversationHashtags")
                        .HasForeignKey("ConversationId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.CompanyHashtag", "Hashtag")
                        .WithMany()
                        .HasForeignKey("HashtagId");

                    b.Navigation("Hashtag");
                });

            modelBuilder.Entity("Travis_backend.ConversationDomain.Models.ConversationWhatsappSenderHistory", b =>
                {
                    b.HasOne("Travis_backend.ConversationDomain.Models.Conversation", null)
                        .WithMany("WhatsAppSenderHistories")
                        .HasForeignKey("ConversationId");
                });

            modelBuilder.Entity("Travis_backend.ConversationServices.Models.FacebookConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("FacebookConfigs")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.ConversationServices.Models.InstagramConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("InstagramConfigs")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.CompanyPaymentFailedLog", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany("CompanyPaymentFailedLogs")
                        .HasForeignKey("CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.CompanySandbox", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithOne("CompanySandbox")
                        .HasForeignKey("Travis_backend.CoreDomain.Models.CompanySandbox", "CompanyId");
                });

            modelBuilder.Entity("Travis_backend.DemoDomain.Models.DemoConversation", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.DemoDomain.Models.DemoConversationMessage", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.DemoDomain.Models.DemoConversation", "DemoConversation")
                        .WithMany("ChatHistory")
                        .HasForeignKey("DemoConversationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("DemoConversation");
                });

            modelBuilder.Entity("Travis_backend.FacebookInstagramIntegrationDomain.Models.FacebookLeadAdsFormFieldMapping", b =>
                {
                    b.HasOne("Travis_backend.FacebookInstagramIntegrationDomain.Models.FacebookLeadAdsForm", null)
                        .WithMany("FieldMappings")
                        .HasForeignKey("FacebookLeadAdsFormId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Travis_backend.FileDomain.Models.UploadedFile", b =>
                {
                    b.HasOne("Travis_backend.MessageDomain.Models.ConversationMessage", null)
                        .WithMany("UploadedFiles")
                        .HasForeignKey("ConversationMessageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Travis_backend.CommonDomain.Models.UserDevice", "SenderDevice")
                        .WithMany()
                        .HasForeignKey("SenderDeviceId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "Sender")
                        .WithMany()
                        .HasForeignKey("SenderId");

                    b.Navigation("Sender");

                    b.Navigation("SenderDevice");
                });

            modelBuilder.Entity("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationContactOwnerMap", b =>
                {
                    b.HasOne("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationConfig", "HubSpotIntegrationConfig")
                        .WithMany("ContactOwnerMaps")
                        .HasForeignKey("HubSpotIntegrationConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HubSpotIntegrationConfig");
                });

            modelBuilder.Entity("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationCustomFieldMap", b =>
                {
                    b.HasOne("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationConfig", "HubSpotIntegrationConfig")
                        .WithMany("CustomFieldMaps")
                        .HasForeignKey("HubSpotIntegrationConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HubSpotIntegrationConfig");
                });

            modelBuilder.Entity("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationSyncTask", b =>
                {
                    b.HasOne("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationConfig", "HubSpotIntegrationConfig")
                        .WithMany("SyncTasks")
                        .HasForeignKey("HubSpotIntegrationConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HubSpotIntegrationConfig");
                });

            modelBuilder.Entity("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationSyncTaskErrorRecord", b =>
                {
                    b.HasOne("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationSyncTask", "HubSpotIntegrationSyncTask")
                        .WithMany("ErrorRecords")
                        .HasForeignKey("HubSpotIntegrationSyncTaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HubSpotIntegrationSyncTask");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyAbandonedCart", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("ShopifyAbandonedCarts")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Travis_backend.ContactDomain.Models.UserProfile", null)
                        .WithMany("ShopifyAbandonedCarts")
                        .HasForeignKey("UserProfileId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyCollectionProductRecord", b =>
                {
                    b.HasOne("Travis_backend.IntegrationServices.Models.ShopifyProductRecord", "ProductRecord")
                        .WithMany()
                        .HasForeignKey("ProductRecordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Travis_backend.IntegrationServices.Models.ShopifyCollectionRecord", "ShopifyCollectionRecord")
                        .WithMany("ProductRecords")
                        .HasForeignKey("ShopifyCollectionRecordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProductRecord");

                    b.Navigation("ShopifyCollectionRecord");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyCollectionRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("ShopifyCollectionRecords")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.BillRecord", "BillRecord")
                        .WithMany()
                        .HasForeignKey("BillRecordId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("ShopifyConfigs")
                        .HasForeignKey("CompanyId");

                    b.Navigation("BillRecord");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyOrderRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("ShopifyOrderRecords")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Travis_backend.ContactDomain.Models.UserProfile", null)
                        .WithMany("ShopifyOrderRecords")
                        .HasForeignKey("UserProfileId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyProductMessageTemplate", b =>
                {
                    b.HasOne("Travis_backend.IntegrationServices.Models.ShopifyConfig", null)
                        .WithMany("ShopifyProductMessageTemplates")
                        .HasForeignKey("ShopifyConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyProductRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("ShopifyProductRecords")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsCompanyAdditionalInfo", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithOne("CmsCompanyAdditionalInfo")
                        .HasForeignKey("Travis_backend.InternalDomain.Models.CmsCompanyAdditionalInfo", "CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsContactOwnerAssignLog", b =>
                {
                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "AssignedByUser")
                        .WithMany()
                        .HasForeignKey("AssignedByUserId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("CmsContactOwnerChangeLogs")
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "FromContactOwner")
                        .WithMany()
                        .HasForeignKey("FromContactOwnerId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "ToContactOwner")
                        .WithMany()
                        .HasForeignKey("ToContactOwnerId");

                    b.Navigation("AssignedByUser");

                    b.Navigation("FromContactOwner");

                    b.Navigation("ToContactOwner");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsHubSpotCompanyMap", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithOne("CmsHubSpotCompanyMap")
                        .HasForeignKey("Travis_backend.InternalDomain.Models.CmsHubSpotCompanyMap", "CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsHubSpotContactOwnerMap", b =>
                {
                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "ContactOwner")
                        .WithMany()
                        .HasForeignKey("ContactOwnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ContactOwner");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsPartnerStackCustomerMap", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithOne("CmsPartnerStackCustomerMap")
                        .HasForeignKey("Travis_backend.InternalDomain.Models.CmsPartnerStackCustomerMap", "CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsSalesPaymentRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.BillRecord", "BillRecord")
                        .WithMany("CmsSalesPaymentRecords")
                        .HasForeignKey("BillRecordId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "CreateUser")
                        .WithMany()
                        .HasForeignKey("CreateUserId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "LastModifiedByUser")
                        .WithMany()
                        .HasForeignKey("LastModifiedByUserId");

                    b.Navigation("BillRecord");

                    b.Navigation("CreateUser");

                    b.Navigation("LastModifiedByUser");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsSalesPaymentRecordFile", b =>
                {
                    b.HasOne("Travis_backend.InternalDomain.Models.CmsSalesPaymentRecord", "CmsSalesPaymentRecord")
                        .WithMany("Files")
                        .HasForeignKey("CmsSalesPaymentRecordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CmsSalesPaymentRecord");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsSleekPayReportData", b =>
                {
                    b.HasOne("Travis_backend.InternalDomain.Models.CmsSleekPayReportCsv", "CmsSleekPayReportCsv")
                        .WithMany()
                        .HasForeignKey("CmsSleekPayReportCsvId");

                    b.Navigation("CmsSleekPayReportCsv");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsWhatsappApplication", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "ContactOwner")
                        .WithMany()
                        .HasForeignKey("ContactOwnerId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId");

                    b.Navigation("Company");

                    b.Navigation("ContactOwner");

                    b.Navigation("CreatedByUser");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.ImportWhatsappHistoryRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.ConversationMessage", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.BroadcastHistory", null)
                        .WithMany("ConversationMessages")
                        .HasForeignKey("BroadcastHistoryId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("CompanyConversationMessages")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.ConversationDomain.Models.Conversation", "Conversation")
                        .WithMany("ChatHistory")
                        .HasForeignKey("ConversationId");

                    b.HasOne("Travis_backend.MessageDomain.Models.EmailSender", "EmailFrom")
                        .WithMany()
                        .HasForeignKey("EmailFromId");

                    b.HasOne("Travis_backend.MessageDomain.Models.InstagramSender", "InstagramReceiver")
                        .WithMany()
                        .HasForeignKey("InstagramReceiverId");

                    b.HasOne("Travis_backend.MessageDomain.Models.InstagramSender", "InstagramSender")
                        .WithMany()
                        .HasForeignKey("InstagramSenderId");

                    b.HasOne("Travis_backend.MessageDomain.Models.LineSender", "LineReceiver")
                        .WithMany()
                        .HasForeignKey("LineReceiverId");

                    b.HasOne("Travis_backend.MessageDomain.Models.LineSender", "LineSender")
                        .WithMany()
                        .HasForeignKey("LineSenderId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "MessageAssignee")
                        .WithMany()
                        .HasForeignKey("MessageAssigneeId");

                    b.HasOne("Travis_backend.CommonDomain.Models.UserDevice", "ReceiverDevice")
                        .WithMany()
                        .HasForeignKey("ReceiverDeviceId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "Receiver")
                        .WithMany()
                        .HasForeignKey("ReceiverId");

                    b.HasOne("Travis_backend.MessageDomain.Models.SMSSender", "SMSReceiver")
                        .WithMany()
                        .HasForeignKey("SMSReceiverId");

                    b.HasOne("Travis_backend.MessageDomain.Models.SMSSender", "SMSSender")
                        .WithMany()
                        .HasForeignKey("SMSSenderId");

                    b.HasOne("Travis_backend.CommonDomain.Models.UserDevice", "SenderDevice")
                        .WithMany()
                        .HasForeignKey("SenderDeviceId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "Sender")
                        .WithMany()
                        .HasForeignKey("SenderId");

                    b.HasOne("Travis_backend.MessageDomain.Models.TelegramSender", "TelegramReceiver")
                        .WithMany()
                        .HasForeignKey("TelegramReceiverId");

                    b.HasOne("Travis_backend.MessageDomain.Models.TelegramSender", "TelegramSender")
                        .WithMany()
                        .HasForeignKey("TelegramSenderId");

                    b.HasOne("Travis_backend.MessageDomain.Models.ViberSender", "ViberReceiver")
                        .WithMany()
                        .HasForeignKey("ViberReceiverId");

                    b.HasOne("Travis_backend.MessageDomain.Models.ViberSender", "ViberSender")
                        .WithMany()
                        .HasForeignKey("ViberSenderId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WeChatSender", "WeChatReceiver")
                        .WithMany()
                        .HasForeignKey("WeChatReceiverId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WeChatSender", "WeChatSender")
                        .WithMany()
                        .HasForeignKey("WeChatSenderId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WebClientSender", "WebClientReceiver")
                        .WithMany()
                        .HasForeignKey("WebClientReceiverId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WebClientSender", "WebClientSender")
                        .WithMany()
                        .HasForeignKey("WebClientSenderId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WhatsApp360DialogSender", "Whatsapp360DialogReceiver")
                        .WithMany()
                        .HasForeignKey("Whatsapp360DialogReceiverId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WhatsApp360DialogSender", "Whatsapp360DialogSender")
                        .WithMany()
                        .HasForeignKey("Whatsapp360DialogSenderId");

                    b.HasOne("Travis_backend.MessageDomain.Models.FacebookSender", "facebookReceiver")
                        .WithMany()
                        .HasForeignKey("facebookReceiverId");

                    b.HasOne("Travis_backend.MessageDomain.Models.FacebookSender", "facebookSender")
                        .WithMany()
                        .HasForeignKey("facebookSenderId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WhatsAppSender", "whatsappReceiver")
                        .WithMany()
                        .HasForeignKey("whatsappReceiverId");

                    b.HasOne("Travis_backend.MessageDomain.Models.WhatsAppSender", "whatsappSender")
                        .WithMany()
                        .HasForeignKey("whatsappSenderId");

                    b.Navigation("Conversation");

                    b.Navigation("EmailFrom");

                    b.Navigation("InstagramReceiver");

                    b.Navigation("InstagramSender");

                    b.Navigation("LineReceiver");

                    b.Navigation("LineSender");

                    b.Navigation("MessageAssignee");

                    b.Navigation("Receiver");

                    b.Navigation("ReceiverDevice");

                    b.Navigation("SMSReceiver");

                    b.Navigation("SMSSender");

                    b.Navigation("Sender");

                    b.Navigation("SenderDevice");

                    b.Navigation("TelegramReceiver");

                    b.Navigation("TelegramSender");

                    b.Navigation("ViberReceiver");

                    b.Navigation("ViberSender");

                    b.Navigation("WeChatReceiver");

                    b.Navigation("WeChatSender");

                    b.Navigation("WebClientReceiver");

                    b.Navigation("WebClientSender");

                    b.Navigation("Whatsapp360DialogReceiver");

                    b.Navigation("Whatsapp360DialogSender");

                    b.Navigation("facebookReceiver");

                    b.Navigation("facebookSender");

                    b.Navigation("whatsappReceiver");

                    b.Navigation("whatsappSender");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.EmailSender", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("EmailSenders")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.ExtendedMessagePayload", b =>
                {
                    b.HasOne("Travis_backend.MessageDomain.Models.ConversationMessage", "ConversationMessage")
                        .WithOne("ExtendedMessagePayload")
                        .HasForeignKey("Travis_backend.MessageDomain.Models.ExtendedMessagePayload", "ConversationMessageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ConversationMessage");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.FacebookSender", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("FacebookSenders")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.FacebookUserOneTimeToken", b =>
                {
                    b.HasOne("Travis_backend.MessageDomain.Models.FacebookOTNTopic", "FacebookOTNTopic")
                        .WithMany("FacebookUserOneTimeTokens")
                        .HasForeignKey("FacebookOTNTopicId");

                    b.HasOne("Travis_backend.MessageDomain.Models.FacebookSender", "FacebookUser")
                        .WithMany()
                        .HasForeignKey("FacebookUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FacebookOTNTopic");

                    b.Navigation("FacebookUser");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.IPAddressInfo", b =>
                {
                    b.HasOne("Travis_backend.MessageDomain.Models.WebClientSender", null)
                        .WithMany("IPAddressInfos")
                        .HasForeignKey("WebClientSenderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.LineSender", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("LineSenders")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.SMSSender", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("SMSSenders")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.SandboxSender", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("SandboxSenders")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.CoreDomain.Models.CompanySandbox", null)
                        .WithMany("SandboxSenders")
                        .HasForeignKey("CompanySandboxId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.WeChatSender", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("WeChatSenders")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.WebClientSender", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("WebClientSenders")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.WhatsAppSender", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("WhatsAppSenders")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.Whatsapp360DialogExtendedMessagePayload", b =>
                {
                    b.HasOne("Travis_backend.MessageDomain.Models.ConversationMessage", "ConversationMessage")
                        .WithOne("Whatsapp360DialogExtendedMessagePayload")
                        .HasForeignKey("Travis_backend.MessageDomain.Models.Whatsapp360DialogExtendedMessagePayload", "ConversationMessageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ConversationMessage");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.WhatsappCloudApiSender", b =>
                {
                    b.HasOne("Travis_backend.ConversationDomain.Models.Conversation", null)
                        .WithOne("WhatsappCloudApiUser")
                        .HasForeignKey("Travis_backend.MessageDomain.Models.WhatsappCloudApiSender", "ConversationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Travis_backend.ContactDomain.Models.UserProfile", null)
                        .WithOne("WhatsappCloudApiUser")
                        .HasForeignKey("Travis_backend.MessageDomain.Models.WhatsappCloudApiSender", "UserProfileId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Travis_backend.Models.ChatChannelConfig.WhatsappCloudApiConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany("WhatsappCloudApiConfigs")
                        .HasForeignKey("CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.Models.ChatChannelConfig.WhatsappCloudApiWabaConnection", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.ResellerDomain.Models.ResellerActivityLog", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId");

                    b.HasOne("Travis_backend.ResellerDomain.Models.ResellerCompanyProfile", "ResellerCompanyProfile")
                        .WithMany("ResellerActivityLogs")
                        .HasForeignKey("ResellerCompanyProfileId");

                    b.Navigation("Company");

                    b.Navigation("CreatedByUser");

                    b.Navigation("ResellerCompanyProfile");
                });

            modelBuilder.Entity("Travis_backend.ResellerDomain.Models.ResellerClientCompanyProfile", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "ClientCompany")
                        .WithOne("ResellerClientCompanyProfile")
                        .HasForeignKey("Travis_backend.ResellerDomain.Models.ResellerClientCompanyProfile", "ClientCompanyId");

                    b.HasOne("Travis_backend.ResellerDomain.Models.ResellerCompanyProfile", "ResellerCompanyProfile")
                        .WithMany("ClientCompanyProfiles")
                        .HasForeignKey("ResellerCompanyProfileId");

                    b.Navigation("ClientCompany");

                    b.Navigation("ResellerCompanyProfile");
                });

            modelBuilder.Entity("Travis_backend.ResellerDomain.Models.ResellerCompanyProfile", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithOne("ResellerCompanyProfile")
                        .HasForeignKey("Travis_backend.ResellerDomain.Models.ResellerCompanyProfile", "CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.ResellerDomain.Models.ResellerProfileLogo", b =>
                {
                    b.HasOne("Travis_backend.ResellerDomain.Models.ResellerCompanyProfile", null)
                        .WithOne("ResellerProfileLogo")
                        .HasForeignKey("Travis_backend.ResellerDomain.Models.ResellerProfileLogo", "ResellerCompanyProfileId");
                });

            modelBuilder.Entity("Travis_backend.ResellerDomain.Models.ResellerStaff", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.ProfilePictureFile", "ProfilePicture")
                        .WithMany()
                        .HasForeignKey("ProfilePictureId");

                    b.HasOne("Travis_backend.ResellerDomain.Models.ResellerCompanyProfile", "ResellerCompanyProfile")
                        .WithMany("ResellerStaffs")
                        .HasForeignKey("ResellerCompanyProfileId");

                    b.Navigation("ProfilePicture");

                    b.Navigation("ResellerCompanyProfile");
                });

            modelBuilder.Entity("Travis_backend.ResellerDomain.Models.ResellerTransactionLog", b =>
                {
                    b.HasOne("Travis_backend.ResellerDomain.Models.ResellerCompanyProfile", "ResellerCompanyProfile")
                        .WithMany("ResellerTransactionLogs")
                        .HasForeignKey("ResellerCompanyProfileId");

                    b.Navigation("ResellerCompanyProfile");
                });

            modelBuilder.Entity("Travis_backend.ShareInvitationDomain.Models.ShareLinkGenerationRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("ShareLinkGenerationRecords")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.ShareInvitationDomain.Models.ShareLinkTrackRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("ShareLinkTrackingRecords")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.ShareInvitationDomain.Models.ShareableInvitation", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("ShareableInvitations")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "GeneratedBy")
                        .WithMany()
                        .HasForeignKey("GeneratedById");

                    b.Navigation("GeneratedBy");
                });

            modelBuilder.Entity("Travis_backend.ShareInvitationDomain.Models.ShareableInvitationRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Staff", "InvitedStaff")
                        .WithMany()
                        .HasForeignKey("InvitedStaffId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Travis_backend.ShareInvitationDomain.Models.ShareableInvitation", null)
                        .WithMany("ShareableInvitationRecords")
                        .HasForeignKey("ShareableInvitationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("InvitedStaff");
                });

            modelBuilder.Entity("Travis_backend.ShoplineIntegrationDomain.Models.ShoplineConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("ShoplineConfigs")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.SleekflowCrmHubDomain.Models.CrmHubEntity", b =>
                {
                    b.HasOne("Travis_backend.ContactDomain.Models.UserProfile", "UserProfile")
                        .WithMany("CrmHubEntities")
                        .HasForeignKey("UserProfileId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("UserProfile");
                });

            modelBuilder.Entity("Travis_backend.StripeIntegrationDomain.Models.StripePaymentConfig", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("StripePaymentConfigs")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.StripeIntegrationDomain.Models.StripeCompanyLogo", "StripeCompanyLogo")
                        .WithMany()
                        .HasForeignKey("StripeCompanyLogoId");

                    b.Navigation("StripeCompanyLogo");
                });

            modelBuilder.Entity("Travis_backend.StripeIntegrationDomain.Models.StripePaymentMessageTemplate", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("StripePaymentMessageTemplates")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.StripeIntegrationDomain.Models.StripePaymentRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("StripePaymentRecords")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.SubscriptionPlanDomain.Models.CustomSubscriptionPlanTranslationMap", b =>
                {
                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "CreatedByUser")
                        .WithMany()
                        .HasForeignKey("CreatedByUserId");

                    b.HasOne("Travis_backend.AccountAuthenticationDomain.Models.ApplicationUser", "UpdatedByUser")
                        .WithMany()
                        .HasForeignKey("UpdatedByUserId");

                    b.Navigation("CreatedByUser");

                    b.Navigation("UpdatedByUser");
                });

            modelBuilder.Entity("Travis_backend.SubscriptionPlanDomain.Models.Promotion", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Travis_backend.SubscriptionPlanDomain.Models.RedeemPromotionRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.HasOne("Travis_backend.SubscriptionPlanDomain.Models.Promotion", "Promotion")
                        .WithMany("RedeemRecords")
                        .HasForeignKey("PromotionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Company");

                    b.Navigation("Promotion");
                });

            modelBuilder.Entity("Travis_backend.SupportTicketDomain.Models.SupportTicketFile", b =>
                {
                    b.HasOne("Travis_backend.SupportTicketDomain.Models.SupportTicket", "SupportTicket")
                        .WithMany("SupportTicketFiles")
                        .HasForeignKey("SupportTicketId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SupportTicket");
                });

            modelBuilder.Entity("Travis_backend.ZapierIntegrationDomain.Models.ZapierPollingRecord", b =>
                {
                    b.HasOne("Travis_backend.CompanyDomain.Models.Company", null)
                        .WithMany("ZapierPollingRecords")
                        .HasForeignKey("CompanyId");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AssignmentRule", b =>
                {
                    b.Navigation("AutomationActions");

                    b.Navigation("AutomationHistories");

                    b.Navigation("FbIgIcebreaker");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AutomationAction", b =>
                {
                    b.Navigation("FbIgAutoReply");

                    b.Navigation("UploadedFiles");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AutomationActionRecord", b =>
                {
                    b.Navigation("UploadedFiles");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.AutomationHistory", b =>
                {
                    b.Navigation("AutomationActionRecords");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.FbIgAutoReply", b =>
                {
                    b.Navigation("FbIgAutoReplyFiles");

                    b.Navigation("FbIgAutoReplyHistoryRecords");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.FbIgIcebreaker", b =>
                {
                    b.Navigation("IcebreakerReplyRules");
                });

            modelBuilder.Entity("Travis_backend.AutomationDomain.Models.IcebreakerReplyRule", b =>
                {
                    b.Navigation("IcebreakerHistoryRecords");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsApp360DialogConfig", b =>
                {
                    b.Navigation("TemplateBookmarks");

                    b.Navigation("WhatsApp360DialogMediaFiles");
                });

            modelBuilder.Entity("Travis_backend.ChannelDomain.Models.WhatsApp360DialogUsageRecord", b =>
                {
                    b.Navigation("TransactionLogs");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.BillRecord", b =>
                {
                    b.Navigation("CmsSalesPaymentRecords");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.BroadcastHistory", b =>
                {
                    b.Navigation("ConversationMessages");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CampaignAutomationAction", b =>
                {
                    b.Navigation("UploadedFiles");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CampaignChannelMessage", b =>
                {
                    b.Navigation("UploadedFiles");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.Company", b =>
                {
                    b.Navigation("AnalyticSegments");

                    b.Navigation("AssignmentRules");

                    b.Navigation("AutomationActions");

                    b.Navigation("BillRecords");

                    b.Navigation("BlastMessageConfig");

                    b.Navigation("CmsCompanyAdditionalInfo");

                    b.Navigation("CmsContactOwnerChangeLogs");

                    b.Navigation("CmsHubSpotCompanyMap");

                    b.Navigation("CmsPartnerStackCustomerMap");

                    b.Navigation("CompanyAPIKeys");

                    b.Navigation("CompanyConversationMessages");

                    b.Navigation("CompanyConversations");

                    b.Navigation("CompanyCustomFields");

                    b.Navigation("CompanyHashtags");

                    b.Navigation("CompanyIconFile");

                    b.Navigation("CompanyPaymentFailedLogs");

                    b.Navigation("CompanySandbox");

                    b.Navigation("CompanyTeams");

                    b.Navigation("CompnayMessageTemplates");

                    b.Navigation("ConversationAssignmentQueue");

                    b.Navigation("CustomUserProfileFields");

                    b.Navigation("EmailSenders");

                    b.Navigation("FacebookConfigs");

                    b.Navigation("FacebookSenders");

                    b.Navigation("Guests");

                    b.Navigation("ImportContactHistories");

                    b.Navigation("InstagramConfigs");

                    b.Navigation("LineConfigs");

                    b.Navigation("LineSenders");

                    b.Navigation("NotificationRecords");

                    b.Navigation("QuickReplies");

                    b.Navigation("RequestChannels");

                    b.Navigation("ResellerClientCompanyProfile");

                    b.Navigation("ResellerCompanyProfile");

                    b.Navigation("RolePermission");

                    b.Navigation("SMSConfigs");

                    b.Navigation("SMSSenders");

                    b.Navigation("SandboxSenders");

                    b.Navigation("ShareLinkGenerationRecords");

                    b.Navigation("ShareLinkTrackingRecords");

                    b.Navigation("ShareableInvitations");

                    b.Navigation("ShopifyAbandonedCarts");

                    b.Navigation("ShopifyCollectionRecords");

                    b.Navigation("ShopifyConfigs");

                    b.Navigation("ShopifyOrderRecords");

                    b.Navigation("ShopifyProductRecords");

                    b.Navigation("ShoplineConfigs");

                    b.Navigation("StaffProfilePictures");

                    b.Navigation("Staffs");

                    b.Navigation("StorageConfig");

                    b.Navigation("StripePaymentConfigs");

                    b.Navigation("StripePaymentMessageTemplates");

                    b.Navigation("StripePaymentRecords");

                    b.Navigation("SubCompanies");

                    b.Navigation("TelegramConfigs");

                    b.Navigation("TwilioUsageRecords");

                    b.Navigation("UserDevices");

                    b.Navigation("UserPreferences");

                    b.Navigation("UserProfiles");

                    b.Navigation("ViberConfigs");

                    b.Navigation("WeChatSenders");

                    b.Navigation("WebClientSenders");

                    b.Navigation("WhatsApp360DialogConfigs");

                    b.Navigation("WhatsApp360DialogUsageRecords");

                    b.Navigation("WhatsAppConfigs");

                    b.Navigation("WhatsAppSenders");

                    b.Navigation("Whatsapp360DialogTopUpConfig");

                    b.Navigation("WhatsappCloudApiConfigs");

                    b.Navigation("ZapierPollingRecords");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyCustomField", b =>
                {
                    b.Navigation("CompanyCustomFieldFieldLinguals");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyCustomUserProfileField", b =>
                {
                    b.Navigation("CustomUserProfileFieldLinguals");

                    b.Navigation("CustomUserProfileFieldOptions");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyMessageTemplate", b =>
                {
                    b.Navigation("CampaignAutomationActions");

                    b.Navigation("CampaignChannelMessages");

                    b.Navigation("UploadedFiles");

                    b.Navigation("broadcastHistories");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyQuickReply", b =>
                {
                    b.Navigation("CompanyQuickReplyLinguals");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CompanyTeam", b =>
                {
                    b.Navigation("Members");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.CustomUserProfileFieldOption", b =>
                {
                    b.Navigation("CustomUserProfileFieldOptionLinguals");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.ImportContactHistory", b =>
                {
                    b.Navigation("ImportedUserProfiles");
                });

            modelBuilder.Entity("Travis_backend.CompanyDomain.Models.Staff", b =>
                {
                    b.Navigation("RegisteredSessions");

                    b.Navigation("UserPreference");
                });

            modelBuilder.Entity("Travis_backend.ContactDomain.Models.UserProfile", b =>
                {
                    b.Navigation("Conversation");

                    b.Navigation("CrmHubEntities");

                    b.Navigation("CustomFields");

                    b.Navigation("ShopifyAbandonedCarts");

                    b.Navigation("ShopifyOrderRecords");

                    b.Navigation("WhatsappCloudApiUser");
                });

            modelBuilder.Entity("Travis_backend.ConversationDomain.Models.Conversation", b =>
                {
                    b.Navigation("AdditionalAssignees");

                    b.Navigation("ChatHistory");

                    b.Navigation("ConversationBookmarks");

                    b.Navigation("WhatsAppSenderHistories");

                    b.Navigation("WhatsappCloudApiUser");

                    b.Navigation("conversationHashtags");
                });

            modelBuilder.Entity("Travis_backend.CoreDomain.Models.CompanySandbox", b =>
                {
                    b.Navigation("SandboxSenders");
                });

            modelBuilder.Entity("Travis_backend.DemoDomain.Models.DemoConversation", b =>
                {
                    b.Navigation("ChatHistory");
                });

            modelBuilder.Entity("Travis_backend.FacebookInstagramIntegrationDomain.Models.FacebookLeadAdsForm", b =>
                {
                    b.Navigation("FieldMappings");
                });

            modelBuilder.Entity("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationConfig", b =>
                {
                    b.Navigation("ContactOwnerMaps");

                    b.Navigation("CustomFieldMaps");

                    b.Navigation("SyncTasks");
                });

            modelBuilder.Entity("Travis_backend.HubSpotIntegrationDomain.Models.HubSpotIntegrationSyncTask", b =>
                {
                    b.Navigation("ErrorRecords");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyCollectionRecord", b =>
                {
                    b.Navigation("ProductRecords");
                });

            modelBuilder.Entity("Travis_backend.IntegrationServices.Models.ShopifyConfig", b =>
                {
                    b.Navigation("ShopifyProductMessageTemplates");
                });

            modelBuilder.Entity("Travis_backend.InternalDomain.Models.CmsSalesPaymentRecord", b =>
                {
                    b.Navigation("Files");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.ConversationMessage", b =>
                {
                    b.Navigation("ExtendedMessagePayload");

                    b.Navigation("UploadedFiles");

                    b.Navigation("Whatsapp360DialogExtendedMessagePayload");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.FacebookOTNTopic", b =>
                {
                    b.Navigation("FacebookUserOneTimeTokens");
                });

            modelBuilder.Entity("Travis_backend.MessageDomain.Models.WebClientSender", b =>
                {
                    b.Navigation("IPAddressInfos");
                });

            modelBuilder.Entity("Travis_backend.ResellerDomain.Models.ResellerCompanyProfile", b =>
                {
                    b.Navigation("ClientCompanyProfiles");

                    b.Navigation("ResellerActivityLogs");

                    b.Navigation("ResellerProfileLogo");

                    b.Navigation("ResellerStaffs");

                    b.Navigation("ResellerTransactionLogs");
                });

            modelBuilder.Entity("Travis_backend.ShareInvitationDomain.Models.ShareableInvitation", b =>
                {
                    b.Navigation("ShareableInvitationRecords");
                });

            modelBuilder.Entity("Travis_backend.SubscriptionPlanDomain.Models.Promotion", b =>
                {
                    b.Navigation("RedeemRecords");
                });

            modelBuilder.Entity("Travis_backend.SupportTicketDomain.Models.SupportTicket", b =>
                {
                    b.Navigation("SupportTicketFiles");
                });
#pragma warning restore 612, 618
        }
    }
}
