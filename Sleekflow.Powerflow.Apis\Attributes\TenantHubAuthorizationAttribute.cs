using System.IdentityModel.Tokens.Jwt;
using System.Text;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.IdentityModel.Tokens;

namespace Sleekflow.Powerflow.Apis.Attributes;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class TenantHubAuthorizationAttribute : Attribute, IAuthorizationFilter
{
    private const string SecretKey = "UDVXRTiHVDQNpGwQFcW3bP2SZYE235Tu9PEkU9TYLCq20uGCjKhUbcK2xxpzfMx";

    public async void OnAuthorization(AuthorizationFilterContext context)
    {
        var allowAnonymous = context.ActionDescriptor.EndpointMetadata.OfType<AllowAnonymousAttribute>().Any();
        if (allowAnonymous)
        {
            return;
        }

        if (context.HttpContext.Request.Headers.TryGetValue("X-Tenant-Hub-Authorization", out var token))
        {
            if (string.IsNullOrEmpty(token) || !await IsValidTokenAsync(token))
            {
                context.Result =
                    new JsonResult(
                        new
                        {
                            message = "Unauthorized"
                        })
                    {
                        StatusCode = StatusCodes.Status401Unauthorized
                    };
            }
            else
            {
                return;
            }
        }

        context.Result = new NotFoundResult();
    }

    private async Task<bool> IsValidTokenAsync(string jwt)
    {
        try
        {
            var jwtSecurityTokenHandler = new JwtSecurityTokenHandler();
            var tokenValidationParameters = new TokenValidationParameters
            {
                ValidateLifetime = true,
                ValidateAudience = false,
                ValidateIssuer = false,
                RequireExpirationTime = true,
                RequireSignedTokens = true,
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes(SecretKey))
            };

            return (await jwtSecurityTokenHandler.ValidateTokenAsync(
                jwt.StartsWith("Bearer ")
                    ? jwt.Substring(7)
                    : jwt,
                tokenValidationParameters)).IsValid;
        }
        catch
        {
            return false;
        }
    }
}
