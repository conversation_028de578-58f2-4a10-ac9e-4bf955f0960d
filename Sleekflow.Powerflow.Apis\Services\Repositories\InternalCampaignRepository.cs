using Microsoft.EntityFrameworkCore;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Database;
using Travis_backend.Extensions;

namespace Sleekflow.Powerflow.Apis.Services.Repositories;

public interface IInternalCampaignRepository
{
    Task<(List<CompanyMessageTemplate> BroadcastCampaigns, int TotalCount)> FindBroadcastCampaignsAsync(
        List<string> companyIds,
        bool isCompanyFilterApplied,
        DateTimeOffset? startDate = null,
        DateTimeOffset? endDate = null,
        bool? isSent = null,
        List<string> statuses = null,
        List<string> channels = null,
        int? offset = null,
        int? limit = null,
        string sortBy = "sentAt",
        string sortOrder = "desc",
        bool enableNoTracking = false);
}

public class InternalCampaignRepository : IInternalCampaignRepository
{
    private readonly ApplicationDbContext _appDbContext;

    public InternalCampaignRepository(ApplicationDbContext appDbContext)
    {
        _appDbContext = appDbContext;
    }

    public async Task<(List<CompanyMessageTemplate> BroadcastCampaigns, int TotalCount)> FindBroadcastCampaignsAsync(
        List<string> companyIds,
        bool isCompanyFilterApplied,
        DateTimeOffset? startDate = null,
        DateTimeOffset? endDate = null,
        bool? isSent = null,
        List<string> statuses = null,
        List<string> channels = null,
        int? offset = null,
        int? limit = null,
        string sortBy = "sentAt",
        string sortOrder = "desc",
        bool enableNoTracking = false)
    {
        var companyCampaignMessagesQueryable =
            FilterCompanyCampaignMessages(
                isSent,
                startDate,
                endDate,
                statuses,
                companyIds,
                isCompanyFilterApplied);

        var companyCampaignMessagesCount = companyCampaignMessagesQueryable.Count();

        if (enableNoTracking)
        {
            companyCampaignMessagesQueryable = companyCampaignMessagesQueryable.AsNoTracking();
        }

        companyCampaignMessagesQueryable =
            SortCompanyCampaignMessages(companyCampaignMessagesQueryable, sortBy, sortOrder);

        if (!channels.IsNullOrEmpty())
        {
            // Since the channels is inside the json string of TargetedChannelModel. We cannot use LINQ expression to filter the condition
            companyCampaignMessagesQueryable = companyCampaignMessagesQueryable
                .Include(x => x.SavedBy)
                .ThenInclude(x => x.Identity)
                .Include(x => x.LastSentBy)
                .ThenInclude(x => x.Identity);

            var companyMessageTemplates = await companyCampaignMessagesQueryable.ToListAsync();

            return FilterCompanyCampaignMessagesByChannels(companyMessageTemplates, channels, offset, limit);
        }

        companyCampaignMessagesQueryable =
            PaginateCompanyCampaignMessages(companyCampaignMessagesQueryable, offset, limit);

        companyCampaignMessagesQueryable = companyCampaignMessagesQueryable
            .Include(x => x.SavedBy)
            .ThenInclude(x => x.Identity)
            .Include(x => x.LastSentBy)
            .ThenInclude(x => x.Identity);

        var companyCampaignMessages = await companyCampaignMessagesQueryable.ToListAsync();

        return (companyCampaignMessages, companyCampaignMessagesCount);
    }


    #region FindBroadcastCampaignsAsync helper methods

    private IQueryable<CompanyMessageTemplate> FilterCompanyCampaignMessages(
        bool? isSent,
        DateTimeOffset? startDate,
        DateTimeOffset? endDate,
        List<string> statuses,
        List<string> companyIds,
        bool isCompanyFilterApplied)
    {
        return _appDbContext.CompanyMessageTemplates
            .WhereIf(isCompanyFilterApplied && companyIds is not null, x => companyIds.Contains(x.CompanyId))
            .WhereIf(isSent is true, x => x.SentAt.HasValue)
            .WhereIf(isSent is false, x => x.SentAt == null)
            .WhereIf(startDate.HasValue, x => x.SentAt.HasValue && x.SentAt >= startDate)
            .WhereIf(endDate.HasValue, x => x.SentAt.HasValue && x.SentAt <= endDate)
            .WhereIf(!statuses.IsNullOrEmpty(), x => statuses.Contains(x.Status));
    }

    private static IQueryable<CompanyMessageTemplate> PaginateCompanyCampaignMessages(
        IQueryable<CompanyMessageTemplate> queryable,
        int? offset,
        int? limit)
    {
        if (offset.HasValue)
        {
            queryable = queryable.Skip(offset.Value);
        }

        if (limit.HasValue)
        {
            queryable = queryable.Take(limit.Value);
        }

        return queryable;
    }

    private static IQueryable<CompanyMessageTemplate> SortCompanyCampaignMessages(
        IQueryable<CompanyMessageTemplate> queryable,
        string sortBy,
        string sortOrder)
    {
        return sortBy.ToLower() switch
        {
            "sentat" => sortOrder.ToLower() == "desc"
                ? queryable.OrderByDescending(cmt => cmt.SentAt)
                : queryable.OrderBy(cmt => cmt.SentAt),
            _ => queryable
        };
    }

    private static (List<CompanyMessageTemplate> CompanyMessageTemplates, int TotalCount)
        FilterCompanyCampaignMessagesByChannels(
            List<CompanyMessageTemplate> companyMessageTemplates,
            List<string> channels,
            int? offset = null,
            int? limit = null)
    {
        var companyCampaignMessagesFilteredByChannel = companyMessageTemplates
            .Where(x => channels.Contains(x.TargetedChannel.ChannelType))
            .ToList();

        var totalCount = companyCampaignMessagesFilteredByChannel.Count;

        if (offset.HasValue)
        {
            companyCampaignMessagesFilteredByChannel =
                companyCampaignMessagesFilteredByChannel.Skip(offset.Value).ToList();
        }

        if (limit.HasValue)
        {
            companyCampaignMessagesFilteredByChannel =
                companyCampaignMessagesFilteredByChannel.Take(limit.Value).ToList();
        }

        return (companyCampaignMessagesFilteredByChannel, totalCount);
    }

    #endregion
}