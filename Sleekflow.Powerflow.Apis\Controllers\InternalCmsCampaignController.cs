using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Configuration;
using Travis_backend.Helper.Pagination;
using GetSentBroadcastCampaignsStatisticsRequest = Sleekflow.Powerflow.Apis.ViewModels.InternalCmsCampaign.GetSentBroadcastCampaignsStatisticsRequest;
using IInternalCampaignMappingService = Sleekflow.Powerflow.Apis.Services.Campaigns.IInternalCampaignMappingService;
using IInternalCampaignService = Sleekflow.Powerflow.Apis.Services.Campaigns.IInternalCampaignService;
using InternalBroadcastCampaignDto = Sleekflow.Powerflow.Apis.ViewModels.InternalCmsCampaign.InternalBroadcastCampaignDto;

namespace Sleekflow.Powerflow.Apis.Controllers;

[Route("/internal/campaign/[action]")]
[Authorize(Roles = ApplicationUserRole.InternalCmsUser)] // Basic Role Requirement
public class InternalCmsCampaignController : InternalControllerBase
{
    private readonly IInternalCampaignService _internalCampaignService;
    private readonly IInternalCampaignMappingService _internalCampaignMappingService;

    public InternalCmsCampaignController(
        UserManager<ApplicationUser> userManager,
        IInternalCampaignService internalCampaignService,
        IInternalCampaignMappingService internalCampaignMappingService)
        : base(userManager)
    {
        _internalCampaignService = internalCampaignService;
        _internalCampaignMappingService = internalCampaignMappingService;
    }

    [HttpPost]
    public async Task<IActionResult> GetSentBroadcastCampaignsStatisticsAsync(
        [FromBody]
        GetSentBroadcastCampaignsStatisticsRequest request)
    {
        var results = await _internalCampaignService.FindSentBroadcastCampaignsAsync(
            companyName: request.CompanyName,
            companyIds: request.CompanyIds,
            startDate: request.StartDate,
            endDate: request.EndDate,
            channels: request.Channels,
            countries: request.Countries,
            request.Industries,
            request.HubSpotIndustry,
            offset: request.Offset,
            limit: request.Limit,
            sortBy: request.SortBy,
            sortOrder: request.SortOrder,
            enableNoTracking: true);

        var internalBroadcastCampaignDtos =
            await _internalCampaignMappingService.MapBroadcastCampaignsToInternalBroadcastCampaignDtos(
                results.BroadcastCampaigns);

        var result = new PagedResult<InternalBroadcastCampaignDto>(
            page: (request.Offset / request.Limit) + 1,
            pageSize: request.Limit,
            totalRecords: results.TotalRecords,
            data: internalBroadcastCampaignDtos);

        return Ok(result);
    }
}