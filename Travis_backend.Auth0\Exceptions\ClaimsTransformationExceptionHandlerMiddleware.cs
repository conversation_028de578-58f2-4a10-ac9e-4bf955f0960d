﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.IdentityModel.Tokens;
using Travis_backend.Exceptions;

namespace Travis_backend.Auth0.Exceptions;

public class ClaimsTransformationExceptionHandlerMiddleware
{
    private readonly RequestDelegate _next;

    public ClaimsTransformationExceptionHandlerMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext httpContext, IClaimsTransformation claimsTransformation)
    {
        try
        {
            await _next(httpContext);
        }
        catch (Exception ex)
        {
            // You can add a specific exception type or check the exception message
            // to ensure it's the expected exception from IClaimsTransformation
            if (ex is SleekflowDiveAuthenticationException)
            {
                httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await httpContext.Response.WriteAsync("Unauthorized");
            }
            else if (ex is SecurityTokenException)
            {
                httpContext.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await httpContext.Response.WriteAsync("Unauthorized");
            }
            else
            {
                throw;
            }
        }
    }
}