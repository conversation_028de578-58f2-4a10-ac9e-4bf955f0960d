using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Travis_backend.Cache;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.PartnerStackIntegrationDomain.Clients;
using Travis_backend.PartnerStackIntegrationDomain.Services;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ResellerDomain.ViewModels;


namespace Travis_backend.InternalDomain.Services;

public interface IInternalPartnerStackService
{
    Task<ResponseWrapper> CreateOrUpdatePartnerStackCustomerMapping(string companyId, string customerKey);

    Task<ResponseWrapper> PowerflowSyncMrrToPartnerStack(string companyId);

    Task<ResponseWrapper> UpdatePartnerStackPartnerKey(string companyId, string partnerKey);

    Task SyncPartnerStackCustomerKeyFromHubSpotContact(string companyId, string userEmail);

    Task SyncAllMrrToPartnerStack();

    Task SyncPartnerStackPartnerKeyFromHubSpotContact(string companyId, string userEmail);

    Task<ResponseWrapper> PowerflowUpdatePartnerStackIndividualCommissionConfig(
        string companyId,
        string syncType,
        int? individualCommissionRate,
        string commissionEndDate);

    Task<ResponseWrapper> SyncPartnerStackInformation(string companyId);

    Task SyncPartnerStackInformationInBackground();

    void SyncAllPartnerStackInformation();

    Task<List<string>> GetAllPartnerStackGroups();

    Task<ResponseWrapper> GetCmsPartnerStackTransactions(string companyId);

    Task<List<CmsCompanyPartnerStackTransactionDto>> GetAllCompaniesCmsPartnerStackTransactions(
        bool isAllowTransactionCache);

    Task<ResponseWrapper> GetPartnerStackCompanies(GetPartnerStackCompaniesRequest request);

    Task SendContractEndDateNotifications();

    Task<ResponseWrapper> UpdatePartnerCountry(string companyId, string country);

    Task<ResponseWrapper> UpdatePartnershipDealOwnerId(string companyId, string partnershipDealOwnerId);

    Task<ResponseWrapper> GetPartnerStackSelections();
}

public class InternalPartnerStackService : IInternalPartnerStackService
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<InternalPartnerStackService> _logger;
    private readonly IPartnerStackIntegrationService _partnerStackIntegrationService;
    private readonly IInternalHubspotRepository _internalHubspotRepository;
    private readonly IConfiguration _configuration;
    private readonly bool _isHubSpotEnable;
    private readonly ICacheManagerService _cacheManagerService;

    private readonly IBillRecordRevenueCalculatorService _billRecordRevenueCalculatorService;

    private readonly ITimezoneAwareMrrCalculationService _timezoneAwareMrrCalculationService;

    public InternalPartnerStackService(
        ApplicationDbContext appDbContext,
        IMapper mapper,
        ILogger<InternalPartnerStackService> logger,
        IPartnerStackIntegrationService partnerStackIntegrationService,
        IInternalHubspotRepository internalHubspotRepository,
        IConfiguration configuration,
        ICacheManagerService cacheManagerService,
        IBillRecordRevenueCalculatorService billRecordRevenueCalculatorService,
        ITimezoneAwareMrrCalculationService timezoneAwareMrrCalculationService)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _logger = logger;
        _partnerStackIntegrationService = partnerStackIntegrationService;
        _internalHubspotRepository = internalHubspotRepository;
        _configuration = configuration;
        _isHubSpotEnable = !string.IsNullOrWhiteSpace(_configuration["HubSpot:InternalHubSpotApiKey"]);
        _cacheManagerService = cacheManagerService;
        _billRecordRevenueCalculatorService = billRecordRevenueCalculatorService;
        _timezoneAwareMrrCalculationService = timezoneAwareMrrCalculationService;
    }

    public async Task<ResponseWrapper> CreateOrUpdatePartnerStackCustomerMapping(
        string companyId,
        string customerKey)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == companyId);

            if (company == null || company.IsDeleted)
            {
                response.ErrorMsg = "Error: Company not found or deleted";
                return response;
            }

            // Check if the customer key already mapped to a company
            var existedPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .FirstOrDefaultAsync(x => x.PartnerStackCustomerKey == customerKey);

            if (existedPartnerStackCustomerMap != null)
            {
                response.ErrorMsg = "Error: Customer key already mapped to a company";
                return response;
            }

            // Check if the Partner Stack customer exists
            var partnerStackCustomer =
                await _partnerStackIntegrationService.RetrievePartnerStackCustomerByCustomerKey(customerKey);

            if (partnerStackCustomer == null)
            {
                response.ErrorMsg = "Error: PartnerStack customer not found";
                return response;
            }

            // Check if the Partner Stack customer is a staff of the company
            var existedCompanyStaff = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.Identity.Email == partnerStackCustomer.Email);

            if (existedCompanyStaff == null)
            {
                response.ErrorMsg = "Error: PartnerStack customer is not a staff of the company";
                return response;
            }

            // Check if the Partner Stack partner exists
            var partnerStackPartner =
                await _partnerStackIntegrationService.RetrievePartnerStackPartnershipByUniqueIdentifier(
                    partnerStackCustomer.PartnerKey);

            if (partnerStackPartner == null)
            {
                response.ErrorMsg = "Error: PartnerStack partner not found";
                return response;
            }

            // Get country from HubSpot contact using partner email
            var country = await GetCountryFromHubSpotContactAsync(partnerStackPartner.Email);

            var newPartnerStackPartnerInformation = new PartnerStackPartnerInformation
            {
                PartnerKey = partnerStackPartner.PartnerKey,
                GroupName = partnerStackPartner.Group.Name,
                TeamName = partnerStackPartner.Team.Name,
                Email = partnerStackPartner.Email,
                Country = country
            };

            var newPartnerStackCustomerInformation = new PartnerStackCustomerInformation
            {
                CreatedAt = partnerStackCustomer.CreatedAt,
                CustomerContractEndNotificationSentAt = null,
                PartnershipDealOwnerId = await GetContactOwnerIdFromHubSpotDealAsync(companyId)
            };

            // Update the company's Partner Stack customer mapping
            var currentCompanyPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (currentCompanyPartnerStackCustomerMap != null)
            {
                currentCompanyPartnerStackCustomerMap.PartnerStackCustomerKey = customerKey;
                currentCompanyPartnerStackCustomerMap.PartnerStackPartnerInformation =
                    newPartnerStackPartnerInformation;
                currentCompanyPartnerStackCustomerMap.PartnerStackCustomerInformation =
                    newPartnerStackCustomerInformation;
                currentCompanyPartnerStackCustomerMap.UpdatedAt = DateTime.UtcNow;
                response.Data = _mapper.Map<CmsPartnerStackCustomerMapDto>(currentCompanyPartnerStackCustomerMap);
            }
            else
            {
                var newCompanyPartnerStackCustomerMap = new CmsPartnerStackCustomerMap
                {
                    CompanyId = companyId,
                    PartnerStackCustomerKey = customerKey,
                    IndividualCommissionConfig = new IndividualCommissionConfig
                    {
                        SyncType = PartnerStackConstants.MrrSyncType,
                        IndividualCommissionRate = null,
                        CommissionEndDate = null
                    },
                    PartnerStackPartnerInformation = newPartnerStackPartnerInformation,
                    PartnerStackCustomerInformation = newPartnerStackCustomerInformation
                };

                await _appDbContext.CmsPartnerStackCustomerMaps.AddAsync(newCompanyPartnerStackCustomerMap);
                response.Data = _mapper.Map<CmsPartnerStackCustomerMapDto>(newCompanyPartnerStackCustomerMap);
            }

            await _appDbContext.SaveChangesAsync();

            response.IsSuccess = true;
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Create or Update Partner Stack Customer Mapping failed, Company ID: {CompanyID}, Customer Key: {CustomerKey}: {ExceptionMessage}",
                nameof(CreateOrUpdatePartnerStackCustomerMapping),
                companyId,
                customerKey,
                ex.Message);

            response.ErrorMsg = "Error: Failed to create or update Partner Stack Customer Mapping";
            return response;
        }
    }

    public async Task<ResponseWrapper> PowerflowSyncMrrToPartnerStack(string companyId)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            // Check if the company got Partner Stack customer mapping
            var existedPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .Include(x => x.Company)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (existedPartnerStackCustomerMap == null)
            {
                response.ErrorMsg = "Error: Company does not have Partner Stack customer mapping";
                return response;
            }

            // Check if the company exists or not deleted
            if (existedPartnerStackCustomerMap.Company == null ||
                existedPartnerStackCustomerMap.Company.IsDeleted)
            {
                response.ErrorMsg = "Error: Company not found or deleted";
                return response;
            }

            var syncResult = await SyncMrrToPartnerStack(existedPartnerStackCustomerMap);

            if (syncResult.Message.Contains("Success"))
            {
                response.IsSuccess = true;
            }

            if (syncResult.TransactionAmountMismatches is { Count: > 0 })
            {
                BackgroundJob.Enqueue<IEmailNotificationService>(x => x.SendSystemAlertToSlackChannel(
                    $"[PartnerStack] PartnerStack Transaction Amount Mismatch",
                    GetTransactionMismatchAlertEmailMessage(
                        new List<SyncMrrToPartnerStackDto>
                        {
                            syncResult
                        }),
                    "partnerships-payment-issues",
                    "alert",
                    null,
                    false));
            }

            response.Data = syncResult.Message;
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Sync Mrr to PartnerStack failed, Company ID: {CompanyID}: {ExceptionMessage}",
                nameof(PowerflowSyncMrrToPartnerStack),
                companyId,
                ex.Message);

            response.ErrorMsg = "Error: Failed to sync MRR to PartnerStack";
            return response;
        }
    }

    public async Task<ResponseWrapper> UpdatePartnerStackPartnerKey(string companyId, string partnerKey)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            var resellerProfile = await _appDbContext.ResellerCompanyProfiles
                .Include(x => x.Company)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            // Check if the reseller profile exists
            if (resellerProfile == null)
            {
                response.ErrorMsg = "Error: Reseller profile not found";
                return response;
            }

            // Check if the company exists or not deleted
            if (resellerProfile.Company == null || resellerProfile.Company.IsDeleted)
            {
                response.ErrorMsg = "Error: Company not found or deleted";
                return response;
            }

            // Check if the Partner Stack partner key is already mapped to a reseller company
            var existedPartnerStackPartnerResellerProfile = await _appDbContext.ResellerCompanyProfiles
                .FirstOrDefaultAsync(x => x.PartnerStackPartnerKey == partnerKey);

            if (existedPartnerStackPartnerResellerProfile != null)
            {
                response.ErrorMsg = "Error: PartnerStack partner key already mapped to a reseller company";
                return response;
            }

            // Check if the Partner Stack partnership exists
            var partnerStackPartnership =
                await _partnerStackIntegrationService.RetrievePartnerStackPartnershipByUniqueIdentifier(partnerKey);

            if (partnerStackPartnership == null)
            {
                response.ErrorMsg = "Error: PartnerStack partnership not found";
                return response;
            }

            // Check if the Partner Stack partnership email is a reseller staff of the company
            var existedResellerStaff = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(x =>
                    x.CompanyId == companyId && x.Identity.Email == partnerStackPartnership.Email);

            if (existedResellerStaff == null)
            {
                response.ErrorMsg = "Error: PartnerStack partnership is not a staff of the company";
                return response;
            }

            // Update the company's Partner Stack partnership key
            resellerProfile.PartnerStackPartnerKey = partnerKey;
            resellerProfile.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            response.IsSuccess = true;
            response.Data = _mapper.Map<ResellerProfileInformation>(resellerProfile);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Update Partner Stack Partner Key failed, Company ID: {CompanyID}: {ExceptionMessage}",
                nameof(UpdatePartnerStackPartnerKey),
                companyId,
                ex.Message);

            response.ErrorMsg = "Error: Failed to update Partner Stack Partner Key";
            return response;
        }
    }

    private async Task<SyncMrrToPartnerStackDto> SyncMrrToPartnerStack(
        CmsPartnerStackCustomerMap cmsPartnerStackCustomerMap)
    {
        if (cmsPartnerStackCustomerMap.IndividualCommissionConfig == null)
        {
            cmsPartnerStackCustomerMap.IndividualCommissionConfig = new IndividualCommissionConfig
            {
                SyncType = PartnerStackConstants.MrrSyncType,
                IndividualCommissionRate = null,
                CommissionEndDate = null
            };

            await _appDbContext.SaveChangesAsync();
        }

        // Check if the Partner Stack customer exists
        var partnerStackCustomer = await _partnerStackIntegrationService.RetrievePartnerStackCustomerByCustomerKey(
            cmsPartnerStackCustomerMap.PartnerStackCustomerKey);

        if (partnerStackCustomer == null)
        {
            return new SyncMrrToPartnerStackDto
            {
                SleekFlowCompanyId = cmsPartnerStackCustomerMap.CompanyId,
                SleekFlowCompanyName = cmsPartnerStackCustomerMap.Company.CompanyName ?? string.Empty,
                PartnerStackCustomerKey = cmsPartnerStackCustomerMap.PartnerStackCustomerKey,
                Message = "Error: PartnerStack customer not found"
            };
        }

        // Get the Latest 10 Transactions from PartnerStack
        var partnerStackTransactions = await _partnerStackIntegrationService.ListPartnerStackTransactions(
            customerExternalKey: cmsPartnerStackCustomerMap.PartnerStackCustomerKey);

        if (partnerStackTransactions == null)
        {
            return new SyncMrrToPartnerStackDto
            {
                SleekFlowCompanyId = cmsPartnerStackCustomerMap.CompanyId,
                SleekFlowCompanyName = cmsPartnerStackCustomerMap.Company.CompanyName ?? string.Empty,
                PartnerStackCustomerKey = cmsPartnerStackCustomerMap.PartnerStackCustomerKey,
                Message = "Error: Failed to get PartnerStack transactions"
            };
        }

        const int syncDayOfMonth = 28;
        var isLastYearInvolved = false;
        var currentDateTime = DateTime.UtcNow;
        var currentQuarterMonths = GetCurrentQuarterMonths(currentDateTime);
        var firstMonthOfQuarter = currentQuarterMonths[0];

        if (firstMonthOfQuarter == 12)
        {
            isLastYearInvolved = true;
        }

        var newTransactions = new List<CreatePartnerStackTransactionRequest>();
        var transactionAmountMismatches = new List<TransactionAmountMismatchDto>();

        var companyBillRecords = await _appDbContext.CompanyBillRecords
            .Include(x => x.CmsSalesPaymentRecords)
            .Where(x => x.CompanyId == cmsPartnerStackCustomerMap.CompanyId &&
                        x.Status != BillStatus.Inactive)
            .AsNoTracking()
            .ToListAsync();

        foreach (var month in currentQuarterMonths)
        {
            var categoryKey = GetCurrentMonthlySyncCategoryKey(
                cmsPartnerStackCustomerMap.IndividualCommissionConfig.SyncType,
                cmsPartnerStackCustomerMap.IndividualCommissionConfig.IndividualCommissionRate);

            var year = isLastYearInvolved && month == 12 ? currentDateTime.Year - 1 : currentDateTime.Year;

            var currentMonthSyncDateTime = new DateTime(
                year,
                month,
                syncDayOfMonth,
                0,
                0,
                0,
                DateTimeKind.Utc);

            // If the company created after the sync date, skip the sync
            if (currentMonthSyncDateTime < cmsPartnerStackCustomerMap.Company.CreatedAt.Date)
            {
                continue;
            }

            // If the sync date is in the future, stop the sync
            if (currentMonthSyncDateTime > currentDateTime)
            {
                break;
            }

            if (cmsPartnerStackCustomerMap.IndividualCommissionConfig.CommissionEndDate.HasValue &&
                currentMonthSyncDateTime >
                cmsPartnerStackCustomerMap.IndividualCommissionConfig.CommissionEndDate.Value)
            {
                categoryKey = GetCurrentMonthlySyncCategoryKey(
                    cmsPartnerStackCustomerMap.IndividualCommissionConfig.SyncType,
                    0);
            }

            int transactionAmount;

            if (cmsPartnerStackCustomerMap.IndividualCommissionConfig.SyncType == PartnerStackConstants.MrrSyncType)
            {
                var currentMonthMrr = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                    companyBillRecords,
                    cmsPartnerStackCustomerMap.Company?.TimeZoneInfoId,
                    currentMonthSyncDateTime);

                transactionAmount = Convert.ToInt32(Math.Round(currentMonthMrr, 2) * 100);
            }
            else
            {
                var eligibleCommissionPeriodEndDate =
                    cmsPartnerStackCustomerMap.IndividualCommissionConfig.CommissionEndDate;

                var totalRevenueInLastMonth = 0.00M;
                var start = currentMonthSyncDateTime.AddMonths(-1);
                var end = currentMonthSyncDateTime.AddDays(-1);

                // Find the bill records started between last month 28th to this month 27th UTC
                var billRecordsStartFromLastMonth = GetLastMonthBillRecords(companyBillRecords, start, end);

                if (billRecordsStartFromLastMonth.Count > 0)
                {
                    totalRevenueInLastMonth += billRecordsStartFromLastMonth.Sum(billRecord =>
                        CalculateActualRevenue(billRecord, eligibleCommissionPeriodEndDate));
                }

                transactionAmount = Convert.ToInt32(Math.Round(totalRevenueInLastMonth, 2) * 100);
            }

            // Check if the transaction already exists
            var productKey = GetCurrentMonthlySyncProductKey(
                cmsPartnerStackCustomerMap.IndividualCommissionConfig.SyncType,
                currentMonthSyncDateTime.ToString("yyyy-MM-dd"));

            var existedTransaction = partnerStackTransactions.Items
                .Find(x => x.ProductKey == productKey);

            if (existedTransaction != null)
            {
                if (transactionAmount != existedTransaction.Amount)
                {
                    transactionAmountMismatches.Add(
                        new TransactionAmountMismatchDto
                        {
                            PartnerStackTransactionCategoryKey = categoryKey,
                            PartnerStackTransactionProductKey = productKey,
                            PartnerStackAmount = Convert.ToDecimal(existedTransaction.Amount) / 100,
                            SleekFlowAmount = Convert.ToDecimal(transactionAmount) / 100
                        });
                }

                continue;
            }

            // If the month is the first month of the quarter, skip the sync
            if (month == firstMonthOfQuarter)
            {
                continue;
            }

            // Create a new transaction
            var newTransaction = new CreatePartnerStackTransactionRequest(
                cmsPartnerStackCustomerMap.PartnerStackCustomerKey,
                transactionAmount,
                "USD",
                categoryKey,
                productKey);

            newTransactions.Add(newTransaction);
        }

        if (newTransactions.Count == 0)
        {
            return new SyncMrrToPartnerStackDto
            {
                SleekFlowCompanyId = cmsPartnerStackCustomerMap.CompanyId,
                SleekFlowCompanyName = cmsPartnerStackCustomerMap.Company.CompanyName ?? string.Empty,
                PartnerStackCustomerKey = cmsPartnerStackCustomerMap.PartnerStackCustomerKey,
                Message = "Success: No new MRR to sync",
                TransactionAmountMismatches = transactionAmountMismatches
            };
        }

        var succeedCount = 0;
        var errorCount = 0;
        var errorProductKeys = new List<string>();
        var syncMrrTransactionToPartnerStackResults = new List<SyncMrrTransactionToPartnerStackResult>();

        foreach (var newTransaction in newTransactions)
        {
            var createdTransaction = await _partnerStackIntegrationService.CreatePartnerStackTransaction(
                newTransaction.CustomerExternalKey,
                newTransaction.Amount,
                newTransaction.Currency,
                newTransaction.CategoryKey,
                newTransaction.ProductKey);

            if (createdTransaction == null)
            {
                _logger.LogError(
                    "[PartnerStack {MethodName}] Company ID: {CompanyID}: Failed to sync mrr to PartnerStack, Product Key: {ProductKey}",
                    nameof(SyncMrrToPartnerStack),
                    cmsPartnerStackCustomerMap.CompanyId,
                    newTransaction.ProductKey);

                errorCount++;
                errorProductKeys.Add(newTransaction.ProductKey);
                syncMrrTransactionToPartnerStackResults.Add(
                    new SyncMrrTransactionToPartnerStackResult
                    {
                        IsSuccess = false,
                        PartnerStackTransactionCategoryKey = newTransaction.CategoryKey,
                        PartnerStackTransactionProductKey = newTransaction.ProductKey,
                    });
            }
            else
            {
                succeedCount++;
                syncMrrTransactionToPartnerStackResults.Add(
                    new SyncMrrTransactionToPartnerStackResult
                    {
                        IsSuccess = true,
                        PartnerStackTransactionCategoryKey = newTransaction.CategoryKey,
                        PartnerStackTransactionProductKey = newTransaction.ProductKey,
                    });
            }
        }

        _logger.LogInformation(
            "SyncMrrToPartnerStack Completed:\n" +
            "--------------------------------------------\n" +
            "Total Mrr Synced: {TotalCount}\n" +
            "Number of Sync Succeed: {SucceedCount}\n" +
            "Number of Sync Error: {ErrorCount}\n\n",
            newTransactions.Count,
            succeedCount,
            errorCount);

        _logger.LogInformation(
            "SyncMrrToPartnerStack Sync Error Product Key: {ErrorProductKeys}",
            string.Join(", ", errorProductKeys));

        return new SyncMrrToPartnerStackDto
        {
            SleekFlowCompanyId = cmsPartnerStackCustomerMap.CompanyId,
            SleekFlowCompanyName = cmsPartnerStackCustomerMap.Company.CompanyName ?? string.Empty,
            PartnerStackCustomerKey = cmsPartnerStackCustomerMap.PartnerStackCustomerKey,
            Message = "Success: MRR sync to PartnerStack completed",
            Results = syncMrrTransactionToPartnerStackResults,
            TransactionAmountMismatches = transactionAmountMismatches
        };
    }

    public async Task SyncPartnerStackCustomerKeyFromHubSpotContact(string companyId, string userEmail)
    {
        try
        {
            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == companyId);

            // Check if the company exists or not deleted
            if (company == null || company.IsDeleted)
            {
                return;
            }

            // Check if the HubSpot Contact exists and has a customer key
            var customerKey = string.Empty;

            if (_isHubSpotEnable)
            {
                var existingContact =
                    await _internalHubspotRepository.GetContactObjectByEmailAsync(userEmail);

                if (existingContact != null && !string.IsNullOrEmpty(existingContact.CustomerKey))
                {
                    customerKey = existingContact.CustomerKey;
                }
            }

            if (string.IsNullOrEmpty(customerKey))
            {
                var partnerStackCustomers = await _partnerStackIntegrationService.ListPartnerStackCustomers(
                    limit: 100);

                if (partnerStackCustomers != null && partnerStackCustomers.Items.Count > 0)
                {
                    customerKey = partnerStackCustomers.Items.Find(x => x.Email == userEmail)?.CustomerKey ??
                                  string.Empty;
                }
            }

            if (string.IsNullOrEmpty(customerKey))
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] HubSpot contact not found with email: {Email}",
                    nameof(SyncPartnerStackCustomerKeyFromHubSpotContact),
                    userEmail);
                return;
            }

            // Check if the customer key already mapped to a company
            var existedPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .FirstOrDefaultAsync(x => x.PartnerStackCustomerKey == customerKey);

            if (existedPartnerStackCustomerMap != null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] A company is already mapped with PartnerStack customer with customer key: {CustomerKey}",
                    nameof(SyncPartnerStackCustomerKeyFromHubSpotContact),
                    customerKey);
                return;
            }

            // Check if the Partner Stack customer exists
            var partnerStackCustomer =
                await _partnerStackIntegrationService.RetrievePartnerStackCustomerByCustomerKey(
                    customerKey);

            if (partnerStackCustomer == null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] PartnerStack customer not found with customer key: {CustomerKey}",
                    nameof(SyncPartnerStackCustomerKeyFromHubSpotContact),
                    customerKey);
                return;
            }

            // Check if the Partner Stack customer is a staff of the company
            var existedCompanyStaff = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.Identity.Email == partnerStackCustomer.Email);

            if (existedCompanyStaff == null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] PartnerStack customer is not a staff in the company with, Customer key: {CustomerKey}, Company ID: {CompanyID}",
                    nameof(SyncPartnerStackCustomerKeyFromHubSpotContact),
                    customerKey,
                    companyId);
                return;
            }

            var partnerStackPartner =
                await _partnerStackIntegrationService.RetrievePartnerStackPartnershipByUniqueIdentifier(
                    partnerStackCustomer.PartnerKey);

            if (partnerStackPartner == null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] PartnerStack partnership not found with partner key: {PartnerKey}",
                    nameof(SyncPartnerStackCustomerKeyFromHubSpotContact),
                    partnerStackCustomer.PartnerKey);
                return;
            }

            // Get country from HubSpot contact using partner email
            var country = await GetCountryFromHubSpotContactAsync(partnerStackPartner.Email);

            var newPartnerStackPartnerInformation = new PartnerStackPartnerInformation
            {
                PartnerKey = partnerStackPartner.PartnerKey,
                GroupName = partnerStackPartner.Group.Name,
                TeamName = partnerStackPartner.Team.Name,
                Email = partnerStackPartner.Email,
                Country = country
            };

            var newPartnerStackCustomerInformation = new PartnerStackCustomerInformation
            {
                CreatedAt = partnerStackCustomer.CreatedAt,
                CustomerContractEndNotificationSentAt = null,
                PartnershipDealOwnerId = await GetContactOwnerIdFromHubSpotDealAsync(companyId)
            };

            // Update the company's Partner Stack customer mapping
            var currentCompanyPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (currentCompanyPartnerStackCustomerMap != null)
            {
                currentCompanyPartnerStackCustomerMap.PartnerStackCustomerKey = customerKey;
                currentCompanyPartnerStackCustomerMap.PartnerStackPartnerInformation =
                    newPartnerStackPartnerInformation;
                currentCompanyPartnerStackCustomerMap.PartnerStackCustomerInformation =
                    newPartnerStackCustomerInformation;
                currentCompanyPartnerStackCustomerMap.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                var newCompanyPartnerStackCustomerMap = new CmsPartnerStackCustomerMap
                {
                    CompanyId = companyId,
                    PartnerStackCustomerKey = customerKey,
                    IndividualCommissionConfig = new IndividualCommissionConfig
                    {
                        SyncType = PartnerStackConstants.MrrSyncType,
                        IndividualCommissionRate = null,
                        CommissionEndDate = null
                    },
                    PartnerStackPartnerInformation = newPartnerStackPartnerInformation,
                    PartnerStackCustomerInformation = newPartnerStackCustomerInformation
                };

                await _appDbContext.CmsPartnerStackCustomerMaps.AddAsync(newCompanyPartnerStackCustomerMap);
            }

            await _appDbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Sync PartnerStack customer key from HubSpot contact failed, Company ID: {CompanyID}, Email: {Email}: {ExceptionMessage}",
                nameof(SyncPartnerStackCustomerKeyFromHubSpotContact),
                companyId,
                userEmail,
                ex.Message);
        }
    }

    public async Task SyncAllMrrToPartnerStack()
    {
        _logger.LogInformation("SyncAllMrrToPartnerStack Started");
        var cmsPartnerStackCustomerMaps = await _appDbContext.CmsPartnerStackCustomerMaps
            .Include(x => x.Company)
            .ToListAsync();

        var filteredCmsPartnerStackCustomerMaps = cmsPartnerStackCustomerMaps
            .Where(x => x.Company is { IsDeleted: false })
            .ToList();

        var succeedCount = 0;
        var errorCount = 0;
        var errorCompanyIds = new List<string>();
        var syncMrrToPartnerStackResults = new List<SyncMrrToPartnerStackDto>();

        foreach (var cmsPartnerStackCustomerMap in filteredCmsPartnerStackCustomerMaps)
        {
            if (cmsPartnerStackCustomerMap.Company == null ||
                cmsPartnerStackCustomerMap.Company.IsDeleted)
            {
                continue;
            }

            try
            {
                var syncResult = await SyncMrrToPartnerStack(cmsPartnerStackCustomerMap);

                if (syncResult.Message.Contains("Success"))
                {
                    succeedCount++;
                }
                else
                {
                    _logger.LogError(
                        "[PartnerStack {MethodName}] Company ID: {CompanyID}: {ErrorMessage}",
                        nameof(SyncAllMrrToPartnerStack),
                        cmsPartnerStackCustomerMap.CompanyId,
                        syncResult);

                    errorCount++;
                    errorCompanyIds.Add(cmsPartnerStackCustomerMap.CompanyId);
                }

                syncMrrToPartnerStackResults.Add(syncResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[PartnerStack {MethodName}] Company ID: {CompanyID}: {ExceptionMessage}",
                    nameof(SyncAllMrrToPartnerStack),
                    cmsPartnerStackCustomerMap.CompanyId,
                    ex.Message);

                errorCount++;
                errorCompanyIds.Add(cmsPartnerStackCustomerMap.CompanyId);
            }
        }

        _logger.LogInformation(
            "SyncAllMrrToPartnerStack Completed:\n" +
            "--------------------------------------------\n" +
            "Total Company: {TotalCompanyCount}\n" +
            "Number of Sync Succeed: {SucceedCount}\n" +
            "Number of Sync Error: {ErrorCount}\n\n",
            filteredCmsPartnerStackCustomerMaps.Count,
            succeedCount,
            errorCount);

        _logger.LogInformation(
            "SyncAllMrrToPartnerStack Sync Error Company: {ErrorCompanyIds}",
            string.Join(", ", errorCompanyIds));

        var serverLocation = _configuration["SF_REGION"] ?? string.Empty;

        BackgroundJob.Enqueue<IEmailNotificationService>(x => x.SendSystemAlertToSlackChannel(
            $"[PartnerStack] Monthly Sync MRR to PartnerStack Completed (Server: {serverLocation})",
            GetPartnerStackNotificationEmailMessage(
                cmsPartnerStackCustomerMaps.Count,
                succeedCount,
                errorCount,
                string.Join(", ", errorCompanyIds),
                syncMrrToPartnerStackResults),
            "partner-stack",
            "notification",
            null,
            false));

        var companiesWithTransactionMismatches = syncMrrToPartnerStackResults
            .Where(x => x.TransactionAmountMismatches is { Count: > 0 })
            .ToList();

        if (companiesWithTransactionMismatches.Count > 0)
        {
            BackgroundJob.Enqueue<IEmailNotificationService>(x => x.SendSystemAlertToSlackChannel(
                $"[PartnerStack] PartnerStack Transaction Amount Mismatch (Server: {serverLocation})",
                GetTransactionMismatchAlertEmailMessage(
                    companiesWithTransactionMismatches),
                "partnerships-payment-issues",
                "alert",
                null,
                false));
        }
    }

    public async Task SyncPartnerStackPartnerKeyFromHubSpotContact(string companyId, string userEmail)
    {
        try
        {
            var resellerProfile = await _appDbContext.ResellerCompanyProfiles
                .Include(x => x.Company)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            // Check if the reseller profile exists
            if (resellerProfile == null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] Reseller profile not found with Company ID: {CompanyID}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    companyId);
                return;
            }

            // Check if the company exists or not deleted
            if (resellerProfile.Company == null || resellerProfile.Company.IsDeleted)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] Company not found with ID: {CompanyID}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    companyId);
                return;
            }

            // Check if the HubSpot Contact exists
            if (!_isHubSpotEnable)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] HubSpot is not enabled, skipping partner key sync for email: {Email}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    userEmail);
                return;
            }

            var existingContact =
                await _internalHubspotRepository.GetContactObjectByEmailAsync(userEmail);

            if (existingContact == null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] HubSpot Contact not found with email: {Email}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    userEmail);
                return;
            }

            // Check if HubSpot Contact has a partner key
            if (string.IsNullOrEmpty(existingContact.PartnerStackPartnerKey))
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] HubSpot Contact partner key is empty with contact id: {ContactID}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    existingContact.Id);
                return;
            }

            // Check if the partner key already mapped to a company
            var existedPartnerStackPartnerResellerProfile = await _appDbContext.ResellerCompanyProfiles
                .FirstOrDefaultAsync(x => x.PartnerStackPartnerKey == existingContact.PartnerStackPartnerKey);

            if (existedPartnerStackPartnerResellerProfile != null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] A company is already mapped with PartnerStack partner with partner key: {PartnerKey}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    existingContact.PartnerStackPartnerKey);
                return;
            }

            // Check if the Partner Stack partnership exists
            var partnerStackPartnership =
                await _partnerStackIntegrationService.RetrievePartnerStackPartnershipByUniqueIdentifier(
                    existingContact.PartnerStackPartnerKey);

            if (partnerStackPartnership == null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] PartnerStack partnership not found with partner key: {PartnerKey}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    existingContact.PartnerStackPartnerKey);
                return;
            }

            // Check if the Partner Stack partner is a staff of the company
            var existedCompanyStaff = await _appDbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId && x.Identity.Email == existingContact.Email);

            if (existedCompanyStaff == null)
            {
                _logger.LogWarning(
                    "[PartnerStack {MethodName}] PartnerStack partner is not a staff in the company with, Partner key: {PartnerKey}, Company ID: {CompanyID}",
                    nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                    existingContact.PartnerStackPartnerKey,
                    companyId);
                return;
            }

            // Update the company's Partner Stack partnership key
            resellerProfile.PartnerStackPartnerKey = existingContact.PartnerStackPartnerKey;
            resellerProfile.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Sync PartnerStack partner key from HubSpot contact failed, Company ID: {CompanyID}, Email: {Email}: {ExceptionMessage}",
                nameof(SyncPartnerStackPartnerKeyFromHubSpotContact),
                companyId,
                userEmail,
                ex.Message);
        }
    }

    public async Task<ResponseWrapper> PowerflowUpdatePartnerStackIndividualCommissionConfig(
        string companyId,
        string syncType,
        int? individualCommissionRate,
        string commissionEndDate)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            var company = await _appDbContext.CompanyCompanies
                .FirstOrDefaultAsync(x => x.Id == companyId);

            if (company == null || company.IsDeleted)
            {
                response.ErrorMsg = "Error: Company not found or deleted";
                return response;
            }

            // Check if the company got Partner Stack customer mapping
            var existedPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (existedPartnerStackCustomerMap == null)
            {
                response.ErrorMsg = "Error: Company does not have Partner Stack customer mapping";
                return response;
            }

            var newIndividualCommissionConfig = new IndividualCommissionConfig
            {
                SyncType = PartnerStackConstants.GetAllSyncType.Contains(syncType)
                    ? syncType
                    : PartnerStackConstants.MrrSyncType,
                IndividualCommissionRate = individualCommissionRate,
                CommissionEndDate = !string.IsNullOrWhiteSpace(commissionEndDate)
                    ? DateTime.Parse(commissionEndDate, CultureInfo.InvariantCulture)
                    : null
            };

            existedPartnerStackCustomerMap.IndividualCommissionConfig = newIndividualCommissionConfig;
            existedPartnerStackCustomerMap.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            response.IsSuccess = true;
            response.Data = _mapper.Map<CmsPartnerStackCustomerMapDto>(existedPartnerStackCustomerMap);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Update Partner Stack individual commission config failed, Company ID: {CompanyID}: {ExceptionMessage}",
                nameof(PowerflowUpdatePartnerStackIndividualCommissionConfig),
                companyId,
                ex.Message);

            response.ErrorMsg = "Error: Failed to update Partner Stack individual commission config";
            return response;
        }
    }

    public async Task<ResponseWrapper> SyncPartnerStackInformation(
        string companyId)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            // Check if the company got Partner Stack customer mapping
            var existedPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (existedPartnerStackCustomerMap == null)
            {
                response.ErrorMsg = "Error: Company does not have Partner Stack customer mapping";
                return response;
            }

            // Check if the Partner Stack customer exists
            var partnerStackCustomer =
                await _partnerStackIntegrationService.RetrievePartnerStackCustomerByCustomerKey(
                    existedPartnerStackCustomerMap.PartnerStackCustomerKey);

            if (partnerStackCustomer == null)
            {
                response.ErrorMsg = "Error: PartnerStack customer not found";
                return response;
            }

            // Check if the Partner Stack partner exists
            var partnerStackPartner =
                await _partnerStackIntegrationService.RetrievePartnerStackPartnershipByUniqueIdentifier(
                    partnerStackCustomer.PartnerKey);

            if (partnerStackPartner == null)
            {
                response.ErrorMsg = "Error: PartnerStack partner not found";
                return response;
            }

            // Get country from HubSpot contact using partner email
            var country = await GetCountryFromHubSpotContactAsync(partnerStackPartner.Email);

            var newPartnerStackPartnerInformation = new PartnerStackPartnerInformation
            {
                PartnerKey = partnerStackPartner.PartnerKey,
                GroupName = partnerStackPartner.Group.Name,
                TeamName = partnerStackPartner.Team.Name,
                Email = partnerStackPartner.Email,
                Country = country
            };

            var newPartnerStackCustomerInformation = new PartnerStackCustomerInformation
            {
                CreatedAt = partnerStackCustomer.CreatedAt,
                CustomerContractEndNotificationSentAt =
                    existedPartnerStackCustomerMap.PartnerStackCustomerInformation
                        ?.CustomerContractEndNotificationSentAt,
                PartnershipDealOwnerId = await GetContactOwnerIdFromHubSpotDealAsync(companyId)
            };

            existedPartnerStackCustomerMap.PartnerStackPartnerInformation = newPartnerStackPartnerInformation;
            existedPartnerStackCustomerMap.PartnerStackCustomerInformation = newPartnerStackCustomerInformation;
            existedPartnerStackCustomerMap.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            response.IsSuccess = true;
            response.Data = _mapper.Map<CmsPartnerStackCustomerMapDto>(existedPartnerStackCustomerMap);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Sync Partner Stack partner information failed, Company ID: {CompanyID}: {ExceptionMessage}",
                nameof(SyncPartnerStackInformation),
                companyId,
                ex.Message);

            response.ErrorMsg = "Error: Failed to sync Partner Stack partner information";
            return response;
        }
    }

    public async Task SyncPartnerStackInformationInBackground()
    {
        _logger.LogInformation("SyncPartnerStackInformationInBackground Started");
        var cmsPartnerStackCustomerMaps = await _appDbContext.CmsPartnerStackCustomerMaps
            .Include(x => x.Company)
            .ToListAsync();

        var filteredCmsPartnerStackCustomerMaps = cmsPartnerStackCustomerMaps
            .Where(x => x.Company is { IsDeleted: false })
            .ToList();

        var succeedCount = 0;
        var errorCount = 0;
        var errorCompanyIds = new List<string>();

        foreach (var cmsPartnerStackCustomerMap in filteredCmsPartnerStackCustomerMaps)
        {
            try
            {
                // Check if the Partner Stack customer exists
                var partnerStackCustomer =
                    await _partnerStackIntegrationService.RetrievePartnerStackCustomerByCustomerKey(
                        cmsPartnerStackCustomerMap.PartnerStackCustomerKey);

                if (partnerStackCustomer == null)
                {
                    _logger.LogError(
                        "[PartnerStack {MethodName}] Company ID: {CompanyID}: PartnerStack customer not found with customer key: {CustomerKey}",
                        nameof(SyncPartnerStackInformationInBackground),
                        cmsPartnerStackCustomerMap.CompanyId,
                        cmsPartnerStackCustomerMap.PartnerStackCustomerKey);

                    errorCount++;
                    errorCompanyIds.Add(cmsPartnerStackCustomerMap.CompanyId);
                    continue;
                }

                // Check if the Partner Stack partner exists
                var partnerStackPartner =
                    await _partnerStackIntegrationService.RetrievePartnerStackPartnershipByUniqueIdentifier(
                        partnerStackCustomer.PartnerKey);

                if (partnerStackPartner == null)
                {
                    _logger.LogError(
                        "[PartnerStack {MethodName}] Company ID: {CompanyID}: PartnerStack partner not found with partner key: {PartnerKey}",
                        nameof(SyncPartnerStackInformationInBackground),
                        cmsPartnerStackCustomerMap.CompanyId,
                        partnerStackCustomer.PartnerKey);

                    errorCount++;
                    errorCompanyIds.Add(cmsPartnerStackCustomerMap.CompanyId);
                    continue;
                }

                // Get country from HubSpot contact using partner email
                var country = await GetCountryFromHubSpotContactAsync(partnerStackPartner.Email);

                var newPartnerStackPartnerInformation = new PartnerStackPartnerInformation
                {
                    PartnerKey = partnerStackPartner.PartnerKey,
                    GroupName = partnerStackPartner.Group.Name,
                    TeamName = partnerStackPartner.Team.Name,
                    Email = partnerStackPartner.Email,
                    Country = country
                };

                var newPartnerStackCustomerInformation = new PartnerStackCustomerInformation
                {
                    CreatedAt = partnerStackCustomer.CreatedAt,
                    CustomerContractEndNotificationSentAt =
                        cmsPartnerStackCustomerMap.PartnerStackCustomerInformation
                            ?.CustomerContractEndNotificationSentAt,
                    PartnershipDealOwnerId =
                        await GetContactOwnerIdFromHubSpotDealAsync(cmsPartnerStackCustomerMap.CompanyId)
                };

                cmsPartnerStackCustomerMap.PartnerStackPartnerInformation = newPartnerStackPartnerInformation;
                cmsPartnerStackCustomerMap.PartnerStackCustomerInformation = newPartnerStackCustomerInformation;
                cmsPartnerStackCustomerMap.UpdatedAt = DateTime.UtcNow;

                await _appDbContext.SaveChangesAsync();

                succeedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[PartnerStack {MethodName}] Company ID: {CompanyID}: {ExceptionMessage}",
                    nameof(SyncPartnerStackInformationInBackground),
                    cmsPartnerStackCustomerMap.CompanyId,
                    ex.Message);

                errorCount++;
                errorCompanyIds.Add(cmsPartnerStackCustomerMap.CompanyId);
            }
        }

        _logger.LogInformation(
            "SyncPartnerStackInformationInBackground Completed:\n" +
            "--------------------------------------------\n" +
            "Total Company: {TotalCompanyCount}\n" +
            "Number of Sync Succeed: {SucceedCount}\n" +
            "Number of Sync Error: {ErrorCount}\n\n",
            filteredCmsPartnerStackCustomerMaps.Count,
            succeedCount,
            errorCount);

        _logger.LogInformation(
            "SyncPartnerStackInformationInBackground Sync Error Company: {ErrorCompanyIds}",
            string.Join(", ", errorCompanyIds));
    }

    public void SyncAllPartnerStackInformation()
    {
        _logger.LogInformation("SyncAllPartnerStackInformation Started - Enqueuing background job");

        BackgroundJob.Enqueue<IInternalPartnerStackService>(x => x.SyncPartnerStackInformationInBackground());

        _logger.LogInformation("SyncAllPartnerStackInformation Completed - Background job enqueued successfully");
    }

    public async Task<List<string>> GetAllPartnerStackGroups()
    {
        bool hasMore;
        string lastGroupKey = null;
        var partnerStackGroups = new List<string>();

        do
        {
            var partnerStackGroupsResponse = await _partnerStackIntegrationService.ListPartnerStackGroups(
                startingAfter: lastGroupKey);

            if (partnerStackGroupsResponse == null)
            {
                break;
            }

            partnerStackGroups.AddRange(partnerStackGroupsResponse.Items.Select(x => x.Name));
            hasMore = partnerStackGroupsResponse.HasMore;
            lastGroupKey = hasMore ? partnerStackGroupsResponse.Items.LastOrDefault()?.Key : null;
        }
        while (hasMore);

        return partnerStackGroups;
    }

    public async Task<ResponseWrapper> GetCmsPartnerStackTransactions(string companyId)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        // Check if the company got Partner Stack customer mapping
        var cmsPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
            .Include(x => x.Company)
            .FirstOrDefaultAsync(x => x.CompanyId == companyId);

        if (cmsPartnerStackCustomerMap == null)
        {
            response.ErrorMsg = "Error: Company does not have Partner Stack customer mapping";
            return response;
        }

        var currentDateTime = DateTime.UtcNow;
        var isLastYearInvolved = false;
        var currentQuarterMonths = GetCurrentQuarterMonths(currentDateTime);
        const int syncDayOfMonth = 28;

        if (currentQuarterMonths[0] == 12)
        {
            isLastYearInvolved = true;
        }

        var transactions = new List<CmsPartnerStackTransactionDto>();

        var companyBillRecords = await _appDbContext.CompanyBillRecords
            .Include(x => x.CmsSalesPaymentRecords)
            .Where(x => x.CompanyId == cmsPartnerStackCustomerMap.CompanyId &&
                        x.Status != BillStatus.Inactive)
            .AsNoTracking()
            .ToListAsync();

        foreach (var month in currentQuarterMonths)
        {
            var year = isLastYearInvolved && month == 12 ? currentDateTime.Year - 1 : currentDateTime.Year;

            var categoryKey = GetCurrentMonthlySyncCategoryKey(
                cmsPartnerStackCustomerMap.IndividualCommissionConfig.SyncType,
                cmsPartnerStackCustomerMap.IndividualCommissionConfig.IndividualCommissionRate);

            var currentMonthSyncDateTime = new DateTime(
                year,
                month,
                syncDayOfMonth,
                0,
                0,
                0,
                DateTimeKind.Utc);

            // If the company created after the sync date, skip the sync
            if (currentMonthSyncDateTime < cmsPartnerStackCustomerMap.Company.CreatedAt.Date)
            {
                continue;
            }

            // If the sync date is in the future, stop the sync
            if (currentMonthSyncDateTime > currentDateTime)
            {
                break;
            }

            if (cmsPartnerStackCustomerMap.IndividualCommissionConfig.CommissionEndDate.HasValue &&
                currentMonthSyncDateTime >
                cmsPartnerStackCustomerMap.IndividualCommissionConfig.CommissionEndDate.Value)
            {
                categoryKey = GetCurrentMonthlySyncCategoryKey(
                    cmsPartnerStackCustomerMap.IndividualCommissionConfig.SyncType,
                    0);
            }

            // Check if the transaction already exists
            var productKey = GetCurrentMonthlySyncProductKey(
                cmsPartnerStackCustomerMap.IndividualCommissionConfig.SyncType,
                currentMonthSyncDateTime.ToString("yyyy-MM-dd"));

            decimal transactionAmount;

            if (cmsPartnerStackCustomerMap.IndividualCommissionConfig.SyncType == PartnerStackConstants.MrrSyncType)
            {
                var currentMonthMrr = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                    companyBillRecords,
                    cmsPartnerStackCustomerMap.Company?.TimeZoneInfoId,
                    currentMonthSyncDateTime);

                transactionAmount = Math.Round(currentMonthMrr, 2);
            }
            else
            {
                var totalRevenueInLastMonth = 0.00M;
                var start = currentMonthSyncDateTime.AddMonths(-1);
                var end = currentMonthSyncDateTime.AddDays(-1);

                // Find the bill records started between last month 28th to this month 27th UTC
                var billRecordsStartFromLastMonth = GetLastMonthBillRecords(companyBillRecords, start, end);

                if (billRecordsStartFromLastMonth.Count > 0)
                {
                    var eligibleCommissionPeriodEndDate =
                        cmsPartnerStackCustomerMap.IndividualCommissionConfig.CommissionEndDate;

                    totalRevenueInLastMonth += billRecordsStartFromLastMonth.Sum(billRecord =>
                         CalculateActualRevenue(billRecord, eligibleCommissionPeriodEndDate));
                }

                transactionAmount = Math.Round(totalRevenueInLastMonth, 2);
            }

            var transaction = new CmsPartnerStackTransactionDto
            {
                SyncDateTime = currentMonthSyncDateTime.ToString("yyyy-MM-dd"),
                CategoryKey = categoryKey,
                ProductKey = productKey,
                Amount = transactionAmount,
                Currency = "USD"
            };

            transactions.Add(transaction);
        }

        response.IsSuccess = true;
        response.Data = transactions;

        return response;
    }

    public async Task<List<CmsCompanyPartnerStackTransactionDto>> GetAllCompaniesCmsPartnerStackTransactions(
        bool isAllowTransactionCache)
    {
        const string cacheKey = "GetAllCompaniesCmsPartnerStackTransactions";
        const int cacheDurationHours = 1;

        if (isAllowTransactionCache)
        {
            try
            {
                var cachedData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);
                if (!string.IsNullOrWhiteSpace(cachedData))
                {
                    var cachedTransactions =
                        JsonConvert.DeserializeObject<List<CmsCompanyPartnerStackTransactionDto>>(cachedData);
                    if (cachedTransactions is { Count: > 0 })
                    {
                        return cachedTransactions;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    ex,
                    "[PartnerStack {MethodName}] Failed to get cached data, proceeding with calculation",
                    nameof(GetAllCompaniesCmsPartnerStackTransactions));
            }
        }

        // Pre-calculate common values to avoid repeated calculations
        var currentDateTime = DateTime.UtcNow;
        var currentQuarterMonths = GetCurrentQuarterMonths(currentDateTime);
        var isLastYearInvolved = currentQuarterMonths[0] == 12;
        const int syncDayOfMonth = 28;

        // Optimize database query with better filtering and selective loading
        var partnerStackCustomerMaps = await _appDbContext.CmsPartnerStackCustomerMaps
            .AsNoTracking()
            .AsSplitQuery() // Split complex query for better performance
            .Include(x => x.Company)
            .Include(x => x.Company.BillRecords.Where(br => br.Status != BillStatus.Inactive))
            .ThenInclude(x => x.CmsSalesPaymentRecords)
            .Select(x => new
            {
                x.CompanyId,
                x.IndividualCommissionConfig,
                x.PartnerStackPartnerInformation,
                x.PartnerStackCustomerInformation,
                x.Company.CreatedAt,
                x.Company.TimeZoneInfoId,
                BillRecords = x.Company.BillRecords.ToList()
            })
            .ToListAsync();

        // Pre-allocate result list with estimated capacity for better memory management
        var estimatedTransactionCount = partnerStackCustomerMaps.Count * currentQuarterMonths.Count;
        var allTransactions = new List<CmsCompanyPartnerStackTransactionDto>(estimatedTransactionCount);

        // Use object pooling for better memory efficiency
        var syncOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = Math.Min(Environment.ProcessorCount, 16)
        };

        // Process partner stack customer maps in parallel with optimized data structures
        var results = new ConcurrentBag<List<CmsCompanyPartnerStackTransactionDto>>();

        // Process partner stack customer maps in parallel with optimized data structures
        Parallel.ForEach(
            partnerStackCustomerMaps,
            syncOptions,
            partnerStackCustomerMap =>
            {
                try
                {
                    // Optimize bill records access - avoid unnecessary ToList() allocation
                    var companyBillRecords = partnerStackCustomerMap.BillRecords;
                    if (companyBillRecords == null || companyBillRecords.Count == 0)
                    {
                        return;
                    }

                    // Cache commission config to avoid repeated property access
                    var commissionConfig = partnerStackCustomerMap.IndividualCommissionConfig;
                    var companySyncType = commissionConfig.SyncType;
                    var companyCommissionRate = commissionConfig.IndividualCommissionRate;
                    var companyCommissionEndDate = commissionConfig.CommissionEndDate;
                    var companyCreatedAt = partnerStackCustomerMap.CreatedAt.Date;
                    var companyId = partnerStackCustomerMap.CompanyId;

                    // Pre-calculate category keys once
                    var normalCategoryKey = GetCurrentMonthlySyncCategoryKey(companySyncType, companyCommissionRate);
                    var zeroCommissionCategoryKey = GetCurrentMonthlySyncCategoryKey(companySyncType, 0);

                    // Pre-allocate with exact capacity and use array for better performance
                    var companyTransactions = new CmsCompanyPartnerStackTransactionDto[currentQuarterMonths.Count];
                    var transactionIndex = 0;

                    // Optimize month processing with direct array access
                    foreach (var month in currentQuarterMonths)
                    {
                        var year = isLastYearInvolved && month == 12 ? currentDateTime.Year - 1 : currentDateTime.Year;
                        var currentMonthSyncDateTime = new DateTime(
                            year,
                            month,
                            syncDayOfMonth,
                            0,
                            0,
                            0,
                            DateTimeKind.Utc);

                        // Early exit conditions
                        if (currentMonthSyncDateTime < companyCreatedAt || currentMonthSyncDateTime > currentDateTime)
                        {
                            continue;
                        }

                        // Optimize string operations with string interpolation
                        var dateString = $"{currentMonthSyncDateTime:yyyy-MM-dd}";
                        var productKey = GetCurrentMonthlySyncProductKey(companySyncType, dateString);
                        var categoryKey = companyCommissionEndDate.HasValue &&
                                          currentMonthSyncDateTime > companyCommissionEndDate.Value
                            ? zeroCommissionCategoryKey
                            : normalCategoryKey;

                        decimal transactionAmount;

                        if (companySyncType == PartnerStackConstants.MrrSyncType)
                        {
                            // Direct MRR calculation without intermediate variables
                            transactionAmount = Math.Round(
                                _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                                    companyBillRecords.ToList(),
                                    partnerStackCustomerMap.TimeZoneInfoId,
                                    currentMonthSyncDateTime),
                                2);
                        }
                        else
                        {
                            // Optimize revenue calculation with pre-calculated bounds
                            var start = currentMonthSyncDateTime.AddMonths(-1);
                            var end = currentMonthSyncDateTime.AddDays(-1);

                            // Use direct enumeration instead of LINQ for better performance
                            var totalRevenueInLastMonth = 0.00M;
                            foreach (var billRecord in companyBillRecords)
                            {
                                if (billRecord.PeriodStart.Date >= start && billRecord.PeriodStart.Date <= end)
                                {
                                    totalRevenueInLastMonth += CalculateActualRevenue(
                                        billRecord,
                                        companyCommissionEndDate);
                                }
                            }

                            transactionAmount = Math.Round(totalRevenueInLastMonth, 2);
                        }

                        // Create transaction object directly in array
                        companyTransactions[transactionIndex++] = new CmsCompanyPartnerStackTransactionDto
                        {
                            CompanyId = companyId,
                            SyncDateTime = dateString,
                            CategoryKey = categoryKey,
                            ProductKey = productKey,
                            Amount = transactionAmount,
                            Currency = "USD"
                        };
                    }

                    // Only add if we have transactions and convert array to list efficiently
                    if (transactionIndex <= 0)
                    {
                        return;
                    }

                    var finalTransactions = new List<CmsCompanyPartnerStackTransactionDto>(transactionIndex);
                    for (var i = 0; i < transactionIndex; i++)
                    {
                        finalTransactions.Add(companyTransactions[i]);
                    }

                    results.Add(finalTransactions);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[PartnerStack {MethodName}] Error processing company {CompanyId}: {ExceptionMessage}",
                        nameof(GetAllCompaniesCmsPartnerStackTransactions),
                        partnerStackCustomerMap.CompanyId,
                        ex.Message);
                }
            });

        // Combine all results efficiently
        foreach (var companyTransactions in results)
        {
            allTransactions.AddRange(companyTransactions);
        }

        // Cache the result asynchronously to avoid blocking the return
        _ = Task.Run(async () =>
        {
            try
            {
                await _cacheManagerService.SaveCacheWithConstantKeyAsync(
                    cacheKey,
                    allTransactions,
                    TimeSpan.FromHours(cacheDurationHours));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    ex,
                    "[PartnerStack {MethodName}] Failed to cache result",
                    nameof(GetAllCompaniesCmsPartnerStackTransactions));
            }
        });

        return allTransactions;
    }

    public async Task<ResponseWrapper> GetPartnerStackCompanies(GetPartnerStackCompaniesRequest request)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            // Get all transactions first for filtering
            var allTransactions = await GetAllCompaniesCmsPartnerStackTransactions(
                request.IsAllowTransactionCache);

            // Pre-filter transactions by TransactionSyncDates if provided
            if (request.TransactionSyncDates is { Count: > 0 })
            {
                // Create HashSet for O(1) lookup performance
                var transactionSyncDatesSet = new HashSet<string>(request.TransactionSyncDates);
                allTransactions = allTransactions
                    .Where(x => transactionSyncDatesSet.Contains(x.SyncDateTime))
                    .ToList();

                // Optimize company ID intersection using HashSet
                var transactionCompanyIds = new HashSet<string>(allTransactions.Select(x => x.CompanyId));
                request.CompanyIds = request.CompanyIds is { Count: > 0 }
                    ? request.CompanyIds.Where(id => transactionCompanyIds.Contains(id)).ToList()
                    : transactionCompanyIds.ToList();
            }

            // Build an optimized query with selective loading - only load what we need
            var partnerStackCompaniesQueryable = _appDbContext.CmsPartnerStackCustomerMaps
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.Company)
                .AsQueryable();

            // Apply filters in order of selectivity (most selective first)
            if (request.CompanyIds != null)
            {
                partnerStackCompaniesQueryable = partnerStackCompaniesQueryable
                    .Where(x => request.CompanyIds.Contains(x.CompanyId));
            }

            if (!string.IsNullOrWhiteSpace(request.CompanyName))
            {
                partnerStackCompaniesQueryable = partnerStackCompaniesQueryable
                    .Where(x => x.Company.CompanyName.Contains(request.CompanyName));
            }

            var partnerStackCompanies = await partnerStackCompaniesQueryable.ToListAsync();

            // Use HashSet for faster filtering operations
            if (request.CustomerKeys != null)
            {
                var customerKeysSet = new HashSet<string>(request.CustomerKeys);
                partnerStackCompanies = partnerStackCompanies
                    .Where(x => customerKeysSet.Contains(x.PartnerStackCustomerKey))
                    .ToList();
            }

            if (request.PartnerKeys != null)
            {
                var partnerKeysSet = new HashSet<string>(request.PartnerKeys);
                partnerStackCompanies = partnerStackCompanies
                    .Where(x => partnerKeysSet.Contains(x.PartnerStackPartnerInformation?.PartnerKey))
                    .ToList();
            }

            if (request.SyncTypes is { Count: > 0 })
            {
                var syncTypesSet = new HashSet<string>(request.SyncTypes);
                partnerStackCompanies = partnerStackCompanies
                    .Where(x => syncTypesSet.Contains(x.IndividualCommissionConfig?.SyncType))
                    .ToList();
            }

            if (request.GroupNames is { Count: > 0 })
            {
                var groupNamesSet = new HashSet<string>(request.GroupNames);
                partnerStackCompanies = partnerStackCompanies
                    .Where(x => groupNamesSet.Contains(x.PartnerStackPartnerInformation?.GroupName))
                    .ToList();
            }

            if (request.TeamNames is { Count: > 0 })
            {
                var teamNamesSet = new HashSet<string>(request.TeamNames);
                partnerStackCompanies = partnerStackCompanies
                    .Where(x => teamNamesSet.Contains(x.PartnerStackPartnerInformation?.TeamName))
                    .ToList();
            }

            if (request.PartnershipDealOwnerIds is { Count: > 0 })
            {
                var partnershipDealOwnerIdsSet = new HashSet<string>(request.PartnershipDealOwnerIds);
                partnerStackCompanies = partnerStackCompanies
                    .Where(x => partnershipDealOwnerIdsSet.Contains(
                        x.PartnerStackCustomerInformation?.PartnershipDealOwnerId))
                    .ToList();
            }

            // Early return if there are no results
            if (partnerStackCompanies.Count == 0)
            {
                response.IsSuccess = true;
                response.Data = new List<CmsPartnerStackCompanyListItemView>();
                return response;
            }

            // Pre-group transactions by company ID for O(1) lookup and cache the result
            var transactionsByCompanyId = allTransactions
                .GroupBy(x => x.CompanyId)
                .ToDictionary(g => g.Key, g => g.ToList());

            // Pre-allocate result list with exact capacity
            var result = new List<CmsPartnerStackCompanyListItemView>(partnerStackCompanies.Count);

            // Process companies efficiently with optimized object creation
            foreach (var partnerStackCompany in partnerStackCompanies)
            {
                var company = partnerStackCompany.Company;

                // Get transactions for this company with null coalescing for performance
                var companyTransactions = transactionsByCompanyId.TryGetValue(company.Id, out var transactions)
                    ? transactions
                    : new List<CmsCompanyPartnerStackTransactionDto>();

                // Convert transactions efficiently with pre-allocated capacity
                var formattedTransactions = new List<CmsPartnerStackTransactionDto>(companyTransactions.Count);
                formattedTransactions.AddRange(
                    companyTransactions.Select(t => new CmsPartnerStackTransactionDto
                    {
                        SyncDateTime = t.SyncDateTime,
                        CategoryKey = t.CategoryKey,
                        ProductKey = t.ProductKey,
                        Amount = t.Amount,
                        Currency = t.Currency
                    }));

                // Sort transactions once after creation
                formattedTransactions.Sort((a, b) => string.Compare(
                    a.SyncDateTime,
                    b.SyncDateTime,
                    StringComparison.Ordinal));

                var companyListItem = new CmsPartnerStackCompanyListItemView
                {
                    CompanyId = company.Id,
                    CompanyName = company.CompanyName,
                    PartnerStackCustomerKey = partnerStackCompany.PartnerStackCustomerKey,
                    IndividualCommissionConfig = partnerStackCompany.IndividualCommissionConfig,
                    PartnerStackPartnerInformation = partnerStackCompany.PartnerStackPartnerInformation,
                    PartnerStackCustomerInformation = partnerStackCompany.PartnerStackCustomerInformation,
                    PartnershipDealOwnerName =
                        string.IsNullOrWhiteSpace(
                            partnerStackCompany.PartnerStackCustomerInformation?.PartnershipDealOwnerId)
                            ? string.Empty
                            : await _appDbContext.Users
                                  .Where(x => x.Id == partnerStackCompany.PartnerStackCustomerInformation
                                      .PartnershipDealOwnerId).Select(x => x.DisplayName).FirstOrDefaultAsync() ??
                              string.Empty,
                    Transactions = formattedTransactions,
                    CreatedAt = company.CreatedAt,
                    CompanyType = company.CompanyType,
                    IsDeleted = company.IsDeleted
                };

                result.Add(companyListItem);
            }

            response.IsSuccess = true;
            response.Data = result;

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Get PartnerStack companies failed: {ExceptionMessage}",
                nameof(GetPartnerStackCompanies),
                ex.Message);

            response.ErrorMsg = "Error: Failed to get PartnerStack companies";
            return response;
        }
    }

    public async Task SendContractEndDateNotifications()
    {
        _logger.LogInformation("SendContractEndDateNotifications Started");
        var cmsPartnerStackCustomerMaps = await _appDbContext.CmsPartnerStackCustomerMaps
            .Include(x => x.Company)
            .ToListAsync();

        var filteredCmsPartnerStackCustomerMaps = cmsPartnerStackCustomerMaps
            .Where(x => x.Company is { IsDeleted: false } &&
                        x.PartnerStackCustomerInformation is { CustomerContractEndNotificationSentAt: null })
            .ToList();

        var totalCount = 0;
        var succeedCount = 0;
        var errorCount = 0;
        var errorCompanyIds = new List<string>();
        var currentDateTime = DateTime.UtcNow;

        foreach (var cmsPartnerStackCustomerMap in filteredCmsPartnerStackCustomerMaps)
        {
            // Check if the Partner Stack customer exists
            var partnerStackCustomer = await _partnerStackIntegrationService.RetrievePartnerStackCustomerByCustomerKey(
                cmsPartnerStackCustomerMap.PartnerStackCustomerKey);

            if (partnerStackCustomer == null)
            {
                _logger.LogError(
                    "[PartnerStack {MethodName}] Company ID: {CompanyID}: PartnerStack customer not found with customer key: {CustomerKey}",
                    nameof(SendContractEndDateNotifications),
                    cmsPartnerStackCustomerMap.CompanyId,
                    cmsPartnerStackCustomerMap.PartnerStackCustomerKey);

                errorCount++;
                errorCompanyIds.Add(cmsPartnerStackCustomerMap.CompanyId);
                continue;
            }

            var monthsToAdd = cmsPartnerStackCustomerMap.PartnerStackPartnerInformation.GroupName.Contains("two year")
                ? 22
                : 10;

            var contractEndDate = cmsPartnerStackCustomerMap.IndividualCommissionConfig.CommissionEndDate ??
                                  cmsPartnerStackCustomerMap.PartnerStackCustomerInformation.CreatedAt.AddMonths(
                                      monthsToAdd);

            // If the contract end date already passed, save the date and skip the notification sending
            if (contractEndDate.Date < currentDateTime.Date)
            {
                cmsPartnerStackCustomerMap.PartnerStackCustomerInformation.CustomerContractEndNotificationSentAt =
                    contractEndDate;
                await _appDbContext.SaveChangesAsync();
                continue;
            }

            // If the contract end date is in the future, skip the notification sending
            if (contractEndDate.Date > currentDateTime.Date)
            {
                continue;
            }

            try
            {
                totalCount += 1;

                var companyBillRecords = await _appDbContext.CompanyBillRecords
                    .Include(x => x.CmsSalesPaymentRecords)
                    .Where(x => x.CompanyId == cmsPartnerStackCustomerMap.CompanyId &&
                                x.Status != BillStatus.Inactive)
                    .AsNoTracking()
                    .ToListAsync();

                var notificationMrr = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                    companyBillRecords,
                    cmsPartnerStackCustomerMap.Company?.TimeZoneInfoId);

                var notificationMrrAmount = Convert.ToInt32(Math.Round(notificationMrr, 2) * 100);

                var notificationMrrTransaction = new CreatePartnerStackTransactionRequest(
                    cmsPartnerStackCustomerMap.PartnerStackCustomerKey,
                    notificationMrrAmount,
                    "USD",
                    PartnerStackConstants.NotificationCategoryKey,
                    PartnerStackConstants.CustomerContractEndNotificationProductKey);

                var createdTransaction = await _partnerStackIntegrationService.CreatePartnerStackTransaction(
                    notificationMrrTransaction.CustomerExternalKey,
                    notificationMrrTransaction.Amount,
                    notificationMrrTransaction.Currency,
                    notificationMrrTransaction.CategoryKey,
                    notificationMrrTransaction.ProductKey);

                if (createdTransaction == null)
                {
                    _logger.LogError(
                        "[PartnerStack {MethodName}] Company ID: {CompanyID}: Failed to create contract end notification transaction to PartnerStack",
                        nameof(SendContractEndDateNotifications),
                        cmsPartnerStackCustomerMap.CompanyId);

                    errorCount++;
                    errorCompanyIds.Add(cmsPartnerStackCustomerMap.CompanyId);
                }
                else
                {
                    succeedCount++;
                    cmsPartnerStackCustomerMap.PartnerStackCustomerInformation.CustomerContractEndNotificationSentAt =
                        currentDateTime;
                    await _appDbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[PartnerStack {MethodName}] Company ID: {CompanyID}: {ExceptionMessage}",
                    nameof(SyncAllMrrToPartnerStack),
                    cmsPartnerStackCustomerMap.CompanyId,
                    ex.Message);

                errorCount++;
                errorCompanyIds.Add(cmsPartnerStackCustomerMap.CompanyId);
            }
        }

        _logger.LogInformation(
            "SendContractEndDateNotifications Completed:\n" +
            "--------------------------------------------\n" +
            "Total Company: {TotalCompanyCount}\n" +
            "Number of Succeed: {SucceedCount}\n" +
            "Number of Error: {ErrorCount}\n\n",
            totalCount,
            succeedCount,
            errorCount);

        _logger.LogInformation(
            "SendContractEndDateNotifications Error Company: {ErrorCompanyIds}",
            string.Join(", ", errorCompanyIds));
    }

    public async Task<ResponseWrapper> UpdatePartnerCountry(string companyId, string country)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            // Check if the company got Partner Stack customer mapping
            var existedPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .Include(x => x.Company)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (existedPartnerStackCustomerMap == null)
            {
                response.ErrorMsg = "Error: Company does not have Partner Stack customer mapping";
                return response;
            }

            // Check if the company exists or not deleted
            if (existedPartnerStackCustomerMap.Company == null || existedPartnerStackCustomerMap.Company.IsDeleted)
            {
                response.ErrorMsg = "Error: Company not found or deleted";
                return response;
            }

            // Check if PartnerStack partner information exists
            if (existedPartnerStackCustomerMap.PartnerStackPartnerInformation == null)
            {
                response.ErrorMsg = "Error: PartnerStack partner information not found";
                return response;
            }

            // Update the partner country in PartnerStack partner information
            existedPartnerStackCustomerMap.PartnerStackPartnerInformation = new PartnerStackPartnerInformation
            {
                PartnerKey = existedPartnerStackCustomerMap.PartnerStackPartnerInformation.PartnerKey,
                GroupName = existedPartnerStackCustomerMap.PartnerStackPartnerInformation.GroupName,
                TeamName = existedPartnerStackCustomerMap.PartnerStackPartnerInformation.TeamName,
                Email = existedPartnerStackCustomerMap.PartnerStackPartnerInformation.Email,
                Country = country
            };
            existedPartnerStackCustomerMap.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            response.IsSuccess = true;
            response.Data = _mapper.Map<CmsPartnerStackCustomerMapDto>(existedPartnerStackCustomerMap);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Update Partner Country failed, Company ID: {CompanyID}, Country: {Country}: {ExceptionMessage}",
                nameof(UpdatePartnerCountry),
                companyId,
                country,
                ex.Message);

            response.ErrorMsg = "Error: Failed to update Partner Country";
            return response;
        }
    }

    public async Task<ResponseWrapper> UpdatePartnershipDealOwnerId(string companyId, string partnershipDealOwnerId)
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            // Check if the company got Partner Stack customer mapping
            var existedPartnerStackCustomerMap = await _appDbContext.CmsPartnerStackCustomerMaps
                .Include(x => x.Company)
                .FirstOrDefaultAsync(x => x.CompanyId == companyId);

            if (existedPartnerStackCustomerMap == null)
            {
                response.ErrorMsg = "Error: Company does not have Partner Stack customer mapping";
                return response;
            }

            // Check if the company exists or not deleted
            if (existedPartnerStackCustomerMap.Company == null || existedPartnerStackCustomerMap.Company.IsDeleted)
            {
                response.ErrorMsg = "Error: Company not found or deleted";
                return response;
            }

            // Check if PartnerStack customer information exists
            if (existedPartnerStackCustomerMap.PartnerStackCustomerInformation == null)
            {
                response.ErrorMsg = "Error: PartnerStack customer information not found";
                return response;
            }

            // Validate that the partnership deal owner ID exists in CmsHubSpotContactOwnerMaps
            if (!string.IsNullOrWhiteSpace(partnershipDealOwnerId))
            {
                var contactOwnerMap = await _appDbContext.CmsHubSpotContactOwnerMaps
                    .FirstOrDefaultAsync(x => x.ContactOwnerId == partnershipDealOwnerId);

                if (contactOwnerMap == null)
                {
                    response.ErrorMsg = "Error: Partnership deal owner ID not found in HubSpot contact owner mappings";
                    return response;
                }
            }

            // Update the partnership deal owner ID in PartnerStack customer information
            existedPartnerStackCustomerMap.PartnerStackCustomerInformation = new PartnerStackCustomerInformation
            {
                CreatedAt = existedPartnerStackCustomerMap.PartnerStackCustomerInformation.CreatedAt,
                CustomerContractEndNotificationSentAt =
                    existedPartnerStackCustomerMap.PartnerStackCustomerInformation
                        .CustomerContractEndNotificationSentAt,
                PartnershipDealOwnerId = partnershipDealOwnerId
            };
            existedPartnerStackCustomerMap.UpdatedAt = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            response.IsSuccess = true;
            response.Data = _mapper.Map<CmsPartnerStackCustomerMapDto>(existedPartnerStackCustomerMap);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Update Partnership Deal Owner ID failed, Company ID: {CompanyID}, Partnership Deal Owner ID: {PartnershipDealOwnerId}: {ExceptionMessage}",
                nameof(UpdatePartnershipDealOwnerId),
                companyId,
                partnershipDealOwnerId,
                ex.Message);

            response.ErrorMsg = "Error: Failed to update Partnership Deal Owner ID";
            return response;
        }
    }

    public async Task<ResponseWrapper> GetPartnerStackSelections()
    {
        var response = new ResponseWrapper
        {
            IsSuccess = false
        };

        try
        {
            // Optimized single query to get all required data
            var allData = await _appDbContext.CmsPartnerStackCustomerMaps
                .AsNoTracking()
                .Where(x => x.PartnerStackPartnerInformation != null)
                .Select(x => new
                {
                    GroupName = x.PartnerStackPartnerInformation.GroupName,
                    TeamName = x.PartnerStackPartnerInformation.TeamName,
                    PartnershipDealOwnerId = x.PartnerStackCustomerInformation != null
                        ? x.PartnerStackCustomerInformation.PartnershipDealOwnerId
                        : null
                })
                .ToListAsync();

            // Process group and team names efficiently
            var groupNames = allData
                .Where(x => !string.IsNullOrWhiteSpace(x.GroupName))
                .Select(x => x.GroupName)
                .Distinct()
                .OrderBy(x => x)
                .ToList();

            var teamNames = allData
                .Where(x => !string.IsNullOrWhiteSpace(x.TeamName))
                .Select(x => x.TeamName)
                .Distinct()
                .OrderBy(x => x)
                .ToList();

            // Get distinct partnership deal owner IDs
            var partnershipDealOwnerIds = allData
                .Where(x => !string.IsNullOrWhiteSpace(x.PartnershipDealOwnerId))
                .Select(x => x.PartnershipDealOwnerId)
                .Distinct()
                .ToList();

            // Get partnership deal owners with their details (only if we have IDs)
            var partnershipDealOwners = new List<CmsUserDto>();
            if (partnershipDealOwnerIds.Count > 0)
            {
                // Use HashSet for O(1) lookup performance
                var ownerIdsSet = new HashSet<string>(partnershipDealOwnerIds);

                partnershipDealOwners = await _appDbContext.Users
                    .AsNoTracking()
                    .Where(x => ownerIdsSet.Contains(x.Id))
                    .Select(x => new CmsUserDto
                    {
                        Id = x.Id,
                        DisplayName = x.DisplayName,
                        Email = x.Email
                    })
                    .OrderBy(x => x.DisplayName)
                    .ToListAsync();
            }

            var selectionsResponse = new GetPartnerStackSelectionsResponse
            {
                GroupNames = groupNames,
                TeamNames = teamNames,
                PartnershipDealOwners = partnershipDealOwners
            };

            response.IsSuccess = true;
            response.Data = selectionsResponse;
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[PartnerStack {MethodName}] Get PartnerStack selections failed: {ExceptionMessage}",
                nameof(GetPartnerStackSelections),
                ex.Message);

            response.ErrorMsg = "Error: Failed to get PartnerStack selections";
            return response;
        }
    }

    private static List<int> GetCurrentQuarterMonths(DateTime dateTime)
    {
        return dateTime.Month switch
        {
            1 or 2 or 3 => [12, 1, 2, 3],
            4 or 5 or 6 => [3, 4, 5, 6],
            7 or 8 or 9 => [6, 7, 8, 9],
            10 or 11 or 12 => [9, 10, 11, 12],
            _ => []
        };
    }

    private static string GetCurrentMonthlySyncCategoryKey(
        string syncType,
        int? individualCommissionRate)
    {
        var baseCategoryKey = syncType == PartnerStackConstants.MrrSyncType
            ? PartnerStackConstants.MonthlyMrrSyncCategoryKey
            : PartnerStackConstants.OneTimeOffSyncCategoryKey;

        var categoryKey = !individualCommissionRate.HasValue
            ? baseCategoryKey
            : $"{baseCategoryKey}_Commission_{individualCommissionRate.Value}%";

        return categoryKey;
    }

    private static string GetCurrentMonthlySyncProductKey(string syncType, string date)
    {
        var baseProductKey = syncType == PartnerStackConstants.MrrSyncType
            ? PartnerStackConstants.MonthlyMrrSyncCategoryKey
            : PartnerStackConstants.OneTimeOffSyncCategoryKey;

        return $"{baseProductKey}_{date}";
    }

    private static string GetPartnerStackNotificationEmailMessage(
        int totalCompanySynced,
        int succeedCount,
        int errorCount,
        string errorCompanyIds,
        List<SyncMrrToPartnerStackDto> syncMrrToPartnerStackResults)
    {
        var sb = new StringBuilder();

        sb.Append(
            "<div>" +
            "<span style='color:#0f6593;'><b>Monthly Sync MRR to PartnerStack Completed:</b></span><br/>" +
            "<hr />" +
            $"<span style='color:#0f103d;'>Total Company Synced: <b>{totalCompanySynced}</b></span><br/>" +
            $"<span style='color:#0f103d;'>Number of Sync Succeed: <b>{succeedCount}</b></span><br/>" +
            $"<span style='color:#0f103d;'>Number of Sync Error: <b>{errorCount}</b></span><br/>" +
            "<hr />" +
            $"<div style='color:#0f103d;'>Sync Error Company IDs: {errorCompanyIds}</div><br/>" +
            "</div>");

        if (syncMrrToPartnerStackResults.Count == 0)
        {
            return sb.ToString();
        }

        sb.Append("<div>");

        foreach (var syncResult in syncMrrToPartnerStackResults)
        {
            sb.Append(
                "<div>" +
                "<span style='color:#0f6593;'><b>Company Info</b></span><br/>" +
                $"<span style='color:#0f103d;'>Company Id: <b>{syncResult.SleekFlowCompanyId}</b></span><br/>" +
                $"<span style='color:#0f103d;'>Company Name: <b>{syncResult.SleekFlowCompanyName}</b></span><br/>" +
                $"<span style='color:#0f103d;'>PartnerStack Customer Key: <b>{syncResult.PartnerStackCustomerKey}</b></span><br/>" +
                $"<span style='color:#0f103d;'>Open In: <a clicktracking=off href='https://powerflow.sleekflow.io/companies/detail/{syncResult.SleekFlowCompanyId}' target='_blank'>Powerflow</a><br/>" +
                $"<span style='color:#0f103d;'>Sync Result: <b>{syncResult.Message}</b></span><br/>");

            if (syncResult.Results == null || syncResult.Results.Count == 0)
            {
                sb.Append(
                    "</div>" +
                    "<hr />" +
                    "<br/>");
                continue;
            }

            sb.Append(
                "<span style='color:#0f103d;'>Sync PartnerStack Transactions Results: </span><br/>" +
                "<table style='border-collapse: collapse; width: 100%;'>" +
                "<tr>" +
                "<th style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>PartnerStack Category Key</th>" +
                "<th style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>PartnerStack Product Key</th>" +
                "<th style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>Result</th>" +
                "</tr>");

            foreach (var syncMrrTransactionResult in syncResult.Results)
            {
                var isSuccessString = syncMrrTransactionResult.IsSuccess ? "Succeed" : "Failed";

                sb.Append(
                    "<tr>" +
                    $"<td style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>{syncMrrTransactionResult.PartnerStackTransactionCategoryKey}</td>" +
                    $"<td style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>{syncMrrTransactionResult.PartnerStackTransactionProductKey}</td>" +
                    $"<td style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>{isSuccessString}</td>" +
                    "</tr>");
            }

            sb.Append(
                "</table>" +
                "</div>" +
                "<hr />" +
                "<br/>");
        }

        sb.Append("</div>");

        return sb.ToString();
    }

    private static string GetTransactionMismatchAlertEmailMessage(
        List<SyncMrrToPartnerStackDto> syncMrrToPartnerStackResults)
    {
        var sb = new StringBuilder();

        sb.Append(
            "<div>" +
            "<span style='color:#e55353;'><b>PartnerStack Transaction Amount Mismatch:</b></span><br/>" +
            "<hr />" +
            "</div>" +
            "<br/>");

        if (syncMrrToPartnerStackResults.Count == 0)
        {
            return sb.ToString();
        }

        sb.Append("<div>");

        foreach (var syncResult in syncMrrToPartnerStackResults)
        {
            sb.Append(
                "<div>" +
                "<span style='color:#0f6593;'><b>Company Info</b></span><br/>" +
                $"<span style='color:#0f103d;'>Company Id: <b>{syncResult.SleekFlowCompanyId}</b></span><br/>" +
                $"<span style='color:#0f103d;'>Company Name: <b>{syncResult.SleekFlowCompanyName}</b></span><br/>" +
                $"<span style='color:#0f103d;'>PartnerStack Customer Key: <b>{syncResult.PartnerStackCustomerKey}</b></span><br/>" +
                $"<span style='color:#0f103d;'>Open In: <a clicktracking=off href='https://powerflow.sleekflow.io/companies/detail/{syncResult.SleekFlowCompanyId}' target='_blank'>Powerflow</a><br/>");

            sb.Append(
                "<span style='color:#0f103d;'>PartnerStack Transactions: </span><br/>" +
                "<table style='border-collapse: collapse; width: 100%;'>" +
                "<tr>" +
                "<th style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>PartnerStack Category Key</th>" +
                "<th style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>PartnerStack Product Key</th>" +
                "<th style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>Previous Amount</th>" +
                "<th style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>Latest Amount</th>" +
                "</tr>");

            foreach (var syncMrrTransactionResult in syncResult.TransactionAmountMismatches)
            {
                sb.Append(
                    "<tr>" +
                    $"<td style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>{syncMrrTransactionResult.PartnerStackTransactionCategoryKey}</td>" +
                    $"<td style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>{syncMrrTransactionResult.PartnerStackTransactionProductKey}</td>" +
                    $"<td style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>{syncMrrTransactionResult.PartnerStackAmount:0.00}</td>" +
                    $"<td style='border: 1px solid #dddddd; text-align: left; padding: 4px;'>{syncMrrTransactionResult.SleekFlowAmount:0.00}</td>" +
                    "</tr>");
            }

            sb.Append(
                "</table>" +
                "</div>" +
                "<hr />" +
                "<br/>");
        }

        sb.Append("</div>");

        return sb.ToString();
    }

    private static List<BillRecord> GetLastMonthBillRecords(
        List<BillRecord> billRecords,
        DateTime start,
        DateTime end)
    {
        var lastMonthBillRecords = new List<BillRecord>();

        var billRecordsStartFromLastMonth = billRecords
            .Where(x => x.PeriodStart.Date >= start && x.PeriodStart.Date <= end)
            .ToList();

        if (billRecordsStartFromLastMonth.Count == 0)
        {
            return lastMonthBillRecords;
        }

        // Subscription Plan
        var subscriptionBillRecords = billRecordsStartFromLastMonth
            .Where(x => ValidSubscriptionPlan.SubscriptionPlan.Contains(x.SubscriptionPlanId))
            .OrderByDescending(x => x.created)
            .ThenByDescending(x => x.PayAmount)
            .ToList();

        if (subscriptionBillRecords.Count > 0)
        {
            // If only have 1 subscription bill record in the last month
            if (subscriptionBillRecords.Count == 1)
            {
                lastMonthBillRecords.Add(subscriptionBillRecords[0]);
            }
            else
            {
                // To avoid duplicate subscription bill records
                var lastMonthSubscriptionBillRecords = new List<BillRecord>();

                for (var date = start; date <= end; date = date.Add(TimeSpan.FromDays(1)))
                {
                    var currentSubscription = subscriptionBillRecords
                        .Find(x => x.PeriodStart.Date <= date && x.PeriodEnd.Date >= date);

                    if (currentSubscription != null)
                    {
                        lastMonthSubscriptionBillRecords.Add(currentSubscription);
                    }
                }

                lastMonthBillRecords.AddRange(lastMonthSubscriptionBillRecords.DistinctBy(x => x.Id));
            }
        }

        // Add-On Plan
        var addOnBillRecords = billRecordsStartFromLastMonth
            .Where(x => ValidSubscriptionPlan.CmsMrrAllAddOnWithoutRevenuePlan.Contains(x.SubscriptionPlanId))
            .ToList();

        lastMonthBillRecords.AddRange(addOnBillRecords);

        return lastMonthBillRecords;
    }

    private async Task<string> GetCountryFromHubSpotContactAsync(string partnerEmail)
    {
        if (!_isHubSpotEnable)
        {
            return string.Empty;
        }

        try
        {
            var hubSpotContact = await _internalHubspotRepository.GetContactObjectByEmailAsync(partnerEmail);
            return !string.IsNullOrEmpty(hubSpotContact?.Country) ? hubSpotContact.Country : string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "[PartnerStack {MethodName}] Failed to get HubSpot contact for partner email: {PartnerEmail}, using empty country. Error: {ExceptionMessage}",
                nameof(GetCountryFromHubSpotContactAsync),
                partnerEmail,
                ex.Message);
            return string.Empty;
        }
    }

    private async Task<string> GetContactOwnerIdFromHubSpotDealAsync(string companyId)
    {
        if (!_isHubSpotEnable)
        {
            return string.Empty;
        }

        try
        {
            // Get HubSpot deal by company ID
            var hubSpotDeal = await _internalHubspotRepository
                .GetLatestDealObjectWithPartnershipsDealOwnerBySleekFlowCompanyIdAsync(companyId);

            if (hubSpotDeal == null || string.IsNullOrEmpty(hubSpotDeal.PartnershipsDealOwner))
            {
                return string.Empty;
            }

            // Search CmsHubSpotContactOwnerMaps table to find the ContactOwnerId
            var contactOwnerMap = await _appDbContext.CmsHubSpotContactOwnerMaps
                .FirstOrDefaultAsync(x => x.HubSpotContactOwnerId == hubSpotDeal.PartnershipsDealOwner);

            return contactOwnerMap?.ContactOwnerId ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "[PartnerStack {MethodName}] Failed to get HubSpot deal contact owner for company ID: {CompanyId}, using empty contact owner ID. Error: {ExceptionMessage}",
                nameof(GetContactOwnerIdFromHubSpotDealAsync),
                companyId,
                ex.Message);
            return string.Empty;
        }
    }

    private decimal CalculateActualRevenue(BillRecord billRecord, DateTime? eligibleCommissionPeriodEndDate)
    {
        var totalRevenue = CurrencyConverter.ConvertToUsd(
            (decimal) billRecord.PayAmount,
            billRecord.currency);

        totalRevenue +=
            billRecord.CmsSalesPaymentRecords.Sum(x => CurrencyConverter.ConvertToUsd(x.SubscriptionFee, x.Currency));

        if (!eligibleCommissionPeriodEndDate.HasValue ||
            billRecord.PeriodEnd.Date <= eligibleCommissionPeriodEndDate.Value ||
            billRecord.PeriodStart.Date > eligibleCommissionPeriodEndDate.Value)
        {
            return totalRevenue;
        }

        var monthsDiffInEligibleDate = _timezoneAwareMrrCalculationService.GetPreciseMonthDiff(billRecord.PeriodStart, eligibleCommissionPeriodEndDate.Value);
        var monthsDiffOfBillRecord = _timezoneAwareMrrCalculationService.GetPreciseMonthDiff(billRecord.PeriodStart, billRecord.PeriodEnd);

        return totalRevenue * monthsDiffInEligibleDate / monthsDiffOfBillRecord;
    }
}