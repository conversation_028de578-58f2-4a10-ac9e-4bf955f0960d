using System;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.Helpers;

namespace Travis_backend.InternalIntegrationHubDomain.Services;

public interface IIrregularPlanInvoiceCalculator
{
    int GetInvoiceSequenceForDate(BillRecord billRecord, DateTime today);
}

/// <summary>
///     Helper that encapsulates invoice sequence calculation logic for irregular plans.
///     Extracted to enable focused unit testing on date edge cases.
/// </summary>
public class IrregularPlanInvoiceCalculator : IIrregularPlanInvoiceCalculator
{
    private readonly IBillRecordRevenueCalculatorService _billRecordRevenueCalculatorService;

    public IrregularPlanInvoiceCalculator(IBillRecordRevenueCalculatorService billRecordRevenueCalculatorService)
    {
        _billRecordRevenueCalculatorService = billRecordRevenueCalculatorService;
    }

    /// <summary>
    ///     Calculates the invoice sequence number for the specified date if an invoice should be created.
    ///     Returns 0 if no invoice should be created on that date.
    /// </summary>
    /// <returns>
    ///     The invoice sequence number for the specified date if an invoice should be created.
    ///     Returns 0 if no invoice should be created on that date.
    /// </returns>
    public int GetInvoiceSequenceForDate(BillRecord billRecord, DateTime today)
    {
        if (billRecord?.PaymentTerms is not > 0)
        {
            return 0;
        }

        return billRecord.PaymentIntervalType switch
        {
            PaymentIntervalType.Custom => CheckCustomPaymentSplits(billRecord, today),
            PaymentIntervalType.Monthly => FindInvoiceCycleForDate(billRecord, today, 1),
            PaymentIntervalType.Yearly => FindInvoiceCycleForDate(billRecord, today, 12),
            _ => 0
        };
    }

    /// <summary>
    ///     Common logic to find which cycle needs an invoice created for the specified date.
    ///     Returns the cycle index (2…N) when the given date equals the invoice creation date
    ///     (cycle start date minus payment terms). Returns 0 when no match is found.
    /// </summary>
    /// <returns>
    ///     The cycle index (2…N) when the given date equals the invoice creation date
    ///     (cycle start date minus payment terms). Returns 0 when no match is found.
    /// </returns>
    public int FindInvoiceCycleForDate(BillRecord billRecord, DateTime today, int monthsPerCycle)
    {
        var subscriptionInterval =
            BillRecordRevenueCalculatorHelper.GetMonthDiff(billRecord.PeriodStart, billRecord.PeriodEnd);
        var maxCycles = monthsPerCycle == 12
            ? subscriptionInterval / 12
            : subscriptionInterval;

        var actualPeriodEnd = BillRecordRevenueCalculatorHelper.GetActualPeriodEndFromMetadata(billRecord);

        // Start from cycle 2 because cycle 1 is already invoiced for non-custom plans
        for (var cycle = 2; cycle <= maxCycles; cycle++)
        {
            var cycleStartDate = billRecord.PeriodStart.Date.AddMonths((cycle - 1) * monthsPerCycle);
            if (cycleStartDate >= actualPeriodEnd.Date)
            {
                break;
            }

            var invoiceCreationDate = cycleStartDate.AddDays(-billRecord.PaymentTerms!.Value);
            if (invoiceCreationDate.Date == today.Date)
            {
                return cycle;
            }
        }

        return 0;
    }

    private int CheckCustomPaymentSplits(BillRecord billRecord, DateTime today)
    {
        var matchingSplit = _billRecordRevenueCalculatorService.FindMatchingPaymentSplit(billRecord, today);
        if (matchingSplit == null)
        {
            return 0;
        }

        var validSplits = BillRecordRevenueCalculatorHelper.GetValidPaymentSplits(billRecord);
        var matchingIndex = validSplits.FindIndex(split => split.PaymentDate.Date == matchingSplit.PaymentDate.Date);
        return matchingIndex >= 0 ? matchingIndex + 1 : 0;
    }
}