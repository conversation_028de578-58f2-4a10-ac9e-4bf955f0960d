#nullable enable

using System;
using Microsoft.Extensions.Logging;

namespace Travis_backend.CompanyDomain.Services
{
    /// <inheritdoc />
    public class TimezoneAwareMrrCalculationService : ITimezoneAwareMrrCalculationService
    {
        #region Constants

        /// <summary>
        /// Average number of seconds in a month (2,628,000 seconds).
        /// Used for pro rata calculations of partial periods.
        /// Based on 30.44 days average month length.
        /// </summary>
        private const decimal SecondsPerMonth = 2628000m;

        #endregion

        #region Dependencies & Constructor

        /// <summary>
        /// Logger for tracking timezone operations.
        /// </summary>
        private readonly ILogger<TimezoneAwareMrrCalculationService> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="TimezoneAwareMrrCalculationService"/> class.
        /// </summary>
        /// <param name="logger">The logger for tracking operations.</param>
        public TimezoneAwareMrrCalculationService(ILogger<TimezoneAwareMrrCalculationService> logger)
        {
            _logger = logger;
        }

        #endregion

        /// <inheritdoc />
        public TimeZoneInfo GetCompanyTimezone(string? timezoneInfoId = null)
        {
            try
            {
                // If timezoneInfoId is provided, use it directly
                if (!string.IsNullOrWhiteSpace(timezoneInfoId))
                {
                    try
                    {
                        var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timezoneInfoId);
                        _logger.LogDebug("Successfully retrieved timezone {TimeZoneId} directly", timezoneInfoId);
                        return timeZoneInfo;
                    }
                    catch (TimeZoneNotFoundException ex)
                    {
                        _logger.LogError(
                            ex,
                            "Invalid timezone ID {TimeZoneId} provided, falling back to UTC",
                            timezoneInfoId);
                        return TimeZoneInfo.Utc;
                    }
                }

                // If no timezoneInfoId provided, return UTC
                _logger.LogDebug("No timezone ID provided, using UTC as default");
                return TimeZoneInfo.Utc;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error retrieving timezone, falling back to UTC");
                return TimeZoneInfo.Utc;
            }
        }

        /// <inheritdoc />
        public (DateTime LocalStart, DateTime LocalEnd) ConvertPeriodToCompanyTimezone(
            DateTime utcPeriodStart,
            DateTime utcPeriodEnd,
            string companyId,
            string? timezoneInfoId = null)
        {
            try
            {
                // If companyId is null or empty, return UTC period
                if (string.IsNullOrWhiteSpace(companyId))
                {
                    _logger.LogWarning("ConvertPeriodToCompanyTimezoneAsync called with null or empty companyId, returning UTC period");
                    return (LocalStart: utcPeriodStart, LocalEnd: utcPeriodEnd);
                }

                var timeZoneInfo = GetCompanyTimezone(timezoneInfoId);

                // Ensure DateTime parameters are properly marked as UTC before conversion
                // This prevents the "Kind property not set correctly" error
                var utcStart = DateTime.SpecifyKind(utcPeriodStart, DateTimeKind.Utc);
                var utcEnd = DateTime.SpecifyKind(utcPeriodEnd, DateTimeKind.Utc);

                var localStart = TimeZoneInfo.ConvertTimeFromUtc(utcStart, timeZoneInfo);
                var localEnd = TimeZoneInfo.ConvertTimeFromUtc(utcEnd, timeZoneInfo);

                _logger.LogDebug(
                    "Converted period UTC({UtcStart} - {UtcEnd}) to Local({LocalStart} - {LocalEnd}) for company {CompanyId} timezone {TimeZoneId}",
                    utcStart,
                    utcEnd,
                    localStart,
                    localEnd,
                    companyId,
                    timeZoneInfo.Id);

                return (LocalStart: localStart, LocalEnd: localEnd);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error converting period for company {CompanyId}, returning original UTC period",
                    companyId);
                return (LocalStart: utcPeriodStart, LocalEnd: utcPeriodEnd);
            }
        }

        /// <inheritdoc />
        public decimal GetPreciseMonthDiff(DateTime periodStart, DateTime periodEnd)
        {
            try
            {
                if (periodStart > periodEnd)
                {
                    _logger.LogWarning(
                        "GetPreciseMonthDiff called with periodStart {PeriodStart} greater than periodEnd {PeriodEnd}",
                        periodStart,
                        periodEnd);
                    return 0m;
                }

                // Handle same date edge case
                if (periodStart.Date == periodEnd.Date)
                {
                    var sameDayHours = (decimal) (periodEnd - periodStart).TotalSeconds / SecondsPerMonth;
                    _logger.LogDebug("Same day period calculated as {SameDayMonths} months", sameDayHours);
                    return Math.Max(sameDayHours, 0.001m); // Minimum fractional value to avoid zero revenue
                }

                // Count full months with exact day/time matching
                var fullMonths = CountFullMonths(periodStart, periodEnd);

                decimal totalMonths = fullMonths;
                decimal partialMonths = 0;

                if (periodStart.Day != periodEnd.Day)
                {
                    // Special handling: Treat February end-of-month to March (same time) as exactly one month
                    // - Non-leap year: Feb 28 -> Mar 28-31 counts as 1 full month
                    // - Leap year: Feb 29 -> Mar 29-31 counts as 1 full month
                    if (fullMonths == 1
                        && IsEndOfMonth(periodStart)
                        && periodStart.Month == 2
                        && periodEnd.Year == periodStart.Year
                        && periodEnd.Month == 3
                        && periodEnd.Day >= periodStart.Day)
                    {
                        _logger.LogDebug(
                            "Applied Feb EOM to March one-month rule for {Start} -> {End}",
                            periodStart,
                            periodEnd);
                        return Math.Max(totalMonths, 0.001m);
                    }

                    // Calculate remaining partial period from last full month anchor
                    var lastFullMonthAnchor = GetLastFullMonthAnchor(periodStart, periodEnd, fullMonths);
                    var remainingSeconds = (decimal) (periodEnd - lastFullMonthAnchor).TotalSeconds;
                    partialMonths = remainingSeconds / SecondsPerMonth;
                    totalMonths += Math.Max(partialMonths, 0m);
                }

                var result = Math.Max(totalMonths, 0.001m);

                _logger.LogDebug(
                    "Precise month calculation: {FullMonths} full months + {PartialMonths} partial months = {TotalMonths} total months",
                    fullMonths,
                    partialMonths,
                    result);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error calculating precise month difference between {PeriodStart} and {PeriodEnd}",
                    periodStart,
                    periodEnd);

                // Fallback to simple calculation
                var totalDays = (decimal) (periodEnd - periodStart).TotalDays;
                return Math.Max(totalDays / 30.44m, 0.001m); // Use average month length as fallback
            }
        }

        /// <inheritdoc />
        public decimal GetPreciseMonthDiff(
            DateTime utcPeriodStart,
            DateTime utcPeriodEnd,
            string companyId,
            string? timezoneInfoId = null)
        {
            try
            {
                // Convert to company timezone first
                var (localStart, localEnd) = ConvertPeriodToCompanyTimezone(
                    utcPeriodStart,
                    utcPeriodEnd,
                    companyId,
                    timezoneInfoId);

                // Calculate with timezone-adjusted periods
                var result = GetPreciseMonthDiff(localStart, localEnd);

                _logger.LogDebug(
                    "Timezone-aware precise month calculation for company {CompanyId}: {Result} months",
                    companyId,
                    result);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error calculating timezone-aware precise month difference for company {CompanyId}",
                    companyId);

                // Fallback to UTC calculation
                return GetPreciseMonthDiff(utcPeriodStart, utcPeriodEnd);
            }
        }

        #region Private Helper Methods

        /// <summary>
        /// Gets the anchor date after advancing a number of full months from the start.
        /// Uses overflow-only adjustment, and preserves end-of-month alignment only when the end is also end-of-month.
        /// </summary>
        /// <param name="start">Start date.</param>
        /// <param name="end">End date.</param>
        /// <param name="fullMonths">Number of full months advanced.</param>
        /// <returns>The anchor date not exceeding <paramref name="end"/>.</returns>
        private DateTime GetLastFullMonthAnchor(DateTime start, DateTime end, int fullMonths)
        {
            // Advance naturally
            var candidate = start.AddMonths(fullMonths);

            // Adjust only when overflow occurred (day changed)
            if (candidate.Day != start.Day)
            {
                var lastDay = DateTime.DaysInMonth(candidate.Year, candidate.Month);
                candidate = new DateTime(
                    candidate.Year,
                    candidate.Month,
                    lastDay,
                    start.Hour,
                    start.Minute,
                    start.Second,
                    start.Millisecond,
                    start.Kind);
            }

            // If start is EOM and end is EOM, align to EOM to preserve exact-month semantics
            if (IsEndOfMonth(start) && IsEndOfMonth(end))
            {
                var lastDay = DateTime.DaysInMonth(candidate.Year, candidate.Month);
                candidate = new DateTime(
                    candidate.Year,
                    candidate.Month,
                    lastDay,
                    start.Hour,
                    start.Minute,
                    start.Second,
                    start.Millisecond,
                    start.Kind);
            }

            // Ensure we do not overshoot the end for partial calculation
            if (candidate > end)
            {
                candidate = end;
            }

            return candidate;
        }

        /// <summary>
        /// Counts the number of full months between two dates where day-of-month and time-of-day match.
        /// A full month means advancing by months while preserving the original day-of-month pattern.
        /// Special handling for end-of-month scenarios where both dates represent the last day of their respective months.
        /// </summary>
        /// <param name="start">The start date.</param>
        /// <param name="end">The end date.</param>
        /// <returns>The number of complete months.</returns>
        private int CountFullMonths(DateTime start, DateTime end)
        {
            try
            {
                var fullMonths = 0;
                while (true)
                {
                    // Prefer natural AddMonths progression; adjust only when the day changes due to shorter months
                    var candidate = start.AddMonths(fullMonths + 1);
                    if (candidate.Day != start.Day)
                    {
                        // Adjust to the last day of the target month when overflow occurs (e.g., Jan 31 -> Feb 28/29)
                        var targetMonth = start.AddMonths(fullMonths + 1);
                        var lastDayOfMonth = DateTime.DaysInMonth(targetMonth.Year, targetMonth.Month);
                        candidate = new DateTime(
                            targetMonth.Year,
                            targetMonth.Month,
                            lastDayOfMonth,
                            start.Hour,
                            start.Minute,
                            start.Second,
                            start.Millisecond,
                            start.Kind);
                    }

                    if (candidate.Date <= end.Date)
                    {
                        fullMonths++;
                    }
                    else
                    {
                        break;
                    }
                }

                _logger.LogDebug(
                    "Counted {FullMonths} full months between {Start} and {End}",
                    fullMonths,
                    start,
                    end);

                return fullMonths;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error counting full months between {Start} and {End}", start, end);
                return 0;
            }
        }

        /// <summary>
        /// Checks if a given date represents the last day of its month.
        /// </summary>
        /// <param name="date">The date to check.</param>
        /// <returns>True if the date is the last day of the month, false otherwise.</returns>
        private bool IsEndOfMonth(DateTime date)
        {
            return date.Day == DateTime.DaysInMonth(date.Year, date.Month);
        }

        #endregion
    }
}