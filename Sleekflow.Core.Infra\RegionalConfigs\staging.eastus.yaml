location_name: east<PERSON>
sku_config:
  sleekflow_core:
    name: P3V3
    tier: PremiumV3
  sleekflow_core_db:
    name: HS_PRMS
    tier: Hyperscale
    family: PRMS
    capacity: 2
  sleekflow_analytic_db:
    name: HS_S_Gen5
    tier: Hyperscale
    family: Gen5
    capacity: 2
  sleekflow_powerflow:
    name: P1V3
    tier: PremiumV3
  sleekflow_sleek_pay:
    name: P0V3
    tier: PremiumV3
  redis:
    default:
      name: Standard
      family: C
      capacity: 1
    caching:
      name: Standard
      family: C
      capacity: 1
sql_db_config:
  administrator_login_random_secret: tHUfpfzIZOYMaT7Vr71SoP3jtTpzOphMvK2KK6CcSfMghiLonV45WOePdPUNxk3HzQVwZh9tK6DXvarM
  administrator_login_password_random_secret: /Ru54rz4+x22rQkFGKfvk26fTM2xk236vMbnVHHjqiHrKibihgS2sfmuITEvxrXoIRyxREm2BHLW6d4U
  whitelist_ip_ranges:
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: **************
      end_ip_address: **************
  is_read_scale_enable: "true"
  high_availability_replica_count: 1
vnet:
  default_address_space: *********/16
  default_subnet_address_prefix: *********/24
  sleekflow_core_db_address_prefix: *********/24
  sleekflow_core_address_prefix: *********/24
  sleekflow_powerflow_address_prefix: *********/24
  sleekflow_sleek_pay_address_prefix: *********/24
  sleekflow_core_worker_address_prefix: *********/24
auto_scale_config:
  sleekflow_core:
    capacity:
      default: "3"
      maximum: "10"
      minimum: "2"
    scale_out_instances: "1"
    scale_down_instances: "1"
sleekflow_core_config:
  aspnetcore_environment: development
  logger:
    gcp_is_enabled: "TRUE"
    gcp_project_id: my-staging-project-405815
    gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  audit_hub:
    endpoint: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/audit-hub
    key: PojVDAQ+yBkChKy2x90fQOPZB1KZD8JdpRo5xQ/8H3GcP3zXRW466h4PhQJqy0AN0ItV5ftUYEu9KLzHtg5x/oRq/sixghDuu06rh6MDqR8=N1mlo3nfWcMfJ5gE
  auth0:
    action_audience: https://api-staging.sleekflow.io/
    action_issuer: https://sso-staging.sleekflow.io/
    audience: https://api-staging.sleekflow.io
    client_id: b8xN94MKdx5rZCYg32r0SSRVJiYyNw4Z
    client_secret: hR4CkoQBF2YnHhXlVrrktizT78IeLCRU5mu5yZknUXZsWCv0igZnQwuaVTJ44sLvkISynEAAPSPPpy1HqIpW8qT6la76rZfdqToJdwsHm0M=qZ7pktI2Q5ipr3Tx
    database_connection_name: Sleekflow-Username-Password-Authentication
    domain: sleekflow-staging.eu.auth0.com
    http_retries: 10
    issuers:
      - https://sso-staging.sleekflow.io/
      - https://sleekflow-staging.eu.auth0.com
    namespace: https://app.sleekflow.io/
    role_claim_type: roles
    user_email_claim_type: email
    user_id_claim_type: user_id
    username_claim_type: user_name
    tenant_hub_secret_key: KjrpP2It8729laEbK64e6WSw2Hbfm3uXTwmYl/oPlaZ9VC+s/IXD1ANEgTEOqLFZ456g6nhp3pYKh8Pv/H8hBU4bDFFKFudXKSlBVdsoZoc=Lg6KDcB88cYSWdgi
    health_check:
      is_enabled: "true"
      client_id: iluSPIuTen44fySBfKy4EAFEDltbBn28
      client_secret: bQx97vhT8Fn0OvjfKqdUe1RR3YRPYcAtI9mK7hqqneo3B3mzoHWaNnNkyxe372LWvBRgCPwcgvFPvneX/uLoqe2ngQsUsoYWzWmZV/vHC7k=6ApR5WdzYKHali2A
      username: <EMAIL>
      password: yzg8ezg@rfb*DQF-dbt
  azure:
    media_service:
      account_name: sfmediastaging
      client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
      client_secret: 7LtrXtJNQYj48Dtedp3Nwrmu/id2eLhzdfoRHJKldX15PqPs6djhyyV6jkYiYJaMeHC1ypbXhSDw=yVE
      resource_group: sleekflow-resource-group-staging7417fc06
      subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
      tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
    text_analytics_credentials: 4a76771e5629485ba34bcdfca9ed3487
    text_analytics_url: https://sleekflowtextanalytics.cognitiveservices.azure.com/
  application_insights:
    is_telemetry_tracer_enabled: "true"
    is_sampling_disabled: "false"
  beamer:
    api_key: ""
    api_url: ""
    webhook_verify_key: ""
  chat_api:
    api_key: RcCL+IfQqt+x9Px/W4GAN3zPn64q5GP3cbnDkJWE9iI=Ip58KOqPbeaXndjt
    api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
  commerce_hub:
    endpoint: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/commerce-hub
    key: vZSc5vt4dQLMS2F/ys/Gd/bII4Ghrs9Wv7hinbiCKNmnmUEnphSqJqa42c2rnjyPUGlkzLqJANXuc/4movsqcnX46ffiFZJhwN/vk8z2QTQZIicE0BrGVT4N6RTFBTXv/nhKInekWGQrLnzhxExtMNxVxxbm9jOJdv4MXLIez9A9lj98PzQxmsaTh/rsBAjnUK5v21HInVDMEpsc
  crm_hub:
    endpoint: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/crm-hub
    key: pC9Cl97lUlxC+ECSE6j5KT1exuDOjFJnSs1hUpCK9vYL6DDyn2D5b/PRKJfF/3dyBLfuMAv=l4eJSj4k
  data_snapshot:
    is_enable: "false"
  development:
    redirect_fb_webhook: https://9633-42-200-140-118.ngrok.io/facebook/Webhook
  environment_features:
    is_recurring_job_enabled: "false"
  epplus:
    excel_package:
      license_context: NonCommercial
  facebook:
    client_id: 812364635796464
    client_secret: CIsqgrxU1lnTqjqd60YUeCvznzpo9i0EGXaiMSW/aftXFiFelNOa7VmDmlL/MLKXl4fKdsbniP3QEF2N
  ffmpeg:
    ffmpeg_exe_name: /usr/bin/ffmpeg
  flow_hub:
    endpoint: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/flow-hub
    key: PLAUJhuRDeugNxPgLCYXy7Pk3CYJBH9DtFVEl5NO0wKXuTo/YOrvmISiHKwu1GFWtAfcXyEKfIijV7+QsbbpLrBDp7TipD9D9JgyRThEsec=R0gvIWVj2MM17=rh
  hub_spot:
    internal_hub_spot_api_key: ""
    is_enable: "false"
  instrumentation_engine_extension_version: disabled
  internal_google_cloud:
    credential:
  general_google_cloud:
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    project_id: my-staging-project-405815
    server_location: us-west1
    google_storage_bucket_name: sleekflow-transcoder-staging
  ip_look_up:
    key: zJoqa9TLuKYppEKQUqLvKubcGILcZkwc4GL/C86CAWjXXF/dalEMTucSq2efYy/Rln4ej18bbKPwQck8
  intelligent_hub:
    endpoint: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/intelligent-hub
    key: A/np07GgfwqyCKwE+yrDMZdV2mdO/xoZqkcD7Ba1RqwYjqdIsESspiLBAWYklZ+Cpjflf9K3jfn57Q+iLR7ZKDP37nbtrNw2vwbax2RmPf4=D2tgVoCox7itYzzx
  webhook_hub:
    endpoint: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/webhook-hub
    key: qjGE/2KmbXCp00al9elOYa5vrgsockdADNcGjN+liTIRBjFFsbcBsIRV1b8M5XAlqG3+2v8Vq3O6qsVcYjZzct+beDz1nlQd8vKfyvq43UU=OVIyI2TUgiqK2WHW
    auth_secret_key: PTM2PCjX7D1dmp1W7b4zNg/6TmOEcrjEQhzrCUiweGbalpetKEZBt1YjUBKFVP8zo0pklcXiY4dTbTcabSqqmsIW14Pv42D2rTvbv40ZhCo=FshRnJp4FJYB6Z2b
  message_hub:
    endpoint: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/messaging-hub
    key: +VQSG40zJWTgKQtIbCXzpaJWzzBX8GvuWde0I9Qvj/IJNVJIfFj9fsQchiIRLZdznuXme7RBJf=PipYZ
  mixpanel:
    token: ef72dd13a0ffccb584cfdf75d3160e25
  mobile_app_management_extension_version: latest
  notification_hub:
    connection_string: Endpoint=sb://sleekflowtesting.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=l5XR33aDeefA79lau7IjY252oJRpdB/h4pP6v3F6hVY=
    hub_name: sleekflowTesting
  public_api_gateway:
    endpoint: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/public-api-gateway
    key: /DmmW3DLjeaiQspUKWEDgQRgoOJrBNsn3ERprF/eJPhb9Y4j/VMKFsqsYsKIqfUUBL=NKSFHuZ2xVV6I
  reseller:
    domain_name: https://partner-uat.sleekflow.io/
  rewardful:
    api_secret: iKzwvZZXiu6h/DIBv4h5hycJq6MycwSbJGdrqDLSVsKxhRkepHmCsL2Q3/It/y5cULiU7nPgSgAfoIVV
  salesforce:
    custom_active_web_app: https://staging-sfmc-custom-activity.vercel.app
  share_hub:
    endpoint: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/share-hub
    key: EAwo20VvQBENluFDWj0HHyGndH4yVb0aFlAc9ixdwJhTigQ5vzyr112VLebbUji1oSdM7nsoVi84PfxV
  shopify:
    shopify_api_key: ko8oOOwM5VjuZVWnknV7hausHOoG6rHEOxG9j29kZbfBHJS5bm2bSHHVLqBUWU301Rtq1Hf2wvRyEMm7
    shopify_secret_key: PpBkzNbcDwgtHbNfIkZGrgG7cIjsnkTdu1hudFvDQyXGsdclWTSwQjln4K7scb0L3AuIKGqxP80BzPcT
  sleek_pay:
    domain_name: https://pay-eus-staging.sleekflow.io
    shopify_graphql_admin_api_version: 2022-10
    stripe:
      public_keys:
        default:
        gb:
        hk:
        my:
        sg:
      secret_keys:
        default:
        gb:
        hk:
        my:
        sg:
      connect_webhook_secrets:
        gb:
        hk:
        my:
        sg:
      report_webhook_secrets:
        gb:
        hk:
        my:
        sg:
      webhook_secrets:
        default:
        gb:
        hk:
        my:
        sg:
  snapshot_debugger_extension_version: disabled
  sql_performance:
    from_raw_sql: "true"
    is_and_condition_enabled: "true"
    is_or_condition_enabled: "true"
    is_conversation_analytics_condition_enabled: "true"
    is_shopify_order_statistics_enabled: "true"
    is_sales_performance_enabled: "true"
    is_public_api_new_upsert_enabled: "false"
    is_zapier_new_upsert_enabled: "false"
  stripe:
    stripe_public_key: gHla3uZ5R25l+Qw7iZPDcu/297uaXuA/hlc0apFWfzjpiqdckplUnCpA5FckyT7RvLjKy0sJBXW865Ls
    stripe_report_key: AfJm+hIsCmTg+sSPoVbquNNekevBMekyg1xXM84J0xT69uOdwwyhVT4UcPlGBDEQh7YIixbFQ016LL7LOZR0wVTR4n17XsbZpmIFaIIuIlNfzLzwlsizsTbEtJxkcoG14E/mdcIcXSeaK5b1v7xwzA==MdZSxtqDwSzYasJz
    stripe_secret_key: yI0tW947K1ICWnouSed+E3fGfKkBHIDnuTYgnZ8VkhlE18tN8dsJIT15cW26pmovtcdxDecykng0dPYd
    stripe_webhook_secret: cJfkLZSAyscdMHTbgz5WVAd3PDSFJvMQ5LbnGYT3Or9u6oe52R5fyEDROkxTYKfhhiUElE0YYe=fJn5q
  stripe_payment:
    stripe_payment_secret_key_gb: fdyjDWLgfO15T0DQUozZyJca5Tym3CRZZdSl0h5tgCZ42fX1yXysWWguNic+baWZY3uwHhD9wCvcv7DsDgOr6y1E1/7UmK+s2vgnBcsIIK1kw1UhzvMuZ82rMNJSWsoS0w1rPOyw/yC3Ibczs37Lsg==ImtIQUIN0RFSJvCS
    stripe_payment_secret_key_hk: ilZf9jj0m+X1HKYw5jd5euI7gjKIMpFBkovwv8B8oZKYx+tT8+Tz/T2kEGVnz5ZBoEbk2nIkQXw/gziMdknznmFlsMbi8oOhrp7UMJhKDt+MjnTJVt9SIgV723cqISYKwgJNiVhB+drIY9XO8d928g==Q=R0huYU1Z2Qery5
    stripe_payment_secret_key_my: Mwvy7ovkFf5hxQYM/3GJd5JZN4Kr5wnC2PzFSAaRSKwvCkQr+8ezwK/zA6od/il0gQciBi4dxXbvnlN74nvuemCeEd6KMaGyo5//NL6kxhlDgDOdbGUHexpOsTrAsZuwUwJgrVxpD/KsoO9+jHS5Lw==8HfkDY2tVdcxkh9p
    stripe_payment_secret_key_sg: wlw0eRX2JZy/XOvqPz1lrrmwKrdq423QnuvPg6RUxJDPe/RWthyXSVSQUa5Cg832OqzmZUHxTeO4sr1eISWQGSyRlWxMlz3xkBcsvDmoX07m/0pjESFPV7Fq3h0lW5jDJjzUTGbyFsAIs0eyWFYLaQ==MwrW5WBSmEjtaKLx
  stripe_report:
    stripe_report_webhook_secret_gb:
    stripe_report_webhook_secret_hk:
    stripe_report_webhook_secret_my:
    stripe_report_webhook_secret_sg:
  tenant_hub:
    endpoint: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/tenant-hub
    key: G3fetKVJFHltF5QwUEhNQO0JfIhaOyyrfZFKYl+zIi5jW+mwsX8iGbxFi7QuipHwbK9jeBxioymWhYehXeIAKZk3FqqKzKxrhqJR8BGosbQ=uYBd=lZRjbcCWHw0
    is_enable_tenant_logic: "true"
  ticketing_hub:
    endpoint: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/ticketing-hub
    key: It8wLf6mzNBGsxYkhLUHtXOyFBQWoYypvgIoGBficYojGP6tXbQEMr4A/HIbx+BSwj3l/rEOm7EyTlkasjo5llVRXoBSdHbZXd/MPKKjyIE=kgEtuhPi8ysOhM1U
    is_enable_ticketing_logic: "false"
  test_swaping: 1
  token:
    audience: https://sleekflow-prod-api.azurewebsites.net
  tokens:
    audience: https://uat.sleekflow.io
    issuer: https://uat.sleekflow.io
    key: XGyLTuaSvjHLXQFYwa0YvgmLyvKB6bjPu85eZq7vdNuqAMxpCZStuY7bEDENdFHTcZLfD1MzQySwgSAG9qZGKXeMSSEEm6juSUpOBa6My+g=14Xnpupo18sNNRa0
    lifetime: 365
  user_event_hub:
    endpoint: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/user-event-hub
    key: t6R6WxL8PvOaZSZHsIXJpvxADpB44UzGn2tbUVw0K53K1q13V7k0uKwR3F/K99ZTcvxvDuE177hGx5/nuNhmwfsrOTXkFIyI6k/IPdFp8IQ=2xaYRaSc3wpAEAcC
  values:
    app_domain_name: https://staging.sleekflow.io
    app_domain_name_v1: https://v1-staging.sleekflow.io
    app_domain_name_v2: https://staging.sleekflow.io
    share_link_function: https://sleekflow-share-tracking-uat.azurewebsites.net
    sleekflow_api_gateway: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net
    sleekflow_company_id: 39ee5f12-7997-45fa-a960-e4feecba425c
    sleekflow_public_api_url: https://api-staging.sleekflow.io
    sleekflow_public_api_key: DX3vH8YxV8+T7IlBWr+DJ/HSxeHO3CilNw8E4w44b5i2JeBLHCaFZLQDPrWdBoef7s4cWfhqLFZdiC0o
    sleekflow_company_should_use_public_api: "true"
  website_http_logging_retention_days: 5
  website_node_default_version: 6.9.1
  whatsapp_cloud_api_template:
    default_image_blob_id: sleekflow.png
  xdt_microsoft_application_insights_base_extensions: disabled
  xdt_microsoft_application_insight_mode: default
  global_pricing:
    is_feature_enabled: "true"
    plan_migration_incentives_start_date: "2024-10-20"
    plan_migration_incentives_end_date: "2025-02-20"
  feature_flags:
    - feature_name: FlowBuilderMonetisation
      is_enabled: "true"
    - feature_name: CancelledSubscriptionTermination
      is_enabled: "true"
    - feature_name: Rbac
      is_enabled: "true"
  contact_safe_deletion:
    is_feature_enabled: "true"
  partner_stack:
    public_key: 4JLtkCKryotYXlraS98g1Nfq7SW+aIp4R3j4jUNl2Ij7Ap3Yx9DsUBEAOwDm1NxkfCFXNx9WoKdXs80E
    secret_key: iBz4FXXtAtTAOKg3/N/D5QlxIFW7ti/F54FJyi781SoidP7hGl0nggiZ7eoBlByPS70AqlcU5gohrEL1
  hangfire_worker:
    worker_count: 20
  internal_integration_hub:
    endpoint: "https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/internal-integration-hub"
    key: yHCR7GViA9FsmQke+377wK+76Cpb3SKRXN3KrSxnDpMTuyqJJIIz79VjcNreiBkz/iB3b7jAh6ejAZ2t6Bizepf2AX+48XuyOjSPlQ6RYEg=rdNWQ5Xrgt1RgQQh
  hangfire_queues:
    disable_instances: ""
  integration_alert:
    endpoint: "https://sleekflow-core-staging-dycncqcebbf4ggag.z01.azurefd.net/api/notifications/integration-disconnected"
    api_key: "zvi5rEUtzHF3GaDwrTfNjpF6oQbqSDF1LR4jj3ga1iHIz9CcmLOiqp/mYFQQfySpAYasMGhoqcwkbQqf"
    host_company_id: "39ee5f12-7997-45fa-a960-e4feecba425c"
    from_phone_number: "15419458252"
    template_name: "integration_disconnect_noti"
    facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"
  rbac:
    is_middleware_verification_enabled: "false"
  flow_builder_flow_enrollments_incentives:
    period_start: "2025-01-24"
    period_end: "2025-04-24"
  hub_spot_smtp:
    username: "<EMAIL>"
    password: "jP3Ox4fOjweWF0fxcbKfLoB3glxOFU"
    send_execution_usage_reached_threshold_email:
      username: "<EMAIL>"
      password: "lBjfD8BpWl8PBsmYYJqKEkXX2ag85w"
  legacy_premium_opt_in_upgrade_incentives:
    period_start: "2025-02-10"
    period_end: "2025-06-10"
  live_chat:
    secret_key: "snMCr7fhJ/8S5pc/hH1yminwwa9laxip13kWfeY0FDh022eve146uWKBIBgwRu0ELyAy8gd7gg=BUrS0"