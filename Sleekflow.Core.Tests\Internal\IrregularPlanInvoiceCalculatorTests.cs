using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalIntegrationHubDomain.Services;

namespace Sleekflow.Core.Tests.Internal;

public class IrregularPlanInvoiceCalculatorTests
{
    private BillRecordRevenueCalculatorService _revenue = null!;
    private IrregularPlanInvoiceCalculator _calc = null!;

    [SetUp]
    public void Setup()
    {
        var revenueLogger = new LoggerFactory().CreateLogger<BillRecordRevenueCalculatorService>();
        var timezoneMock = new Mock<ITimezoneAwareMrrCalculationService>();
        _revenue = new BillRecordRevenueCalculatorService(revenueLogger, timezoneMock.Object);
        _calc = new IrregularPlanInvoiceCalculator(_revenue);
    }
    #region Sequence detection (dates → cycle)

    [Test]
    public void FindInvoiceCycleForDate_Monthly_EOM_LeapYear_And_CrossYear()
    {
        var bill = new BillRecord
        {
            PeriodStart = new DateTime(2024, 1, 31),
            PeriodEnd = new DateTime(2024, 12, 31),
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PaymentTerms = 14,
            PayAmount = 1000
        };

        // Cycle 2 starts at 2024-02-29 (leap year Feb), invoice date is 2024-02-15
        var cycle2 = _calc.FindInvoiceCycleForDate(bill, new DateTime(2024, 2, 15), 1);
        cycle2.Should().Be(2);

        // Cycle 3 starts 2024-03-31, invoice date is 2024-03-17
        var cycle3 = _calc.FindInvoiceCycleForDate(bill, new DateTime(2024, 3, 17), 1);
        cycle3.Should().Be(3);
    }

    [Test]
    public void GetInvoiceSequenceForDate_Custom_PaymentSplits_With_Terms()
    {
        var bill = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 12, 31),
            PaymentIntervalType = PaymentIntervalType.Custom,
            PaymentTerms = 14,
            PayAmount = 10000,
            PaymentSplits =
            [
                new PaymentSplit
                {
                    PaymentDate = new DateTime(2025, 8, 1), SplitPercentage = 40
                },
                new PaymentSplit
                {
                    PaymentDate = new DateTime(2025, 9, 1), SplitPercentage = 60
                }
            ]
        };

        // 2025-07-18 should focus on split at 2025-08-01 with 14-day terms => sequence 1
        var seq1 = _calc.GetInvoiceSequenceForDate(bill, new DateTime(2025, 7, 18));
        seq1.Should().Be(1);

        // 2025-08-18 should target split at 2025-09-01 => sequence 2
        var seq2 = _calc.GetInvoiceSequenceForDate(bill, new DateTime(2025, 8, 18));
        seq2.Should().Be(2);
    }

    [Test]
    public void NullOrNonPositive_Terms_Returns_Zero_For_All_Intervals()
    {
        // Monthly
        var monthlyNullTerms = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 10, 1),
            PayAmount = 3000,
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PaymentTerms = null
        };
        _calc.GetInvoiceSequenceForDate(monthlyNullTerms, new DateTime(2025, 7, 15)).Should()
            .Be(0);
        var monthlyZeroTerms = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 10, 1),
            PayAmount = 3000,
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PaymentTerms = 0
        };
        _calc.GetInvoiceSequenceForDate(monthlyZeroTerms, new DateTime(2025, 7, 15)).Should()
            .Be(0);

        // Yearly
        var yearlyNullTerms = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 10, 1),
            PayAmount = 3000,
            PaymentIntervalType = PaymentIntervalType.Yearly,
            PaymentTerms = null
        };
        _calc.GetInvoiceSequenceForDate(yearlyNullTerms, new DateTime(2025, 7, 15)).Should()
            .Be(0);
        var yearlyZeroTerms = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 10, 1),
            PayAmount = 3000,
            PaymentIntervalType = PaymentIntervalType.Yearly,
            PaymentTerms = 0
        };
        _calc.GetInvoiceSequenceForDate(yearlyZeroTerms, new DateTime(2025, 7, 15)).Should()
            .Be(0);

        // Custom
        var customNullTerms = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 10, 1),
            PayAmount = 3000,
            PaymentIntervalType = PaymentIntervalType.Custom,
            PaymentTerms = null,
            PaymentSplits = []
        };
        _calc.GetInvoiceSequenceForDate(customNullTerms, new DateTime(2025, 7, 15)).Should()
            .Be(0);
        var customZeroTerms = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 10, 1),
            PayAmount = 3000,
            PaymentIntervalType = PaymentIntervalType.Custom,
            PaymentTerms = 0,
            PaymentSplits = []
        };
        _calc.GetInvoiceSequenceForDate(customZeroTerms, new DateTime(2025, 7, 15)).Should()
            .Be(0);
    }

    [Test]
    public void Custom_Splits_Null_Empty_OutOfPeriod_Unordered()
    {
        var periodStart = new DateTime(2025, 7, 1);
        var periodEnd = new DateTime(2025, 12, 31);
        var todayFirst = new DateTime(2025, 7, 18); // targets 2025-08-01 when terms=14
        var todaySecond = new DateTime(2025, 8, 18); // targets 2025-09-01 when terms=14

        // Null/empty splits => sequence 0
        var billNull = new BillRecord
        {
            PeriodStart = periodStart,
            PeriodEnd = periodEnd,
            PaymentIntervalType = PaymentIntervalType.Custom,
            PaymentTerms = 14,
            PaymentSplits = null
        };
        _calc.GetInvoiceSequenceForDate(billNull, todayFirst).Should().Be(0);
        var billEmpty = new BillRecord
        {
            PeriodStart = periodStart,
            PeriodEnd = periodEnd,
            PaymentIntervalType = PaymentIntervalType.Custom,
            PaymentTerms = 14,
            PaymentSplits = []
        };
        _calc.GetInvoiceSequenceForDate(billEmpty, todayFirst).Should().Be(0);

        // Out-of-period split => ignored
        var billOutOfPeriod = new BillRecord
        {
            PeriodStart = periodStart,
            PeriodEnd = periodEnd,
            PaymentIntervalType = PaymentIntervalType.Custom,
            PaymentTerms = 14,
            PaymentSplits =
            [
                new PaymentSplit
                {
                    PaymentDate = new DateTime(2025, 6, 15), SplitPercentage = 50
                },
                new PaymentSplit
                {
                    PaymentDate = new DateTime(2026, 1, 1), SplitPercentage = 50
                }
            ]
        };
        _calc.GetInvoiceSequenceForDate(billOutOfPeriod, todayFirst).Should().Be(0);

        // Unordered splits => sequence indexes by ordered dates
        var billUnordered = new BillRecord
        {
            PeriodStart = periodStart,
            PeriodEnd = periodEnd,
            PaymentIntervalType = PaymentIntervalType.Custom,
            PaymentTerms = 14,
            PaymentSplits =
            [
                new PaymentSplit
                {
                    PaymentDate = new DateTime(2025, 9, 1), SplitPercentage = 50
                },
                new PaymentSplit
                {
                    PaymentDate = new DateTime(2025, 8, 1), SplitPercentage = 50
                }
            ]
        };
        _calc.GetInvoiceSequenceForDate(billUnordered, todayFirst).Should().Be(1);
        _calc.GetInvoiceSequenceForDate(billUnordered, todaySecond).Should().Be(2);
    }

    [Test]
    public void Monthly_Does_Not_Return_Sequence1_At_PeriodStart_Minus_Terms()
    {
        var bill = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 10, 1),
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PaymentTerms = 14
        };

        // Today equals PeriodStart - terms => should not produce sequence 1
        _calc.GetInvoiceSequenceForDate(bill, new DateTime(2025, 6, 17)).Should().Be(0);
    }

    [Test]
    public void Year_End_Transition_And_Yearly_Cycle_Detection()
    {
        // Monthly year-end transition
        var monthly = new BillRecord
        {
            PeriodStart = new DateTime(2025, 12, 31),
            PeriodEnd = new DateTime(2026, 6, 30),
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PaymentTerms = 14
        };
        // Cycle 2 start 2026-01-31, invoice 2026-01-17
        _calc.FindInvoiceCycleForDate(monthly, new DateTime(2026, 1, 17), 1).Should().Be(2);

        // Yearly 2-year subscription, cycle 2 invoice
        var yearly = new BillRecord
        {
            PeriodStart = new DateTime(2024, 1, 1),
            PeriodEnd = new DateTime(2026, 1, 1),
            PaymentIntervalType = PaymentIntervalType.Yearly,
            PaymentTerms = 14
        };
        // Cycle 2 start 2025-01-01, invoice 2024-12-18
        _calc.GetInvoiceSequenceForDate(yearly, new DateTime(2024, 12, 18)).Should().Be(2);
    }

    #endregion

    #region Amount calculation

    [Test]
    public void CalculateInvoiceAmount_Monthly_And_Yearly_And_Custom()
    {
        // Monthly: 3 months => 3000 / 3 = 1000
        var monthly = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 10, 1),
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PayAmount = 3000
        };
        _revenue.CalculateInvoiceAmount(monthly, new DateTime(2025, 7, 18))
            .Should().Be(1000);

        // Yearly: 12 months => 12,000 / 1 = 12,000
        var yearly = new BillRecord
        {
            PeriodStart = new DateTime(2025, 1, 1),
            PeriodEnd = new DateTime(2026, 1, 1),
            PaymentIntervalType = PaymentIntervalType.Yearly,
            PayAmount = 12000
        };
        _revenue.CalculateInvoiceAmount(yearly, new DateTime(2025, 6, 1))
            .Should().Be(12000);

        // Custom: uses split percentage for today
        var custom = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 12, 31),
            PaymentIntervalType = PaymentIntervalType.Custom,
            PaymentTerms = 10,
            PayAmount = 5000,
            PaymentSplits =
            [
                new PaymentSplit
                {
                    PaymentDate = new DateTime(2025, 7, 20), SplitPercentage = 30
                },
                new PaymentSplit
                {
                    PaymentDate = new DateTime(2025, 8, 20), SplitPercentage = 70
                }
            ]
        };
        _revenue.CalculateInvoiceAmount(custom, new DateTime(2025, 7, 10))
            .Should().Be(1500); // 30% of 5000
    }

    [Test]
    public void CalculateInvoiceAmount_Custom_ZeroOrNegativeSplit_Throws()
    {
        var bill = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 12, 31),
            PaymentIntervalType = PaymentIntervalType.Custom,
            PaymentTerms = 10,
            PayAmount = 5000,
            PaymentSplits =
            [
                new PaymentSplit
                {
                    PaymentDate = new DateTime(2025, 7, 20), SplitPercentage = 0
                }
            ]
        };
        Action actZero = () => _revenue.CalculateInvoiceAmount(bill, new DateTime(2025, 7, 10));
        actZero.Should().Throw<InvalidOperationException>();

        bill.PaymentSplits[0].SplitPercentage = -10;
        Action actNegative = () => _revenue.CalculateInvoiceAmount(bill, new DateTime(2025, 7, 10));
        actNegative.Should().Throw<InvalidOperationException>();
    }

    [Test]
    public void CalculateInvoiceAmount_InvalidPeriods_And_YearlyMonths_Throws()
    {
        var monthlyInvalid = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 6, 30), // end before start
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PayAmount = 1000
        };
        Action actMonthly = () =>
            _revenue.CalculateInvoiceAmount(monthlyInvalid, new DateTime(2025, 7, 15));
        actMonthly.Should().Throw<InvalidOperationException>();

        var yearlyInvalid = new BillRecord
        {
            PeriodStart = new DateTime(2025, 1, 1),
            PeriodEnd = new DateTime(2025, 11, 1), // 10 months, not multiple of 12
            PaymentIntervalType = PaymentIntervalType.Yearly,
            PayAmount = 12000
        };
        Action actYearly = () =>
            _revenue.CalculateInvoiceAmount(yearlyInvalid, new DateTime(2025, 6, 1));
        actYearly.Should().Throw<InvalidOperationException>();
    }

    [Test]
    public void CalculateInvoiceAmount_OneOff_Returns_Full_And_Monthly_Negative_Pay_Amount()
    {
        var oneOff = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 7, 2),
            PaymentIntervalType = PaymentIntervalType.OneOff,
            PayAmount = 1234
        };
        _revenue.CalculateInvoiceAmount(oneOff, new DateTime(2025, 7, 1)).Should().Be(1234);

        var monthlyNegative = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 10, 1),
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PayAmount = -3000
        };
        _revenue.CalculateInvoiceAmount(monthlyNegative, new DateTime(2025, 7, 18)).Should()
            .Be(-1000);
    }

    #endregion

    #region Misc

    [Test]
    public void Unsupported_Interval_Type_Behavior()
    {
        var bill = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 10, 1),
            PaymentIntervalType = "Weekly",
            PaymentTerms = 7,
            PayAmount = 1000
        };
        _calc.GetInvoiceSequenceForDate(bill, new DateTime(2025, 7, 10)).Should().Be(0);
        Action act = () => _revenue.CalculateInvoiceAmount(bill, new DateTime(2025, 7, 10));
        act.Should().Throw<InvalidOperationException>();
    }

    [Test]
    public void Metadata_Invalid_ActualPeriodEnd_Falls_Back_To_PeriodEnd()
    {
        var bill = new BillRecord
        {
            PeriodStart = new DateTime(2025, 7, 1),
            PeriodEnd = new DateTime(2025, 10, 1),
            PaymentIntervalType = PaymentIntervalType.Monthly,
            PaymentTerms = 14,
            metadata = new Dictionary<string, string>
            {
                ["actual_period_end"] = "invalid-date"
            }
        };

        // Should behave as if no metadata overrides; cycle 2 start 2025-08-01, invoice 2025-07-18
        _calc.GetInvoiceSequenceForDate(bill, new DateTime(2025, 7, 18)).Should().Be(2);
    }

    #endregion
}