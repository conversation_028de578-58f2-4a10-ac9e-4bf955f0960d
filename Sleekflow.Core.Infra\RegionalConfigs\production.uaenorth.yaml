location_name: u<PERSON><PERSON>h
sku_config:
  sleekflow_core:
    name: P3V3
    tier: PremiumV3
  sleekflow_core_db:
    name: GP_Gen5_2
    tier: GeneralPurpose
    family: Gen5
    capacity: 2
  sleekflow_analytic_db:
    name: HS_S_Gen5
    tier: Hyperscale
    family: Gen5
    capacity: 2
  sleekflow_powerflow:
    name: P1V3
    tier: PremiumV3
  sleekflow_sleek_pay:
    name: P0V3
    tier: PremiumV3
  redis:
    default:
      name: Standard
      family: C
      capacity: 1
    caching:
      name: Standard
      family: C
      capacity: 1
sql_db_config:
  administrator_login_random_secret: sS6Gm7ktyukZ93lQ4twr3tqKA4Jpw7H5iXVb7FQUiB/k5AfAjHMWYLRQlXbDadEDS=6U2siasJl4jN1F
  administrator_login_password_random_secret: FZR+py5v7KsRf17lOMY2oYcBRD4gDpC9BT59cF9Y6RdyiiVJDGjpFwxkFGEXknrIkxMFZ2pvhaZzFs30
  whitelist_ip_ranges:
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: **************
      end_ip_address: **************
    - start_ip_address: *************
      end_ip_address: *************
    - start_ip_address: *************
      end_ip_address: *************
    - start_ip_address: **************
      end_ip_address: **************
  is_read_scale_enable: "false"
  high_availability_replica_count: 0
vnet:
  default_address_space: ********/16
  default_subnet_address_prefix: ********/24
  sleekflow_core_db_address_prefix: ********/24
  sleekflow_core_address_prefix: ********/24
  sleekflow_powerflow_address_prefix: ********/24
  sleekflow_sleek_pay_address_prefix: ********/24
  sleekflow_core_worker_address_prefix: ********/24
auto_scale_config:
  sleekflow_core:
    capacity:
      default: "3"
      maximum: "10"
      minimum: "2"
    scale_out_instances: "1"
    scale_down_instances: "1"
sleekflow_core_config:
  aspnetcore_environment: production
  logger:
    gcp_is_enabled: "TRUE"
    gcp_project_id: my-production-project-405815
    gcp_credential_json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  audit_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/audit-hub
    key: 00Qjp32oVNdhB+Kiwr9/qp6/NjaIDjeSBHVfDQK7oGRyIEjAhXKy7w4U+kRSb3m1jRK9MX4M3yjDFLy760ap/wYKeLOdLuiDtJYa8bAmQjQ=haQscYJvWHm7M4Jm
  auth0:
    action_audience: https://api.sleekflow.io/
    action_issuer: https://sso.sleekflow.io/
    audience: https://api.sleekflow.io
    client_id: txLGs6X2eN17XOXuxZpDakoxpyxoOlHW
    client_secret: aK50ltUXOGHt0VKM+JUNU9t8qAlZpHVS8v4f28rOgZscQSdhaVly2EMY3rM2QLCkcL3A91wqPJgCkWG/RWNI1KBwPv2gNpf7XhQqWGMINa4=PWC34E=dUaBT5Hbr
    database_connection_name: Sleekflow-Username-Password-Authentication
    domain: sleekflow.eu.auth0.com
    http_retries: 10
    issuers:
      - https://sso.sleekflow.io/
      - https://sleekflow.eu.auth0.com
    namespace: https://app.sleekflow.io/
    role_claim_type: roles
    user_email_claim_type: email
    user_id_claim_type: user_id
    username_claim_type: user_name
    tenant_hub_secret_key: nzFmQyuaAOWWHQs1jSInlajStKrMCNaqMc66jDfTLvw81tl7dOcocIMYQHW9RRmJQfpuQjsGPsMWM7d9I5DWad6Svh6NowxOSbIxmAgYyxk=Q2W16jvEWnkDLg9=
    health_check:
      is_enabled: "false"
      client_id: OrxDhNRbAyWXKoSVoMuS0hIWNo3v2An0
      client_secret: /JcRiKhllkt8sRTAtkq7Ul2uffM+EWJo1vsYnBYXq22wsTiJG/XcaT+frEHl6RKJXay3uwropVTZtlAJd/PSW2+12oPukLzs9tcQGH4pVtM=mpaxpjV4jQ2Sfeb3
      username: *******
      password: dfuiogho2420h0bjnbj3##$
  azure:
    media_service:
      account_name: sfmediaproduction
      client_id: 048e63db-bf3d-442e-bf1e-d30b2fd4b555
      client_secret: O5xHlFQVorrK/Fk6Ew80/nV4WvK/BoVMIWCiw7iEUZLL6vrwHjZbRT87GsQjiZQSgTj0jYvDsbX0VN6z
      resource_group: sleekflow-resource-group-production853b96c8
      subscription_id: c19c9b56-93e9-4d4c-bc81-838bd3f72ad6
      tenant_id: d66fa1cc-347d-42e9-9444-19c5fd0bbcce
    text_analytics_credentials: 226f2d8217704dd6b223b4a1fd6e51c6
    text_analytics_url: https://sleekflow-prod-sp-text-analytics.cognitiveservices.azure.com/
  application_insights:
    is_telemetry_tracer_enabled: "true"
    is_sampling_disabled: "false"
  beamer:
    api_key: nk3l/NVe9dr7shVVNQ7qzdEssQDHMS+wFCESCgYN0jMjSXDQGfrgZbE1yzlF7G+iLWW4DiZm6mDNWbiE
    api_url: https://api.getbeamer.com/v0/nps
    webhook_verify_key: z2txAHuNeAWasai0YnzNaGj8BpkXdYdhAx3IGNk5Bxg9SRq16uVn0GHQs/IWohuy3uVPCgrwd+JLW1ihaUeK+sJ2c429eCyOpuD2zzH4Owk=Pz1t3=PYxtVvW5rK
  chat_api:
    api_key: qtQDRSuXdKJz9255aoVZ0wX5y94g4vK1QgI2FOMiCZo=G1NmbYMv9aJhgKEw
    api_url: https://us-central1-app-chat-api-com.cloudfunctions.net
  commerce_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/commerce-hub
    key: ****************************************************/gQCyth12Asn150027cLl8S6+CCFVeElAPAjR+sj/qevYSkRIjsk4PCH7WfeNetTTfXF5S2iKAiAemy8eT5ihWNDU4jC1hjfp11fbtJJ2UKVtUMdHUxrY0fNvMifWmkP989k9BOkTaDuEzsiXHNER5fem3O=
  crm_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/crm-hub
    key: Q1e/HY1n2vitec0ua81A9onN8pwq3RDfRuJYY5CZ+nnXCkL5yzz5+N02jLYwBerRBSiOFUezkdZbba66
  data_snapshot:
    is_enable: "true"
  development:
    redirect_fb_webhook: https://9633-42-200-140-118.ngrok.io/facebook/Webhook
  epplus:
    excel_package:
      license_context: NonCommercial
  facebook:
    client_id: 812364635796464
    client_secret: mncjDrU6bKVSpiNKOaJQQ7n3q/AmO4OONcZFcPUyXvOmyd/QOU2NNxDK3ZbGyh8iw6S9G6myXB6igoH0
  environment_features:
    is_recurring_job_enabled: "true"
  ffmpeg:
    ffmpeg_exe_name: /usr/bin/ffmpeg
  flow_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/flow-hub
    key: iymGb97mGzzPCmVeiKOZlEjmkoLJ6sQCu5mIO9LlbX66QdaUKMRmX6CZQNJgaRr28eJo72akPU7QVWnC8N/DJ2wIwoMZkm9NuhS56y62bnw=TTSyYZKcJfcKo5SE
  hub_spot:
    internal_hub_spot_api_key: FJqn0V/rK50ptGTdBAydPe+e4Nkr+jcfFrKSInNrrotK3EyAsvoSiLveHXKyQiCQp5kDRXDVFTZ7w8iq
    is_enable: "true"
  instrumentation_engine_extension_version: disabled
  internal_google_cloud:
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  general_google_cloud:
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    project_id: my-production-project-405815
    server_location: europe-west1
    google_storage_bucket_name: sleekflow-transcoder-prod-eu
  ip_look_up:
    key: DaxejwttPW2mnagGRHTMmeSA6UNQX3WbbWZjWzrOrzo/O/2vyga+1gv9NidxkZdOiUhhmgExxqwQlYan
  intelligent_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/intelligent-hub
    key: mSidoCZ8Czf78HuFF+PTPPiXgiAFEMW9kZku2Ahez15+LeG8AE/yhL3i77uSZJpITzTC4334c19Mq1sRYo3hGQ/6rNqfES8FGhR3VzcOUyg=91wOPk10t11asJhZ
  webhook_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/webhook-hub
    key: MD18xhxSVmJB1bBbBguNP0c19nzucdX4/LZfsp+p66Re3q/vAu56lMmJZjs1HHX29DN5uA9RP4EiTT2+c8oZitfytqn+HMw6tHe7TQLGlp0=udmeRJCz=sJVJ5GR
    auth_secret_key: b4U/Z54ns88/QvSnwzzB5QHPTB21DG1WYTVql94DLNzpFesuV6YtWOnVG3EiGpTJ0UpxdyBm8oDoduROnkicKvXynu4VPA+vDa7nM0sRyUw=8BdZ1I7NCojEtY02
  message_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/messaging-hub
    key: hU94nG7LvLGP7OmuG+gS8DTsVbxlHdUJNdr8+wswnsp5rEMy7pEmbKcIDaymstOY74hAUcufvJOON1zT
  mixpanel:
    token: ef72dd13a0ffccb584cfdf75d3160e25
  mobile_app_management_extension_version: latest
  notification_hub:
    connection_string: Endpoint=sb://sleekflowproduction.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=pbzIBQQoNB8rGgrHdt1vmCIuTIB/Aaey5iION3eiCbQ=
    hub_name: SleekflowProduction
  public_api_gateway:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/public-api-gateway
    key: 64ZauE7ISAAQNY15lKKkbff8qFbvpge+Vvm5MSjwG0evdjF11KtqGs4NAYFpXsR4y9qPNRYBLBl8Y1yF
  reseller:
    domain_name: https://partner.sleekflow.io
  rewardful:
    api_secret: pzy1LTySnZoQPepw6VvJ2vU1SU7/bdJqeU2C2ApO9CUZ38Q7+rQHSaDJCl33veYXrjJySf921bUVxifg
  salesforce:
    custom_active_web_app: https://sfmc-custom-activity.vercel.app
  share_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/share-hub
    key: KHiZKT8/udygI60J7FCELZPyb5tmkuo5Zt1j1LUv97a9cAtf+3hybXD7OxwMNkf08tu9QR=7HnEOICkJ
  shopify:
    shopify_api_key: RKUDL/rA2CnZUo1kL7czAENxMWPWP/aNWzWcaZCmBcDngUCkAMLGlwVQLfyZI+DUz6PriGaEPodpmt8U
    shopify_secret_key: p3vHIR9yKrYjb72948Qm3m9GfG7kYVvBGAAf4mvt2FfWfILFCEuxVsDc1oEzQZ0VneGJ0Ri7sbBdy6P0
  sleek_pay:
    domain_name: https://pay-uaen.sleekflow.io
    shopify_graphql_admin_api_version: 2022-10
    stripe:
      public_keys:
        default: FVSl1Qk7oVZUACrrgSltq/7nCkHVRmvPnR6T+uUJ66VtZ7UKNNFUU+RgR9KtCfXSBKwMTMNGfOSsAORjs3tfZUU3yc7nMSuz1NeRpEaQI8vl9ZP2jRPeU0MtA2nWxzBfmrA9GEr0436WBSmRiq87yQ==PVr=oO6TCGN4Wfob
        gb: qBEwrOJviNmNpMszI5ThuUn8uoFOGxOi0WhP1nlWQXikPsPefNUP5pgHrB+3BRWpwx2DgDlqdpE9K3nJpc/gDGwc3gvEilCDm6Sm0kBgfl0XuRmzMv4YaGvlSsGLcjZQhhoMbYO4rCqBHscojJjfiw==nJdr42VRkzqIIqN6
        hk: LsviBRundwafPBA78qtz4BJN8JbsVkF3vjioAakvg3iC0ioN1DHU0HqgcQ7DTYy+jyBEPr9pvI90iaSDSLrQ55xclz9F9YtQGTJ8Wmb1GsV03TxwXbHkAKDmOej4GNd8HUP2ya8uJg95WOGflabhOA==ti6RbC6=sGkV87hR
        my: 0Kh/mfumXJ/CQIBNeahDtKh9g+dWBrTQ1iTNKVs+/CnKPGEgpWzhlHR5QQ8PUIhdGBdMZCN1cpAirglrqNtwPzbPPSNWMA2sH9OUQ50Ji97jTKXOIWbHYJIkeRR4dD9sb4HJr9m1ILHpWM7G7/hfjg==x=sxFpN1khCvKj03
        sg: Xcqm+YsjCkmk6DGBxHje/EhKDalYwd75Dwl6y2Xh2K2SSdSrrHzmBJksTER/NZXfdyG/qxmHeqDQZ10rCNC08AXNBReDd0AzUXpjTi+P1uH9Z7AueLaE1S4jzf8IWuRkNanlTQK+0hdOHJSTzjAW/w==IOOfLZR5temNeoJ6
      secret_keys:
        default: sUVdTUmTdxTzjl8nIGsiAIRdgaHr2CG1U2qSIZUIrZM+V1Er49jUQAaSKMtEY8iJCIimCBPr+X53GrodMVyJ2vc9iq+Gz9PV2jGX73c5lCiuWcjDbHfjwX7ouPd9yWNzQTSrYnQehP1ZABM1+X1F2w==WnHGTgFBcXREcQGa
        gb: 14CI8F+eOl9j18t2NieQkGutV6JQwXcsp8cl9OQm4uRm4tcJh8NU/l1sj59ujpUkXbPNO2xhNY6daWvFTTlJGfR7V3MXnaqJ4w3UFkfQxzUm9SK1qmn/QikyJYxwuXjZienn6k4P8g5saF+Fawr/Uw==CsDYAbBfxM4eZNTg
        hk: DtuqwFroN+7KCR4iS8QvnSBKz+kfbSsQ+YtJSAhzX5GlhcKOWEPFluL8Ks/EvmPYZhwi5rYnAa/L9RgX6TYvz9rvjx+W66wh52rJEqJlxL5O+ex+hPnl6laNOBLIWu418abdgpqrc6K+SZQI7xbWng==mKTH=bJ4WV91XEO2
        my: TPLSTQqh02XtsOE5VBIefhmqA4K7Lt8HoRPX9lejaua6rJ14yM6+ZgWGqA7HEwyO1bExbtISrKUWwPaIIIbbpfXLQH0Mf0BgOBWT7M0QRORcRT98x8IHNfrwz4296rFtMvTTnVwF7fLXcevCtBO4mQ==QpAF6PMk=yIBx3uS
        sg: QDIYgDmMWlOJQ0I+V9M+ANH13qdM+OqmMrt9gT49GsUDb9ZaNsx0OS7P2FYxPBEUIdWZgJDcbcT+Z4930XXW8HIHmtpJEEPSzxlK8m7slt8WVuYDpr86g9XhwQ8YYoZqNOdN3GWmE/PHEL1smPCaag==px7WNtsF1mWQe9x3
      connect_webhook_secrets:
        gb: ktdl+3o7Ojl6QNPbLpgmDpgglHAougu2xZy1ko5B/xdpyrUbDxjt7viUcQIQyonnZRshgkIqu0I2gkqD
        hk: HVBTey9ar9yKNqvDwvlxfhwpmmTjl7s/ZGgBWrWaH0WzrhRjMVxTHo+4elnJI00bFBKb1hM5P2672qer
        my: l20cu0CAxG3PPx+FXJSUKJVZ7O8Pj8Pjcvaob7hWNuXikNGQ8cDgdCCeYGsErhhONYFB=5QwCmOe9IqH
        sg: vyoFdNASgHbWJqSDps/FIttJOfxKXo/mH2gBvs6iUTKaFOLrmy7mXW47lc7kx4dqLA7qzlZMNeLge4YC
      report_webhook_secrets:
        gb: j7NDXdsaN08L8lGAVCnM986FqZ8WL1w3Cn2x6djVjRNqtLK2jCooshCdrijy7hejjfUlXhi7xCJYKvZc
        hk: +SQ1VXOJsXW/A81xHbbSqLoggxrQAjrBGAtixa7FLiOZ483n7MTh6DYHUWMCIBHWsiwFfahHdm4t7P=6
        my: gC+nZchjqSywZ9hdnYL1QhphAwqeD4Vb9Pfo5SkrcZKDYkb6qs9BmF/BcpP7qsjcZImrNt8XKwP9fTmg
        sg: u39oh7HlpEneNxQliO3keeAybOniSJcuiIrcRhiNay1PQicafNNxG5ajdyGBP1x9ff=h2BzAzSOhs8fZ
      webhook_secrets:
        default: wtBmsk4FGEH8RxmF0kz+4KS5d3hhtl1qTmcx4clD3sdQzTGCIBpZ8AufY4ooWsWs1fYEDxO1cHhX46yK
        gb: 4PqwO8jXE/0ooqbaztVPeSqlelltq/Z/2910Zje9xp6G5NxSSmSrqh8WWRTYqvJ0qLz9kjS9hSXz4opY
        hk: crn6pW949Rf1PcGUZDEe5aUgNebZfFSFtnL46e5ugdBV3UxbH2hXqkQsi/encf+pbRG1zJCIgkL8=JQU
        my: xZtbVRDJvedLQGyhb67eVj2ldkSJbKjUuQR6W5on0hLKNudsK0bgoJYSaXbmz/PR6M0DmWbpSeJqj4IH
        sg: QU5cGM/ltUmqh8muOm4l5MubAlx9sq/ZgIX2r5tSKIXwijAF5XaWQSz7Yh7l/RfnRSRWqoH7vqFNEIUn
  snapshot_debugger_extension_version: disabled
  sql_performance:
    from_raw_sql: "true"
    is_and_condition_enabled: "true"
    is_or_condition_enabled: "true"
    is_conversation_analytics_condition_enabled: "false"
    is_shopify_order_statistics_enabled: "false"
    is_sales_performance_enabled: "false"
    is_public_api_new_upsert_enabled: "false"
    is_zapier_new_upsert_enabled: "false"
  stripe:
    stripe_public_key: 752/rNQ2wsgcvIwtFyrooT7PUEtHKBf6XlsSbW06e3HGBNbvktzljj6LP4ixUJVfvJnCYbywJd7dopEh
    stripe_report_key: bMKOvjwqvNlXYa0wH4gsqGcA33ZkQylo9C5UE5pI32qIRQ7kqjwUj9JDoiVvnFmcYm83Sv1wxRVxLdsc8Ma86mizB0L1Xa4pho29EkQUNwMUd62d+l2Zq/We0E8MbRQTQCRLREsRioAXpTo64YU9eA==Usac0iXYL3G0=5qk
    stripe_secret_key: rILjNWEbvWTmPONTeVYSkj6MBVOye/EvN0zqrn7EE0jGnv3sjt9wzV4sq5ZTAmvB4ytNHPOBadrm9jhA
    stripe_webhook_secret: ZrtWC1wmvCiCkhRZ/vjIK0vDGLOU9yKHA4tjLl/e6hiw+EF5dZwB8uRa4wDsaMYhlKKmuwcuEUOOW3hv
  stripe_payment:
    stripe_payment_secret_key_gb: FEfqZ4tReYeME2Wahlwwz6hnEvxkqe4UO9pLO/hgan22ZsW+Fi/pLNLDThw/fDmlMIkkhBNRHnZYuMj/QD4lZA8fQy4c4PFWjqwPwMBhY65/v4IJmu0Hh0kSerIQvPAbPWQgMnHPRmbBw6phKrQc4A==RFhwG4mgtTODTn30
    stripe_payment_secret_key_hk: orqyRs1tYQ7+NuLDRWxcxy807aph5GzyXCr4JQVEg8jlCoiNFCKmN7HuABpB57av5yKso8s22g/kZT/qtb7WVnBvaG5dZPk+tB86rbnxc1SxnvBmr94KkGQhVL3YYGJCBXQPw/Qoi0cpUkLhSM35FQ==VCXGj7BERmd95cbj
    stripe_payment_secret_key_my: qv1ScG4U2NhR4riK2QNJ1I7u7kEL3JHhKLPuWbiCfqFVZmp2hUPksO0rTZYsB7sjWRNJDT3UoVrp786yCIySoh00YSo0DIaipDQ1870x5xy8FPV4CbRlxUpMluYGExnj9uv0xL3tRK+f5ZrRvHEkKA==IUwQiAkxGwVF3nyG
    stripe_payment_secret_key_sg: AIHon66xJ5Nvt+XTeitvJk5xz7ESqeXaMBcZL1o8XTdbOFKsJYD/BiH3BHN8LAmmG7rqc4A3Yk31oN8q+JmepT+z1VLh3cPysM7fEj5oPRL9/PLiggMhtWC3NYeQmJH2uzgjrvB29cQbYlUWmCLOAw==rYaEtjpnGxvXHhid
  stripe_report:
    stripe_report_webhook_secret_gb:
    stripe_report_webhook_secret_hk: S+wKLHt/it7JyJT75tILP83RAYcf/GJwJFuhc8zsTDg+rk7DotjMUtIZ3XrZMOuAgFSvmV0ELqm09SNO
    stripe_report_webhook_secret_my:
    stripe_report_webhook_secret_sg:
  tenant_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/tenant-hub
    key: nnMTnI3YtxazfWHI1xMMOQzDYBLM/wmmHDIIx1Zi8ifiy5O+kiOjFewdbBMGJbTZrKEHGbVUMVOIoGzXEtN9Uxfk8E6SiMQHfomREIQUIaE=pjJq=gAgvGneNkQi
    is_enable_tenant_logic: "true"
  ticketing_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/ticketing-hub
    key: jQaw9pOR+z9kPJl6riI67+JolNDRm3txozYqMsATo8KZgXByqj9mTvcbWVCPCfWL4KClmXekwfxoKq7POKAKgb6bT87FsZ/xTAt21+HcDxU=maCKCzvGCZAphhIB
    is_enable_ticketing_logic: "false"
  test_swaping: 1
  token:
    audience: https://sleekflow-prod-api.azurewebsites.net
  tokens:
    audience: https://travis-crm-api-hk.azurewebsites.net
    issuer: https://sleekflow-prod-api.azurewebsites.net
    key: LFUVhoi3VhaN5XnP4k30H8TIP3at1aHa5+uqApy1Shb+nm9GlbAAkYNmmvtQA37D/EhOpbDN830sx63PMCUG+Pe8/Xnq0inPCfcvIQZesww=dtkd7hBoH4kixZLc
    lifetime: 60
  user_event_hub:
    endpoint: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/user-event-hub
    key: UcHrA9uxOtPmFtGrOVUQLvAdKgnGQ8yXljNuoqhp0NXv7sIjdn6DwzkoCTaL9ffCNfE0l2itl4+U9f+MsocNCrlP1DRJjLxL0PZHkTvhQxc=7SGr10vRrSyt5GHP
  values:
    app_domain_name: https://app.sleekflow.io
    app_domain_name_v1: https://v1.sleekflow.io
    app_domain_name_v2: https://app.sleekflow.io
    share_link_function: https://share.sleekflow.io
    sleekflow_api_gateway: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net
    sleekflow_company_id: 471a6289-b9b7-43c3-b6ad-395a1992baea
    sleekflow_public_api_url: https://api.sleekflow.io
    sleekflow_public_api_key: IPgl3JYUntuOtW+3WEzbnu7Fz2fuMCnR1C3YRX9pfXOeT3E4AF2PEkLQF7e4cz1GWgmMVi5tBi45LNMV
    sleekflow_company_should_use_public_api: "true"
  website_http_logging_retention_days: 5
  website_node_default_version: 6.9.1
  whatsapp_cloud_api_template:
    default_image_blob_id: sleekflow.png
  xdt_microsoft_application_insights_base_extensions: disabled
  xdt_microsoft_application_insight_mode: default
  global_pricing:
    is_feature_enabled: "true"
    plan_migration_incentives_start_date: "2024-11-20"
    plan_migration_incentives_end_date: "2025-03-10"
  feature_flags:
    - feature_name: FlowBuilderMonetisation
      is_enabled: "true"
    - feature_name: CancelledSubscriptionTermination
      is_enabled: "true"
    - feature_name: Rbac
      is_enabled: "true"
  contact_safe_deletion:
    is_feature_enabled: "true"
  partner_stack:
    public_key: lqFAc41etZS6MCkEFVvJAMuaEzec++m00a6QOAzLkxT4bI3q6UgpLsfCVtxMEIKPFe7l08GMMqKK4VD0
    secret_key: mCHyO10PtRGKJ+r+MMi1DMCAkF1QY8m9j+/cap26MbD+woF+h6EbfINUDt2FB96dylYCDxaIf3Sd4IK9
  hangfire_worker:
    worker_count: 20
  internal_integration_hub:
    endpoint: "https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/internal-integration-hub"
    key: IiMuW8aOGjfVtlkTOQCMY8JdyPqHVOBU5ZlpWsAeJuza4tEzuDmNqJJyqkx/pjQl6mQGOEJDFL5r7H7LE3ZQE9XRHbthi8i5gkXJawhnJlk=kW4XZEG9S2BgxBHC
  hangfire_queues:
    disable_instances: ""
  integration_alert:
    endpoint: "https://api.sleekflow.io/api/notifications/integration-disconnected"
    api_key: "9XGfT3qJX94nKYIsqKfjJUYb+dojG9IdyamrpNklVRmZQcI5b5OYraLHOTirfdaElwD282Q9aNGotASf"
    host_company_id: "8d9eaafa-ab5b-47d3-8e0a-3920ac7c05cd"
    from_phone_number: "17209612030"
    template_name: "integration_disconnect_noti"
    facebook_lead_ads_help_center_url: "https://help.sleekflow.io/integrations/facebook-lead-ads-integration"
  rbac:
    is_middleware_verification_enabled: "false"
  flow_builder_flow_enrollments_incentives:
    period_start: "2025-02-07"
    period_end: "2025-06-25"
  hub_spot_smtp:
    username: "*******"
    password: "yus47rNx7M7Ca0xqdiQNZVGEOXb1yh"
    send_execution_usage_reached_threshold_email:
      username: "*******"
      password: "k4nCTQARqae2a2i3yX2d2qVuiJIvTz"
  legacy_premium_opt_in_upgrade_incentives:
    period_start: "2025-03-10"
    period_end: "2025-06-10"
  live_chat:
    secret_key: "LBjYeOQXFcb1lgeMriOci4BAQEoVCnYmwMfcq3hLMm9O8bQ6T2FDV7e8tryzzlvFBOqVI78Sashl97gz"