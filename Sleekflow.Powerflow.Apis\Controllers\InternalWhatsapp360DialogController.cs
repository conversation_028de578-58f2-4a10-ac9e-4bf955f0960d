using System.Globalization;
using System.Net.Mime;
using System.Text;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using CsvHelper;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Sleekflow.Powerflow.Apis.ViewModels;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.Configuration;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Exceptions;
using Travis_backend.Extensions;
using Travis_backend.Filters;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Services;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.MessageDomain.Models;
using WABA360Dialog;
using WABA360Dialog.ApiClient.Exceptions;
using WABA360Dialog.PartnerClient.Converters;
using WABA360Dialog.PartnerClient.Exceptions;
using WABA360Dialog.PartnerClient.Models;
using WABA360Dialog.PartnerClient.Payloads;
using Clear360DialogChannelTemplateCacheRequest = Sleekflow.Powerflow.Apis.ViewModels.Clear360DialogChannelTemplateCacheRequest;
using CmsCacheKeyHelper = Sleekflow.Powerflow.Apis.Helpers.CmsCacheKeyHelper;
using CmsConnectWhatsApp360DialogChannelRequest = Sleekflow.Powerflow.Apis.ViewModels.CmsConnectWhatsApp360DialogChannelRequest;
using CmsReconnect360DialogChannelRequest = Sleekflow.Powerflow.Apis.ViewModels.CmsReconnect360DialogChannelRequest;
using CmsUpdate360DialogChannelApiKeyRequest = Sleekflow.Powerflow.Apis.ViewModels.CmsUpdate360DialogChannelApiKeyRequest;
using CmsUpdate360DialogChannelStatusRequest = Sleekflow.Powerflow.Apis.ViewModels.CmsUpdate360DialogChannelStatusRequest;
using CmsWhatsApp360DialogUsageRecordViewModel = Sleekflow.Powerflow.Apis.ViewModels.CmsWhatsApp360DialogUsageRecordViewModel;
using CreateTransactionLogRequest = Sleekflow.Powerflow.Apis.ViewModels.CreateTransactionLogRequest;
using GetAllPartnerClientByClientResponse = Sleekflow.Powerflow.Apis.ViewModels.GetAllPartnerClientByClientResponse;
using GetAllPartnerClientRequest = Sleekflow.Powerflow.Apis.ViewModels.GetAllPartnerClientRequest;
using GetAllPartnerClientResponse = Sleekflow.Powerflow.Apis.ViewModels.GetAllPartnerClientResponse;
using GetPartnerClientUsageRequest = Sleekflow.Powerflow.Apis.ViewModels.GetPartnerClientUsageRequest;
using GetWhatsApp360DialogUsageRecordsRequest = Sleekflow.Powerflow.Apis.ViewModels.GetWhatsApp360DialogUsageRecordsRequest;
using GetWhatsApp360DialogUsageRecordsResponse = Sleekflow.Powerflow.Apis.ViewModels.GetWhatsApp360DialogUsageRecordsResponse;
using Migrate360DialogAutomationSendMessageDataRequest = Sleekflow.Powerflow.Apis.ViewModels.Migrate360DialogAutomationSendMessageDataRequest;
using PartnerChannelsByClientId = Sleekflow.Powerflow.Apis.ViewModels.PartnerChannelsByClientId;
using PartnerChannelsCompanyInfo = Sleekflow.Powerflow.Apis.ViewModels.PartnerChannelsCompanyInfo;
using ReCalculateUsageFromTransactionLogsRequest = Sleekflow.Powerflow.Apis.ViewModels.ReCalculateUsageFromTransactionLogsRequest;
using ReconnectWhatsApp360DialogConnectRequest = Sleekflow.Powerflow.Apis.ViewModels.ReconnectWhatsApp360DialogConnectRequest;
using ReconnectWhatsApp360DialogConnectResponse = Sleekflow.Powerflow.Apis.ViewModels.ReconnectWhatsApp360DialogConnectResponse;
using ToggleCompanyEnterpriseTopUpSettingRequest = Sleekflow.Powerflow.Apis.ViewModels.ToggleCompanyEnterpriseTopUpSettingRequest;
using UpdateChannelStatusRequest = Sleekflow.Powerflow.Apis.ViewModels.UpdateChannelStatusRequest;
using UpdateClientMaxChannelRequest = Sleekflow.Powerflow.Apis.ViewModels.UpdateClientMaxChannelRequest;
using UpdateUsageRecordMarkupRequest = Sleekflow.Powerflow.Apis.ViewModels.UpdateUsageRecordMarkupRequest;
using WhatsApp360DialogUsageTransactionLogDto = Sleekflow.Powerflow.Apis.ViewModels.WhatsApp360DialogUsageTransactionLogDto;

namespace Sleekflow.Powerflow.Apis.Controllers;

/// <summary>
/// Manage 360Dialog Usage, TopUp.
/// </summary>
[Route("/internal/360dialog/[action]")]
[Authorize(Roles = ApplicationUserRole.InternalCmsSuperUser)] // Basic Role Requirement
[TypeFilter(typeof(MessagingHubExceptionFilter))]
public class InternalWhatsapp360DialogController : InternalControllerBase
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly ICoreWhatsApp360DialogPartnerAuthService _coreWhatsApp360DialogPartnerAuthService;
    private readonly ICoreWhatsApp360DialogUsageService _coreWhatsApp360DialogUsageService;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly ICompanyInfoCacheService _companyInfoCacheService;
    private readonly IWhatsApp360DialogService _whatsApp360DialogService;
    private readonly IUserProfileService _userProfileService;
    private readonly IInternalWhatsappCloudApiService _internalWhatsappCloudApiService;

    public InternalWhatsapp360DialogController(
        ApplicationDbContext appDbContext,
        UserManager<ApplicationUser> userManager,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<InternalWhatsapp360DialogController> logger,
        ICoreWhatsApp360DialogPartnerAuthService coreWhatsApp360DialogPartnerAuthService,
        ICoreWhatsApp360DialogUsageService coreWhatsApp360DialogUsageService,
        ICacheManagerService cacheManagerService,
        ICompanyInfoCacheService companyInfoCacheService,
        IWhatsApp360DialogService whatsApp360DialogService,
        IUserProfileService userProfileService,
        IInternalWhatsappCloudApiService internalWhatsappCloudApiService)
        : base(userManager)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _coreWhatsApp360DialogPartnerAuthService = coreWhatsApp360DialogPartnerAuthService;
        _coreWhatsApp360DialogUsageService = coreWhatsApp360DialogUsageService;
        _cacheManagerService = cacheManagerService;
        _companyInfoCacheService = companyInfoCacheService;
        _whatsApp360DialogService = whatsApp360DialogService;
        _userProfileService = userProfileService;
        _internalWhatsappCloudApiService = internalWhatsappCloudApiService;
    }

    /// <summary>
    /// Get All Partner Channels.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<List<GetAllPartnerClientResponse>>> GetAllPartnerClients(
        [FromBody]
        GetAllPartnerClientRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var allPartnerChannels = await _whatsApp360DialogService.GetAllPartnerChannels(request.PartnerId);

        return Ok(
            new GetAllPartnerClientResponse
            {
                PartnerId = request.PartnerId, PartnerChannels = allPartnerChannels
            });
    }

    /// <summary>
    /// Get All Partner Channels by client id.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetAllPartnerClientByClientResponse>> GetAllPartnerChannelsByClientId(
        [FromBody]
        GetAllPartnerClientRequest request)
    {
        var cacheKey = CmsCacheKeyHelper.GetAllPartnerChannelsByClientId(request.PartnerId);

        if (request.AllowCache)
        {
            var resultCacheData = await _cacheManagerService.GetCacheWithConstantKeyAsync(cacheKey);

            if (!string.IsNullOrWhiteSpace(resultCacheData))
            {
                return Content(resultCacheData, MediaTypeNames.Application.Json, Encoding.UTF8);
            }
        }

        var allPartnerChannels = await _whatsApp360DialogService.GetAllPartnerChannels(request.PartnerId);
        var allPartnerClients = await _whatsApp360DialogService.GetAllPartnerClients(request.PartnerId);

        var partnerChannelsByClientId = allPartnerChannels.GroupBy(x => x.ClientId)
            .Select(
                x =>
                {
                    var client = x.First().Client;
                    var clientInfo = allPartnerClients.First(c => c.Id == x.Key);
                    return new PartnerChannelsByClientId
                    {
                        ClientId = x.Key,
                        ClientName = client.Name,
                        ClientStatus = clientInfo.Status.GetString(),
                        MaxChannels = clientInfo.MaxChannels,
                        NumberOfChannels = x.Count(),
                        CreatedAt = clientInfo.CreatedAt,
                        Suspicious = clientInfo.Suspicious,
                        PartnerId = request.PartnerId,
                        Channels = x.ToList(),
                    };
                })
            .ToList();

        var connectedChannelClients = await _appDbContext
            .ConfigWhatsApp360DialogConfigs
            .AsNoTracking()
            .Include(x => x.Company)
            .Where(x => partnerChannelsByClientId.Select(c => c.ClientId).Contains(x.ClientId))
            .Select(
                x => new
                {
                    ClientId = x.ClientId, CompanyId = x.CompanyId, CompanyName = x.Company.CompanyName,
                })
            .ToListAsync();

        var usageRecords = await _appDbContext.CompanyWhatsApp360DialogUsageRecords
            .AsNoTracking()
            .Where(x => x.PartnerId == request.PartnerId)
            .ToListAsync();

        connectedChannelClients.ForEach(
            x =>
            {
                var channelsByClientId = partnerChannelsByClientId.FirstOrDefault(p => p.ClientId == x.ClientId);
                if (channelsByClientId != null)
                {
                    var companyInfo = new PartnerChannelsCompanyInfo
                    {
                        CompanyId = x.CompanyId, CompanyName = x.CompanyName
                    };

                    var usageRecord = usageRecords.FirstOrDefault(u => u.Waba360DialogClientId == x.ClientId);
                    if (usageRecord != null)
                    {
                        companyInfo.WhatsApp360DialogUsageRecordId = usageRecord.Id;
                        companyInfo.Credit = usageRecord.Credit;
                        companyInfo.AllTimeUsage = usageRecord.AllTimeUsage;
                        companyInfo.Balance = usageRecord.Balance;
                        companyInfo.TopUpMode = usageRecord.TopUpMode;
                        companyInfo.DirectPaymentBalance = usageRecord.DirectPaymentBalance;
                    }

                    if (channelsByClientId.Companies.All(c => c.CompanyId != companyInfo.CompanyId))
                    {
                        channelsByClientId.Companies.Add(companyInfo);
                    }
                }
            });

        var result = new GetAllPartnerClientByClientResponse
        {
            PartnerId = request.PartnerId, PartnerChannels = partnerChannelsByClientId
        };

        await _cacheManagerService.SaveCacheWithConstantKeyAsync(
            cacheKey,
            result,
            TimeSpan.FromHours(1),
            _jsonSerializerSettings
        );

        return Ok(result);
    }

    /// <summary>
    /// Get Partner Client Usages.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetAllPartnerClientByClientResponse>> GetPartnerClientUsageRequest(
        [FromBody]
        GetPartnerClientUsageRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var result = await _coreWhatsApp360DialogUsageService.GetAllPartnerClientsUsage(
            new DateTime(request.From.Year, request.From.Month, 1),
            new DateTime(request.To.Year, request.To.Month, 1),
            request.PartnerIds,
            request.ClientIds);

        if (request.IsResponseAsCsv)
        {
            var ms = new MemoryStream();
            await using (var writer = new StreamWriter(ms, leaveOpen: true))
            {
                await using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
                {
                    await csv.WriteRecordsAsync(result);
                }
            }

            return File(
                ms.ToArray(),
                "text/csv",
                $"PartnerClientUsage-{new DateTime(request.From.Year, request.From.Month, 1):yyyyMMdd}-{new DateTime(request.To.Year, request.To.Month, 1):yyyyMMdd}.json");
        }

        return Ok(result);
    }

    /// <summary>
    /// Sync Missing UserProfile 360dialog Senders.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> SyncMissingUserProfileSenders([FromQuery] string companyId)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        BackgroundJob.Enqueue<WhatsApp360DialogService>(
            x => x.AddBackWhatsApp360DialogUserInUserProfilesAndConversations(companyId));

        return Ok();
    }

    /// <summary>
    /// Update Client Max Channel.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> UpdateClientMaxChannel([FromBody] UpdateClientMaxChannelRequest maxChannelRequest)
    {
        var token = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(maxChannelRequest.PartnerId);

        var partnerApiClient = new WABA360DialogPartnerClient(new PartnerInfo(token.PartnerId), token.AccessToken);
        var getPartnerClientsResponse = await partnerApiClient.GetPartnerClientsAsync(
            filters: new GetPartnerClientsFilter()
            {
                Id = maxChannelRequest.ClientId
            });

        var client = getPartnerClientsResponse.Clients.FirstOrDefault();

        if (client == null)
        {
            return Ok(
                new ResponseViewModel()
                {
                    message = "Client not found"
                });
        }

        var clientChannels = await partnerApiClient.GetPartnerChannelsAsync(
            1000,
            0,
            filters: new GetPartnerChannelsFilter()
            {
                ClientId = maxChannelRequest.ClientId
            });

        if (maxChannelRequest.MaxClient.HasValue && clientChannels.Count >= maxChannelRequest.MaxClient)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message =
                        $"The client currently has {clientChannels.Count} channels, but input max value({maxChannelRequest.MaxClient}) is less then that."
                });
        }

        try
        {
            var result = await partnerApiClient.UpdateClientAsync(
                maxChannelRequest.ClientId,
                string.Empty,
                maxChannelRequest.MaxClient);

            return Ok(
                new ResponseViewModel()
                {
                    message = $"The client max client updated."
                });
        }
        catch (Exception e)
        {
            return Ok(
                new ResponseViewModel()
                {
                    message = "Error on updating client maximum channel value."
                });
        }
    }

    public record BulkUpdateClientRequest(string PartnerId, int? AdditionalMaxClient);

    /// <summary>
    /// Update Client.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult> BulkUpdateClientMaxChannel([FromBody] BulkUpdateClientRequest request)
    {
        var token = await _coreWhatsApp360DialogPartnerAuthService.GetAuthTokenAsync(request.PartnerId);

        var partnerApiClient = new WABA360DialogPartnerClient(new PartnerInfo(token.PartnerId), token.AccessToken);

        var getPartnerClientsResponse = await partnerApiClient.GetPartnerClientsAsync(2000, 0);

        foreach (var client in getPartnerClientsResponse.Clients)
        {
            if (client.Id == "SYDJjUeJCL")
            {
                continue;
            }

            try
            {
                var clientChannels = await partnerApiClient.GetPartnerChannelsAsync(
                    1000,
                    0,
                    filters: new GetPartnerChannelsFilter()
                    {
                        ClientId = client.Id
                    });
                var max = clientChannels.Count + request.AdditionalMaxClient;

                await partnerApiClient.UpdateClientAsync(client.Id, client.PartnerPayload ?? string.Empty, max);
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToString());
            }
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "done"
            });
    }

    /// <summary>
    /// Enable Company Enterprise Top Up Setting.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> Add360DialogChannel(
        [FromBody]
        CmsConnectWhatsApp360DialogChannelRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        WhatsApp360DialogConfig newWhatsApp360DialogConfig = null;

        try
        {
            newWhatsApp360DialogConfig = await _whatsApp360DialogService.ConnectChannel(
                request.CompanyId,
                request.ChannelName,
                request.ApiKey);
        }
        catch (ApiClientException ex)
        {
            return BadRequest(
                new WhatsApp360DialogErrorResponse()
                {
                    Message = "Error during connect channel! The provided API Key is invalid.",
                    WhatsApp360DialogApiError = new WhatsApp360DialogApiError()
                    {
                        Error = ex.Error?.ToList(), Meta = ex.Meta
                    }
                });
        }
        catch (Exception ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = $"Error during connect channel! {ex.Message}"
                });
        }

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel()
            {
                message = $"Added new 360dialog channel: '{newWhatsApp360DialogConfig.WhatsAppPhoneNumber}'."
            });
    }

    /// <summary>
    /// Remove 360Dialog Channel using existing 360dialog Api Key.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> Clear360DialogChannelTemplateCache(
        [FromBody]
        Clear360DialogChannelTemplateCacheRequest request)
    {
        if (!await _appDbContext.ConfigWhatsApp360DialogConfigs.AnyAsync(
                x => x.CompanyId == request.CompanyId && x.Id == request.WhatsApp360DialogConfigId))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Channel not found."
                });
        }

        var cacheKey = WhatsApp360DialogCacheKeyHelper.GetTemplateResponseCacheKey(
            request.CompanyId,
            request.WhatsApp360DialogConfigId,
            1000,
            0);
        await _cacheManagerService.DeleteCacheWithConstantKeyAsync(cacheKey);

        return Ok(
            new ResponseViewModel()
            {
                message = "Cache cleared."
            });
    }

    /// <summary>
    /// Remove 360Dialog Channel using existing 360dialog Api Key.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> Remove360DialogChannel(
        [FromBody]
        CmsReconnect360DialogChannelRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        if (!await _appDbContext.ConfigWhatsApp360DialogConfigs.AnyAsync(
                x => x.CompanyId == request.CompanyId && x.Id == request.WhatsApp360DialogConfigId))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Channel not found."
                });
        }

        await _whatsApp360DialogService.RemoveChannel(request.CompanyId, request.WhatsApp360DialogConfigId);

        await _companyInfoCacheService.RemoveCompanyInfoCache(request.CompanyId);

        return Ok(
            new ResponseViewModel()
            {
                message = "Channel deleted."
            });
    }

    /// <summary>
    /// Reconnect 360Dialog Channel using existing Api Key.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> Reconnect360DialogChannel(
        [FromBody]
        CmsReconnect360DialogChannelRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        if (!await _appDbContext.ConfigWhatsApp360DialogConfigs.AnyAsync(
                x => x.CompanyId == request.CompanyId && x.Id == request.WhatsApp360DialogConfigId))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Channel not found."
                });
        }

        try
        {
            var whatsApp360DialogConfig = await _whatsApp360DialogService.ReconnectChannel(
                request.CompanyId,
                request.WhatsApp360DialogConfigId);
            return Ok(
                new ResponseViewModel()
                {
                    message = $"Reconnected 360dialog channel: '{whatsApp360DialogConfig.WhatsAppPhoneNumber}'."
                });
        }
        catch (ApiClientException ex)
        {
            await _whatsApp360DialogService.UpdateChannelErrorStatus(
                request.CompanyId,
                request.WhatsApp360DialogConfigId,
                ex);

            return BadRequest(
                new WhatsApp360DialogErrorResponse()
                {
                    Message = "Error during Reconnect 360 Dialog Channel.",
                    WhatsApp360DialogApiError = new WhatsApp360DialogApiError()
                    {
                        Error = ex.Error?.ToList(), Meta = ex.Meta
                    }
                });
        }
    }

    /// <summary>
    /// Update 360dialog Channel.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> Update360DialogChannelApiKey(
        [FromBody]
        CmsUpdate360DialogChannelApiKeyRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        if (!await _appDbContext.ConfigWhatsApp360DialogConfigs.AnyAsync(
                x => x.CompanyId == request.CompanyId && x.Id == request.WhatsApp360DialogConfigId))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Channel not found."
                });
        }

        try
        {
            var whatsApp360DialogConfig = await _whatsApp360DialogService.UpdateChannel(
                request.CompanyId,
                request.WhatsApp360DialogConfigId,
                null,
                request.ApiKey);
            return Ok(
                new ResponseViewModel()
                {
                    message = $"360dialog channel API Key Updated."
                });
        }
        catch (ApiClientException ex)
        {
            await _whatsApp360DialogService.UpdateChannelErrorStatus(
                request.CompanyId,
                request.WhatsApp360DialogConfigId,
                ex);

            return BadRequest(
                new WhatsApp360DialogErrorResponse()
                {
                    Message = "Error during Update 360 Dialog Channel.",
                    WhatsApp360DialogApiError = new WhatsApp360DialogApiError()
                    {
                        Error = ex.Error?.ToList(), Meta = ex.Meta
                    }
                });
        }
        catch (Exception e)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Error on updating 360 Dialog channel."
                });
        }
    }

    /// <summary>
    /// Update 360dialog Channel Status.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> Update360DialogChannelStatus(
        [FromBody]
        CmsUpdate360DialogChannelStatusRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        if (!await _appDbContext.ConfigWhatsApp360DialogConfigs.AnyAsync(
                x => x.CompanyId == request.CompanyId && x.Id == request.WhatsApp360DialogConfigId))
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Channel not found."
                });
        }

        try
        {
            if (request.IsSuspend)
            {
                await _whatsApp360DialogService.SuspendChannel(request.CompanyId, request.WhatsApp360DialogConfigId);
            }
            else
            {
                await _whatsApp360DialogService.ActivateChannel(request.CompanyId, request.WhatsApp360DialogConfigId);
            }

            return Ok(
                new ResponseViewModel()
                {
                    message = $"Update 360 Dialog Channel Status to ${(request.IsSuspend ? "Suspend" : "Active")}."
                });
        }
        catch (Exception ex)
        {
            return Ok(
                new WhatsApp360DialogErrorResponse()
                {
                    Message =
                        $"Error during Update 360 Dialog Channel Status to {(request.IsSuspend ? "Suspend" : "Active")}."
                });
        }
    }

    /// <summary>
    /// Enable Company Enterprise Top Up Setting.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> ToggleCompanyEnterpriseTopUpSetting(
        [FromBody]
        ToggleCompanyEnterpriseTopUpSettingRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var result = await _coreWhatsApp360DialogUsageService.EnablePartnerPayment(
            request.CompanyId,
            request.PartnerId,
            request.ClientId,
            request.MarkupPercentage);

        if (!result)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Error during enable Enterprise top up setting!"
                });
        }

        return Ok(
            new ResponseViewModel()
            {
                message = "Enterprise top up setting enabled."
            });
    }

    /// <summary>
    /// Get WhatsApp 360Dialog Usage Records.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetWhatsApp360DialogUsageRecordsResponse>> GetWhatsApp360DialogUsageRecords(
        [FromBody]
        GetWhatsApp360DialogUsageRecordsRequest request)
    {
        if (await GetCurrentValidInternalUser(
                new List<string>()
                {
                    ApplicationUserRole.InternalCmsSuperUser
                }) == null)
        {
            return Unauthorized();
        }

        var usageRecords = await _appDbContext
            .CompanyWhatsApp360DialogUsageRecords
            .AsSplitQuery()
            .AsNoTracking()
            .Include(x => x.TransactionLogs)
            .Where(x => x.CompanyId == request.CompanyId)
            .ProjectTo<CmsWhatsApp360DialogUsageRecordViewModel>(_mapper.ConfigurationProvider)
            .ToListAsync();

        var result = new GetWhatsApp360DialogUsageRecordsResponse
        {
            WhatsApp360DialogUsageRecords = usageRecords
        };

        usageRecords.ForEach(
            x =>
                x.TransactionLogs.Sort((x, y) => x.CreatedAt > y.CreatedAt ? -1 : 1));

        return Ok(result);
    }

    /// <summary>
    /// Update Usage Record Markup.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<WhatsApp360DialogUsageTransactionLogDto>> UpdateUsageRecordMarkup(
        [FromBody]
        UpdateUsageRecordMarkupRequest request)
    {
        var currentUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        var whatsApp360DialogUsageRecord = await _appDbContext
            .CompanyWhatsApp360DialogUsageRecords
            .Where(x => x.Id == request.WhatsApp360DialogUsageRecordId)
            .FirstOrDefaultAsync();

        if (whatsApp360DialogUsageRecord == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Usage Record Not Found."
                });
        }

        whatsApp360DialogUsageRecord.MarkupPercentage = request.MarkupPercentage;

        await _appDbContext.SaveChangesAsync();

        return Ok(
            new ResponseViewModel()
            {
                message = "Markup updated"
            });
    }

    /// <summary>
    /// Create Transaction Log.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> CreateTransactionLog(
        [FromBody]
        CreateTransactionLogRequest request)
    {
        var currentUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        var waba360DialogClientId = await _appDbContext
            .CompanyWhatsApp360DialogUsageRecords
            .Where(x => x.Id == request.WhatsApp360DialogUsageRecordId)
            .Select(x => x.Waba360DialogClientId)
            .FirstOrDefaultAsync();

        if (waba360DialogClientId == null)
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Usage Record Not Found."
                });
        }

        var whatsApp360DialogUsageRecords = await _appDbContext
            .CompanyWhatsApp360DialogUsageRecords
            .Where(x => x.Waba360DialogClientId == waba360DialogClientId)
            .ToListAsync();

        foreach (var whatsApp360DialogUsageRecord in whatsApp360DialogUsageRecords)
        {
            var newTransactionLog = new WhatsApp360DialogUsageTransactionLog
            {
                Total = request.Total,
                Currency = request.Currency,
                TransactionType = request.TransactionType,
                InternalUserId = currentUser.Id,
                InvoiceId = request.InvoiceId,
                IsInternalTestingUse = request.IsInternalTestingUse,
                CompanyId = whatsApp360DialogUsageRecord.CompanyId,
                IsMarkedInUsageRecord = false,
            };

            whatsApp360DialogUsageRecord.TransactionLogs.Add(newTransactionLog);

            await _appDbContext.SaveChangesAsync();

            await _coreWhatsApp360DialogUsageService.CalculateUsage(whatsApp360DialogUsageRecord.Id);
        }

        return Ok(
            new ResponseViewModel()
            {
                message =
                    $"{whatsApp360DialogUsageRecords.Count} {request.TransactionType.ToString()} Transaction created."
            });
    }

    /// <summary>
    /// Get All Top Up Transaction Logs
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<GetAll360DialogUsageTopUpTransactionLogsResponse>> GetAllTopUpTransactionLogs()
    {
        var currentUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        var topups = await _appDbContext
            .CompanyWhatsApp360DialogUsageTransactionLogs
            .Where(x => x.TransactionType == WhatsApp360DialogUsageTransactionType.TopUp)
            .Include(x => x.WhatsApp360DialogUsageRecord)
            .Include(x => x.WhatsApp360DialogUsageRecord.Company)
            .Select(
                x => new WhatsApp360DialogUsageTopUpTransactionLogDto
                {
                    Id = x.Id,
                    WhatsApp360DialogUsageRecordId = x.WhatsApp360DialogUsageRecordId,
                    CompanyId = x.CompanyId,
                    Total = x.Total,
                    Currency = x.Currency,
                    ToUsageCurrencyExchangeRate = x.ToUsageCurrencyExchangeRate,
                    InternalUserId = x.InternalUserId,
                    InternalUserDisplayName = _appDbContext.Users.First(u => u.Id == x.InternalUserId).DisplayName,
                    InvoiceId = x.InvoiceId,
                    IsInternalTestingUse = x.IsInternalTestingUse,
                    IsMarkedInUsageRecord = x.IsMarkedInUsageRecord,
                    TransactionType = x.TransactionType,
                    BillingPeriod = x.BillingPeriod,
                    ConversationPeriodUsage = x.ConversationPeriodUsage,
                    PhoneNumberPeriodUsage = x.PhoneNumberPeriodUsage,
                    NewPhoneNumberFee = x.NewPhoneNumberFee,
                    CreatedAt = x.CreatedAt,
                    UpdatedAt = x.UpdatedAt,
                    Waba360DialogClientId = x.WhatsApp360DialogUsageRecord.Waba360DialogClientId,
                    Waba360DialogClientName = x.WhatsApp360DialogUsageRecord.Waba360DialogClientName,
                    CompanyName = x.WhatsApp360DialogUsageRecord.Company.CompanyName
                })
            .OrderByDescending(x => x.CreatedAt)
            .ToListAsync();

        return Ok(new GetAll360DialogUsageTopUpTransactionLogsResponse()
        {
            Whatsapp360DialogUsageTopUpTransactionLogs = topups
        });
    }

    /// <summary>
    /// Re Calculate Usage From Transaction Logs.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<CmsWhatsApp360DialogUsageRecordViewModel>> ReCalculateUsageFromTransactionLogs(
        [FromBody]
        ReCalculateUsageFromTransactionLogsRequest request)
    {
        var currentUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        if (!await _appDbContext
                .CompanyWhatsApp360DialogUsageRecords
                .Where(x => x.Id == request.WhatsApp360DialogUsageRecordId)
                .AnyAsync())
        {
            return BadRequest(
                new ResponseViewModel
                {
                    message = "Usage Record Not Found."
                });
        }

        await _coreWhatsApp360DialogUsageService.ReCalculateUsageFromTransactionLogs(
            request.WhatsApp360DialogUsageRecordId);

        var usageRecords = await _appDbContext
            .CompanyWhatsApp360DialogUsageRecords
            .AsSplitQuery()
            .Include(x => x.TransactionLogs)
            .Where(x => x.Id == request.WhatsApp360DialogUsageRecordId)
            .ProjectTo<CmsWhatsApp360DialogUsageRecordViewModel>(_mapper.ConfigurationProvider)
            .FirstOrDefaultAsync();

        return Ok(usageRecords);
    }

    /// <summary>
    /// Updated Channel Status.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ResponseViewModel>> UpdateChannelStatus(
        [FromBody]
        UpdateChannelStatusRequest request)
    {
        var currentUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        try
        {
            var success = await _whatsApp360DialogService.UpdateCompanyChannelStatus(
                request.CompanyId,
                request.WhatsApp360DialogConfigId);

            return Ok(
                new ResponseViewModel()
                {
                    message = $"360dialog channel status updated"
                });
        }
        catch (ApiClientException ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName} endpoint] Company {CompanyId} error updating 360Dialog channel status for config id {360DialogConfigId}. {ExceptionMessage}",
                nameof(UpdateChannelStatus),
                request.CompanyId,
                request.WhatsApp360DialogConfigId,
                ex.Message);

            await _whatsApp360DialogService.UpdateChannelErrorStatus(
                request.CompanyId,
                request.WhatsApp360DialogConfigId,
                ex);

            return BadRequest(
                new ResponseViewModel()
                {
                    message = "error updating 360Dialog channel status"
                });
        }
    }

    /// <summary>
    /// Reconnect Channels.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<ReconnectWhatsApp360DialogConnectResponse>> ReconnectChannels(
        [FromBody]
        ReconnectWhatsApp360DialogConnectRequest request)
    {
        var currentUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        var whatsApp360DialogConfigs = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .AsNoTracking()
            .WhereIf(request.ConfigIds is { Count: > 0 }, x => request.ConfigIds.Contains(x.Id))
            .Select(
                x => new
                {
                    x.Id, x.CompanyId
                })
            .ToListAsync();

        var result = new ReconnectWhatsApp360DialogConnectResponse();

        foreach (var whatsApp360DialogConfig in whatsApp360DialogConfigs)
        {
            try
            {
                var success = await _whatsApp360DialogService.ReconnectChannel(
                    whatsApp360DialogConfig.CompanyId,
                    whatsApp360DialogConfig.Id);
                result.SuccessConfigIds.Add(whatsApp360DialogConfig.Id);
            }
            catch (ApiClientException ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId} error reconnect 360Dialog channel for config id {360DialogConfigId}. {ExceptionMessage}",
                    nameof(ReconnectChannels),
                    whatsApp360DialogConfig.CompanyId,
                    whatsApp360DialogConfig.Id,
                    ex.Message);

                result.FailedConfigIds.Add(whatsApp360DialogConfig.Id);
            }
        }

        return Ok(result);
    }

    /// <summary>
    /// Get 360Dialog Channel API.
    /// </summary>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [HttpPost]
    public async Task<ActionResult<CreateWhatsapp360DialogOnboardingChannelApiKeyResponse>>
        GetWhatsapp360DialogChannelApi([FromBody] CreateWhatsapp360DialogOnboardingChannelApiKeyRequest request)
    {
        var currentUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        try
        {
            var info = await _whatsApp360DialogService.CreateOnboardingChannelInfo(
                request.PartnerId,
                request.ClientId,
                request.ChannelId);

            return Ok(
                new CreateWhatsapp360DialogOnboardingChannelApiKeyResponse()
                {
                    Whatsapp360DialogOnboardingChannelInfo = info
                });
        }
        catch (PartnerClientException ex)
        {
            return BadRequest(
                new WhatsApp360DialogErrorResponse()
                {
                    Message = "Error during create channel api key: " + ex.ResponseBody
                });
        }
        catch (Exception e)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "Error on creating channel api key."
                });
        }
    }

    [HttpPost]
    public async Task<ActionResult<Migrated360DialogAssignmentRulesResponse>> Migrate360DialogAutomationData(
        [FromBody]
        Migrate360DialogAutomationSendMessageDataRequest request)
    {
        var currentUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "CompanyId is required"
                });
        }

        Migrated360DialogAssignmentRulesResponse response;

        try
        {
            response = await _internalWhatsappCloudApiService.Migrate360DialogAssignmentRulesAsync(request.CompanyId);
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<MigratedWhatsapp360DialogDefaultChannelResponse>>
        MigrateWhatsapp360DialogDefaultChannelToCloudApi(
            [FromBody]
            MigratedWhatsapp360DialogDefaultChannelRequest request)
    {
        var currentUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "CompanyId is required"
                });
        }

        MigratedWhatsapp360DialogDefaultChannelResponse response;

        try
        {
            response =
                await _internalWhatsappCloudApiService.MigrateWhatsapp360DialogDefaultChannelAsync(request.CompanyId);
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<MigratedWhatsapp360DialogLastChannelResponse>> MigrateWhatsapp360DialogLastChannel(
        [FromBody]
        MigratedWhatsapp360DialogLastChannelRequest request)
    {
        var currentUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "CompanyId is required"
                });
        }

        MigratedWhatsapp360DialogLastChannelResponse response;

        try
        {
            response =
                await _internalWhatsappCloudApiService.MigrateWhatsapp360DialogLastChannelAsync(request.CompanyId);
        }
        catch (SleekflowUiException ex)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = ex.Message
                });
        }

        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult>
        MigrateWhatsapp360DialogMessages(
            [FromBody]
            MigrateWhatsapp360DialogConversationMessagesRequest request)
    {
        var currentUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(
                new ResponseViewModel()
                {
                    message = "CompanyId is required"
                });
        }

        BackgroundJob.Enqueue<IInternalWhatsappCloudApiService>(
            x => x.MigrateWhatsapp360DialogConversationMessagesAsync(request.CompanyId, request.IsConfigExist ?? true));

        return Ok();
    }


#if DEBUG
    [HttpPost]
    public async Task<ActionResult> MigrateDirectPaymentAccount(string partnerId)
    {
        var configs = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .Where(x => x.PartnerId == partnerId)
            .ToListAsync();

        foreach (var config in configs)
        {
            await _coreWhatsApp360DialogUsageService.EnableDirectPayment(
                config.CompanyId,
                config.PartnerId,
                config.ClientId);
        }

        return Ok();
    }

    [HttpPost]
    public async Task<ActionResult<List<CmsCurrencyExchangeRate>>> UpdatePhoneNumberToSender([FromQuery] long? configId)
    {
        var configs = await _appDbContext.ConfigWhatsApp360DialogConfigs
            .WhereIf(configId.HasValue, x => x.Id == configId)
            .ToListAsync();

        foreach (var config in configs)
        {
            await _appDbContext.SenderWhatsApp360DialogSenders
                .Where(x => x.ChannelId == config.Id)
                .ExecuteUpdateAsync(
                    calls =>
                        calls.SetProperty(p => p.ChannelWhatsAppPhoneNumber, config.WhatsAppPhoneNumber));

            await _appDbContext.SaveChangesAsync();
        }

        return Ok();
    }

    public record MergeDuplicatedContactRequest(
        string CompanyId,
        List<string> DuplicatedUserProfileIds,
        bool DeleteDuplicatedUserProfile = false);

    [HttpPost]
    public async Task<ActionResult<List<CmsCurrencyExchangeRate>>> MergeDuplicatedContact(
        [FromBody]
        MergeDuplicatedContactRequest request)
    {
        List<string> duplicatedUserProfileIdsToBeRemoved = new List<string>();
        foreach (var userProfileId in request.DuplicatedUserProfileIds)
        {
            var duplicatedConversation = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .ThenInclude(x => x.WhatsApp360DialogUser)
                .Include(x => x.UserProfile)
                .ThenInclude(x => x.CustomFields)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.conversationHashtags)
                .Include(x => x.AdditionalAssignees)
                .FirstOrDefaultAsync(x => x.UserProfileId == userProfileId && x.CompanyId == request.CompanyId);

            if (duplicatedConversation == null ||
                !string.IsNullOrEmpty(duplicatedConversation.UserProfile?.PhoneNumber))
            {
                continue;
            }

            var conversationToBeMergedInto = await _appDbContext.Conversations
                .Include(x => x.UserProfile)
                .ThenInclude(x => x.WhatsApp360DialogUser)
                .Include(x => x.UserProfile)
                .ThenInclude(x => x.CustomFields)
                .Include(x => x.conversationHashtags)
                .Include(x => x.AdditionalAssignees)
                .FirstOrDefaultAsync(
                    x => x.CompanyId == request.CompanyId && x.UserProfile.PhoneNumber ==
                        duplicatedConversation.UserProfile.WhatsApp360DialogUser.WhatsAppId);

            if (conversationToBeMergedInto == null)
            {
                continue;
            }

            // Assignee
            if (conversationToBeMergedInto.AssigneeId == null)
            {
                conversationToBeMergedInto.AssigneeId = duplicatedConversation.AssigneeId;
            }

            if (duplicatedConversation.AdditionalAssignees.Count > 0)
            {
                foreach (var additionalAssignee in duplicatedConversation.AdditionalAssignees.Where(
                             x => !conversationToBeMergedInto.AdditionalAssignees.Select(a => a.AssigneeId)
                                 .Contains(x.AssigneeId)).ToList())
                {
                    conversationToBeMergedInto.AdditionalAssignees.Add(
                        new AdditionalAssignee()
                        {
                            ConversationId = conversationToBeMergedInto.Id,
                            AssigneeId = additionalAssignee.AssigneeId,
                            CompanyId = conversationToBeMergedInto.CompanyId
                        });
                }
            }

            // Hash Tag
            foreach (var hashtag in duplicatedConversation.conversationHashtags.Where(
                         x => !conversationToBeMergedInto.conversationHashtags.Select(a => a.HashtagId)
                             .Contains(x.HashtagId)).ToList())
            {
                conversationToBeMergedInto.conversationHashtags.Add(
                    new ConversationHashtag
                    {
                        ConversationId = conversationToBeMergedInto.Id,
                        HashtagId = hashtag.HashtagId,
                        CompanyId = conversationToBeMergedInto.CompanyId,
                    });
            }

            // UserProfile - custom fields
            foreach (var customField in duplicatedConversation.UserProfile.CustomFields.Where(
                         x => !conversationToBeMergedInto.UserProfile.CustomFields.Select(a => a.CompanyDefinedFieldId)
                             .Contains(x.CompanyDefinedFieldId)).ToList())
            {
                conversationToBeMergedInto.UserProfile.CustomFields.Add(
                    new UserProfileCustomField()
                    {
                        UserProfileId = conversationToBeMergedInto.UserProfile.Id,
                        CompanyDefinedFieldId = customField.CompanyDefinedFieldId,
                        Value = customField.Value,
                        CompanyId = conversationToBeMergedInto.UserProfile.CompanyId
                    });
            }

            var conversationMessages = await _appDbContext.ConversationMessages
                .Where(x => x.ConversationId == duplicatedConversation.Id)
                .ToListAsync();

            foreach (var message in conversationMessages)
            {
                message.ConversationId = conversationToBeMergedInto.Id;
                if (message.Whatsapp360DialogReceiverId != null)
                {
                    message.Whatsapp360DialogReceiverId = conversationToBeMergedInto.WhatsApp360DialogUser.Id;
                }

                if (message.Whatsapp360DialogSenderId != null)
                {
                    message.Whatsapp360DialogSenderId = conversationToBeMergedInto.WhatsApp360DialogUser.Id;
                }
            }

            await _appDbContext.SaveChangesAsync();

            duplicatedUserProfileIdsToBeRemoved.Add(duplicatedConversation.UserProfileId);

            _logger.LogInformation(
                $"duplicatedConversation Id:{duplicatedConversation.Id}, {duplicatedConversation.UserProfileId}; conversationToBeMergedInto Id:{conversationToBeMergedInto.Id}, {conversationToBeMergedInto.UserProfileId}; ");
        }

        if (request.DeleteDuplicatedUserProfile)
        {
            foreach (var duplicatedUserProfileIdToBeRemoved in duplicatedUserProfileIdsToBeRemoved)
            {
                _logger.LogInformation(
                    "[InternalWhatsapp360Dialog {MethodName} endpoint] Company {CompanyId} is deleting duplicate user profile {UserProfileId}",
                    nameof(MergeDuplicatedContact),
                    request.CompanyId,
                    duplicatedUserProfileIdToBeRemoved);

                await _userProfileService.DeleteUserProfiles(
                    request.CompanyId,
                    new UserProfileIdsViewModel()
                    {
                        UserProfileIds = new List<string>()
                        {
                            duplicatedUserProfileIdToBeRemoved
                        }
                    },
                    new UserProfileDeletionTriggerContext(
                        UpdateUserProfileTriggerSource.DuplicateContactMerge,
                        null));
            }
        }

        return Ok(
            new
            {
                duplicatedUserProfileIdsToBeRemoved
            });
    }

    [HttpPost]
    public async Task<ActionResult<List<CmsCurrencyExchangeRate>>> MergeDuplicatedContactV2(
        [FromBody]
        MergeDuplicatedContactRequestV2 request)
    {
        var duplicatedUserProfileIdsToBeRemoved =
            await _userProfileService.DuplicatedUserProfileIdsToBeRemoved(request);

        return Ok(
            new
            {
                duplicatedUserProfileIdsToBeRemoved
            });
    }

    [HttpPost]
    public async Task<ActionResult<List<CmsCurrencyExchangeRate>>> TestJob(
        [FromQuery]
        string clientId,
        [FromQuery]
        string channelId,
        [FromQuery]
        string period,
        [FromQuery]
        int mode,
        [FromQuery]
        long? usageRecordId)
    {
        if (mode == 1)
        {
            await _coreWhatsApp360DialogUsageService.NewPhoneNumberAdded(clientId, channelId);
        }

        if (mode == 2)
        {
            await _coreWhatsApp360DialogUsageService.CurrentPhoneNumberBillings(
                new List<string>()
                {
                    clientId
                });
        }

        if (mode == 3)
        {
            await _coreWhatsApp360DialogUsageService.MonthlyPhoneNumberBillings(
                new List<string>()
                {
                    clientId
                });
        }

        if (mode == 4)
        {
            await _coreWhatsApp360DialogUsageService.MonthlyConversationBillings(
                new List<string>()
                {
                    clientId
                });
        }

        if (mode == 5)
        {
            await _coreWhatsApp360DialogUsageService.DailyConversationBillings(
                new List<string>()
                {
                    clientId
                });
        }

        if (mode == 6)
        {
            await _coreWhatsApp360DialogUsageService.DailyConversationBillings();
        }

        if (mode == 7)
        {
            if (string.IsNullOrWhiteSpace(clientId) || string.IsNullOrWhiteSpace(period))
            {
                return BadRequest();
            }

            await _coreWhatsApp360DialogUsageService.PreviousMonthlyConversationBillings(clientId, period);
        }

        // if (mode == 8)
        // {
        //     if (string.IsNullOrWhiteSpace(period))
        //         return BadRequest();
        //
        //     var usageRecordsConfigs = await _appDbContext.CompanyWhatsApp360DialogUsageRecords
        //         .AsNoTracking()
        //         .Where(x =>
        //             _appDbContext.CompanyWhatsapp360DialogTopUpConfigs
        //                 .Where(t => t.TopUpMode == TopUpMode.PartnerPayment)
        //                 .Select(t => t.ClientId).Contains(x.Waba360DialogClientId))
        //         .ToListAsync();
        //
        //     foreach (var usage in usageRecordsConfigs)
        //     {
        //         await _coreWhatsApp360DialogUsageService.PreviousMonthlyConversationBillings(usage.Waba360DialogClientId, period);
        //     }
        // }
        if (mode == 9)
        {
            await _coreWhatsApp360DialogUsageService.UpdateDirectPaymentAccountBalance();
        }

        if (mode == 10)
        {
            await _whatsApp360DialogService.UpdateAllChannelStatus();
        }

        if (mode == 11)
        {
            // for Nov account migration
            var configs = await _appDbContext.ConfigWhatsApp360DialogConfigs.AsNoTracking().Where(x => x.IsClient)
                .ToListAsync();

            configs = configs.DistinctBy(x => x.CompanyId).ToList();

            var allPartners = await _coreWhatsApp360DialogPartnerAuthService
                .GetAllWhatsApp360DialogPartnerAuthCredentialsAsync();

            foreach (var config in configs)
            {
                if (allPartners.Any(x => x.PartnerId == config.PartnerId & x.TopUpMode == TopUpMode.DirectPayment))
                {
                    await _coreWhatsApp360DialogUsageService.EnableDirectPayment(
                        config.CompanyId,
                        config.PartnerId,
                        config.ClientId);
                }
                else if (allPartners.Any(
                             x => x.PartnerId == config.PartnerId & x.TopUpMode == TopUpMode.PartnerPayment))
                {
                    await _coreWhatsApp360DialogUsageService.EnablePartnerPayment(
                        config.CompanyId,
                        config.PartnerId,
                        config.ClientId);
                }
            }
        }

        if (mode == 99)
        {
            if (usageRecordId == null)
            {
                return BadRequest();
            }

            await _coreWhatsApp360DialogUsageService.ReCalculateUsageFromTransactionLogs(usageRecordId.Value);
        }

        return Ok();
    }

    [HttpPost]
    public async Task<ActionResult<List<CmsCurrencyExchangeRate>>> SetupCurrency()
    {
        var currentUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        var exchangeRates = new List<CmsCurrencyExchangeRate>()
        {
            new ()
            {
                CurrencyFrom = "EUR", CurrencyTo = "USD", ExchangeRate = 1.2m
            },
            new ()
            {
                CurrencyFrom = "USD", CurrencyTo = "EUR", ExchangeRate = 0.8m
            },
            new ()
            {
                CurrencyFrom = "HKD", CurrencyTo = "USD", ExchangeRate = 0.13m
            },
            new ()
            {
                CurrencyFrom = "USD", CurrencyTo = "HKD", ExchangeRate = 7.8m
            },
            new ()
            {
                CurrencyFrom = "USD", CurrencyTo = "USD", ExchangeRate = 1
            },
            new ()
            {
                CurrencyFrom = "HKD", CurrencyTo = "HKD", ExchangeRate = 1
            },
            new ()
            {
                CurrencyFrom = "EUR", CurrencyTo = "EUR", ExchangeRate = 1
            },
        };

        foreach (var rate in exchangeRates)
        {
            if (!await _appDbContext.CmsCurrencyExchangeRates
                    .AnyAsync(
                        x =>
                            x.CurrencyFrom == rate.CurrencyFrom
                            && x.CurrencyTo == rate.CurrencyTo))
            {
                _appDbContext.CmsCurrencyExchangeRates.Add(rate);
            }
        }

        await _appDbContext.SaveChangesAsync();

        return Ok(exchangeRates);
    }

    [HttpPost]
    public async Task<ActionResult> SetupItemPrice()
    {
        var currentUser = await GetCurrentValidInternalUser(
            new List<string>()
            {
                ApplicationUserRole.InternalCmsSuperUser
            });

        if (currentUser == null)
        {
            return Unauthorized();
        }

        _appDbContext.Cms360DialogItemCosts.Add(
            new Cms360DialogItemCost
            {
                UnitPrice = 12.5m, Currency = "USD", Cms360DialogCostItemType = Cms360DialogCostItemType.PhoneNumber
            });

        await _appDbContext.SaveChangesAsync();

        return Ok();
    }
#endif
}