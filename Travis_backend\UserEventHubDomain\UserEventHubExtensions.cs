﻿using System.Net.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Apis.UserEventHub.Api;
using Travis_backend.Constants;
using Travis_backend.UserEventHubDomain.HttpClientHandlers;

namespace Travis_backend.UserEventHubDomain;

public static class UserEventHubExtensions
{
    public static IServiceCollection RegisterUserEventHubServices(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var userEventHubConfig = new Sleekflow.Apis.UserEventHub.Client.Configuration
        {
            BasePath = configuration.GetValue<string>("UserEventHub:Endpoint")
        };

        services.AddSingleton(userEventHubConfig);

        services.AddTransient<UserEventHubHttpClientHandler>();
        services.AddHttpClient(
            HttpClientHandlerName.UserEventHub,
            (sp, client) =>
            {
                var config = sp.GetRequiredService<IConfiguration>();
                client.DefaultRequestHeaders.TryAddWithoutValidation(
                    "X-Sleekflow-Key",
                    config.GetValue<string>("UserEventHub:Key"));
            });

        services.AddTransient<IMessagesApi, MessagesApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.UserEventHub),
                    sp.GetRequiredService<Sleekflow.Apis.UserEventHub.Client.Configuration>(),
                    sp.GetRequiredService<UserEventHubHttpClientHandler>()));

        services.AddTransient<IAssociationsApi, AssociationsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.UserEventHub),
                    sp.GetRequiredService<Sleekflow.Apis.UserEventHub.Client.Configuration>(),
                    sp.GetRequiredService<UserEventHubHttpClientHandler>()));

        services.AddTransient<INotificationsApi, NotificationsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.UserEventHub),
                    sp.GetRequiredService<Sleekflow.Apis.UserEventHub.Client.Configuration>(),
                    sp.GetRequiredService<UserEventHubHttpClientHandler>()));

        services.AddTransient<ILiveChatTrackingEventsApi, LiveChatTrackingEventsApi>(
            sp =>
                new (
                    sp.GetRequiredService<IHttpClientFactory>()
                        .CreateClient(HttpClientHandlerName.UserEventHub),
                    sp.GetRequiredService<Sleekflow.Apis.UserEventHub.Client.Configuration>(),
                    sp.GetRequiredService<UserEventHubHttpClientHandler>()));

        return services;
    }
}