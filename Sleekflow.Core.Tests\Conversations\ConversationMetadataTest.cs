using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Moq;
using StackExchange.Redis;
using Travis_backend.Cache;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.Repositories;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationDomain.ViewModels.Mappers;
using Travis_backend.ConversationDomain.ViewModels.Mappers;
using Travis_backend.Database;
using Travis_backend.Database.Services;
using Travis_backend.Mapping;
using Travis_backend.StripeIntegrationDomain.Services;

namespace Sleekflow.Core.Tests.Conversations
{
    [TestFixture]
    public class ConversationMetadataTest
    {
        private ApplicationDbContext? _appDbContext;
        private ConversationService? _conversationService;
        private static IMapper? _mapper;

        [SetUp]
        public void Setup()
        {
            var connectionString =
                "Server=tcp:sleekflow-core-sql-server-eas-dev928ea268.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=s81604a6f;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=100;";

            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseSqlServer(connectionString)
                .Options;
            _appDbContext = new ApplicationDbContext(options);

            _mapper = new MapperConfiguration(
                static cfg =>
                {
                    cfg.AddProfile(new ViewModelToEntityMappingProfile());
                    cfg.AddProfile(new CrmHubMappingProfile());
                    cfg.AddProfile(new InternalCmsMappingProfile());
                    cfg.AddProfile(new PublicApiMappingProfile());
                    cfg.AddProfile(new TelegramWebhookMappingProfile());
                    cfg.AddProfile(new ViberWebhookMappingProfile());
                    cfg.AddProfile(new WhatsappCloudApiMappingProfile());
                }).CreateMapper();

            var config = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json")
                .AddEnvironmentVariables()
                .Build();

            var connectionMultiplexer = ConnectionMultiplexer.Connect(
                "sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False");

            var lockService = new LockService(
                new Mock<ILogger<LockService>>().Object,
                new Mock<IDistributedCache>().Object,
                connectionMultiplexer);

            var conversationNoCompanyResponseViewModelMapperMock = new Mock<IConversationNoCompanyResponseViewModelMapper>();

        _conversationService = new ConversationService(
            _appDbContext,
            _mapper,
            config,
            NullLogger<ConversationService>.Instance,
            lockService,
            new Mock<ISleekPayService>().Object,
            new Mock<IDbContextService>().Object,
            new Mock<IConversationMessageRepository>().Object,
            new Mock<IAccessControlAggregationService>().Object,
            new Mock<IServiceProvider>().Object,
            new Mock<IRbacConversationPermissionManager>().Object,
            new Mock<IConversationNoCompanyResponseViewModelMapper>().Object,
            new Mock<IConversationReadOnlyRepository>().Object);
    }

        private static class TestingConstants
        {
            public const string CompanyId = "3e4556b4-35d8-4b9b-a60f-583f056521ff";
            public const string ConversationId = "6f40d5bd-cf09-4fac-beaa-08985594012e";
        }

        // Test case is disabled due to these tests need to be updated
        // Team: Team BugBQ
        // [TestCase(
        //     @"{
        //         ""id"": ""ea0413137430a205bea69bd317419b21"",
        //         ""expiration_timestamp"": **********,
        //         ""origin"": {
        //             ""type"": ""marketing""
        //         }
        //     }",
        //     "15075568862")]
        // [TestCase(
        //     @"{
        //         ""id"": ""9407c15eceb3d8f941fe05ebc0726fdd"",
        //         ""expiration_timestamp"": 0,
        //         ""origin"": {
        //             ""type"": ""marketing""
        //         }
        //     }",
        //     "85262057079")]
        // public async Task AddMetadata(string metadataString, string channelIdentityId)
        // {
        //     var metadata = JsonConvert.DeserializeObject<WhatsappCloudApiConversationObject>(metadataString);
        //
        //     var key = $"whatsappcloudapi:conversation:{channelIdentityId}";
        //
        //     await _conversationService.UpdateConversationMetadataAsync(
        //         TestingConstants.CompanyId,
        //         TestingConstants.ConversationId,
        //         key,
        //         metadata);
        //
        //     var resultMetadata = await _appDbContext.Conversations
        //         .Where(x => x.CompanyId == TestingConstants.CompanyId && x.Id == TestingConstants.ConversationId)
        //         .Select(x => x.Metadata)
        //         .FirstOrDefaultAsync();
        //
        //     Assert.That(resultMetadata, Is.Not.Null);
        //
        //     Assert.Multiple(
        //         () =>
        //         {
        //             Assert.That(resultMetadata, Does.ContainKey(key));
        //
        //             Assert.That(
        //                 metadata,
        //                 Is.EqualTo(
        //                     JsonConvert.DeserializeObject<WhatsappCloudApiConversationObject>(
        //                         JsonConvert.SerializeObject(resultMetadata?[key]))));
        //         });
        // }


        // [TestCase(TestingConstants.ConversationId)]
        // public async Task GetConversation(string conversationId)
        // {
        //     var conversation = await _appDbContext.Conversations.Where(x => x.Id == conversationId)
        //         .Include(x => x.UserProfile)
        //         .ThenInclude(x => x.WhatsappCloudApiUser)
        //         .FirstOrDefaultAsync();
        //     var conversationViewModel = _mapper.Map<ConversationNoCompanyResponseViewModel>(conversation);
        //     Assert.That(conversationViewModel, Is.Not.Null);
        //     Assert.That(conversationViewModel.Metadata, Is.Not.Null);
        //     Assert.That(conversationViewModel.Metadata, Is.TypeOf(typeof(ConversationMetadataResponseViewModel)));
        // }
    }
}