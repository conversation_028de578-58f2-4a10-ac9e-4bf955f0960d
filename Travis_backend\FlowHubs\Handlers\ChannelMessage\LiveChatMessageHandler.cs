using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Sleekflow.Apis.FlowHub.Model;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.FlowHubs.Handlers.ChannelMessage;

public class LiveChatMessageHandler : BaseChannelMessageHandler
{
    public LiveChatMessageHandler(
        IHttpClientFactory httpClientFactory,
        ILogger<LiveChatMessageHandler> logger)
        : base(httpClientFactory, logger)
    {
    }

    public override string ChannelType => ChannelTypes.LiveChat;

    public override async Task<ConversationMessage> PrepareConversationMessageAsync(
        string companyId,
        string channelIdentityId,
        string messageType,
        MessageBody messageBody,
        Conversation conversation,
        List<IFormFile> files)
    {
        var newConversationMessage = new ConversationMessage
        {
            ConversationId = conversation.Id,
            Channel = ChannelType,
            WebClientReceiver = conversation.WebClient,
            DeliveryType = DeliveryType.FlowHubAction,
            AnalyticTags = null,
            MessageType = messageType,
            IsSentFromSleekflow = true
        };

        switch (messageType)
        {
            case "text":
                newConversationMessage.MessageContent = messageBody.LiveChatMessage.Message;
                break;

            case "audio" or "video" or "image" or "document":
                newConversationMessage.MessageType = "file";
                var formFile = await GetFormFileAsync(messageType, messageBody);
                files.Add(formFile);
                break;

            default:
                throw new NotImplementedException($"Message type {messageType} not implemented for {ChannelType}");
        }

        return newConversationMessage;
    }
}