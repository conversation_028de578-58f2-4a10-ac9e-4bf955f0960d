using System.Collections.Generic;

namespace Travis_backend.ChannelDomain.ViewModels.LiveChatV2.DTO;

/// <summary>
/// Represents the data transfer object for a live chat sender tracking event.
/// </summary>
public class LiveChatTrackingEventDto
{
    /// <summary>
    /// Gets or sets the unique identifier of the event.
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// Gets or sets the timestamp of the event.
    /// </summary>
    public string StartedAt { get; set; }

    /// <summary>
    /// Gets or sets the session timestamp.
    /// </summary>
    public string EndedAt { get; set; }

    /// <summary>
    /// Gets or sets the name of the event.
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// Gets or sets the metadata associated with the event.
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; }
}