using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.Database.Services;

namespace Travis_backend.ConversationDomain.Repositories;

public interface IConversationReadOnlyRepository
{
    public Task<Conversation> FindConversationByIdAsync(
        string conversationId,
        string companyId,
        Type viewModelType = null,
        CancellationToken cancellationToken = default);

    public Task<List<Conversation>> FindConversationsByIdsAsync(
        List<string> conversationIds,
        string companyId,
        Type viewModelType = null,
        CancellationToken cancellationToken = default);
}

public class ConversationReadOnlyRepository : IConversationReadOnlyRepository
{
    private readonly ILogger<ConversationReadOnlyRepository> _logger;
    private readonly IDbContextService _dbContextService;

    private IQueryable<Conversation> Conversations =>
        _dbContextService.GetDbContext().Conversations.AsNoTracking();

    public ConversationReadOnlyRepository(IDbContextService dbContextService, ILogger<ConversationReadOnlyRepository> logger)
    {
        _dbContextService = dbContextService;
        _logger = logger;
    }


    public async Task<Conversation> FindConversationByIdAsync(string conversationId, string companyId, Type viewModelType = null, CancellationToken cancellationToken = default)
    {
        var baseQuery = Conversations
            .Where(x => x.CompanyId == companyId && x.Id == conversationId);

        return await ProcessConversationForViewModelTypeAsync(baseQuery, viewModelType, cancellationToken); // Includes LiveChat data
    }

    public async Task<List<Conversation>> FindConversationsByIdsAsync(
        List<string> conversationIds,
        string companyId,
        Type viewModelType = null,
        CancellationToken cancellationToken = default)
    {
        var baseQuery = Conversations
            .Where(x => conversationIds.Contains(x.Id) && x.CompanyId == companyId);

        return await ProcessConversationsForViewModelTypeAsync(baseQuery, companyId, viewModelType, cancellationToken);
    }

    // ✅ ULTIMATE UNIFIED METHOD - handles everything based on view model type
    private async Task<Conversation> ProcessConversationForViewModelTypeAsync(
        IQueryable<Conversation> baseQuery,
        Type viewModelType,
        CancellationToken cancellationToken)
    {
        // Step 1: Apply database query optimization based on view model type
        var queryable = GetQueryableForViewModelType(baseQuery, viewModelType);

        // Step 2: Execute query (single or collection)
        var conversation = await queryable.FirstOrDefaultAsync(cancellationToken);

        if (conversation is null)
        {
            return null;
        }

        // Step 3: Populate [NotMapped] properties based on view model type
        await PopulateNotMappedPropertiesForViewModelTypeAsync(conversation, viewModelType, cancellationToken);

        // Step 4: Return appropriate result
        return conversation;
    }

    // ✅ ULTIMATE UNIFIED METHOD - handles everything based on view model type
    private async Task<List<Conversation>> ProcessConversationsForViewModelTypeAsync(
        IQueryable<Conversation> baseQuery,
        string companyId,
        Type viewModelType,
        CancellationToken cancellationToken)
    {
        // Step 1: Apply database query optimization based on view model type
        var queryable = GetQueryableForViewModelType(baseQuery, viewModelType);

        // Step 2: Execute query (single or collection)
        var conversation = await queryable.ToListAsync(cancellationToken);

        if (conversation.Count == 0)
        {
            return [];
        }

        // Step 3: Populate [NotMapped] properties based on view model type
        await PopulateNotMappedPropertiesForViewModelTypeAsync(conversation, companyId, viewModelType, cancellationToken);

        // Step 4: Return appropriate result
        return conversation;
    }

    private static IQueryable<Conversation> GetQueryableForViewModelType(
        IQueryable<Conversation> baseQuery,
        Type viewModelType)
    {
        return viewModelType switch
        {
            _ when viewModelType == typeof(ConversationNoCompanyResponseViewModel) =>
                GetConversationNoCompanyResponseViewModelQueryable(baseQuery),
            _ => baseQuery
        };
    }

    private static IQueryable<Conversation> GetConversationNoCompanyResponseViewModelQueryable(IQueryable<Conversation> baseQuery)
    {
        return baseQuery.IncludeChannelRelatedEntities()
            .Include(x => x.UserProfile)
            .Include(x => x.Assignee.Identity)
            .Include(x => x.AdditionalAssignees)
            .ThenInclude(x => x.Assignee.Identity)
            .Include(x => x.AssignedTeam)
            .Include(x => x.conversationHashtags)
            .ThenInclude(x => x.Hashtag)
            .AsSplitQuery();
    }

    // ✅ [NotMapped] POPULATION - centralized by view model type
    private async Task PopulateNotMappedPropertiesForViewModelTypeAsync(
        Conversation conversation,
        Type viewModelType,
        CancellationToken cancellationToken)
    {
        switch (viewModelType)
        {
            case var _ when viewModelType == typeof(ConversationNoCompanyResponseViewModel):
                await PopulateForConversationNoCompanyResponseViewModelAsync(conversation, cancellationToken);
                break;
        }
    }


    // ✅ [NotMapped] POPULATION - centralized by view model type
    private async Task PopulateNotMappedPropertiesForViewModelTypeAsync(
        List<Conversation> conversations,
        string companyId,
        Type viewModelType,
        CancellationToken cancellationToken)
    {
        switch (viewModelType)
        {
            case var _ when viewModelType == typeof(ConversationNoCompanyResponseViewModel):
                await PopulateForConversationNoCompanyResponseViewModelAsync(conversations, companyId, cancellationToken);
                break;
        }
    }

    // ✅ SPECIFIC [NotMapped] POPULATION - optimized for batch processing
    private async Task PopulateForConversationNoCompanyResponseViewModelAsync(
        Conversation conversation,
        CancellationToken cancellationToken)
    {
        if (conversation is null)
        {
            return;
        }

        try
        {
            // Batch fetch for better performance
            var allLiveChatUsers = await _dbContextService.GetDbContext().SenderWebClientSenders.AsNoTracking()
                .Where(sender => sender.ConversationId == conversation.Id && sender.CompanyId == conversation.CompanyId)
                .ToListAsync(cancellationToken);

            if (allLiveChatUsers.Count == 0)
            {
                conversation.LiveChatV2Users =[];
                return;
            }

            conversation.LiveChatV2Users = allLiveChatUsers;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] Failed to populate LiveChatV2Users for conversation {ConversationId} in company {CompanyId}. Defaulting to empty list.",
                nameof(PopulateForConversationNoCompanyResponseViewModelAsync),
                conversation.Id,
                conversation.CompanyId);

            conversation.LiveChatV2Users =[];
        }
    }

    // ✅ SPECIFIC [NotMapped] POPULATION - optimized for batch processing
    private async Task PopulateForConversationNoCompanyResponseViewModelAsync(
        List<Conversation> conversations,
        string companyId,
        CancellationToken cancellationToken)
    {
        if (conversations.Count == 0)
        {
            return;
        }

        try
        {
            // Batch fetch for better performance
            var conversationIds = conversations.Select(c => c.Id).ToList();
            var allLiveChatUsers = await _dbContextService.GetDbContext().SenderWebClientSenders.AsNoTracking().Where(
                    sender => conversationIds.Contains(sender.ConversationId) && sender.CompanyId == companyId)
                .ToListAsync(cancellationToken);

            // Group by conversation and assign
            var usersByConversation = allLiveChatUsers.GroupBy(u => u.ConversationId)
                .ToDictionary(g => g.Key, g => g.ToList());

            foreach (var conversation in conversations)
            {
                conversation.LiveChatV2Users = usersByConversation.TryGetValue(conversation.Id, out var users)
                    ? users
                    :[];
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "[{MethodName}] Failed to populate LiveChatV2Users for {ConversationCount} conversations in company {CompanyId}. " +
                "ConversationIds: {ConversationIds}. Defaulting all to empty lists.",
                nameof(PopulateForConversationNoCompanyResponseViewModelAsync),
                conversations.Count,
                companyId,
                string.Join(", ", conversations.Select(c => c.Id).Take(10))); // Limit to first 10 IDs

            // Set empty lists as fallback
            foreach (var conversation in conversations)
            {
                conversation.LiveChatV2Users = [];
            }
        }
    }
}