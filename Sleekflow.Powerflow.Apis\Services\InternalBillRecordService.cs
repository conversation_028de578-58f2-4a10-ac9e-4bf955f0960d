using Microsoft.EntityFrameworkCore;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Database;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Models;

namespace Sleekflow.Powerflow.Apis.Services;

public record BillRecordsWithTotalCount(List<BillRecord> BillRecords, int TotalCount);

public interface IInternalBillRecordService
{
    Task<BillRecordsWithTotalCount> GetBillRecordsAsync(
        string paymentMethod,
        int offset,
        int limit,
        bool enableNoTracking);

    Task<bool> UpdateAutoRenewalAsync(long billRecordId, bool enabled, string currentUserId);
}

public class InternalBillRecordService : IInternalBillRecordService
{
    private readonly ApplicationDbContext _appDbContext;

    public InternalBillRecordService(
        ApplicationDbContext appDbContext)
    {
        _appDbContext = appDbContext;
    }

    public async Task<BillRecordsWithTotalCount> GetBillRecordsAsync(
        string paymentMethod,
        int offset,
        int limit,
        bool enableNoTracking)
    {
        var query = _appDbContext.CompanyBillRecords.AsQueryable();

        if (!string.IsNullOrEmpty(paymentMethod))
        {
            var paymentMethodEnum = Enum.Parse<PaymentMethod>(paymentMethod);
            query = query.Where(x => x.CmsSalesPaymentRecords.Any(y => y.PaymentMethod == paymentMethodEnum));
        }

        var totalRecords = await query.CountAsync();

        if (enableNoTracking)
        {
            query = query.AsNoTracking();
        }

        var billRecords = await query
            .OrderByDescending(x => x.created)
            .Skip(offset)
            .Take(limit)
            .ToListAsync();

        return new BillRecordsWithTotalCount(billRecords, totalRecords);
    }

    public async Task<bool> UpdateAutoRenewalAsync(long billRecordId, bool enabled, string currentUserId)
    {
        var billRecord = await _appDbContext.CompanyBillRecords
            .FirstOrDefaultAsync(x => x.Id == billRecordId) ?? throw new InvalidOperationException("Bill record not found");

        // Validate that this is an enterprise plan with notice period
        if (!billRecord.IsIrregularPlan ||
            !billRecord.SubscriptionPlanId.Contains("enterprise") ||
            !billRecord.NoticePeriod.HasValue)
        {
            throw new InvalidOperationException("Auto-renewal can only be updated for enterprise plans with notice period");
        }

        // Check if we're within the notice period (prevent changes during notice period)
        var today = DateTime.UtcNow.Date;
        var actualPeriodEnd = BillRecordRevenueCalculatorHelper.GetActualPeriodEndFromMetadata(billRecord);

        var noticeDate = actualPeriodEnd.AddDays(-billRecord.NoticePeriod.Value).Date;

        if (today >= noticeDate)
        {
            throw new InvalidOperationException("Cannot update auto-renewal during notice period");
        }

        // Update the auto-renewal status
        billRecord.IsAutoRenewalEnabled = enabled;
        billRecord.UpdatedAt = DateTime.UtcNow;

        await _appDbContext.SaveChangesAsync();

        return true;
    }
}