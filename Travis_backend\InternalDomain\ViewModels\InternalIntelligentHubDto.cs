using System.Collections.Generic;
using Sleekflow.Apis.IntelligentHub.Model;

namespace Travis_backend.InternalDomain.ViewModels;

public class InternalIntelligentHubDto
{
    public Dictionary<string, int> UsageLimits { get; set; }

    public Dictionary<string, int> UsageLimitOffsets { get; set; }

    public Dictionary<string, int> FeatureUsages { get; set; }

    public GetInternalCompanyAgentConfigsOutput CompanyAgentConfigs { get; set; }
}