using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Travis_backend.Database.Services;
using Travis_backend.MessageDomain.Models;

namespace Travis_backend.ChannelDomain.Repositories;

public interface ILiveChatV2SenderReadOnlyRepository
{
    public Task<WebClientSender> FindSenderBySenderIdAsync(string senderId);

    public Task<WebClientSender> FindSenderBySenderIdAsync(string senderId, string companyId);

    public Task<List<WebClientSender>> FindSendersByConversationIdAsync(string conversationId, string companyId);

    public Task<List<WebClientSender>> FindSendersByConversationIdsAsync(
        List<string> conversationIds,
        string companyId);

    public Task<List<WebClientSender>> FindSendersByUserProfileIdAsync(string userProfileId, string companyId);

    public Task<List<WebClientSender>> FindSendersByUserProfileIdsAsync(List<string> userProfileIds, string companyId);
}

public class LiveChatV2SenderReadOnlyRepository : ILiveChatV2SenderReadOnlyRepository
{
    private readonly IDbContextService _dbContextService;

    private IQueryable<WebClientSender> Senders =>
        _dbContextService.GetDbContext().SenderWebClientSenders.AsNoTracking();

    public LiveChatV2SenderReadOnlyRepository(IDbContextService dbContextService)
    {
        _dbContextService = dbContextService;
    }

    public async Task<WebClientSender> FindSenderBySenderIdAsync(string senderId)
    {
        var senders = await Senders
            .Where(sender => sender.WebClientUUID == senderId)
            .FirstOrDefaultAsync();
        return senders;
    }

    public async Task<WebClientSender> FindSenderBySenderIdAsync(string senderId, string companyId)
    {
        var senders = await Senders
            .Where(sender => sender.WebClientUUID == senderId && sender.CompanyId == companyId)
            .FirstOrDefaultAsync();
        return senders;
    }

    public async Task<List<WebClientSender>> FindSendersByConversationIdAsync(string conversationId, string companyId)
    {
        var senders = await Senders
            .Where(sender => sender.ConversationId == conversationId && sender.CompanyId == companyId)
            .ToListAsync();

        return senders;
    }

    public async Task<List<WebClientSender>> FindSendersByConversationIdsAsync(
        List<string> conversationIds,
        string companyId)
    {
        return await Senders
            .Where(
                sender => !string.IsNullOrEmpty(sender.ConversationId) &&
                          conversationIds.Contains(sender.ConversationId) && sender.CompanyId == companyId)
            .ToListAsync();
    }

    public async Task<List<WebClientSender>> FindSendersByUserProfileIdAsync(string userProfileId, string companyId)
    {
        var senders = await Senders
            .Where(sender => sender.UserProfileId == userProfileId && sender.CompanyId == companyId)
            .ToListAsync();
        return senders;
    }

    public async Task<List<WebClientSender>> FindSendersByUserProfileIdsAsync(
        List<string> userProfileIds,
        string companyId)
    {
        return await Senders
            .Where(
                sender => !string.IsNullOrEmpty(sender.UserProfileId) &&
                          userProfileIds.Contains(sender.UserProfileId) && sender.CompanyId == companyId)
            .ToListAsync();
    }
}