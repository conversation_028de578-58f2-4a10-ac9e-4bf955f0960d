﻿#nullable enable
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Apis.CommerceHub.Api;
using Sleekflow.Apis.CommerceHub.Model;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Sleekflow.Apis.FlowHub.Model;
using Sleekflow.Apis.IntelligentHub.Model;
using Sleekflow.Apis.TicketingHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationServices;
using Travis_backend.ConversationServices.Models;
using Travis_backend.CustomObjectDomain.Constants;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs.Constants;
using Travis_backend.FlowHubs.Interfaces;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.Models;
using Travis_backend.IntegrationServices.Models;
using Travis_backend.TicketingHubDomain.Services;
using Filter = Sleekflow.Apis.TicketingHub.Model.Filter;
using IInternalsApi = Sleekflow.Apis.IntelligentHub.Api.IInternalsApi;
using Sort = Sleekflow.Apis.TicketingHub.Model.Sort;

namespace Travis_backend.FlowHubs.Controllers;

[Route("FlowHub/SelectOptions")]
public class FlowHubSelectOptionsController : ControllerBase
{
    private readonly ICoreService _coreService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IDbContextService _dbContextService;
    private readonly ITicketingHubService _ticketingHubService;
    private readonly IProvidersApi _providersApi;
    private readonly ISchemasApi _schemasApi;
    private readonly IEnumerable<IChannelOptionsProvider> _channelOptionsProviders;
    private readonly IFacebookService _facebookService;
    private readonly IWhatsappCloudApiService _whatsappCloudApiService;
    private readonly IInstagramService _instagramService;
    private readonly ILogger<FlowHubSelectOptionsController> _logger;
    private readonly IInternalsApi _intelligentHubInternalsApi;
    private readonly IVtexApi _vtexApi;

    public FlowHubSelectOptionsController(
        ICoreService coreService,
        UserManager<ApplicationUser> userManager,
        IDbContextService dbContextService,
        ITicketingHubService ticketingHubService,
        IProvidersApi providersApi,
        ISchemasApi schemasApi,
        IEnumerable<IChannelOptionsProvider> channelOptionsProviders,
        IFacebookService facebookService,
        IInstagramService instagramService,
        ILogger<FlowHubSelectOptionsController> logger,
        IWhatsappCloudApiService whatsappCloudApiService,
        IInternalsApi intelligentHubInternalsApi,
        IVtexApi vtexApi)
    {
        _coreService = coreService;
        _userManager = userManager;
        _dbContextService = dbContextService;
        _ticketingHubService = ticketingHubService;
        _providersApi = providersApi;
        _schemasApi = schemasApi;
        _channelOptionsProviders = channelOptionsProviders;
        _facebookService = facebookService;
        _instagramService = instagramService;
        _whatsappCloudApiService = whatsappCloudApiService;
        _logger = logger;
        _intelligentHubInternalsApi = intelligentHubInternalsApi;
        _vtexApi = vtexApi;
    }

    [HttpPost("ContactLists")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetContactListOptions(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();

        var contactLists = await dbContext.CompanyImportContactHistories
            .Where(x => x.CompanyId == staff.CompanyId)
            .WhereIf(!string.IsNullOrEmpty(searchName), x => x.ImportName.Contains(searchName))
            .Skip(offset)
            .Take(limit)
            .Select(
                x => new NeedConfigOption(
                    x.Id.ToString(),
                    x.ImportName,
                    x.Id.ToString(),
                    null,
                    null,
                    null))
            .ToListAsync();

        return Ok(new GenericOptionsResponse(contactLists));
    }

    public class GetChannelOptionsRequest
    {
        [JsonProperty("channel_type")]
        public string? ChannelType { get; set; }

        [JsonProperty("trigger_id")]
        public string? TriggerId { get; set; }

        [JsonProperty("incoming_channel_option_enabled")]
        public bool? IncomingChannelOptionEnabled { get; set; }
    }

    public class GenericOptionsResponse
    {
        [JsonProperty("options")]
        public List<NeedConfigOption> Options { get; set; }

        [JsonConstructor]
        public GenericOptionsResponse(
            List<NeedConfigOption> options)
        {
            Options = options;
        }
    }

    private bool IsWhatsappRelatedTrigger(string triggerId)
    {
        return triggerId == TriggerIds.ClickToWhatsappAd || triggerId == TriggerIds.MessageStatusUpdated;
    }

    [HttpPost("Channels")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetChannelOptions(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetChannelOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var triggerId = request?.TriggerId;
        var channelType = request?.ChannelType;
        var dbContext = _dbContextService.GetDbContext();

        // Trigger specific channel types
        if (!string.IsNullOrEmpty(triggerId) && IsWhatsappRelatedTrigger(triggerId))
        {
            channelType = ChannelTypes.WhatsappCloudApi;
        }

        List<NeedConfigOption> results;

        // return all channels if it's incoming channel type or empty
        if (channelType == "{{ trigger_event_body.channel }}" ||
            string.IsNullOrEmpty(channelType))
        {
            var desiredOrder = new List<string>
            {
                ChannelTypes.WhatsappCloudApi,
                ChannelTypes.Facebook,
                ChannelTypes.Instagram,
                ChannelTypes.Telegram,
                ChannelTypes.Wechat,
                ChannelTypes.LiveChat,
                ChannelTypes.Line,
                ChannelTypes.Viber,
                ChannelTypes.Sms,
            };

            var orderedProviders = _channelOptionsProviders
                .OrderBy(p => desiredOrder.IndexOf(p.ChannelType))
                .ToList();

            var allOptions = new List<NeedConfigOption>();
            foreach (var provider in orderedProviders)
            {
                var options = await provider.GetOptionsAsync(dbContext, staff.CompanyId, searchName);
                allOptions.AddRange(options);
            }

            results = allOptions
                .Skip(offset)
                .Take(limit)
                .ToList();
        }
        else
        {
            // Get specific channel type options
            var provider = _channelOptionsProviders.FirstOrDefault(p => p.ChannelType == channelType);
            if (provider == null)
            {
                return BadRequest($"Channel type {channelType} not supported.");
            }

            results = await provider.GetOptionsAsync(dbContext, staff.CompanyId, searchName, null, offset, limit);
        }

        if (request?.IncomingChannelOptionEnabled is true)
        {
            var incomingChannelOption = new NeedConfigOption(
                "{{ trigger_event_body.channel_id }}",
                "incoming-channel",
                "{{ trigger_event_body.channel_id }}",
                null,
                request.ChannelType,
                "incoming-channel");

            results.Insert(0, incomingChannelOption);
        }

        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("ChannelNames")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetChannelNameOptions(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetChannelOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var triggerId = request?.TriggerId;
        var channelType = request?.ChannelType;
        var dbContext = _dbContextService.GetDbContext();

        // Trigger specific channel types
        if (!string.IsNullOrEmpty(triggerId) && IsWhatsappRelatedTrigger(triggerId))
        {
            channelType = ChannelTypes.WhatsappCloudApi;
        }

        List<NeedConfigOption> results;

        if (string.IsNullOrEmpty(channelType))
        {
            var desiredOrder = new List<string>
            {
                ChannelTypes.WhatsappCloudApi,
                ChannelTypes.Facebook,
                ChannelTypes.Instagram,
                ChannelTypes.Telegram,
                ChannelTypes.Wechat,
                ChannelTypes.LiveChat,
                ChannelTypes.Line,
                ChannelTypes.Viber,
                ChannelTypes.Sms,
            };

            var orderedProviders = _channelOptionsProviders
                .OrderBy(p => desiredOrder.IndexOf(p.ChannelType))
                .ToList();

            var allOptions = new List<NeedConfigOption>();
            foreach (var provider in orderedProviders)
            {
                var options = await provider.GetOptionsAsync(dbContext, staff.CompanyId, searchName);
                allOptions.AddRange(options);
            }

            results = allOptions
                .Skip(offset)
                .Take(limit)
                .ToList();
        }
        else
        {
            // Get specific channel type options
            var provider = _channelOptionsProviders.FirstOrDefault(p => p.ChannelType == channelType);
            if (provider == null)
            {
                return BadRequest($"Channel type {channelType} not supported.");
            }

            results = await provider.GetOptionsAsync(dbContext, staff.CompanyId, searchName, null, offset, limit);
        }

        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("ChannelTypes")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetChannelTypesOptions(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetChannelOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var results = new List<NeedConfigOption>()
        {
            new NeedConfigOption(
                ChannelTypes.WhatsappCloudApi,
                "whatsapp",
                ChannelTypes.WhatsappCloudApi,
                icon: ChannelTypes.WhatsappCloudApi),
            new NeedConfigOption(ChannelTypes.Facebook, "Facebook", ChannelTypes.Facebook, icon: ChannelTypes.Facebook),
            new NeedConfigOption(ChannelTypes.Instagram, "Instagram", ChannelTypes.Instagram, ChannelTypes.Instagram),
            new NeedConfigOption(ChannelTypes.Telegram, "Telegram", ChannelTypes.Telegram, ChannelTypes.Telegram),
            new NeedConfigOption(ChannelTypes.Wechat, "WeChat", ChannelTypes.Wechat, ChannelTypes.Wechat),
            new NeedConfigOption(ChannelTypes.LiveChat, "Live Chat", ChannelTypes.LiveChat, ChannelTypes.LiveChat),
            new NeedConfigOption(ChannelTypes.Line, "LINE", ChannelTypes.Line, ChannelTypes.Line),
            new NeedConfigOption(ChannelTypes.Viber, "Viber", ChannelTypes.Viber, ChannelTypes.Viber),
            new NeedConfigOption(ChannelTypes.Sms, "SMS", ChannelTypes.Sms, ChannelTypes.Sms)
        };

        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("FbIgChannels")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetFbIgChannelOptions(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetChannelOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();

        var results = new List<NeedConfigOption>();

        var facebookConfigs =
            await dbContext.ConfigFacebookConfigs.Where(x => staff.CompanyId == x.CompanyId).AsNoTracking().ToListAsync();

        foreach (var facebookConfig in facebookConfigs)
        {
            results.Add(
                new NeedConfigOption(
                    facebookConfig.ChannelIdentityId,
                    facebookConfig.ChannelDisplayName,
                    facebookConfig.ChannelIdentityId,
                    facebookConfig.ChannelDisplayName,
                    facebookConfig.ChannelType));
        }

        var instagramConfigs = await dbContext.ConfigInstagramConfigs.Where(x => staff.CompanyId == x.CompanyId)
            .AsNoTracking().ToListAsync();

        foreach (var instagramConfig in instagramConfigs)
        {
            results.Add(
                new NeedConfigOption(
                    instagramConfig.ChannelIdentityId,
                    instagramConfig.ChannelDisplayName,
                    instagramConfig.ChannelIdentityId,
                    instagramConfig.ChannelDisplayName,
                    instagramConfig.ChannelType));
        }

        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("Labels")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetLabelOptions(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();

        var results = await dbContext.CompanyDefinedHashtags
            .Where(x => x.CompanyId == staff.CompanyId)
            .WhereIf(!string.IsNullOrEmpty(searchName), x => x.Hashtag.Contains(searchName))
            .Select(
                x => new NeedConfigOption(
                    x.Id,
                    x.Hashtag,
                    x.Hashtag,
                    x.HashTagType.ToString(),
                    x.HashTagColor.ToString(),
                    null))
            .Skip(offset)
            .Take(limit)
            .ToListAsync();

        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("Teams")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetTeamOptions(
        CancellationToken cancellationToken,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();

        var results = await dbContext.CompanyStaffTeams
            .Where(t => t.CompanyId == staff.CompanyId)
            .WhereIf(!string.IsNullOrEmpty(searchName), t => t.TeamName.Contains(searchName))
            .Skip(offset)
            .Take(limit)
            .Select(
                x => new NeedConfigOption(
                    x.Id.ToString(),
                    x.TeamName,
                    x.Id.ToString(),
                    null,
                    null,
                    null))
            .ToListAsync(cancellationToken);

        return Ok(new GenericOptionsResponse(results));
    }

    public class GetStaffOptionsRequest
    {
        [JsonProperty("team_id")]
        public string TeamId { get; set; }
    }

    [HttpPost("Staffs")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetStaffOptions(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetStaffOptionsRequest request,
        CancellationToken cancellationToken,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();
        long? teamId;

        if (long.TryParse(request?.TeamId, out var teamIdParsed))
        {
            teamId = teamIdParsed;
        }
        else
        {
            teamId = !string.IsNullOrWhiteSpace(request?.TeamId)
                ? -1L
                : null;
        }

        var staffIdentityQueryable = teamId.HasValue
            ? dbContext.CompanyTeamMembers
                .Where(
                    m =>
                        m.CompanyTeamId == teamId.Value
                        && m.Staff.CompanyId == staff.CompanyId)
                .Select(x => x.Staff.Identity)
            : dbContext.UserRoleStaffs
                .Where(s => s.CompanyId == staff.CompanyId)
                .Select(x => x.Identity);

        // search the email only if the DisplayName is empty
        staffIdentityQueryable = staffIdentityQueryable.WhereIf(
                !string.IsNullOrEmpty(searchName),
                s =>
                    s.DisplayName.Contains(searchName) ||
                    (string.IsNullOrWhiteSpace(s.DisplayName) && !string.IsNullOrEmpty(s.Email) && s.Email.Contains(searchName)))
            .Skip(offset)
            .Take(limit);

        var results = await staffIdentityQueryable
            .Select(
                s => new NeedConfigOption(
                    s.Id,
                    string.IsNullOrWhiteSpace(s.DisplayName)
                        ? s.Email ?? string.Empty
                        : s.DisplayName,
                    s.Id,
                    null,
                    null,
                    null))
            .ToListAsync(cancellationToken);

        return Ok(new GenericOptionsResponse(results));
    }

    private async Task<ActionResult<GenericOptionsResponse>> GetContactProperties(
        CancellationToken cancellationToken,
        string searchName,
        int offset,
        int limit,
        Expression<Func<CompanyCustomUserProfileField, bool>>? additionalFilter = null)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();

        var query = dbContext.CompanyCustomUserProfileFields
            .Where(x => x.CompanyId == staff.CompanyId)
            .WhereIf(!string.IsNullOrEmpty(searchName), x => x.FieldName.Contains(searchName));

        if (additionalFilter != null)
        {
            query = query.Where(additionalFilter);
        }

        var results = await query
            .Skip(offset)
            .Take(limit)
            .Select(
                x => new NeedConfigOption(
                    x.Id,
                    x.FieldName,
                    x.Id,
                    x.Type.ToString(),
                    null,
                    null))
            .ToListAsync(cancellationToken);

        var firstNameOption = new NeedConfigOption(
            "FirstName",
            "First Name",
            "FirstName",
            "SingleLineText");
        var lastNameOption = new NeedConfigOption(
            "LastName",
            "Last Name",
            "LastName",
            "SingleLineText");
        results.AddRange(
            new[]
            {
                firstNameOption,
                lastNameOption
            });

        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("ContactProperties")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetAllContactProperties(
        CancellationToken cancellationToken,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        return await GetContactProperties(
            cancellationToken,
            searchName,
            offset,
            limit,
            null);
    }

    [HttpPost("ContactDateTimeProperties")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetContactDateTimeProperties(
        CancellationToken cancellationToken,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        return await GetContactProperties(
            cancellationToken,
            searchName,
            offset,
            limit,
            x => x.Type == FieldDataType.DateTime || x.Type == FieldDataType.Date);
    }

    [HttpPost("CustomFieldOptions/{customFieldId}")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetCustomFieldOptions(
        [FromRoute(Name = "customFieldId")]
        string customFieldId,
        [FromQuery]
        string searchName,
        CancellationToken cancellationToken,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();

        var results = await dbContext.CompanyCustomUserProfileFieldOptions
            .AsNoTracking()
            .Where(x => x.CompanyCustomUserProfileFieldId == customFieldId)
            .WhereIf(!string.IsNullOrEmpty(searchName), x => x.Value.Contains(searchName))
            .Skip(offset)
            .Take(limit)
            .Select(
                x => new NeedConfigOption(
                    x.Id.ToString(),
                    x.Value,
                    x.Value,
                    null,
                    null,
                    null))
            .ToListAsync(cancellationToken);

        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("TicketTypes")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetTicketTypes(
        CancellationToken cancellationToken,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);
        var results = new GenericOptionsResponse([]);

        var getTicketTypeOutput = await _ticketingHubService.GetTicketTypesAsync(
            staff.CompanyId,
            [
                new FilterGroup(
                [
                    new Filter(
                        "record_statuses",
                        "array_contains",
                        "Active")
                ])
            ],
            new Sort(
                "sequence",
                "asc"),
            100,
            null,
            ticketUser);

        if (getTicketTypeOutput.Success)
        {
            results = new GenericOptionsResponse(
                getTicketTypeOutput.Data.Records
                    .Where(x => string.IsNullOrEmpty(searchName) || x.Label.Contains(searchName))
                    .Skip(offset)
                    .Take(limit)
                    .Select(
                        x => new NeedConfigOption(
                            x.Id,
                            x.Label,
                            x.Id))
                    .ToList());
        }

        return Ok(results);
    }

    [HttpPost("TicketTypeNames")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetTicketTypeNames(
        CancellationToken cancellationToken,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);
        var results = new GenericOptionsResponse([]);

        var getTicketTypeOutput = await _ticketingHubService.GetTicketTypesAsync(
            staff.CompanyId,
            [
                new FilterGroup(
                [
                    new Filter(
                        "record_statuses",
                        "array_contains",
                        "Active")
                ])
            ],
            new Sort(
                "sequence",
                "asc"),
            100,
            null,
            ticketUser);

        if (getTicketTypeOutput.Success)
        {
            results = new GenericOptionsResponse(
                getTicketTypeOutput.Data.Records
                    .Where(x => string.IsNullOrEmpty(searchName) || x.Label.Contains(searchName))
                    .Skip(offset)
                    .Take(limit)
                    .Select(
                        x => new NeedConfigOption(
                            x.Label,
                            x.Label,
                            x.Label))
                    .ToList());
        }

        return Ok(results);
    }

    [HttpPost("TicketPriorities")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> TicketPriorities(
        CancellationToken cancellationToken,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);
        var results = new GenericOptionsResponse([]);

        var getTicketPrioritiesOutput = await _ticketingHubService.GetTicketPrioritiesAsync(
            staff.CompanyId,
            ticketUser);

        if (getTicketPrioritiesOutput.Success)
        {
            results = new GenericOptionsResponse(
                getTicketPrioritiesOutput.Data.Records
                    .Where(x => string.IsNullOrEmpty(searchName) || x.Label.Contains(searchName))
                    .Skip(offset)
                    .Take(limit)
                    .Select(
                        x => new NeedConfigOption(
                            x.Id,
                            x.Label,
                            x.Id))
                    .ToList());
        }

        return Ok(results);
    }

    [HttpPost("TicketPriorityNames")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> TicketPriorityNames(
        CancellationToken cancellationToken,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);
        var results = new GenericOptionsResponse([]);

        var getTicketPrioritiesOutput = await _ticketingHubService.GetTicketPrioritiesAsync(
            staff.CompanyId,
            ticketUser);

        if (getTicketPrioritiesOutput.Success)
        {
            results = new GenericOptionsResponse(
                getTicketPrioritiesOutput.Data.Records
                    .Where(x => string.IsNullOrEmpty(searchName) || x.Label.Contains(searchName))
                    .Skip(offset)
                    .Take(limit)
                    .Select(
                        x => new NeedConfigOption(
                            x.Label,
                            x.Label,
                            x.Label))
                    .ToList());
        }

        return Ok(results);
    }

    [HttpPost("TicketStatuses")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> TicketStatuses(
        CancellationToken cancellationToken,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);
        var results = new GenericOptionsResponse([]);

        var getTicketStatusesOutput = await _ticketingHubService.GetTicketStatusesAsync(
            staff.CompanyId,
            ticketUser);

        if (getTicketStatusesOutput.Success)
        {
            results = new GenericOptionsResponse(
                getTicketStatusesOutput.Data.Records
                    .Where(x => string.IsNullOrEmpty(searchName) || x.Label.Contains(searchName))
                    .Skip(offset)
                    .Take(limit)
                    .Select(
                        x => new NeedConfigOption(
                            x.Id,
                            x.Label,
                            x.Id))
                    .ToList());
        }

        return Ok(results);
    }

    [HttpPost("TicketStatusNames")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetTicketStatusNames(
        CancellationToken cancellationToken,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var ticketUser = await _ticketingHubService.GetTicketUserAsync(staff);
        var results = new GenericOptionsResponse([]);

        var getTicketStatusesOutput = await _ticketingHubService.GetTicketStatusesAsync(
            staff.CompanyId,
            ticketUser);

        if (getTicketStatusesOutput.Success)
        {
            results = new GenericOptionsResponse(
                getTicketStatusesOutput.Data.Records
                    .Where(x => string.IsNullOrEmpty(searchName) || x.Label.Contains(searchName))
                    .Skip(offset)
                    .Take(limit)
                    .Select(
                        x => new NeedConfigOption(
                            x.Label,
                            x.Label,
                            x.Label))
                    .ToList());
        }

        return Ok(results);
    }

    [HttpPost("TicketAssignees")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetTicketAssignees(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetStaffOptionsRequest request,
        CancellationToken cancellationToken,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();
        long? teamId;

        if (long.TryParse(request?.TeamId, out var teamIdParsed))
        {
            teamId = teamIdParsed;
        }
        else
        {
            teamId = !string.IsNullOrWhiteSpace(request?.TeamId)
                ? -1L
                : null;
        }

        var staffQueryable = teamId.HasValue
            ? dbContext.CompanyTeamMembers
                .Where(
                    m =>
                        m.CompanyTeamId == teamId.Value
                        && m.Staff.CompanyId == staff.CompanyId)
                .WhereIf(!string.IsNullOrEmpty(searchName), s => s.Staff.Identity.DisplayName.Contains(searchName))
                .Select(x => x.Staff)
            : dbContext.UserRoleStaffs
                .Where(s => s.CompanyId == staff.CompanyId)
                .WhereIf(!string.IsNullOrEmpty(searchName), s => s.Identity.DisplayName.Contains(searchName));

        staffQueryable = staffQueryable
            .Skip(offset)
            .Take(limit);

        var results = await staffQueryable
            .Select(
                s => new NeedConfigOption(
                    s.Id.ToString(),
                    s.Identity.DisplayName,
                    s.Id.ToString(),
                    null,
                    null,
                    null))
            .ToListAsync(cancellationToken);

        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("TicketUpdatedProperties")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetTicketUpdatedProperties()
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        return Ok(
            new GenericOptionsResponse(
                TicketUpdatedProperties.IdLabelDict.Select(x => new NeedConfigOption(x.Key, x.Value, x.Key, null, null))
                    .ToList()));
    }

    [HttpPost("ConversationStatuses")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetConversationStatuses(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var statusOptions = new List<NeedConfigOption>
        {
            new ("open", "Open", "Open"), new ("closed", "Closed", "Closed"),
            new ("pending", "Pending", "Pending")
        };

        if (!string.IsNullOrEmpty(searchName))
        {
            return Ok(
                new GenericOptionsResponse(
                    statusOptions.Where(x => x.Label.Contains(searchName))
                        .Skip(offset)
                        .Take(limit)
                        .ToList()));
        }

        return Ok(
            new GenericOptionsResponse(
                statusOptions.Skip(offset)
                    .Take(limit)
                    .ToList()));
    }

    [HttpPost("GoogleSheetsConnections")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetGoogleSheetsConnections(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var googleSheetsConnections = (await _providersApi.ProvidersGetProviderConnectionsPostAsync(
            getProviderConnectionsInput: new GetProviderConnectionsInput(
                sleekflowCompanyId: staff.CompanyId,
                providerName: "google-sheets-integrator"))).Data.Connections;
        var results = googleSheetsConnections
            .Where(c => string.IsNullOrEmpty(searchName) || c.Name.ContainsIgnoreCase(searchName))
            .Select(c => new NeedConfigOption(
                id: c.Id, label: c.Name, value: c.Id)).ToList();
        return Ok(new GenericOptionsResponse(results));
    }

    public class GetGoogleSheetsSpreadsheetOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }
    }

    [HttpPost("GoogleSheetsSpreadsheets")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetGoogleSheetsSpreadsheets(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetGoogleSheetsSpreadsheetOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var connectionExternalResources = (await _providersApi.ProvidersGetProviderConnectionExternalResourcesPostAsync(
            getProviderConnectionExternalResourcesInput: new GetProviderConnectionExternalResourcesInput(
                sleekflowCompanyId: staff.CompanyId,
                providerName: "google-sheets-integrator",
                providerConnectionId: request.ConnectionId))).Data.ExternalResources;
        var spreadsheetResources = connectionExternalResources.Where(
            r => r.ResourceType == "spreadsheet").ToList();
        var results = spreadsheetResources
            .Where(r => string.IsNullOrEmpty(searchName) || r.Name.ContainsIgnoreCase(searchName))
            .Select(r => new NeedConfigOption(
                id: r.Id, label: r.Name, value: r.Id)).ToList();
        return Ok(new GenericOptionsResponse(results));
    }

    public class GetGoogleSheetsWorksheetOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }

        [JsonProperty("spreadsheet_id")]
        public string SpreadsheetId { get; set; }
    }

    [HttpPost("GoogleSheetsWorksheets")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetGoogleSheetsWorksheets(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetGoogleSheetsWorksheetOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var connectionExternalResources = (await _providersApi.ProvidersGetProviderConnectionExternalResourcesPostAsync(
            getProviderConnectionExternalResourcesInput: new GetProviderConnectionExternalResourcesInput(
                sleekflowCompanyId: staff.CompanyId,
                providerName: "google-sheets-integrator",
                providerConnectionId: request.ConnectionId))).Data.ExternalResources;
        var spreadsheetResource = connectionExternalResources.FirstOrDefault(
            r => r.ResourceType == "spreadsheet" && r.Id == request.SpreadsheetId);
        if (spreadsheetResource?.SubResources is null)
        {
            return Ok(new GenericOptionsResponse(new List<NeedConfigOption>()));
        }

        var worksheetResources = spreadsheetResource.SubResources.Where(
            r => r.ResourceType == "worksheet").ToList();
        var results = worksheetResources
            .Where(r => string.IsNullOrEmpty(searchName) || r.Name.ContainsIgnoreCase(searchName))
            .Select(r => new NeedConfigOption(
                id: r.Id, label: r.Name, value: r.Id))
            .ToList();

        return Ok(new GenericOptionsResponse(results));
    }

    public class GetGoogleSheetsFieldOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }

        [JsonProperty("spreadsheet_id")]
        public string SpreadsheetId { get; set; }

        [JsonProperty("worksheet_id")]
        public string WorksheetId { get; set; }

        [JsonProperty("header_row_id")]
        public string HeaderRowId { get; set; }
    }

    [HttpPost("GoogleSheetsFields")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetGoogleSheetsFields(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetGoogleSheetsFieldOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var googleSheetsFields =
            (await _providersApi.ProvidersGetProviderTypeFieldsV2PostAsync(
                getProviderTypeFieldsV2Input: new GetProviderTypeFieldsV2Input(
                    sleekflowCompanyId: staff.CompanyId,
                    providerConnectionId: request.ConnectionId,
                    typedIds: new List<TypedId>
                    {
                        new ("Spreadsheet", request.SpreadsheetId),
                        new ("Worksheet", request.WorksheetId),
                        new ("HeaderRow", request.HeaderRowId)
                    },
                    entityTypeName: "Row",
                    providerName: "google-sheets-integrator"))).Data.CreatableFields;
        var results = googleSheetsFields
            .Where(r => string.IsNullOrEmpty(searchName) || r.Name.ContainsIgnoreCase(searchName))
            .Select(r => new NeedConfigOption(
                id: r.Name, label: r.Label, value: r.Name, r.Type, null))
            .ToList();

        return Ok(new GenericOptionsResponse(results));
    }

    public class GetGoogleSheetsRowOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }

        [JsonProperty("spreadsheet_id")]
        public string SpreadsheetId { get; set; }

        [JsonProperty("worksheet_id")]
        public string WorksheetId { get; set; }
    }

    [HttpPost("GoogleSheetsRows")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetGoogleSheetsRows(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetGoogleSheetsRowOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        // get the row count by searching with empty condition
        var googleSheetsRows = (await _providersApi.ProvidersSearchObjectsPostAsync(
            searchObjectsInput: new SearchObjectsInput(
                sleekflowCompanyId: staff.CompanyId,
                providerConnectionId: request.ConnectionId,
                providerName: "google-sheets-integrator",
                entityTypeName: "Row",
                conditions: null,
                typedIds: new List<TypedId>
                {
                    new ("Spreadsheet", request.SpreadsheetId), new ("Worksheet", request.WorksheetId)
                }))).Data.Records;
        var rowCount = googleSheetsRows.Count;

        var results = Enumerable.Range(1, rowCount)
            .Select(i => new NeedConfigOption(
                id: (i + 1).ToString(),
                label: (i + 1).ToString(),
                value: (i + 1).ToString(),
                null,
                null))
            .Where(o => string.IsNullOrEmpty(searchName) || o.Label.ContainsIgnoreCase(searchName))
            .ToList();

        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("CustomObjectSchemas")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetCustomObjectSchemas(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var companyId = staff.CompanyId;

        var schemas = new List<SchemaDto>();

        var filterGroup = new List<GetSchemasFilterGroup>
        {
            new (
                new List<SchemaFilter>(
                    new List<SchemaFilter>
                    {
                        new SchemaFilter("is_deleted", "=", false)
                    })),
            new GetSchemasFilterGroup(
                new List<SchemaFilter>(
                    new List<SchemaFilter>
                    {
                        new SchemaFilter("is_enabled", "=", true)
                    })),
            new GetSchemasFilterGroup(
                new List<SchemaFilter>(
                    new List<SchemaFilter>
                    {
                        new SchemaFilter("schema_accessibility_settings.category", "in", new List<string>
                        {
                            SchemaCategories.Custom, SchemaCategories.Vtex, SchemaCategories.Shopify
                        })
                    })),
            new GetSchemasFilterGroup(
                new List<SchemaFilter>(
                    new List<SchemaFilter>
                    {
                        new SchemaFilter("display_name", "contains", searchName ?? string.Empty)
                    }))
        };
        var sort = new List<SchemaSort>
        {
            new SchemaSort("sorting_weight", "asc")
        };

        string continuationToken = null;
        int count;
        do
        {
            var getSchemasOutputOutput =
                await _schemasApi.SchemasGetSchemasPostAsync(
                    getSchemasInput: new GetSchemasInput(
                        companyId,
                        100,
                        continuationToken,
                        filterGroup,
                        sort));

            count = getSchemasOutputOutput.Data.Count;
            continuationToken = getSchemasOutputOutput.Data.ContinuationToken;

            if (count > 0)
            {
                schemas.AddRange(getSchemasOutputOutput.Data.Schemas);
            }
        }
        while (count > 0 && !string.IsNullOrEmpty(continuationToken));

        var results = schemas.Select(
                x => new NeedConfigOption(
                    x.Id,
                    x.DisplayName,
                    x.Id))
            .Skip(offset)
            .Take(limit)
            .ToList();

        return Ok(new GenericOptionsResponse(results));
    }

    private async Task<ActionResult<GenericOptionsResponse>> GetSchemaProperties(
        GetCustomObjectSchemaPropertiesRequest request,
        string searchName,
        int offset,
        int limit,
        Func<Property, bool>? additionalFilter = null)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var companyId = staff.CompanyId;

        var schema =
            await _schemasApi.SchemasGetSchemaPostAsync(
                getSchemaInput: new GetSchemaInput(companyId, request.SchemaId));

        var query = schema.Data.Schema.Properties
            .Where(x => string.IsNullOrEmpty(searchName) || x.DisplayName.Contains(searchName));

        if (additionalFilter != null)
        {
            query = query.Where(additionalFilter);
        }

        var results = query
            .Select(
                x => new NeedConfigOption(
                    x.Id,
                    x.DisplayName,
                    x.Id,
                    x.DataType.Name))
            .Skip(offset)
            .Take(limit)
            .ToList();

        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("CustomObjectSchemaProperties")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> CustomObjectSchemaProperties(
        [FromBody]
        GetCustomObjectSchemaPropertiesRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        return await GetSchemaProperties(request, searchName, offset, limit);
    }

    [HttpPost("CustomObjectSchemaDateTimeProperties")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> CustomObjectSchemaDateTimeProperties(
        [FromBody]
        GetCustomObjectSchemaPropertiesRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        return await GetSchemaProperties(request, searchName, offset, limit, x => x.DataType.Name == SchemaPropertyDataTypes.DateTime || x.DataType.Name == SchemaPropertyDataTypes.Date);
    }

    public class GetCustomObjectSchemaPropertiesRequest
    {
        [JsonProperty("schema_id")]
        public string SchemaId { get; set; }
    }

    [HttpPost("HubspotConnections")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetHubspotConnections(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var hubspotConnections = (await _providersApi.ProvidersGetProviderConnectionsPostAsync(
            getProviderConnectionsInput: new GetProviderConnectionsInput(
                sleekflowCompanyId: staff.CompanyId,
                providerName: "hubspot-integrator"))).Data.Connections;
        var results = hubspotConnections
            .Where(c => string.IsNullOrEmpty(searchName) || c.Name.ContainsIgnoreCase(searchName))
            .Select(c => new NeedConfigOption(
                id: c.Id, label: c.Name, value: c.Id)).ToList();
        return Ok(new GenericOptionsResponse(results));
    }

    public class GetHubspotEntityTypeNameOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }
    }

    [HttpPost("HubspotEntityTypeNames")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetHubspotEntityTypeNames(
        [FromBody]
        GetHubspotEntityTypeNameOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var hubspotCustomObjectTypes = (await _providersApi.ProvidersGetProviderCustomObjectTypesPostAsync(
            getProviderCustomObjectTypesInput: new GetProviderCustomObjectTypesInput(
                sleekflowCompanyId: staff.CompanyId,
                providerName: "hubspot-integrator",
                providerConnectionId: request.ConnectionId))).Data.CustomObjectTypes;
        var customObjectTypeOptions = hubspotCustomObjectTypes
            .Select(
                c => new NeedConfigOption(
                    id: c.ApiName,
                    label: c.DisplayName,
                    value: c.ApiName,
                    c.DisplayName,
                    null)).ToList();

        var results = new List<NeedConfigOption>
        {
            new (id: "Contact", label: "Contact", value: "Contact"),
        };
        results.AddRange(customObjectTypeOptions);

        results = results
            .Where(o => string.IsNullOrEmpty(searchName) || o.Label.ContainsIgnoreCase(searchName))
            .ToList();

        return Ok(new GenericOptionsResponse(results));
    }

    public class GetHubspotUpdatableEntityTypeFieldOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }
    }

    [HttpPost("HubspotUpdatableEntityTypeFields")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetHubspotUpdatableEntityTypeFields(
        [FromBody]
        GetHubspotUpdatableEntityTypeFieldOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var hubspotUpdatableFields = (await _providersApi.ProvidersGetProviderTypeFieldsV2PostAsync(
            getProviderTypeFieldsV2Input: new GetProviderTypeFieldsV2Input(
                sleekflowCompanyId: staff.CompanyId,
                providerName: "hubspot-integrator",
                providerConnectionId: request.ConnectionId,
                typedIds: null,
                entityTypeName: request.EntityTypeName))).Data.UpdatableFields;
        var results = hubspotUpdatableFields
            .Where(c => string.IsNullOrEmpty(searchName) || c.Label.ContainsIgnoreCase(searchName))
            .Select(c => new NeedConfigOption(
                id: c.Name, label: c.Label, value: c.Name)).ToList();

        return Ok(new GenericOptionsResponse(results));
    }

    public class GetHubspotSearchableEntityTypeFieldOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }
    }

    [HttpPost("HubspotSearchableEntityTypeFields")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetHubspotSearchableEntityTypeFields(
        [FromBody]
        GetHubspotSearchableEntityTypeFieldOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var hubspotFieldsOutput = (await _providersApi.ProvidersGetProviderTypeFieldsV2PostAsync(
            getProviderTypeFieldsV2Input: new GetProviderTypeFieldsV2Input(
                sleekflowCompanyId: staff.CompanyId,
                providerConnectionId: request.ConnectionId,
                typedIds: null,
                entityTypeName: request.EntityTypeName,
                providerName: "hubspot-integrator"))).Data;
        var hubspotFields = hubspotFieldsOutput.ViewableFields
            .Concat(hubspotFieldsOutput.CreatableFields)
            .Concat(hubspotFieldsOutput.UpdatableFields)
            .GroupBy(f => f.Name)
            .Select(g => g.First())
            .ToHashSet();
        var results = hubspotFields
            .Where(c => string.IsNullOrEmpty(searchName) || c.Label.ContainsIgnoreCase(searchName))
            .Select(c => new NeedConfigOption(
                id: c.Name, label: c.Label, value: c.Name)).ToList();
        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("ZohoConnections")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetZohoConnections(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var zohoConnections = (await _providersApi.ProvidersGetProviderConnectionsPostAsync(
            getProviderConnectionsInput: new GetProviderConnectionsInput(
                sleekflowCompanyId: staff.CompanyId,
                providerName: "zoho-integrator"))).Data.Connections;

        var results = zohoConnections
            .Where(x => string.IsNullOrEmpty(searchName) || x.Name.ContainsIgnoreCase(searchName))
            .Select(
                c => new NeedConfigOption(
                    id: c.Id,
                    label: c.Name,
                    value: c.Id)).ToList();

        return Ok(new GenericOptionsResponse(results));
    }

    public class GetZohoEntityTypeNameOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }
    }

    [HttpPost("ZohoEntityTypeNames")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetZohoEntityTypeNames(
        [FromBody]
        GetZohoEntityTypeNameOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var zohoCustomObjectTypes = (await _providersApi.ProvidersGetProviderCustomObjectTypesPostAsync(
            getProviderCustomObjectTypesInput: new GetProviderCustomObjectTypesInput(
                sleekflowCompanyId: staff.CompanyId,
                providerName: "zoho-integrator",
                providerConnectionId: request.ConnectionId))).Data.CustomObjectTypes;
        var customObjectTypeOptions = zohoCustomObjectTypes
            .Select(
                c => new NeedConfigOption(
                    id: c.ApiName,
                    label: c.DisplayName,
                    value: c.ApiName)).ToList();

        var results = new List<NeedConfigOption>
        {
            new (id: "Contacts", label: "Contacts", value: "Contacts")
        };
        results.AddRange(customObjectTypeOptions);

        return Ok(new GenericOptionsResponse(
            results.Where(x => string.IsNullOrEmpty(searchName) || x.Label.ContainsIgnoreCase(searchName)).ToList()));
    }

    public class GetZohoUpdatableEntityTypeFieldOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }
    }

    [HttpPost("ZohoUpdatableEntityTypeFields")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetZohoUpdatableEntityTypeFields(
        [FromBody]
        GetZohoUpdatableEntityTypeFieldOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var zohoUpdatableFields = (await _providersApi.ProvidersGetProviderTypeFieldsV2PostAsync(
            getProviderTypeFieldsV2Input: new GetProviderTypeFieldsV2Input(
                sleekflowCompanyId: staff.CompanyId,
                providerName: "zoho-integrator",
                providerConnectionId: request.ConnectionId,
                typedIds: null,
                entityTypeName: request.EntityTypeName))).Data.UpdatableFields;

        var results = zohoUpdatableFields
            .Where(x => string.IsNullOrEmpty(searchName) || x.Label.ContainsIgnoreCase(searchName))
            .Select(c => new NeedConfigOption(
                id: c.Name, label: c.Label, value: c.Name)).ToList();
        return Ok(new GenericOptionsResponse(results));
    }

    public class GetZohoSearchableEntityTypeFieldOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }
    }

    [HttpPost("ZohoSearchableEntityTypeFields")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetZohoSearchableEntityTypeFields(
        [FromBody]
        GetZohoSearchableEntityTypeFieldOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var zohoFieldsOutput = (await _providersApi.ProvidersGetProviderTypeFieldsV2PostAsync(
            getProviderTypeFieldsV2Input: new GetProviderTypeFieldsV2Input(
                sleekflowCompanyId: staff.CompanyId,
                providerConnectionId: request.ConnectionId,
                typedIds: null,
                entityTypeName: request.EntityTypeName,
                providerName: "zoho-integrator"))).Data;
        var zohoFields = zohoFieldsOutput.ViewableFields
            .Concat(zohoFieldsOutput.CreatableFields)
            .Concat(zohoFieldsOutput.UpdatableFields)
            .GroupBy(f => f.Name)
            .Select(g => g.First())
            .ToHashSet();

        var results = zohoFields
            .Where(x => string.IsNullOrEmpty(searchName) || x.Label.ContainsIgnoreCase(searchName))
            .Select(c => new NeedConfigOption(
                id: c.Name, label: c.Label, value: c.Name)).ToList();
        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("WhatsAppBusinessAccounts")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetWhatsAppBusinessAccounts(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();

        var result = await dbContext.ConfigWhatsappCloudApiConfigs
            .Where(x => x.CompanyId == staff.CompanyId)
            .WhereIf(!string.IsNullOrEmpty(searchName), x => x.ChannelDisplayName.Contains(searchName))
            .Select(
                x => new NeedConfigOption(
                    x.ChannelIdentityId,
                    x.ChannelDisplayName,
                    x.FacebookWabaId,
                    null,
                    null,
                    null))
            .Skip(offset)
            .Take(limit)
            .ToListAsync();

        return Ok(new GenericOptionsResponse(result));
    }

    [HttpPost("ContactRecordSourceKeys")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetContactRecordSourceKeys(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        // currently supported keys
        const string phoneNumberCustomFieldName = "PhoneNumber";
        const string emailCustomFieldName = "Email";
        const string crmSourceObjectIdFieldName = "CRM Source Object Id";

        const string phoneNumberKeyLabel = "Phone Number";
        const string emailKeyLabel = "Email";
        const string crmSourceObjectIdKeyLabel = "CRM Source Object Id";

        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();

        var result = await dbContext.CompanyCustomUserProfileFields
            .Where(
                x => x.CompanyId == staff.CompanyId
                     && (x.FieldName == phoneNumberCustomFieldName
                         || x.FieldName == emailCustomFieldName
                         || (x.Type == FieldDataType.CrmSourceObjectId && x.FieldName == crmSourceObjectIdFieldName)))
            .WhereIf(!string.IsNullOrEmpty(searchName), x => x.FieldName.Contains(searchName))
            .Select(
                x => new NeedConfigOption(
                    x.Id,
                    x.FieldName == phoneNumberCustomFieldName
                        ? phoneNumberKeyLabel
                        : x.FieldName == emailCustomFieldName
                            ? emailKeyLabel
                            : crmSourceObjectIdKeyLabel,
                    x.Id,
                    null,
                    null,
                    null))
            .Skip(offset)
            .Take(limit)
            .ToListAsync();

        return Ok(new GenericOptionsResponse(result));
    }

    [HttpPost("SalesforceConnections")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetSalesforceConnections(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var salesforceConnections = (await _providersApi.ProvidersGetProviderConnectionsPostAsync(
            getProviderConnectionsInput: new GetProviderConnectionsInput(
                sleekflowCompanyId: staff.CompanyId,
                providerName: "salesforce-integrator"))).Data.Connections;
        var results = salesforceConnections
            .Where(c => string.IsNullOrEmpty(searchName) || c.Name.ContainsIgnoreCase(searchName))
            .Select(c => new NeedConfigOption(
                id: c.Id, label: c.Name, value: c.Id)).ToList();
        return Ok(new GenericOptionsResponse(results));
    }

    public class GetSalesforceEntityTypeNameOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }
    }

    [HttpPost("SalesforceEntityTypeNames")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetSalesforceEntityTypeNames(
        [FromBody]
        GetSalesforceEntityTypeNameOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var salesforceCustomObjectTypes = (await _providersApi.ProvidersGetProviderCustomObjectTypesPostAsync(
            getProviderCustomObjectTypesInput: new GetProviderCustomObjectTypesInput(
                sleekflowCompanyId: staff.CompanyId,
                providerName: "salesforce-integrator",
                providerConnectionId: request.ConnectionId))).Data.CustomObjectTypes;
        var customObjectTypeOptions = salesforceCustomObjectTypes
            .Select(
                c => new NeedConfigOption(
                    id: c.ApiName,
                    label: c.DisplayName,
                    value: c.ApiName)).ToList();

        var results = new List<NeedConfigOption>
        {
            new (id: "Contact", label: "Contact", value: "Contact", "Contact", null),
            new (id: "Account", label: "Account", value: "Account", "Account", null),
            new (id: "Lead", label: "Lead", value: "Lead", "Lead", null),
            new (id: "Opportunity", label: "Opportunity", value: "Opportunity", "Opportunity", null),
            new (id: "Campaign", label: "Campaign", value: "Campaign", "Campaign", null),
        };
        results.AddRange(customObjectTypeOptions);

        results = results
            .Where(o => string.IsNullOrEmpty(searchName) || o.Label.ContainsIgnoreCase(searchName))
            .ToList();

        return Ok(new GenericOptionsResponse(results));
    }

    public class GetSalesforceUpdatableEntityTypeFieldOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }
    }

    [HttpPost("SalesforceUpdatableEntityTypeFields")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetSalesforceUpdatableEntityTypeFields(
        [FromBody]
        GetSalesforceUpdatableEntityTypeFieldOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var salesforceUpdatableFields = (await _providersApi.ProvidersGetProviderTypeFieldsV2PostAsync(
            getProviderTypeFieldsV2Input: new GetProviderTypeFieldsV2Input(
                sleekflowCompanyId: staff.CompanyId,
                providerName: "salesforce-integrator",
                providerConnectionId: request.ConnectionId,
                typedIds: null,
                entityTypeName: request.EntityTypeName))).Data.UpdatableFields;
        var results = salesforceUpdatableFields
            .Where(c => string.IsNullOrEmpty(searchName) || c.Label.ContainsIgnoreCase(searchName))
            .Select(c => new NeedConfigOption(
                id: c.Name, label: c.Label, value: c.Name)).ToList();
        return Ok(new GenericOptionsResponse(results));
    }

    public class GetSalesforceSearchableEntityTypeFieldOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }
    }

    [HttpPost("SalesforceSearchableEntityTypeFields")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetSalesforceSearchableEntityTypeFields(
        [FromBody]
        GetSalesforceSearchableEntityTypeFieldOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff is null)
        {
            return Unauthorized();
        }

        var salesforceFieldsOutput = (await _providersApi.ProvidersGetProviderTypeFieldsV2PostAsync(
            getProviderTypeFieldsV2Input: new GetProviderTypeFieldsV2Input(
                sleekflowCompanyId: staff.CompanyId,
                providerConnectionId: request.ConnectionId,
                typedIds: null,
                entityTypeName: request.EntityTypeName,
                providerName: "salesforce-integrator"))).Data;
        var salesforceFields = salesforceFieldsOutput.ViewableFields
            .Concat(salesforceFieldsOutput.CreatableFields)
            .Concat(salesforceFieldsOutput.UpdatableFields)
            .GroupBy(f => f.Name)
            .Select(g => g.First())
            .ToHashSet();
        var results = salesforceFields
            .Where(c => string.IsNullOrEmpty(searchName) || c.Label.ContainsIgnoreCase(searchName))
            .Select(c => new NeedConfigOption(
                id: c.Name, label: c.Label, value: c.Name)).ToList();
        return Ok(new GenericOptionsResponse(results));
    }

    public class GetWhatsAppCloudApiChannelOptionsRequest
    {
        [JsonProperty("waba_id")]
        public string? WabaId { get; set; }
    }

    [HttpPost("WhatsAppCloudApiChannels")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetWhatsAppCloudApiChannelOptions(
    [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
    GetWhatsAppCloudApiChannelOptionsRequest request,
    [FromQuery]
    string searchName,
    [FromQuery]
    int offset = 0,
    [FromQuery]
    int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();
        var wabaId = request?.WabaId;

        var provider = _channelOptionsProviders.FirstOrDefault(p => p.ChannelType == ChannelTypes.WhatsappCloudApi);
        if (provider == null)
        {
            return BadRequest($"WhatsApp Cloud API provider not found.");
        }

        var results = await provider.GetOptionsAsync(
            dbContext,
            staff.CompanyId,
            searchName,
            new Dictionary<string, string> { { "waba_id", wabaId ?? string.Empty } },
            offset,
            limit);

        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("fbIgPages")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetFacebookIgPages(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        if (staff is null || staff.CompanyId.IsNullOrEmpty())
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();

        // Execute Facebook query
        var facebookConfigs = await dbContext.ConfigFacebookConfigs
            .Where(x => x.CompanyId == staff.CompanyId && !string.IsNullOrEmpty(x.PageId))
            .Where(x => x.Status == FacebookStatus.Authenticated)
            .Where(x => string.IsNullOrEmpty(searchName) || x.PageName.Contains(searchName))
            .OrderByDescending(x => x.PageId)
            .Select(
                x => new NeedConfigOption(
                    x.PageId,
                    x.PageName,
                    x.PageId,
                    string.Empty,
                    "facebook",
                    null))
            .ToListAsync(HttpContext.RequestAborted);

        // Execute Instagram query
        var instagramConfigs = await dbContext.ConfigInstagramConfigs
            .Where(x => x.CompanyId == staff.CompanyId && !string.IsNullOrEmpty(x.InstagramPageId))
            .Where(x => x.Status == FacebookStatus.Authenticated)
            .Where(x => string.IsNullOrEmpty(searchName) || x.PageName.Contains(searchName))
            .OrderByDescending(x => x.InstagramPageId)
            .Select(
                x => new NeedConfigOption(
                    x.InstagramPageId,
                    x.PageName,
                    x.InstagramPageId,
                    string.Empty,
                    "instagram",
                    null))
            .ToListAsync(HttpContext.RequestAborted);

        // Combine and paginate
        var combinedResults = facebookConfigs.Concat(instagramConfigs)
            .Skip(offset)
            .Take(limit)
            .ToList();

        return Ok(new GenericOptionsResponse(combinedResults));
    }

    public class GetFbIgPostsOptionsRequest
    {
        [JsonProperty("page_id")]
        public string PageId { get; set; }

        [JsonProperty("channel")]
        public string Channel { get; set; }
    }

    [HttpPost("fbIgPosts")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetFacebookIgPosts(
        [FromBody]
        GetFbIgPostsOptionsRequest request,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        if (string.IsNullOrEmpty(request.PageId) || string.IsNullOrEmpty(request.Channel))
        {
            _logger.LogError("parameter error: {Parameter}", request);
            return NotFound();
        }
        if (User.Identity is { IsAuthenticated: true })
        {

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }
            var _appDbContext = _dbContextService.GetDbContext();

            Company company = null;

            company = await _appDbContext.CompanyCompanies
                    .Where(x => x.Id == companyUser.CompanyId)
                    .Include(x => x.InstagramConfigs)
                    .Include(com => com.FacebookConfigs)
                    .FirstAsync();
            if (request.Channel.EqualsIgnoreCase("instagram") ? company.InstagramConfigs.IsNullOrEmpty() : company.FacebookConfigs.IsNullOrEmpty())
            {
                _logger.LogInformation("no facebook config and instagram config found. companyId: {CompanyId}", companyUser.CompanyId);
                return Ok(new ArrayList());
            }

            var pageAccessToken = request.Channel.EqualsIgnoreCase("instagram")
                    ? company.InstagramConfigs.Find(x => x.InstagramPageId == request.PageId)?.PageAccessToken
                    : company.FacebookConfigs.Find(x => x.PageId == request.PageId)?.PageAccessToken
                ;

            if (pageAccessToken == null)
            {
                _logger.LogError("pageAccessToken not found. pageId: {PageId}", request.PageId);
                return NotFound();
            }

            try
            {
                if (request.Channel.EqualsIgnoreCase("facebook"))
                {
                    var facebookPosts = await _facebookService.GetCompanyFacebookPosts(
                        request.PageId,
                        pageAccessToken,
                        null,
                        null,
                        offset,
                        limit,
                        string.Empty);
                    facebookPosts.ForEach(x => x.CreatedAt = x.CreatedAt.ToDisplayTime(company.TimeZoneInfoId));
                    return Ok(facebookPosts);
                }
                else
                {
                    var instagramMedias = await _instagramService.GetCompanyInstagramMedias(
                        request.PageId,
                        pageAccessToken,
                        null,
                        null,
                        offset,
                        limit,
                        string.Empty);
                    instagramMedias.ForEach(x => x.CreatedAt = x.CreatedAt.ToDisplayTime(company.TimeZoneInfoId));
                    return Ok(instagramMedias);
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "error occured.");
                throw;
            }
        }

        return Unauthorized();
    }

    [HttpPost("AiAgents")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetAiAgents(
    [FromBody] object request,
    [FromQuery] string searchName,
    [FromQuery] int offset = 0,
    [FromQuery] int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }
        var getAgentsOutput = await _intelligentHubInternalsApi.InternalsGetCompanyAgentConfigListPostAsync(
            getCompanyAgentConfigListInput: new GetCompanyAgentConfigListInput(
                sleekflowCompanyId: staff.CompanyId));

        var agents = getAgentsOutput.Data.CompanyAgentConfigs;

        var results = agents
            .Select(c => new NeedConfigOption(id: c.Id, label: c.Name, value: c.Id))
            .Where(a => string.IsNullOrEmpty(searchName) || a.Label.Contains(searchName, StringComparison.CurrentCultureIgnoreCase))
            .ToList();

        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("VtexAuthentications")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetVtexAuthentications(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var vtexAuthentications = (await _vtexApi.VtexGetVtexAuthenticationsPostAsync(
                getVtexAuthenticationsInput: new GetVtexAuthenticationsInput(
                    staff.CompanyId)))
            .Data
            .VtexAuthentications;
        var results = vtexAuthentications
            .Where(c => string.IsNullOrEmpty(searchName) || c.Title.ContainsIgnoreCase(searchName))
            .Select(c => new NeedConfigOption(
                id: c.Id, label: c.Title, value: c.Id, c.Title, null)).ToList();

        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("WabaAccounts")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetWabaAccounts(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }
        var dbContext = _dbContextService.GetDbContext();

        var results = await dbContext.ConfigWhatsappCloudApiConfigs
            .AsNoTracking()
            .Where(x => x.CompanyId == staff.CompanyId)
            .GroupBy(x => x.MessagingHubWabaId)
            .Select(
                g => new NeedConfigOption(
                    g.Key,
                    g.Select(x => x.FacebookWabaName).FirstOrDefault() ?? string.Empty,
                    g.Key,
                    string.Empty,
                    string.Empty,
                    null))
            .WhereIf(!string.IsNullOrEmpty(searchName), x => x.Label.ContainsIgnoreCase(searchName))
            .Skip(offset)
            .Take(limit)
            .ToListAsync();

        return Ok(new GenericOptionsResponse(results));
    }

    public class GetWhatsappFlowsRequest
    {
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }
    }

    [HttpPost("WhatsappFlows")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetWhatsappFlows(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetWhatsappFlowsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var results = await _whatsappCloudApiService.GetWhatsappFlowsByWabaIdAsync(
            staff.CompanyId,
            request.WabaId,
            null);

        var terminalScreensPayloadTasks = results.Data.Select(async x => new
        {
            FlowId = x.Id,
            Payloads = await _whatsappCloudApiService.GetTerminalScreensPayloadsAsync(
                staff.CompanyId,
                request.WabaId,
                x.Id)
        });

        var flowsWithValidSubmissionData = new HashSet<string>();
        try
        {
            var terminalScreensPayloads = await Task.WhenAll(terminalScreensPayloadTasks);

            foreach (var flowPayloads in terminalScreensPayloads)
            {
                var hasValidSubmissionData = flowPayloads.Payloads.Exists(p =>
                    WhatsappCloudApiFlowSubmissionHelper.TryParseWhatsappCloudApiFlowSubmission(
                        p.SubmissionDataPayload,
                        out var whatsappCloudApiFlowSubmission));

                if (hasValidSubmissionData)
                {
                    flowsWithValidSubmissionData.Add(flowPayloads.FlowId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting terminal screens payloads for WhatsApp flows in Company {CompanyId}, Waba {WabaId}",
                staff.CompanyId, request.WabaId);

            // If there's an error, we won't be able to filter properly, so we'll return an empty result
            return Ok(new GenericOptionsResponse(new List<NeedConfigOption>()));
        }

        var mappedResults = results.Data
            .Where(x => flowsWithValidSubmissionData.Contains(x.Id))
            .Where(x => string.IsNullOrEmpty(searchName) || x.Name.ContainsIgnoreCase(searchName))
            .Select(x => new NeedConfigOption(
                x.Id,
                x.Name,
                x.Id,
                string.Empty,
                string.Empty))
            .Skip(offset)
            .Take(limit)
            .ToList();

        return Ok(new GenericOptionsResponse(mappedResults));
    }

    public class GetLiveChatChannelOptionsRequest
    {
        [JsonProperty("trigger_id")]
        public string? TriggerId { get; set; }
    }

    [HttpPost("LiveChatChannels")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetLiveChatChannelOptions(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetLiveChatChannelOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();

        // Get live chat v2 channel options
        var provider = _channelOptionsProviders.FirstOrDefault(p => p.ChannelType == ChannelTypes.LiveChatV2);
        if (provider == null)
        {
            return BadRequest($"Channel type {ChannelTypes.LiveChatV2} not supported.");
        }

        var results = await provider.GetOptionsAsync(
            dbContext,
            staff.CompanyId,
            searchName,
            null,
            offset,
            limit);


        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("MessageTypes")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetMessageTypeOptions(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var messageTypeList = new List<NeedConfigOption>()
        {
            new NeedConfigOption("text", "Text", "text"),
            new NeedConfigOption("file", "File", "file"),
            new NeedConfigOption("interactive", "Interactive", "interactive"),
            new NeedConfigOption("location", "Location", "location"),
            new NeedConfigOption("image", "Image", "image"),
            new NeedConfigOption("media", "Media", "media"),
            new NeedConfigOption("order", "Order", "order"),
            new NeedConfigOption("reaction", "Reaction", "reaction"),
            new NeedConfigOption("template", "Template", "template"),
        };

        if (!string.IsNullOrEmpty(searchName))
        {
            return Ok(
                new GenericOptionsResponse(
                    messageTypeList.Where(x => x.Id.Contains(searchName.ToLower())).Skip(offset).Take(limit).ToList()));
        }

        return Ok(new GenericOptionsResponse(messageTypeList.Skip(offset).Take(limit).ToList()));
    }

    [HttpPost("MessageStatuses")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetMessageStatuses(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var messageStatusList = new List<NeedConfigOption>()
        {
            new NeedConfigOption(
                MessageStatus.Read.ToString(),
                MessageStatus.Read.ToString(),
                MessageStatus.Read.ToString()),
            new NeedConfigOption(
                MessageStatus.Received.ToString(),
                MessageStatus.Received.ToString(),
                MessageStatus.Received.ToString()),
            new NeedConfigOption(
                MessageStatus.Failed.ToString(),
                MessageStatus.Failed.ToString(),
                MessageStatus.Failed.ToString()),
            new NeedConfigOption(
                MessageStatus.Sent.ToString(),
                MessageStatus.Sent.ToString(),
                MessageStatus.Sent.ToString()),
            new NeedConfigOption(
                MessageStatus.OutOfCredit.ToString(),
                "Out of credit",
                MessageStatus.OutOfCredit.ToString()),
            new NeedConfigOption(
                MessageStatus.Undelivered.ToString(),
                MessageStatus.Undelivered.ToString(),
                MessageStatus.Undelivered.ToString()),
            new NeedConfigOption(
                MessageStatus.Sending.ToString(),
                MessageStatus.Sending.ToString(),
                MessageStatus.Sending.ToString()),
            new NeedConfigOption(
                MessageStatus.Scheduled.ToString(),
                MessageStatus.Scheduled.ToString(),
                MessageStatus.Scheduled.ToString()),
            new NeedConfigOption(MessageStatus.Deleted.ToString(), MessageStatus.Deleted.ToString())
        };

        if (!string.IsNullOrEmpty(searchName))
        {
            return Ok(new GenericOptionsResponse(messageStatusList.Where(x => x.Id.Contains(searchName)).Skip(offset).Take(limit).ToList()));
        }

        return Ok(new GenericOptionsResponse(messageStatusList.Skip(offset).Take(limit).ToList()));
    }

    [HttpPost("MessageDeliveryTypes")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetMessageDeliveryTypes(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var messageDeliveryTypesList = new List<NeedConfigOption>()
        {
            new NeedConfigOption(
                DeliveryType.AutomatedMessage.ToString(),
                "Automated",
                DeliveryType.AutomatedMessage.ToString()),
            new NeedConfigOption(
                DeliveryType.Broadcast.ToString(),
                "Broadcast",
                DeliveryType.Broadcast.ToString()),
            new NeedConfigOption(
                DeliveryType.Normal.ToString(),
                "Normal",
                DeliveryType.Normal.ToString()),
            new NeedConfigOption(
                DeliveryType.FlowHubAction.ToString(),
                "Flowhub",
                DeliveryType.FlowHubAction.ToString()),
            new NeedConfigOption(
                DeliveryType.AiAgentAction.ToString(),
                "AI Agent",
                DeliveryType.AiAgentAction.ToString()),
            new NeedConfigOption(
                DeliveryType.QuickReply.ToString(),
                "Quick Reply",
                DeliveryType.QuickReply.ToString()),
        };

        if (!string.IsNullOrEmpty(searchName))
        {
            return Ok(
                new GenericOptionsResponse(
                    messageDeliveryTypesList.Where(x => x.Id.Contains(searchName)).Skip(offset).Take(limit).ToList()));
        }

        return Ok(new GenericOptionsResponse(messageDeliveryTypesList.Skip(offset).Take(limit).ToList()));
    }

    [HttpPost("TikTokAdsConnections")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetTikTokAdsConnections(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var tikTokAdsConnections = (await _providersApi.ProvidersGetProviderConnectionsPostAsync(
            getProviderConnectionsInput: new GetProviderConnectionsInput(
                sleekflowCompanyId: staff.CompanyId,
                providerName: "tiktok-ads-integrator",
                connectionScope: "tiktok-ads"))).Data.Connections;
        var results = tikTokAdsConnections
            .Where(c => string.IsNullOrEmpty(searchName) || c.Name.ContainsIgnoreCase(searchName))
            .Select(c => new NeedConfigOption(
                id: c.Id, label: c.Name, value: c.Id, c.Name, null)).ToList();
        return Ok(new GenericOptionsResponse(results));
    }

    public class GetTikTokAdsAdvertisersOptionsRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }
    }

    [HttpPost("TikTokAdsAdvertisers")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetTikTokAdsAdvertisers(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetTikTokAdsAdvertisersOptionsRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var searchObjectsOutputOutput = await _providersApi.ProvidersSearchObjectsPostAsync(
            searchObjectsInput: new SearchObjectsInput(
                staff.CompanyId,
                request.ConnectionId,
                "tiktok-ads-integrator",
                "Advertiser"));

        var advertisers = TikTokAdsSearchObjectsHelper.ConvertToAdvertiserDtos(searchObjectsOutputOutput);

        var results = advertisers
            .Where(r => string.IsNullOrEmpty(searchName) || r.AdvertiserName.ContainsIgnoreCase(searchName))
            .Select(r => new NeedConfigOption(
                id: r.AdvertiserId, label: r.AdvertiserName, value: r.AdvertiserId, r.AdvertiserName, null)).ToList();
        return Ok(new GenericOptionsResponse(results));
    }

    public class GetTikTokAdsPagesRequest
    {
        [JsonProperty("connection_id")]
        public string ConnectionId { get; set; }

        [JsonProperty("advertiser_id")]
        public string AdvertiserId { get; set; }
    }

    [HttpPost("TikTokAdsPages")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetTikTokAdsPages(
        [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)]
        GetTikTokAdsPagesRequest request,
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var searchObjectsOutputOutput = await _providersApi.ProvidersSearchObjectsPostAsync(
            searchObjectsInput: new SearchObjectsInput(
                staff.CompanyId,
                request.ConnectionId,
                "tiktok-ads-integrator",
                "InstantPage",
                typedIds: new List<TypedId>
                {
                    new ("advertiser_id", request.AdvertiserId)
                }));

        var instantPageOverviews = TikTokAdsSearchObjectsHelper.ConvertToInstantPageOverviewDtos(searchObjectsOutputOutput);

        var results = instantPageOverviews
            .Where(r => string.IsNullOrEmpty(searchName) || r.Title.ContainsIgnoreCase(searchName))
            .Select(r => new NeedConfigOption(
                id: r.PageId, label: r.Title, value: r.PageId, r.Title, null)).ToList();
        return Ok(new GenericOptionsResponse(results));
    }

    [HttpPost("ShopifyConfigs")]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<GenericOptionsResponse>> GetShopifyConfigs(
        [FromQuery]
        string searchName,
        [FromQuery]
        int offset = 0,
        [FromQuery]
        int limit = 10)
    {
        var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

        if (staff is null)
        {
            return Unauthorized();
        }

        var dbContext = _dbContextService.GetDbContext();

        var shopifyConfigs = await dbContext.ConfigShopifyConfigs.Where(
            c => c.CompanyId == staff.CompanyId && c.Status == ShopifyStatus.Connected).ToListAsync();
        var results = shopifyConfigs
            .Where(c => string.IsNullOrEmpty(searchName) || c.Name.ContainsIgnoreCase(searchName))
            .Select(c => new NeedConfigOption(
                id: c.Id.ToString(), label: c.Name, value: c.Id.ToString(), c.Name.ToString())).ToList()
            .Skip(offset)
            .Take(limit)
            .ToList();

        return Ok(new GenericOptionsResponse(results));
    }
}