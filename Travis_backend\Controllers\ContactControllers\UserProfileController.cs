﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.BackgroundTaskServices;
using Travis_backend.BackgroundTaskServices.Helpers;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Data.BackgroundTask;
using Travis_backend.Database;
using Travis_backend.Database.Filters;
using Travis_backend.Enums;
using Travis_backend.Exceptions;
using Travis_backend.Helpers;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.IntegrationServices.Models;
using AutoMapper;
using Travis_backend.Cache;
using Travis_backend.ContactDomain.Filters;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.PiiMaskingDomain.Services;
using Travis_backend.PiiMasking.Models;
using Travis_backend.TenantHubDomain.Services;
using Travis_backend.Telemetries;
using Travis_backend.Telemetries.Constants;

namespace Travis_backend.Controllers.ContactControllers
{
    [Authorize]
    public class UserProfileController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger _logger;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly IBackgroundTaskService _backgroundTaskService;
        private readonly ICoreService _coreService;
        private readonly IDbUserProfileService _dbUserProfileService;
        private readonly IContactDeletionConfig _contactDeletionConfig;
        private readonly IUserProfileSafeDeleteService _userProfileSafeDeleteService;
        private readonly IMapper _mapper;
        private readonly IPiiMaskingService _piiMaskingService;
        private readonly ILockService _lockService;
        private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
        private readonly IRbacService _rbacService;
        private readonly IAccessControlAggregationService _accessControlAggregationService;

        public UserProfileController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            ILogger<UserProfileController> logger,
            IAzureBlobStorageService azureBlobStorageService,
            IBackgroundTaskService backgroundTaskService,
            ICoreService coreService,
            IDbUserProfileService dbUserProfileService,
            IContactDeletionConfig contactDeletionConfig,
            IUserProfileSafeDeleteService userProfileSafeDeleteService,
            IMapper mapper,
            IPiiMaskingService piiMaskingService,
            ILockService lockService,
            IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
            IRbacService rbacService,
            IAccessControlAggregationService accessControlAggregationService)
        {
            _userManager = userManager;
            _appDbContext = appDbContext;
            _logger = logger;
            _azureBlobStorageService = azureBlobStorageService;
            _backgroundTaskService = backgroundTaskService;
            _coreService = coreService;
            _dbUserProfileService = dbUserProfileService;
            _contactDeletionConfig = contactDeletionConfig;
            _userProfileSafeDeleteService = userProfileSafeDeleteService;
            _mapper = mapper;
            _piiMaskingService = piiMaskingService;
            _lockService = lockService;
            _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
            _rbacService = rbacService;
            _accessControlAggregationService = accessControlAggregationService;
        }

        [HttpGet]
        [Route("UserProfile/Total")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<UserProfileTotalResponse>> GetUserProfileCount()
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var userProfileCount = await _dbUserProfileService.GetUserProfileCountAsync(null, companyUser: companyUser);

            return Ok(
                new UserProfileTotalResponse
                {
                    TotalNumberOfUserProfile = userProfileCount.TotalResult
                });
        }

        [HttpPost]
        [Route("UserProfile/Total")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<SelectAllContactsResponse>> GetUserProfileCount(
            [FromBody]
            List<Condition> conditions,
            [FromQuery(Name = "status")]
            string status = "all",
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var userProfileCount = await _dbUserProfileService.GetUserProfileCountAsync(
                conditions,
                companyUser,
                status,
                channels,
                channelIds);
            return userProfileCount;
        }

        [HttpGet]
        [Route("UserProfile")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<SearchUserContactsResponse>> GetUserProfile(
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "sortby")]
            string sortby = "createdat",
            [FromQuery(Name = "order")]
            string order = "desc")
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiGetRequestReceived,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            var userProfileResult =
                await _dbUserProfileService.GetUserProfileAsync(companyUser, offset, limit, sortby, order);

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiGetRequestCompleted,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            return Ok(userProfileResult);
        }

        [HttpPost]
        [Route("UserProfile/Add")]
        public async Task<ActionResult<List<UserProfileNoCompanyResponse>>> AddUserProfile(
            [FromBody]
            List<NewProfileViewModel> newProfileViewModels)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.ContactApiAddRequestReceived,
                    new Dictionary<string, string>
                    {
                        { "company_id", companyUser.CompanyId }
                    });

                var userProfileResponse =
                    await _dbUserProfileService.AddUserProfileAsync(companyUser, newProfileViewModels);

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.ContactCreatedWithSource,
                    new Dictionary<string, string>
                    {
                        {
                            "source", UserProfileSource.ManualEntry.ToString()
                        },
                        {
                            "company_id", companyUser.CompanyId
                        }
                    },
                    new Dictionary<string, double>
                    {
                        {
                            "count", userProfileResponse.Count
                        }
                    });

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.ContactApiAddRequestCompleted,
                    new Dictionary<string, string>
                    {
                        { "company_id", companyUser.CompanyId }
                    });

                return Ok(userProfileResponse);
            }
            catch (DuplicatedContactException ex)
            {
                dynamic response = new JObject();

                response.message = $"Duplicated {ex.DuplicatedFieldName}";
                response.duplicationMode = ex.DuplicationMode;

                if (ex.HasPermissionToViewProfile)
                {
                    response.userProfileId = ex.ExistingUserProfileId;
                }

                return BadRequest(response);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("UserProfile/Update/{userProfileId}")]
        public async Task<ActionResult<UserProfileNoCompanyResponse>> UpdateUserProfile(
            [FromBody]
            NewProfileViewModel profileUpdateViewModel,
            string userProfileId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.ContactApiUpdateRequestReceived,
                    new Dictionary<string, string>
                    {
                        { "company_id", companyUser.CompanyId }
                    });

                var userProfileResponse = await _dbUserProfileService.UpdateUserProfileAsync(
                    companyUser,
                    userProfileId,
                    profileUpdateViewModel);

                await MaskUserProfileNoCompanyResponseAsync(
                    userProfileResponse,
                    companyUser.CompanyId,
                    companyUser.RoleType);

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.ContactApiUpdateRequestCompleted,
                    new Dictionary<string, string>
                    {
                        { "company_id", companyUser.CompanyId }
                    });

                return Ok(userProfileResponse);
            }
            catch (EntryPointNotFoundException nex)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = nex.Message
                    });
            }
            catch (FormatException fex)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = fex.Message
                    });
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("UserProfile/Search")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<SearchUserContactsResponse>> SearchUserProfile(
            [FromBody]
            List<Condition> conditions,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "fields")]
            string fields = null,
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "sortby")]
            string sortBy = "createdat",
            [FromQuery(Name = "order")]
            string order = "desc")
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiSearchRequestReceived,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            // Hacky way to fulfil the DEVS-13923
            var isRbacEnabled = _rbacService.IsRbacEnabled();
            if (isRbacEnabled)
            {
                var staff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(companyUser);

                if (staff.CanViewAllContacts())
                {
                    // Mocking as admin for query
                    companyUser.RoleType = StaffUserRole.Admin;
                }
            }

            var selectedUserProfileResponse = await _dbUserProfileService.SearchUserProfileAsync(
                companyUser,
                conditions,
                offset,
                limit,
                fields,
                channels,
                channelIds,
                sortBy,
                order);

            await MaskSearchUserContactsResponseAsync(
                selectedUserProfileResponse,
                companyUser.CompanyId,
                companyUser.RoleType);

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiSearchRequestCompleted,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            return Ok(selectedUserProfileResponse);
        }

        private async Task MaskSearchUserContactsResponseAsync(
            SearchUserContactsResponse response,
            string companyId,
            StaffUserRole role)
        {
            if (!await _piiMaskingService.IsConfiguredAsync(companyId)
                || !Enum.TryParse(role.ToString(), out MaskingRoles maskingRole))
            {
                return;
            }

            var companyCustomUserProfileFieldIdToType = await _appDbContext.CompanyCustomUserProfileFields
                .Where(x => x.CompanyId == companyId)
                .ToDictionaryAsync(x => x.Id, x => x.Type);

            foreach (var userProfile in response.UserProfiles)
            {
                if (!string.IsNullOrWhiteSpace(userProfile.FirstName))
                {
                    userProfile.FirstName = await _piiMaskingService.MaskPiiIfEnabledAsync(
                        companyId,
                        userProfile.FirstName,
                        MaskingLocations.Contact,
                        maskingRole);
                }

                if (!string.IsNullOrWhiteSpace(userProfile.LastName))
                {
                    userProfile.LastName = await _piiMaskingService.MaskPiiIfEnabledAsync(
                        companyId,
                        userProfile.LastName,
                        MaskingLocations.Contact,
                        maskingRole);
                }

                foreach (var field in userProfile.CustomFields)
                {
                    var type = companyCustomUserProfileFieldIdToType.GetValueOrDefault(field.CompanyDefinedFieldId);

                    if (field.Value is null
                        || (type != FieldDataType.SingleLineText
                            && type != FieldDataType.MultiLineText
                            && type != FieldDataType.Number
                            && type != FieldDataType.PhoneNumber
                            && type != FieldDataType.Email))
                    {
                        continue;
                    }

                    field.Value = await _piiMaskingService.MaskPiiIfEnabledAsync(
                        companyId,
                        field.Value,
                        MaskingLocations.Contact,
                        maskingRole);
                }
            }
        }

        /// <summary>
        /// Search contact.
        /// </summary>
        /// <param name="conditions"><b>**** SleekFlow Search condition format: <a href="https://www.notion.so/sleekflow/Contact-field-supported-condition-operator-d1ab7f3192884b909684ab07b20089f4">Detail</a></b></param>
        /// <param name="offset">For pagination.</param>
        /// <param name="limit">For pagination.</param>
        /// <param name="status">[ <b>all, open, pending, closed</b> ].</param>
        /// <param name="channels">General channel name like: <b>whatsapp,whatsapp360dialog,facebook</b> ;Separated by. <b>,</b></param>
        /// <param name="channelIds">Specify the channel id like:<br />
        /// <param name="assignedTo"><b>all</b>: List all manageable conversations (For <b>Admin</b> and <b>TeamAdmin</b> role only)<br />
        /// Twilio: **********************************;whatsapp:+8526452244<br />
        /// 360Dialog: 175.
        /// </param>
        /// <returns></returns>
        [HttpPost]
        [Route("v2/UserProfile/Search/")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<SearchUserConversationResponse>> SearchUserProfileV2(
            [FromBody]
            List<Condition> conditions,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "status")]
            string status = "all",
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "assignedTo")]
            string assignedTo = null,
            [FromQuery(Name = "teamId")]
            long? teamId = null)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiSearchRequestReceived,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            // Hacky way to fulfil the DEVS-13923
            var isRbacEnabled = _rbacService.IsRbacEnabled();
            if (isRbacEnabled)
            {
                var staff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(companyUser);

                if (staff.CanViewAllContacts())
                {
                    // Mocking as admin for query
                    companyUser.RoleType = StaffUserRole.Admin;
                }
            }

            var selectedUserProfileResponse = await _dbUserProfileService.SearchUserProfileWithConversationAsync(
                companyUser,
                conditions,
                offset,
                limit,
                status,
                channels,
                channelIds,
                assignedTo,
                teamId);

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiSearchRequestCompleted,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            return Ok(selectedUserProfileResponse);
        }

        /// <summary>
        /// Search contact with conversation
        /// </summary>
        /// <param name="conditions"><b>**** SleekFlow Search condition format: <a href="https://www.notion.so/sleekflow/Contact-field-supported-condition-operator-d1ab7f3192884b909684ab07b20089f4">Detail</a></b></param>
        /// <param name="offset">For pagination.</param>
        /// <param name="limit">For pagination.</param>
        /// <param name="status">[ <b>all, open, pending, closed</b> ].</param>
        /// <param name="channels">General channel name like: <b>whatsapp,whatsapp360dialog,facebook</b> ;Separated by. <b>,</b></param>
        /// <param name="channelIds">Specify the channel id like:<br />
        /// <param name="assignedTo"><b>all</b>: List all manageable conversations (For <b>Admin</b> and <b>TeamAdmin</b> role only)<br />
        /// Twilio: **********************************;whatsapp:+8526452244<br />
        /// 360Dialog: 175.
        /// </param>
        /// <returns></returns>
        [HttpPost]
        [Route("v3/UserProfile/Search/")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<SearchUserConversationResponse>> SearchUserProfileV3(
            [FromBody]
            List<Condition> conditions,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "status")]
            string status = "all",
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null,
            [FromQuery(Name = "assignedTo")]
            string assignedTo = null,
            [FromQuery(Name = "teamId")]
            long? teamId = null,
            CancellationToken cancellationToken = default)
        {

            _logger.LogInformation(
                "[{MethodName}] Parameters: conditions={Conditions}, offset={Offset}, limit={Limit}, " +
                "status={Status}, channels={Channels}, channelIds={ChannelIds}, assignedTo={AssignedTo}, " +
                "teamId={TeamId}, cancellationToken={CancellationToken}",
                nameof(SearchUserProfileV3),
                conditions,
                offset,
                limit,
                status,
                channels,
                channelIds,
                assignedTo,
                teamId,
                cancellationToken);
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiSearchRequestReceived,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            // Hacky way to fulfil the DEVS-13923
            var isRbacEnabled = _rbacService.IsRbacEnabled();
            if (isRbacEnabled)
            {
                var staff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(companyUser);

                if (staff.CanViewAllContacts())
                {
                    // Mocking as admin for query
                    companyUser.RoleType = StaffUserRole.Admin;
                }
            }

            var selectedUserProfileResponse = await _dbUserProfileService.SearchUserProfileWithConversationAsync(
                companyUser,
                conditions,
                offset,
                limit,
                status,
                channels,
                channelIds,
                assignedTo,
                teamId,
                "2",
                cancellationToken);

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiSearchRequestCompleted,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            return Ok(selectedUserProfileResponse);
        }

        [HttpPost]
        [Route("UserProfile/SelectAll")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<SelectAllContactsResponse>> SelectAllUserProfile(
            [FromBody]
            List<Condition> conditions,
            [FromQuery(Name = "status")]
            string status = "all",
            [FromQuery(Name = "channel")]
            string channels = null,
            [FromQuery(Name = "channelIds")]
            string channelIds = null)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiGetRequestReceived,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            var response = await _dbUserProfileService.GetAllUserProfileIdAsync(
                companyUser,
                conditions,
                status,
                channels,
                channelIds);

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiGetRequestCompleted,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            return Ok(response);
        }

        [HttpPost]
        [Route("UserProfile/Description/{userProfileId}")]
        public async Task<ActionResult<UserProfileNoCompanyResponse>> UpdateUserProfileDescription(
            string userProfileId,
            [FromBody]
            UserProfileDescriptionModel userProfileDescriptionModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.ContactApiUpdateRequestReceived,
                    new Dictionary<string, string>
                    {
                        { "company_id", companyUser.CompanyId }
                    });

                var response = await _dbUserProfileService.UpdateUserProfileDescriptionAsync(
                    companyUser,
                    userProfileId,
                    userProfileDescriptionModel);

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.ContactApiUpdateRequestCompleted,
                    new Dictionary<string, string>
                    {
                        { "company_id", companyUser.CompanyId }
                    });

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpGet]
        [Route("userprofile/conversation/{userProfileId}")]
        public async Task<ActionResult<ConversationNoCompanyResponseViewModel>> GetConversationByUserProfileId(
            string userProfileId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var conversationResponse =
                    await _dbUserProfileService.GetConversationByUserProfileIdAsync(companyUser, userProfileId);

                return Ok(conversationResponse);
            }
            catch (EntryPointNotFoundException notFoundEx)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = notFoundEx.Message
                    });
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpGet]
        [Route("UserProfile/{userProfileId}")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<UserProfileNoCompanyResponse>> GetUserProfile(string userProfileId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiGetRequestReceived,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            var results = await _dbUserProfileService.GetUserProfileByIdAsync(companyUser, userProfileId);

            await MaskUserProfileNoCompanyResponseAsync(
                results,
                companyUser.CompanyId,
                companyUser.RoleType);

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiGetRequestCompleted,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            return Ok(results);
        }

        private async Task MaskUserProfileNoCompanyResponseAsync(
            UserProfileNoCompanyResponse response,
            string companyId,
            StaffUserRole role)
        {
            if (!await _piiMaskingService.IsConfiguredAsync(companyId)
                || !Enum.TryParse(role.ToString(), out MaskingRoles maskingRole))
            {
                return;
            }

            if (!string.IsNullOrWhiteSpace(response.FirstName))
            {
                response.FirstName = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    companyId,
                    response.FirstName,
                    MaskingLocations.Contact,
                    maskingRole);
            }

            if (!string.IsNullOrWhiteSpace(response.LastName))
            {
                response.LastName = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    companyId,
                    response.LastName,
                    MaskingLocations.Contact,
                    maskingRole);
            }

            var companyCustomUserProfileFieldIdToType = await _appDbContext.CompanyCustomUserProfileFields
                .Where(x => x.CompanyId == companyId)
                .ToDictionaryAsync(x => x.Id, x => x.Type);

            foreach (var field in response.CustomFields)
            {
                var type = companyCustomUserProfileFieldIdToType.GetValueOrDefault(field.CompanyDefinedFieldId);

                if (field.Value is null
                    || (type != FieldDataType.SingleLineText
                        && type != FieldDataType.MultiLineText
                        && type != FieldDataType.Number
                        && type != FieldDataType.PhoneNumber
                        && type != FieldDataType.Email))
                {
                    continue;
                }

                field.Value = await _piiMaskingService.MaskPiiIfEnabledAsync(
                    companyId,
                    field.Value,
                    MaskingLocations.Contact,
                    maskingRole);
            }
        }

        [HttpGet]
        [Route("UserProfile/customFields/{userProfileId}")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<List<UserProfileCustomFieldNoOptionsViewModel>>> GetCustomFieldsUserProfile(
            string userProfileId)
        {
            _logger.LogInformation($"GetUserProfile: userProfileId {userProfileId}");

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var results = await _dbUserProfileService.GetUserProfileCustomFieldAsync(companyUser, userProfileId);
            return Ok(results);
        }

        [HttpPost]
        [Route("UserProfile/CustomFields/{userProfileId}")]
        public async Task<ActionResult<UserProfileNoCompanyResponse>> UpdateUserProfile(
            [FromBody]
            List<AddCustomFieldsViewModel> addCustomFieldsViewModels,
            string userProfileId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.ContactApiUpdateRequestReceived,
                    new Dictionary<string, string>
                    {
                        { "company_id", companyUser.CompanyId }
                    });

                var responseVm = await _dbUserProfileService.UpdateUserProfileCustomFieldAsync(
                    companyUser,
                    userProfileId,
                    addCustomFieldsViewModels);

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.ContactApiUpdateRequestCompleted,
                    new Dictionary<string, string>
                    {
                        { "company_id", companyUser.CompanyId }
                    });

                return Ok(responseVm);
            }
            catch (EntryPointNotFoundException nex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = nex.Message
                    });
            }
            catch (FormatException fex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = fex.Message
                    });
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpDelete]
        [Route("UserProfile")]
        public async Task<ActionResult<ResponseViewModel>> DeleteUserProfile(
            [FromBody]
            UserProfileIdsViewModel userprofileViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.ContactApiDeleteRequestReceived,
                    new Dictionary<string, string>
                    {
                        { "company_id", companyUser.CompanyId }
                    });

                var deleteSuccess = _contactDeletionConfig.IsContactSafeDeleteEnabled
                    ? await _userProfileSafeDeleteService.SoftDeleteUserProfilesAsync(
                        companyUser,
                        companyUser.CompanyId,
                        userprofileViewModel.UserProfileIds.ToHashSet(),
                        new UserProfileDeletionTriggerContext(UpdateUserProfileTriggerSource.StaffManual, companyUser))
                    : await _dbUserProfileService.DeleteUserProfileAsync(companyUser, userprofileViewModel);

                if (deleteSuccess)
                {
                    _applicationInsightsTelemetryTracer.TraceEvent(
                        TraceEventNames.ContactApiDeleteRequestCompleted,
                        new Dictionary<string, string>
                        {
                            { "company_id", companyUser.CompanyId }
                        });

                    return Ok(
                        new ResponseViewModel
                        {
                            message = "success"
                        });
                }

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "failed"
                    });
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "failed"
                    });
            }
        }

        [HttpPost]
        [Route("UserProfile/CustomFields")]
        public async Task<ActionResult<List<UserProfileNoCompanyResponse>>> UpdateBulkUserProfile(
            [FromBody]
            BulkUpdateCustomFieldsViewModel bulkUpdateCustomFieldsViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.ContactApiUpdateRequestReceived,
                    new Dictionary<string, string>
                    {
                        { "company_id", companyUser.CompanyId }
                    });

                var userProfile = await _dbUserProfileService.BulkUpdateUserProfileCustomFieldAsync(
                    companyUser,
                    bulkUpdateCustomFieldsViewModel);

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.ContactApiUpdateRequestCompleted,
                    new Dictionary<string, string>
                    {
                        { "company_id", companyUser.CompanyId }
                    });

                return Ok(userProfile);
            }
            catch (EntryPointNotFoundException nex)
            {
                return NotFound(nex.ToString());
            }
            catch (FormatException fex)
            {
                return BadRequest(fex.ToString());
            }
            catch (Exception ex)
            {
                return BadRequest(ex.ToString());
            }
        }

        [HttpPost]
        [Route("UserProfile/CustomFields/background")]
        public async Task<ActionResult<BackgroundTaskViewModel>> UpdateBulkUserProfileInBackground(
            [FromBody]
            BulkUpdateCustomFieldsViewModel bulkUpdateCustomFieldsViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiUpdateRequestReceived,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            var backgroundTask = await _backgroundTaskService.EnqueueBulkUpdateCustomFieldsTask(
                companyUser.IdentityId,
                companyUser.CompanyId,
                companyUser.Id,
                bulkUpdateCustomFieldsViewModel);

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ContactApiUpdateRequestCompleted,
                new Dictionary<string, string>
                {
                    { "company_id", companyUser.CompanyId }
                });

            return Ok(backgroundTask.MapToResultViewModel());
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("userprofile/picture/{profilePictureId}")]
        public async Task<IActionResult> GetProfilePicture(
            string profilePictureId,
            [FromQuery]
            string mode = "redirect")
        {
            var file = await _appDbContext.UserProfilePictureFiles.FirstOrDefaultAsync(
                x => x.ProfilePictureId == profilePictureId);

            if (mode is "redirect")
            {
                var url = new Uri(_azureBlobStorageService.GetAzureBlobSasUri(file.Filename, file.BlobContainer));
                return Redirect(url.AbsoluteUri);
            }

            var stream = await _azureBlobStorageService.DownloadFromAzureBlob(file.Filename, file.BlobContainer);
            return File(
                stream.ToArray(),
                string.IsNullOrEmpty(file.MIMEType) ? "application/octet-stream" : file.MIMEType,
                file.Filename);
        }

        #region UserProfileSafeDeletion

        [HttpGet]
        [Route("Userprofile/SafeDeletion/GetUserProfiles")]
        public async Task<ActionResult<SafeDeletedUserProfilesResponse>> GetSafeDeletedUserProfiles(
            [FromQuery(Name = "offset")] int offset = 0,
            [FromQuery(Name = "limit")] int limit = 20,
            [FromQuery(Name = "orderBy")] string orderBy = "DeletedAt",
            [FromQuery(Name = "direction")] string direction = "DESC")
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var response = new SafeDeletedUserProfilesResponse();
            var userProfileDeletionBuffers =
                await _userProfileSafeDeleteService.GetSoftDeletedUserProfilesAsync(
                    companyUser.CompanyId,
                    offset,
                    limit,
                    orderBy,
                    direction);
            var totalResult =
                await _userProfileSafeDeleteService.CountSoftDeletedUserProfilesAsync(companyUser.CompanyId);

            response.SafeDeletedUserProfiles = userProfileDeletionBuffers.Select(
                    profile => new SafeDeletedUserProfileViewModel()
                    {
                        Id = profile.Id,
                        UserProfile = profile.UserProfile,
                        DeletedAt = profile.DeletedAt,
                        ScheduledHardDeleteAt = profile.ScheduledHardDeleteAt,
                        DeletedByStaff = _mapper.Map<StaffWithoutCompanyResponse>(profile.DeletedByStaff)
                    })
                .ToList();
            response.TotalResult = totalResult;
            return Ok(response);
        }

        [HttpGet]
        [Route("Userprofile/SafeDeletion/GetTotalCount")]
        public async Task<ActionResult<long>> GetSafeDeletedUserProfilesTotalCount()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var totalDeletedCount = await _userProfileSafeDeleteService.CountSoftDeletedUserProfilesAsync(companyUser.CompanyId);

            return Ok(totalDeletedCount);
        }

        [HttpGet]
        [Route("Userprofile/SafeDeletion/GetUserProfileIds")]
        public async Task<ActionResult<List<string>>> GetSafeDeletedUserProfilesIds()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var totalDeletedIds = await _userProfileSafeDeleteService.GetSoftDeletedUserProfileIdsAsync(companyUser.CompanyId);

            return Ok(totalDeletedIds);
        }

        [HttpPost]
        [Route("Userprofile/SafeDeletion/Recover")]
        [TypeFilter(typeof(ContactSafeDeletionExceptionFilter))]
        public async Task<ActionResult<int>> RecoverSoftDeletedUserProfiles(
                [FromBody]
                HashSet<string> userProfileIds)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var @lock = await _userProfileSafeDeleteService.GetContactSafeDeletionConcurrentCallLockAsync(
                companyUser.CompanyId,
                nameof(RecoverSoftDeletedUserProfiles));
            try
            {
                var totalRecoveredCount = await _userProfileSafeDeleteService.RecoverSoftDeletedUserProfilesAsync(
                    companyUser.CompanyId,
                    userProfileIds,
                    new UserProfileRecoveryTriggerContext(
                        UpdateUserProfileTriggerSource.StaffManual,
                        companyUser));

                return Ok(totalRecoveredCount);
            }
            finally
            {
                await _userProfileSafeDeleteService
                    .ReleaseContactSafeDeletionConcurrentCallLockAsync(@lock);
            }
        }

        [HttpPost]
        [Route("Userprofile/SafeDeletion/HardDelete")]
        [TypeFilter(typeof(ContactSafeDeletionExceptionFilter))]
        public async Task<ActionResult<int>> HardDeleteSoftDeletedUserProfiles(
            [FromBody]
            HashSet<string> userProfileIds)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var @lock = await _userProfileSafeDeleteService.GetContactSafeDeletionConcurrentCallLockAsync(
                companyUser.CompanyId,
                nameof(HardDeleteSoftDeletedUserProfiles));
            try
            {
                var totalHardDeletedCount = await _userProfileSafeDeleteService.HardDeleteSoftDeletedUserProfilesAsync(
                    companyUser.CompanyId,
                    userProfileIds,
                    new UserProfileDeletionTriggerContext(
                        UpdateUserProfileTriggerSource.StaffManual,
                        companyUser));

                return Ok(totalHardDeletedCount);
            }
            finally
            {
                await _userProfileSafeDeleteService
                    .ReleaseContactSafeDeletionConcurrentCallLockAsync(@lock);
            }
        }
        #endregion

        [HttpGet]
        [Route("userprofile/list/")]
        public async Task<ActionResult<UserGroupResult>> GetGroupList(
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10,
            [FromQuery(Name = "name")]
            string name = null)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var response = await _dbUserProfileService.GetUserProfileListAsync(companyUser, name, offset, limit);

            return Ok(response);
        }

        [HttpGet]
        [Route("userprofile/list/brief")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<UserGroupBriefResult>> GetGroupListBrief(
            [FromQuery(Name = "name")] string name = null)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var response = await _dbUserProfileService.GetUserProfileListBriefResultAsync(companyUser, name);

            return Ok(response);
        }

        [HttpDelete]
        [Route("userprofile/list/")]
        public async Task<ActionResult<ResponseViewModel>> DeleteGroupList(
            [FromBody]
            GroupListViewModel groupListViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                await _dbUserProfileService.DeleteUserProfileListAsync(companyUser, companyUser.Id, groupListViewModel);

                return Ok(
                    new ResponseViewModel
                    {
                        message = "success"
                    });
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"Unable to remove this list: {ex.Message}"
                    });
            }
        }

        [HttpPost]
        [Route("userprofile/list/bookmark")]
        public async Task<ActionResult<ResponseViewModel>> SetBookmarkList(
            [FromBody]
            List<BookmarkViewModel> listReorderViewModels)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                await _dbUserProfileService.BookmarkUserProfileListAsync(companyUser, listReorderViewModels);

                return Ok(
                    new ResponseViewModel
                    {
                        message = "success"
                    });
            }
            catch (Exception ex)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("userprofile/list/reorder")]
        public async Task<ActionResult<ResponseViewModel>> ReorderList(
            [FromBody]
            List<ListReorderViewModel> listReorderViewModels)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            await _dbUserProfileService.ReorderUserProfileListAsync(companyUser, listReorderViewModels);
            return Ok(
                new ResponseViewModel
                {
                    message = "success"
                });
        }

        [HttpGet]
        [Route("userprofile/list/{groupId}")]
        public async Task<ActionResult<ImportContactHistoryResponse>> GetUserProfileListCount(long groupId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var response = await _dbUserProfileService.GetUserProfileListCountAsync(companyUser, groupId);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("userprofile/list/{groupId}/add")]
        public async Task<ActionResult<ResponseViewModel>> AddUserProfileToList(
            long groupId,
            [FromBody]
            UserProfileIdsViewModel userprofileViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var addedUsers = await _dbUserProfileService.AddUserProfileToUserProfileListAsync(
                    companyUser,
                    groupId,
                    userprofileViewModel);

                return Ok(
                    new ResponseViewModel()
                    {
                        message = $"{addedUsers.Count} Users have added to list."
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId} error adding contacts to list {ListId}. {ExceptionMessage}",
                    nameof(AddUserProfileToList),
                    companyUser.CompanyId,
                    groupId,
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("userprofile/list/{groupId}/add/background")]
        public async Task<ActionResult<BackgroundTaskViewModel>> AddUserProfileToListBackground(
            long groupId,
            [FromBody]
            UserProfileIdsViewModel userprofileViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var createdTask = await _backgroundTaskService.EnqueueAddContactsToListTask(
                    companyUser.IdentityId,
                    companyUser.Id,
                    companyUser.CompanyId,
                    groupId,
                    userprofileViewModel);

                return Ok(createdTask.MapToResultViewModel());
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {Companyid} error enqueueing add contacts to list {ListId}. {ExceptionMessage}",
                    nameof(AddUserProfileToListBackground),
                    companyUser.CompanyId,
                    groupId,
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("userprofile/list/{groupId}/remove")]
        public async Task<ActionResult<ImportContactHistoryResponse>> RemoveUserProfileToList(
            long groupId,
            [FromBody]
            UserProfileIdsViewModel userprofileViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var response = await _dbUserProfileService.RemoveUserProfileFromUserProfileListAsync(
                    companyUser,
                    groupId,
                    userprofileViewModel);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId} error remove contacts from list {ListId}. {ExceptionMessage}",
                    nameof(RemoveUserProfileToList),
                    companyUser.CompanyId,
                    groupId,
                    ex.Message);
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "error"
                });
        }

        [HttpPost]
        [Route("userprofile/list/create")]
        public async Task<ActionResult<ImportContactHistoryResponse>> CreateUserProfileList(
            [FromBody]
            UserProfileIdsViewModel userprofileViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var response =
                    await _dbUserProfileService.CreateUserProfileListAsync(companyUser, userprofileViewModel);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId} creating contact list {ListName}. {ExceptionMessage}",
                    nameof(CreateUserProfileList),
                    companyUser.CompanyId,
                    userprofileViewModel.GroupListName,
                    ex.Message);
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "error"
                });
        }

        [HttpPost]
        [Route("UserProfile/export")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<IActionResult> ExportContacts([FromBody] UserProfileIdsViewModel userprofileViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser is null)
            {
                return Unauthorized();
            }

            if (!_rbacService.IsRbacEnabled() && !IsAllowedToAdmins(companyUser))
            {
                return Unauthorized();
            }

            try
            {
                var (exportResultCsv, totalCount) =
                    await _dbUserProfileService.ExportUserProfileToCsvAsync(companyUser, userprofileViewModel);

                _logger.LogInformation(
                    "[ExportContacts] Company {CompanyId} User {UserId} exported {TotalCount} contacts, ListIds: {ListIds}, UserProfileIds Count: {UserProfileIdsCount}",
                    companyUser.CompanyId,
                    companyUser.IdentityId,
                    totalCount,
                    userprofileViewModel.ListIds != null ? string.Join(",", userprofileViewModel.ListIds) : null,
                    userprofileViewModel.UserProfileIds?.Count ?? 0);

                return File(
                    Encoding.UTF8.GetBytes(exportResultCsv),
                    "text/csv",
                    $"SleekFlow Exported Contacts{DateTime.UtcNow.Date}.csv");
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId} error exporting contacts. {ExceptionMessage}",
                    nameof(ExportContacts),
                    companyUser.CompanyId,
                    ex.Message);
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "error"
                });
        }

        private bool IsAllowedToAdmins(Staff companyUser)
        {
            return companyUser.RoleType is StaffUserRole.Admin or StaffUserRole.SuperAdmin;
        }

        [HttpPost]
        [Route("UserProfile/export/background")]
        public async Task<ActionResult<BackgroundTaskViewModel>> ExportContactsInBackground(
            [FromBody]
            UserProfileIdsViewModel userprofileViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser is null)
            {
                return Unauthorized();
            }

            if (!_rbacService.IsRbacEnabled() && !IsAllowedToAdmins(companyUser))
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "[ExportContactsInBackground] Company {CompanyId} User {UserId} is exporting contacts, ListIds: {ListIds}, UserProfileIds Count: {UserProfileIdsCount}",
                companyUser.CompanyId,
                companyUser.IdentityId,
                userprofileViewModel.ListIds != null ? string.Join(",", userprofileViewModel.ListIds) : null,
                userprofileViewModel.UserProfileIds?.Count ?? 0);

            try
            {
                var backgroundTask = await _backgroundTaskService.EnqueueExportContactsListToCsvTask(
                    companyUser.IdentityId,
                    companyUser.CompanyId,
                    companyUser.Id,
                    userprofileViewModel);

                return Ok(backgroundTask.MapToResultViewModel());
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId} error enqueueing export contacts. {ExceptionMessage}",
                    nameof(ExportContacts),
                    companyUser.CompanyId,
                    ex.Message);
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "error"
                });
        }

        // /// <summary>
        // /// [Deprecated] Get imported history
        // /// </summary>
        // /// <param name="offset"></param>
        // /// <param name="limit"></param>
        // /// <returns></returns>
        // [HttpGet]
        // [Route("userprofile/import/history")]
        // public async Task<ActionResult<UserGroupResult>> GetImportHistory([FromQuery(Name = "offset")] int offset = 0, [FromQuery(Name = "limit")] int limit = 10)
        // {
        //     var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
        //
        //     if (companyUser == null) return Unauthorized();
        //
        //     var response = await _userProfileControllerService.GetUserProfileImportHistoryAsync(companyUser, offset, limit);
        //
        //     return Ok(response);
        // }
        [HttpGet]
        [Route("userprofile/import/spreadsheet")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<IActionResult> GetImportSpreadsheet()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            var sampleCsv = await _dbUserProfileService.GetUserProfileImportSpreadsheetSampleInCsvAsync(companyUser);

            return File(Encoding.UTF8.GetBytes(sampleCsv), "text/csv", "SleekFlow Example Imports - Contacts.csv");
        }

        [HttpGet]
        [Route("userprofile/import/excel")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<IActionResult> GetImportExcel()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            var workbook = await _dbUserProfileService.GetUserProfileImportSpreadsheetSampleInExcelAsync(companyUser);
            var memoryStream = new MemoryStream();
            workbook.Write(memoryStream);

            return File(
                memoryStream.ToArray(),
                ExcelHelper.ExcelMimeType,
                $"SleekFlow Example Imports - Contacts.xlsx");
        }

        /// <summary>
        /// Return sample CSV file for express import.
        /// </summary>
        /// <returns>CSV file.</returns>
        [HttpGet]
        [Route("userprofile/bulk-import/spreadsheet")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<IActionResult> GetBulkImportSpreadsheet()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            var sampleCsv = await _dbUserProfileService.GetUserProfileBulkImportSpreadsheetSampleInCsvAsync(companyUser.CompanyId);

            return File(Encoding.UTF8.GetBytes(sampleCsv), "text/csv", "SleekFlow Example Imports - Contacts.csv");
        }

        /// <summary>
        /// Return sample XLSX file for express import.
        /// </summary>
        /// <returns>XLSX file.</returns>
        [HttpGet]
        [Route("userprofile/bulk-import/excel")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<IActionResult> GetBulkImportExcel()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            var workbook = await _dbUserProfileService.GetUserProfileBulkImportSpreadsheetSampleInExcelAsync(companyUser.CompanyId);
            var memoryStream = new MemoryStream();
            workbook.Write(memoryStream);

            return File(
                memoryStream.ToArray(),
                ExcelHelper.ExcelMimeType,
                $"SleekFlow Example Imports - Contacts.xlsx");
        }

        [HttpPost]
        [Route("userprofile/import/spreadsheet")]
        public async Task<ActionResult<ResponseViewModel>> ImportFromSpreadsheet(
            [FromForm]
            ImportSpreadsheetViewModel importSpreadsheetViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            await _dbUserProfileService.ImportUserProfileAsync(companyUser, importSpreadsheetViewModel);

            return Ok(
                new ResponseViewModel
                {
                    message = "importing..."
                });
        }

        [HttpPost]
        [Route("userprofile/import/spreadsheet/background")]
        public async Task<ActionResult<BackgroundTaskViewModel>> ImportFromSpreadsheetInBackground(
            [FromForm]
            ImportSpreadsheetViewModel importSpreadsheetViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            foreach (var file in importSpreadsheetViewModel.files)
            {
                var checksum = SHA256Helper.ComputeFileChecksum(file);

                var importFileLock = await _lockService.AcquireLockAsync(
                    $"Importing_contact_{companyUser.CompanyId}_{checksum}",
                    TimeSpan.FromMinutes(5));

                if (importFileLock == null)
                    return BadRequest(new ResponseViewModel() { message = "Import in progress" });
            }

            var backgroundTask =
                await _dbUserProfileService.ImportUserProfileInBackgroundAsync(companyUser, importSpreadsheetViewModel);

            return Ok(backgroundTask.MapToResultViewModel());
        }

        [HttpPost]
        [Route("userprofile/import-into-list/spreadsheet/background")]
        public async Task<ActionResult<BackgroundTaskViewModel>> ImportIntoListFromSpreadsheetInBackground(
            [FromForm]
            ImportIntoListSpreadsheetViewModel importIntoListSpreadsheetViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            foreach (var file in importIntoListSpreadsheetViewModel.Files)
            {
                var checksum = SHA256Helper.ComputeFileChecksum(file);

                var importFileLock = await _lockService.AcquireLockAsync(
                    $"Importing_contact_{companyUser.CompanyId}_{checksum}",
                    TimeSpan.FromMinutes(5));

                if (importFileLock == null)
                    return BadRequest(new ResponseViewModel() { message = "Import in progress" });
            }

            var backgroundTask = await _dbUserProfileService.ImportUserProfileToListInBackgroundAsync(
                companyUser,
                importIntoListSpreadsheetViewModel);

            return Ok(backgroundTask.MapToResultViewModel());
        }

        /// <summary>
        /// Bulk import contacts from spreadsheet in background, also known as express import.
        /// </summary>
        /// <param name="importSpreadsheetViewModel"><see cref="ImportSpreadsheetViewModel"/>.</param>
        /// <returns><see cref="BackgroundTaskViewModel"/>.</returns>
        [HttpPost]
        [Route("userprofile/bulk-import/spreadsheet/background")]
        public async Task<ActionResult<BackgroundTaskViewModel>> BulkImportFromSpreadsheetInBackground(
            [FromForm] ImportSpreadsheetViewModel importSpreadsheetViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            foreach (var file in importSpreadsheetViewModel.files)
            {
                var checksum = SHA256Helper.ComputeFileChecksum(file);

                var importFileLock = await _lockService.AcquireLockAsync(
                    $"Importing_contact_{companyUser.CompanyId}_{checksum}",
                    TimeSpan.FromMinutes(5));

                if (importFileLock == null)
                    return BadRequest(new ResponseViewModel() { message = "Import in progress" });
            }

            var backgroundTask =
                await _dbUserProfileService.BulkImportUserProfileInBackgroundAsync(companyUser, importSpreadsheetViewModel);

            return Ok(backgroundTask.MapToResultViewModel());
        }

        /// <summary>
        /// Bulk import contacts from spreadsheet into existing list in background, also known as express import.
        /// </summary>
        /// <param name="importIntoListSpreadsheetViewModel"><see cref="ImportIntoListSpreadsheetViewModel"/>.</param>
        /// <returns><see cref="BackgroundTaskViewModel"/>.</returns>
        [HttpPost]
        [Route("userprofile/bulk-import-into-list/spreadsheet/background")]
        public async Task<ActionResult<BackgroundTaskViewModel>> BulkImportIntoListFromSpreadsheetInBackground(
            [FromForm]
            ImportIntoListSpreadsheetViewModel importIntoListSpreadsheetViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            foreach (var file in importIntoListSpreadsheetViewModel.Files)
            {
                var checksum = SHA256Helper.ComputeFileChecksum(file);

                var importFileLock = await _lockService.AcquireLockAsync(
                    $"Importing_contact_{companyUser.CompanyId}_{checksum}",
                    TimeSpan.FromMinutes(5));

                if (importFileLock == null)
                    return BadRequest(new ResponseViewModel() { message = "Import in progress" });
            }

            var backgroundTask = await _dbUserProfileService.BulkImportUserProfileToListInBackgroundAsync(
                companyUser,
                importIntoListSpreadsheetViewModel);

            return Ok(backgroundTask.MapToResultViewModel());
        }

        [HttpPost]
        [Route("userprofile/import/validate")]
        public async Task<ActionResult<ImportSpreadSheetValidationResult>> ValidateImportSpreadsheet(
            [FromForm]
            ImportSpreadsheetViewModel importSpreadsheetViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (string.IsNullOrEmpty(companyUser.CompanyId))
            {
                return BadRequest();
            }

            try
            {
                return Ok(
                    await _dbUserProfileService.ValidateImportSpreadsheetAsync(
                        companyUser,
                        importSpreadsheetViewModel));
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("userprofile/import/preview")]
        public async Task<ActionResult<ImportSpreadsheet>> PreviewImportSpreadsheet(
            [FromForm]
            ImportSpreadsheetViewModel importSpreadsheetViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (string.IsNullOrEmpty(companyUser.CompanyId))
            {
                return BadRequest();
            }

            try
            {
                var importSpreadsheet = await _dbUserProfileService.PreviewImportSpreadsheet(
                    companyUser,
                    importSpreadsheetViewModel);

                return Ok(importSpreadsheet);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("userprofile/channel/switch")]
        public async Task<ActionResult<UserProfileNoCompanyResponse>> SwitchUserProfileMessagingChannel(
            [FromBody]
            SwitchMessagingChannelRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (string.IsNullOrEmpty(companyUser.CompanyId))
            {
                return BadRequest();
            }

            try
            {
                var response = await _dbUserProfileService.SwitchMessagingChannelAsync(
                    companyUser,
                    request);

                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpPost]
        [Route("userprofile/chatapi/instance/{userProfileId}")]
        public async Task<ActionResult<UserProfileNoCompanyResponse>> ChangeChatApiInstance(
            string userProfileId,
            [FromBody]
            ChangeChatAPIInstance chatAPIInstance)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (string.IsNullOrEmpty(companyUser.CompanyId))
            {
                return BadRequest();
            }

            try
            {
                var response = await _dbUserProfileService.SwitchWhatsAppTwilioChannel(
                    companyUser,
                    userProfileId,
                    chatAPIInstance);

                return Ok(response);
            }
            catch (FormatException fex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = fex.Message
                    });
            }
        }

        [HttpPost]
        [Route("userprofile/whatsapp360dialog/switch-channel")]
        public async Task<ActionResult<UserProfileNoCompanyResponse>> SwitchChannelChannel(
            [FromBody]
            SwitchChannelChannelRequest request)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return BadRequest();
            }

            try
            {
                var response = await _dbUserProfileService.SwitchWhatsapp360DialogChannelAsync(companyUser, request);

                return Ok(response);
            }
            catch (FormatException fex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = fex.Message
                    });
            }
        }

        [HttpPost]
        [Route("userprofile/whatsapp/cloudapi/switch-channel")]
        public async Task<ActionResult<UserProfileNoCompanyResponse>> SwitchCloudApiChannelChannel(
            [FromBody]
            SwitchWhatsappCloudApiChannelRequest request)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return BadRequest();
            }

            try
            {
                var response = await _dbUserProfileService.SwitchWhatsappCloudApiChannelAsync(companyUser, request);

                return Ok(response);
            }
            catch (FormatException fex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = fex.Message
                    });
            }
        }

        [Obsolete("Use AuditHub instead")]
        [HttpGet]
        [Route("userprofile/activity/{userProfileId}")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<List<RemarkResponse>>> GetRemarks(
            string userProfileId,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var response = await _dbUserProfileService.GetUserProfileAuditLogsAsync(
                    companyUser,
                    userProfileId,
                    offset,
                    limit);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId} error retrieving activity logs for {UserProfileId}. {ExceptionMessage}",
                    nameof(GetRemarks),
                    companyUser.CompanyId,
                    userProfileId,
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [Obsolete("Use AuditHub instead")]
        [HttpPost]
        [Route("userprofile/activity/{userProfileId}")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<RemarkResponse>> AddRemarks(
            string userProfileId,
            [FromBody]
            RemarkViewModel conversationRemarkViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var responseVm = await _dbUserProfileService.AddUserProfileActivityAsync(
                    companyUser,
                    userProfileId,
                    conversationRemarkViewModel);
                return Ok(responseVm);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        [HttpGet]
        [Route("userprofile/webclient/info/{webClientUuid}")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<WebClientInfoResponse>> GetWebClientTrackingInfo(
            string webClientUuid,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var response = await _dbUserProfileService.GetWebClientTrackingInfoAsync(companyUser, webClientUuid);

            return Ok(response);
        }

        [HttpGet]
        [Route("userprofile/shopify/order/{userProfileId}")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<ActionResult<ShopifyOrderResponse>> GetShopifyOrders(
            string userProfileId,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 10)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var response = await _dbUserProfileService.GetShopifyOrderByUserProfileIdAsync(
                companyUser,
                userProfileId,
                offset,
                limit);
            return Ok(response);
        }

        [HttpGet]
        [Route("userprofile/shopify/abandoned/{userProfileId}")]
        [ServiceFilter(typeof(ReadOnlyEndpointAttribute))]
        public async Task<IActionResult> GetShopifyAbandonedCart(string userProfileId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            try
            {
                var shopifyOrders =
                    await _dbUserProfileService.GetShopifyAbandonedCartByUserProfileIdAsync(companyUser, userProfileId);

                return Ok(shopifyOrders);
            }
            catch (Exception ex)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = ex.Message
                    });
            }
        }

        #region private

        private enum MessageChannel
        {
            native = 0,
            whatsapp,
            facebook,
            email,
            wechat,
            webclient,
            sms
        }

        #endregion
    }
}