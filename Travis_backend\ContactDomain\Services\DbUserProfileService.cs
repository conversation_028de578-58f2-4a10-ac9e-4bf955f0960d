using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Hangfire;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Travis_backend.BackgroundTaskServices;
using Travis_backend.Cache;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Constants;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Repositories;
using Travis_backend.ContactDomain.Services.Cache;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.ConversationAccessControl;
using Travis_backend.ConversationDomain.ConversationInboxFilterConstants;
using Travis_backend.ConversationDomain.ConversationQueryables;
using Travis_backend.ConversationDomain.ConversationSpecifications;
using Travis_backend.ConversationDomain.Extensions;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationDomain.Services;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels.Mappers;
using Travis_backend.ConversationServices;
using Travis_backend.Database.Services;
using Travis_backend.Enums;
using Travis_backend.Exceptions;
using Travis_backend.Extensions;
using Travis_backend.FlowHubs;
using Travis_backend.Helpers;
using Travis_backend.IntegrationServices.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.Models.BackgroundTask;
using Travis_backend.SignalR;
using Travis_backend.TenantHubDomain.Services;

namespace Travis_backend.ContactDomain.Services;

public interface IDbUserProfileService
{
    Task<SelectAllContactsResponse> GetUserProfileCountAsync(
        List<Condition> conditions,
        Staff companyUser,
        string status = "all",
        string channels = null,
        string channelIds = null);

    Task<SearchUserContactsResponse> GetUserProfileAsync(
        Staff companyUser,
        int offset,
        int limit,
        string sortBy,
        string order);

    Task<List<UserProfileNoCompanyResponse>> AddUserProfileAsync(
        Staff companyUser,
        List<NewProfileViewModel> newProfileViewModels);

    Task<UserProfileNoCompanyResponse> UpdateUserProfileAsync(
        Staff companyUser,
        string userProfileId,
        NewProfileViewModel profileUpdateViewModel);

    Task<SearchUserContactsResponse> SearchUserProfileAsync(
        Staff companyUser,
        List<Condition> conditions,
        int offset,
        int limit,
        string fields,
        string channels,
        string channelIds,
        string sortBy,
        string order);

    Task<SearchUserConversationResponse> SearchUserProfileWithConversationAsync(
        Staff companyUser,
        List<Condition> conditions,
        int offset,
        int limit,
        string status,
        string channels,
        string channelIds,
        string assignedTo,
        long? teamId,
        string version = "1",
        CancellationToken cancellationToken = default);

    Task<SelectAllContactsResponse> GetAllUserProfileIdAsync(
        Staff companyUser,
        List<Condition> conditions,
        string status,
        string channels,
        string channelIds);

    Task<UserProfileNoCompanyResponse> UpdateUserProfileDescriptionAsync(
        Staff companyUser,
        string userProfileId,
        UserProfileDescriptionModel userProfileDescriptionModel);

    Task<ConversationNoCompanyResponseViewModel> GetConversationByUserProfileIdAsync(
        Staff companyUser,
        string userProfileId);

    Task<UserProfileNoCompanyResponse> GetUserProfileByIdAsync(Staff companyUser, string userProfileId);

    Task<List<UserProfileCustomFieldNoOptionsViewModel>> GetUserProfileCustomFieldAsync(
        Staff companyUser,
        string userProfileId);

    Task<UserProfileNoCompanyResponse> UpdateUserProfileCustomFieldAsync(
        Staff companyUser,
        string userProfileId,
        List<AddCustomFieldsViewModel> addCustomFieldsViewModels);

    Task<bool> DeleteUserProfileAsync(Staff companyUser, UserProfileIdsViewModel userprofileViewModel);

    Task<List<UserProfileNoCompanyResponse>> BulkUpdateUserProfileCustomFieldAsync(
        Staff companyUser,
        BulkUpdateCustomFieldsViewModel bulkUpdateCustomFieldsViewModel);

    Task<UserGroupResult> GetUserProfileListAsync(Staff companyUser, string name, int offset, int limit);

    Task<UserGroupBriefResult> GetUserProfileListBriefResultAsync(Staff companyUser, string name = null);

    Task DeleteUserProfileListAsync(Staff companyUser, long staffId, GroupListViewModel groupListViewModel);

    Task BookmarkUserProfileListAsync(Staff companyUser, List<BookmarkViewModel> listReorderViewModels);

    Task ReorderUserProfileListAsync(Staff companyUser, List<ListReorderViewModel> listReorderViewModels);

    Task<List<ImportedUserProfile>> AddUserProfileToUserProfileListAsync(
        Staff companyUser,
        long groupId,
        UserProfileIdsViewModel userprofileViewModel);

    Task<ImportContactHistoryResponse> RemoveUserProfileFromUserProfileListAsync(
        Staff companyUser,
        long groupId,
        UserProfileIdsViewModel userprofileViewModel);

    Task<ImportContactHistoryResponse> CreateUserProfileListAsync(
        Staff companyUser,
        UserProfileIdsViewModel userprofileViewModel);

    Task<(string Csv, int TotalCount)> ExportUserProfileToCsvAsync(Staff companyUser, UserProfileIdsViewModel userprofileViewModel);

    Task<ImportContactHistoryResponse> GetUserProfileListCountAsync(Staff companyUser, long groupId);

    Task<string> GetUserProfileImportSpreadsheetSampleInCsvAsync(Staff companyUser);

    Task<IWorkbook> GetUserProfileImportSpreadsheetSampleInExcelAsync(Staff companyUser);

    /// <summary>
    /// Return the sample CSV file as string for express import.
    /// </summary>
    /// <param name="companyId">SleekFlow Company ID.</param>
    /// <returns>Sample CSV file as string.</returns>
    Task<string> GetUserProfileBulkImportSpreadsheetSampleInCsvAsync(string companyId);

    /// <summary>
    /// Return the sample XLSX file in proprietary Excel workbook format for express import.
    /// </summary>
    /// <param name="companyId">SleekFlow Company ID.</param>
    /// <returns>Sample XLSX file as workbook object.</returns>
    Task<IWorkbook> GetUserProfileBulkImportSpreadsheetSampleInExcelAsync(string companyId);

    Task ImportUserProfileAsync(Staff companyUser, ImportSpreadsheetViewModel importSpreadsheetViewModel);

    Task<BackgroundTask> ImportUserProfileInBackgroundAsync(
        Staff companyUser,
        ImportSpreadsheetViewModel importSpreadsheetViewModel);

    Task<BackgroundTask> ImportUserProfileToListInBackgroundAsync(
        Staff companyUser,
        ImportIntoListSpreadsheetViewModel importIntoListSpreadsheetViewModel);

    Task<BackgroundTask> BulkImportUserProfileInBackgroundAsync(
        Staff companyUser,
        ImportSpreadsheetViewModel importSpreadsheetViewModel);

    Task<BackgroundTask> BulkImportUserProfileToListInBackgroundAsync(
        Staff companyUser,
        ImportIntoListSpreadsheetViewModel importIntoListSpreadsheetViewModel);

    Task<ImportSpreadSheetValidationResult> ValidateImportSpreadsheetAsync(
        Staff companyUser,
        ImportSpreadsheetViewModel importSpreadsheetViewModel);

    Task<ImportSpreadsheet> PreviewImportSpreadsheet(
        Staff companyUser,
        ImportSpreadsheetViewModel importSpreadsheetViewModel);

    Task<UserProfileNoCompanyResponse> SwitchMessagingChannelAsync(
        Staff companyUser,
        SwitchMessagingChannelRequest request);

    Task<UserProfileNoCompanyResponse> SwitchWhatsAppTwilioChannel(
        Staff companyUser,
        string userProfileId,
        ChangeChatAPIInstance chatAPIInstance);

    Task<UserProfileNoCompanyResponse> SwitchWhatsapp360DialogChannelAsync(
        Staff companyUser,
        SwitchChannelChannelRequest request);

    Task<UserProfileNoCompanyResponse> SwitchWhatsappCloudApiChannelAsync(
        Staff companyUser,
        SwitchWhatsappCloudApiChannelRequest request);

    Task<List<RemarkResponse>> GetUserProfileAuditLogsAsync(
        Staff companyUser,
        string userProfileId,
        int offset,
        int limit);

    Task<RemarkResponse> AddUserProfileActivityAsync(
        Staff companyUser,
        string userProfileId,
        RemarkViewModel conversationRemarkViewModel);

    Task<WebClientInfoResponse> GetWebClientTrackingInfoAsync(Staff companyUser, string webClientUuid);

    Task<ShopifyOrderResponse> GetShopifyOrderByUserProfileIdAsync(
        Staff companyUser,
        string userProfileId,
        int offset,
        int limit);

    Task<ShopifyAbandonedCart> GetShopifyAbandonedCartByUserProfileIdAsync(Staff companyUser, string userProfileId);
}

public class DbUserProfileService : IDbUserProfileService
{
    private readonly IDbContextService _dbContextService;
    private readonly IMapper _mapper;
    private readonly IConfiguration _configuration;
    private readonly ILogger _logger;
    private readonly ILockService _lockService;
    private readonly ICacheManagerService _cacheManagerService;
    private readonly IContactCacheService _contactCacheService;
    private readonly IUserProfileService _userProfileService;
    private readonly IUserProfileSqlService _userProfileSqlService;
    private readonly ISignalRService _signalRService;
    private readonly IBackgroundTaskService _backgroundTaskService;
    private readonly IAuditHubAuditLogService _auditHubAuditLogService;
    private readonly IConversationService _conversationService;
    private readonly IImportSpreadsheetService _importSpreadsheetService;
    private readonly IUserProfileDuplicationConfigRepository _userProfileDuplicationConfigRepository;
    private readonly IConversationAssigneeService _conversationAssigneeService;
    private readonly IUserProfileListHooks _userProfileListHooks;
    private readonly IAccessControlAggregationService _accessControlAggregationService;
    private readonly IRbacConversationPermissionManager _rbacConversationPermissionManager;
    private readonly IServiceProvider _serviceProvider;
    private readonly IContactImportUsageService _contactImportUsageService;
    private readonly IMentionQueryableResolver _mentionQueryableResolver;
    private readonly IConversationNoCompanyResponseViewModelMapper _conversationNoCompanyResponseViewModelMapper;

    public DbUserProfileService(
        IDbContextService dbContextService,
        IMapper mapper,
        IConfiguration configuration,
        ILogger<DbUserProfileService> logger,
        ILockService lockService,
        ICacheManagerService cacheManagerService,
        IContactCacheService contactCacheService,
        IUserProfileService userProfileService,
        IUserProfileSqlService userProfileSqlService,
        ISignalRService signalRService,
        IBackgroundTaskService backgroundTaskService,
        ICompanyUsageService companyUsageService,
        IAuditHubAuditLogService auditHubAuditLogService,
        IConversationService conversationService,
        IImportSpreadsheetService importSpreadsheetService,
        IUserProfileDuplicationConfigRepository userProfileDuplicationConfigRepository,
        IConversationAssigneeService conversationAssigneeService,
        IUserProfileListHooks userProfileListHooks,
        IAccessControlAggregationService accessControlAggregationService,
        IServiceProvider serviceProvider,
        IRbacConversationPermissionManager rbacConversationPermissionManager,
        IContactImportUsageService contactImportUsageService,
        IMentionQueryableResolver mentionQueryableResolver,
        IConversationNoCompanyResponseViewModelMapper conversationNoCompanyResponseViewModelMapper)
    {
        _dbContextService = dbContextService;
        _mapper = mapper;
        _configuration = configuration;
        _logger = logger;
        _lockService = lockService;
        _cacheManagerService = cacheManagerService;
        _contactCacheService = contactCacheService;
        _userProfileService = userProfileService;
        _userProfileSqlService = userProfileSqlService;
        _signalRService = signalRService;
        _backgroundTaskService = backgroundTaskService;
        _auditHubAuditLogService = auditHubAuditLogService;
        _conversationService = conversationService;
        _importSpreadsheetService = importSpreadsheetService;
        _userProfileDuplicationConfigRepository = userProfileDuplicationConfigRepository;
        _conversationAssigneeService = conversationAssigneeService;
        _userProfileListHooks = userProfileListHooks;
        _accessControlAggregationService = accessControlAggregationService;
        _rbacConversationPermissionManager = rbacConversationPermissionManager;
        _serviceProvider = serviceProvider;
        _contactImportUsageService = contactImportUsageService;
        _mentionQueryableResolver = mentionQueryableResolver;
        _conversationNoCompanyResponseViewModelMapper = conversationNoCompanyResponseViewModelMapper;
    }

    public async Task<SelectAllContactsResponse> GetUserProfileCountAsync(
        List<Condition> conditions,
        Staff companyUser,
        string status = "all",
        string channels = null,
        string channelIds = null)
    {
        var dbContext = _dbContextService.GetDbContext();

        var getUserProfileTotalCacheKeyPattern = new GetUserProfileTotalCacheKeyPattern(companyUser.CompanyId, companyUser.IdentityId,
            conditions, status, channels, channelIds);

        var data = await _cacheManagerService.GetCacheAsync(getUserProfileTotalCacheKeyPattern);

        if (!string.IsNullOrEmpty(data))
        {
            return JsonConvert.DeserializeObject<SelectAllContactsResponse>(data);
        }

        var channel = new List<string>();

        if (!string.IsNullOrEmpty(channels))
        {
            channel = channels.Split(",").ToList();
        }

        var channelsSet = new List<string>();

        if (!string.IsNullOrEmpty(channelIds))
        {
            channelsSet = channelIds.Split(",").ToList();
        }

        var isFromRawSql = _configuration.GetValue<bool>("SqlPerformance:FromRawSql");

        if (!isFromRawSql && conditions?.Count > 0)
        {
            var condLastChannel = conditions
                .FirstOrDefault(
                    x =>
                        !string.IsNullOrEmpty(x.FieldName)
                        && x.FieldName.ToLower() == "lastchannel"
                        && x.ConditionOperator == SupportedOperator.Contains);

            if (condLastChannel != null)
            {
                foreach (var value in condLastChannel.Values)
                {
                    var set = value.Split(":");

                    if (set.Count() > 1)
                    {
                        if (!channel.Contains(set[0]))
                        {
                            channel.Add(set[0]);
                        }

                        channelsSet.Add(value.Substring(value.IndexOf(":") + 1));
                    }
                }
            }
        }

        IQueryable<string> selectedUserProfileIdsQuery = null;

        switch (conditions?.Count)
        {
            case > 0 when conditions.Any(x => x.NextOperator == SupportedNextOperator.Or):
                {
                    var getUserProfilesIdsByFields = await _userProfileService.GetUserProfilesIdsByFields(
                        companyUser.CompanyId,
                        conditions,
                        null,
                        null,
                        "createdat",
                        "desc",
                        (companyUser.RoleType != StaffUserRole.Admin) ? (long?) companyUser.Id : null,
                        channel,
                        channelsSet,
                        companyUser.RoleType);

                    selectedUserProfileIdsQuery = dbContext.UserProfiles
                        .Where(x => getUserProfilesIdsByFields.UserProfileIds.Contains(x.Id))
                        .Select(x => x.Id);

                    if (status != "all")
                    {
                        var conversationsQuery = from conversation in dbContext.Conversations
                                                 where selectedUserProfileIdsQuery.Contains(conversation.UserProfileId)
                                                 orderby conversation.UpdatedTime descending
                                                 select conversation;

                        if (status != "all")
                        {
                            conversationsQuery = from conversation in conversationsQuery
                                                 where conversation.Status == status.ToLower()
                                                 orderby conversation.UpdatedTime descending
                                                 select conversation;
                        }

                        selectedUserProfileIdsQuery = conversationsQuery.Select(x => x.UserProfileId);
                    }

                    var selectAllUserProfileResponse = new SelectAllContactsResponse
                    {
                        TotalResult = await selectedUserProfileIdsQuery.CountAsync()
                    };

                    await _cacheManagerService.SaveCacheAsync(
                        getUserProfileTotalCacheKeyPattern,
                        selectAllUserProfileResponse,
                        TimeSpan.FromSeconds(10));

                    return selectAllUserProfileResponse;
                }
            case > 0:
                {
                    var userProfileCount = 0;
                    IQueryable<UserProfile> getUserProfilesIdsByFields;

                    if (isFromRawSql)
                    {
                        var (_, count, queryableUserProfile) = await _userProfileSqlService.GetUserProfilesAsync(
                            companyUser.CompanyId,
                            conditions,
                            null,
                            null,
                            "createdat",
                            "desc",
                            (companyUser.RoleType != StaffUserRole.Admin) ? (long?) companyUser.Id : null,
                            channel,
                            channelsSet,
                            companyUser.RoleType,
                            getUserProfileIdOnly: true);

                        userProfileCount = count;
                        getUserProfilesIdsByFields = queryableUserProfile;
                    }
                    else
                    {
                        getUserProfilesIdsByFields = await _userProfileService.GetIQueryableUserProfilesWithFilter(
                            companyId: companyUser.CompanyId,
                            conditions: conditions,
                            offset: null,
                            limit: null,
                            sortBy: "createdat",
                            order: "desc",
                            assigneeId: companyUser.RoleType != StaffUserRole.Admin ? companyUser.Id : null,
                            channels: channel,
                            channelIds: channelsSet,
                            staffUserRole: companyUser.RoleType);
                    }

                    if (status != "all")
                    {
                        getUserProfilesIdsByFields = dbContext.Conversations
                            .Join(
                                getUserProfilesIdsByFields,
                                conversation => conversation.UserProfileId,
                                profile => profile.Id,
                                (conversation, profile) => new
                                {
                                    conversation,
                                    profile
                                })
                            .Where(x => x.conversation.Status == status.ToLower())
                            .Select(x => x.profile);
                    }

                    if (status != "all" ||
                        !isFromRawSql)
                    {
                        userProfileCount = await getUserProfilesIdsByFields.CountAsync();
                    }

                    var selectAllUserProfileResponse = new SelectAllContactsResponse
                    {
                        TotalResult = userProfileCount
                    };

                    await _cacheManagerService.SaveCacheAsync(
                        getUserProfileTotalCacheKeyPattern,
                        selectAllUserProfileResponse,
                        TimeSpan.FromSeconds(10));

                    return selectAllUserProfileResponse;
                }
            default:
                {
                    var userProfilesQ = _userProfileService.GetManageableUserProfilesQueryable(companyUser);

                    var selectAllUserProfileResponse = new SelectAllContactsResponse
                    {
                        TotalResult = await userProfilesQ
                            .Select(x => x.Id)
                            .CountAsync()
                    };

                    await _cacheManagerService.SaveCacheAsync(
                        getUserProfileTotalCacheKeyPattern,
                        selectAllUserProfileResponse,
                        TimeSpan.FromSeconds(10));

                    return selectAllUserProfileResponse;
                }
        }
    }

    public async Task<SearchUserContactsResponse> GetUserProfileAsync(
        Staff companyUser,
        int offset,
        int limit,
        string sortBy,
        string order)
    {
        var dbContext = _dbContextService.GetDbContext();

        var getUserProfileCacheKeyPattern = new GetUserProfileCacheKeyPattern(companyUser.CompanyId, companyUser.IdentityId, offset, limit, sortBy, order);
        var data = await _cacheManagerService.GetCacheAsync(getUserProfileCacheKeyPattern);

        if (!string.IsNullOrEmpty(data))
        {
            return JsonConvert.DeserializeObject<SearchUserContactsResponse>(data);
        }

        companyUser.Company.RolePermission = await dbContext.CompanyRolePermissions
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .AsNoTracking()
            .ToListAsync();

        companyUser.Company.CustomUserProfileFields =
            await _contactCacheService.GetCompanyCustomUserProfileFieldsCachedAsync(companyUser.CompanyId);

        var response = new SearchUserContactsResponse();

        if (_configuration.GetValue<bool>("SqlPerformance:FromRawSql"))
        {
            _logger.LogInformation(
                "[DbUserProfileService GetUserProfileAsync] " +
                "[companyId: {CompanyId}]" +
                " retrieving user profiles with raw SQL",
                companyUser.CompanyId);

            var emptyCondition = new List<Condition>();

            var (userProfileSqlQuery, count, _) = await _userProfileSqlService.GetUserProfilesAsync(
                companyUser.CompanyId,
                emptyCondition,
                offset,
                limit,
                sortBy,
                order,
                companyUser.Id,
                channels: null,
                channelIds: null,
                companyUser.RoleType,
                enableNoTracking: true);

            response.TotalResult = count;
            response.UserProfiles = userProfileSqlQuery
                .Select(x => _mapper.Map<UserProfileNoCompanyResponse>(x))
                .ToList();
        }
        else
        {
            _logger.LogInformation(
                "[DbUserProfileService GetUserProfileAsync] " +
                "[companyId: {CompanyId}]" +
                " retrieving user profiles with LINQ",
                companyUser.CompanyId);

            companyUser.Company.CustomUserProfileFields =
                await _contactCacheService.GetCompanyCustomUserProfileFieldsCachedAsync(companyUser.CompanyId);

            companyUser.Company.RolePermission = await dbContext.CompanyRolePermissions
                .Where(x => x.CompanyId == companyUser.CompanyId)
                .AsNoTracking()
                .ToListAsync();

            var userProfilesQ = _userProfileService.GetManageableUserProfilesQueryable(companyUser);

            switch (sortBy.ToLower())
            {
                case "updatedat":
                    if (order == "desc")
                    {
                        userProfilesQ = userProfilesQ.OrderByDescending(x => x.UpdatedAt);
                    }
                    else
                    {
                        userProfilesQ = userProfilesQ.OrderBy(x => x.UpdatedAt);
                    }

                    break;
                case "firstname":
                    if (order == "desc")
                    {
                        userProfilesQ = userProfilesQ.OrderByDescending(x => x.FirstName);
                    }
                    else
                    {
                        userProfilesQ = userProfilesQ.OrderBy(x => x.FirstName);
                    }

                    break;
                case "lastname":
                    if (order == "desc")
                    {
                        userProfilesQ = userProfilesQ.OrderByDescending(x => x.LastName);
                    }
                    else
                    {
                        userProfilesQ = userProfilesQ.OrderBy(x => x.LastName);
                    }

                    break;
                case "displayname":
                    if (order == "desc")
                    {
                        userProfilesQ = userProfilesQ
                            .OrderByDescending(x => x.FirstName)
                            .ThenByDescending(x => x.LastName);
                    }
                    else
                    {
                        userProfilesQ = userProfilesQ
                            .OrderBy(x => x.FirstName)
                            .ThenBy(x => x.LastName);
                    }

                    break;
                case "createdat":
                    if (order == "desc")
                    {
                        userProfilesQ = userProfilesQ.OrderByDescending(x => x.CreatedAt);
                    }
                    else
                    {
                        userProfilesQ = userProfilesQ.OrderBy(x => x.CreatedAt);
                    }

                    break;
                case "lastcontactfromcustomers":
                    if (order == "desc")
                    {
                        userProfilesQ = userProfilesQ
                            .Where(x => x.LastContactFromCustomers.HasValue)
                            .OrderByDescending(x => x.LastContactFromCustomers);
                    }
                    else
                    {
                        userProfilesQ = userProfilesQ
                            .Where(x => x.LastContactFromCustomers.HasValue)
                            .OrderBy(x => x.LastContactFromCustomers);
                    }

                    break;
                case "lastcontact":
                    if (order == "desc")
                    {
                        userProfilesQ = userProfilesQ
                            .Where(x => x.LastContact.HasValue)
                            .OrderByDescending(x => x.LastContact);
                    }
                    else
                    {
                        userProfilesQ = userProfilesQ
                            .Where(x => x.LastContact.HasValue)
                            .OrderBy(x => x.LastContact);
                    }

                    break;
            }

            var userProfileResult = await userProfilesQ
                .Include(x => x.CustomFields)
                .Include(x => x.FacebookAccount)
                .Include(x => x.WhatsAppAccount)
                .Include(x => x.EmailAddress)
                .Include(x => x.RegisteredUser.Identity)
                .Include(x => x.UserDevice)
                .Include(x => x.WebClient)
                .Include(x => x.WeChatUser)
                .Include(X => X.LineUser)
                .Include(x => x.ViberUser)
                .Include(x => x.InstagramUser)
                .Include(x => x.TelegramUser)
                .Include(x => x.TikTokUser)
                .Include(x => x.WhatsApp360DialogUser)
                .Include(x => x.WhatsappCloudApiUser)
                .Include(x => x.SMSUser)
                .Skip(offset)
                .Take(limit)
                .AsSplitQuery()
                .AsNoTracking()
                .ProjectTo<UserProfileNoCompanyResponse>(_mapper.ConfigurationProvider)
                .ToListAsync();

            response.TotalResult = await userProfilesQ.CountAsync();
            response.UserProfiles = userProfileResult;
        }

        var isCompanyShowCollaborators = companyUser.Company?.CompanySetting?.IsShowCollaboratorsOn;

        var collaborators = new List<UserProfileContactCollaboratorViewModel>();
        if (isCompanyShowCollaborators != null && (bool) isCompanyShowCollaborators)
        {
            var userProfileIds = response.UserProfiles
                .Select(x => x.Id)
                .ToList();

            collaborators = await _userProfileService.GetContactCollaboratorsByUserProfileIds(
                companyUser.CompanyId,
                userProfileIds);
        }

        foreach (var userProfile in response.UserProfiles)
        {
            if (isCompanyShowCollaborators != null && (bool) isCompanyShowCollaborators)
            {
                userProfile.Collaborators = collaborators
                    .FirstOrDefault(x => x.UserProfileId == userProfile.Id)?
                    .AdditionalAssignees;
            }

            if (companyUser.Company.RolePermission?.Count > 0)
            {
                var permission = companyUser.Company
                    .RolePermission
                    .FirstOrDefault(x => x.StaffUserRole == companyUser.RoleType);

                if (permission != null)
                {
                    ExecuteCompanyRolePermissions(permission, companyUser, userProfile);
                }
            }

            var conversationId = await dbContext.Conversations
                .Where(conversation => conversation.UserProfileId == userProfile.Id)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();

            if (conversationId != null)
            {
                userProfile.ConversationHashtags = (await dbContext.ConversationHashtags
                        .Include(x => x.Hashtag)
                        .Where(x => x.ConversationId == conversationId)
                        .ProjectTo<ConversationHashtagResponse>(_mapper.ConfigurationProvider)
                        .AsNoTracking()
                        .ToListAsync())
                    .DistinctBy(x => x.Id)
                    .ToList();

                userProfile.ConversationId = conversationId;
            }

            // add list
            var lists = await dbContext.CompanyImportContactHistories
                .Where(x => x.ImportedUserProfiles.Any(y => y.UserProfileId == userProfile.Id))
                .OrderByDescending(x => x.IsBookmarked)
                .ThenBy(x => x.Order)
                .ThenByDescending(x => x.CreatedAt)
                .Select(
                    x => new
                    {
                        x.Id,
                        x.ImportName
                    })
                .AsNoTracking()
                .ToListAsync();

            // Init ContactLists
            userProfile.ContactLists = [];

            foreach (var list in lists)
            {
                userProfile.ContactLists.Add(
                    new ContactJoinedList
                    {
                        Id = list.Id,
                        ListName = list.ImportName
                    });
            }
        }

        await _cacheManagerService.SaveCacheAsync(getUserProfileCacheKeyPattern, response);

        return response;
    }

    public async Task<List<UserProfileNoCompanyResponse>> AddUserProfileAsync(
        Staff companyUser,
        List<NewProfileViewModel> newProfileViewModels)
    {
        var dbContext = _dbContextService.GetDbContext();

        try
        {
            _logger.LogInformation(
                "Manually adding the new contact: {CompanyId} {IdentityId} {NewProfileViewModels}",
                companyUser.CompanyId,
                companyUser.IdentityId,
                JsonConvert.SerializeObject(newProfileViewModels));

            var createdUserProfiles = await _userProfileService.AddUserProfiles(
                companyUser.CompanyId,
                companyUser.IdentityId,
                newProfileViewModels,
                recoveryTriggerContext: new UserProfileRecoveryTriggerContext(
                    UpdateUserProfileTriggerSource.StaffManual,
                    companyUser));

            var userProfileResponse = _mapper.Map<List<UserProfileNoCompanyResponse>>(
                createdUserProfiles);

            return userProfileResponse;
        }
        catch (FormatException ex)
        {
            var message = ex.Message.Split(":");
            if (!Guid.TryParse(message[0], out var existingUserProfileId))
            {
                throw;
            }
            var duplicatedFieldName = message[1];

            var duplicationConfig = await _userProfileDuplicationConfigRepository.GetByCompanyIdAsync(companyUser.CompanyId);
            if (duplicationConfig is not null
                && duplicationConfig.DuplicationMode == DuplicationMode.AutomaticCollaboratorAddition)
            {
                var existingUserProfile = await _userProfileService.GetUserProfileByUserProfileId(
                    companyUser.CompanyId,
                    existingUserProfileId.ToString());

                var conversation = await _userProfileService.GetConversationByUserProfileId(
                    companyUser.CompanyId,
                    existingUserProfile.Id);

                await _conversationAssigneeService.AddAdditionalAssignees(
                        conversation,
                        new List<long> { companyUser.Id },
                        companyUser,
                        userProfileSource: UserProfileSource.ManualEntry);
            }

            var staffAccessControlAggregate = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(companyUser);

            var hasPermissionToViewProfile = companyUser.RoleType switch
            {
                StaffUserRole.Admin => true,
                StaffUserRole.Staff => await dbContext.UserProfiles
                    .AnyAsync(
                        u =>
                            u.Id.Equals(existingUserProfileId.ToString())
                            && u.CompanyId.Equals(companyUser.CompanyId)
                            && u.ActiveStatus.Equals(ActiveStatus.Active)
                            && (u.Conversation.AssigneeId.Equals(companyUser.Id) // Assigned to self
                                || u.Conversation.AdditionalAssignees.Any(
                                    a => a.AssigneeId.Equals(companyUser.Id) && a.CompanyId == companyUser.CompanyId) // Assigned to self as additional
                                || u.ContactOwnerId.Equals(companyUser.IdentityId) // Own the contact
                                || (u.Conversation.AssignedTeamId == null // Not yet assigned to any team/staff
                                    && u.Conversation.AssigneeId == null)
                                || (u.Conversation.AssignedTeam.Members.Any(
                                        x => x.StaffId == companyUser.Id) // Assigned to the self's team but not yet assigned to any team member
                                    && u.Conversation.AssigneeId == null))),
                StaffUserRole.TeamAdmin => await dbContext.UserProfiles
                    .AnyAsync(
                        u =>
                            u.Id.Equals(existingUserProfileId.ToString())
                            && u.CompanyId.Equals(companyUser.CompanyId)
                            && u.ActiveStatus.Equals(ActiveStatus.Active)
                            && (u.Conversation.AssigneeId.Equals(companyUser.Id) // Assigned to self
                                || u.Conversation.AdditionalAssignees.Any(
                                    a => a.AssigneeId.Equals(companyUser.Id) && a.CompanyId == companyUser.CompanyId) // Assigned to self as additional
                                || u.ContactOwnerId.Equals(companyUser.IdentityId) // Own the contact
                                || (u.Conversation.AssignedTeamId == null // Not yet assigned to any team/staff
                                    && u.Conversation.AssigneeId == null)
                                || u.Conversation.AssignedTeam.Members.Any(
                                    m => m.StaffId.Equals(companyUser.Id))// Assigned to self's team
                                || u.Conversation.AdditionalAssignees.Any(a => staffAccessControlAggregate.GetAssociatedTeamMemberIds().Contains((long) a.AssigneeId)) // Team admins should be able to search for the userprofile when their teammate are collaborator
                                )),
                _ => false
            };

            if (companyUser.CompanyId is "7dceb5fd-4baa-4c4c-bd66-00b11d7c20c7"
                or "98527647-4306-480f-bdf0-7fde93db7676")
            {
                hasPermissionToViewProfile = true;
            }

            var duplicationMode = duplicationConfig?.DuplicationMode.ToString()
                ?? DuplicationMode.StrictDuplicatePrevention.ToString();

            throw new DuplicatedContactException(
                existingUserProfileId.ToString(),
                hasPermissionToViewProfile,
                duplicatedFieldName,
                duplicationMode,
                ex);
        }
    }

    public async Task<UserProfileNoCompanyResponse> UpdateUserProfileAsync(
        Staff companyUser,
        string userProfileId,
        NewProfileViewModel profileUpdateViewModel)
    {
        var dbContext = _dbContextService.GetDbContext();

        var userProfile = await dbContext.UserProfiles
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyUser.CompanyId
                    && x.Id == userProfileId);

        profileUpdateViewModel.UserProfileFields ??= new();

        if (userProfile.FirstName != profileUpdateViewModel.FirstName
            && !profileUpdateViewModel.UserProfileFields
                .Exists(x => x.CustomFieldName == "FirstName"))
        {
            profileUpdateViewModel.UserProfileFields.Add(
                new("FirstName", profileUpdateViewModel.FirstName));
        }

        if (userProfile.LastName != profileUpdateViewModel.LastName
            && !profileUpdateViewModel.UserProfileFields
                .Exists(x => x.CustomFieldName == "LastName"))
        {
            profileUpdateViewModel.UserProfileFields.Add(
                new("LastName", profileUpdateViewModel.LastName));
        }

        if (profileUpdateViewModel.UserProfileFields is { Count: > 0 })
        {
            userProfile = await _userProfileService.UpdateUserProfileCustomFields(
                companyUser.CompanyId,
                userProfileId,
                companyUser.Id,
                profileUpdateViewModel.UserProfileFields,
                userProfileSource: UserProfileSource.ManualEntry);
        }
        else
        {
            await _signalRService.SignalROnUserProfileUpdated(userProfile);
        }

        var responseVm = _mapper.Map<UserProfileNoCompanyResponse>(userProfile);

        // DEVS-12035 Adding labels to response
        var conversationId = await dbContext.Conversations.Where(x => x.UserProfileId == userProfile.Id).Select(x => x.Id)
            .FirstOrDefaultAsync();

        if (!string.IsNullOrEmpty(conversationId))
        {
            responseVm.ConversationHashtags = await dbContext.ConversationHashtags.Where(x => x.ConversationId == conversationId)
                .ProjectTo<ConversationHashtagResponse>(_mapper.ConfigurationProvider)
                .ToListAsync();
        }

        return responseVm;
    }

    public async Task<SearchUserContactsResponse> SearchUserProfileAsync(
        Staff companyUser,
        List<Condition> conditions,
        int offset,
        int limit,
        string fields,
        string channels,
        string channelIds,
        string sortBy,
        string order)
    {
        var dbContext = _dbContextService.GetDbContext();

        companyUser.Company.RolePermission = await dbContext.CompanyRolePermissions
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .AsNoTracking()
            .ToListAsync();

        companyUser.Company.CustomUserProfileFields = await dbContext.CompanyCustomUserProfileFields
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .AsNoTracking()
            .ToListAsync();

        var channelNameList = new List<string>();

        if (!string.IsNullOrEmpty(channels))
        {
            channelNameList = channels.Split(",").ToList();
        }

        var channelIdList = new List<string>();

        if (!string.IsNullOrEmpty(channelIds))
        {
            channelIdList = channelIds.Split(",").ToList();
        }

        var isFromRawSql = _configuration.GetValue<bool>("SqlPerformance:FromRawSql");

        if (!isFromRawSql && conditions?.Count > 0)
        {
            var condLastChannel = conditions.FirstOrDefault(
                x =>
                    !string.IsNullOrEmpty(x.FieldName)
                    && x.FieldName.ToLower() == "lastchannel"
                    && x.ConditionOperator == SupportedOperator.Contains);

            if (condLastChannel != null)
            {
                foreach (var value in condLastChannel.Values)
                {
                    var set = value.Split(":");

                    if (set.Count() > 1)
                    {
                        if (!channelNameList.Contains(set[0]))
                        {
                            channelNameList.Add(set[0]);
                        }

                        channelIdList.Add(value.Substring(value.IndexOf(":") + 1));
                    }
                }
            }
        }

        if (channelNameList.Count > 0)
        {
            conditions.Add(
                new Condition
                {
                    FieldName = "LastChannel",
                    ConditionOperator = SupportedOperator.Contains,
                    Values = channelNameList
                });
        }

        var userProfileResult = await _userProfileService.GetUserProfilesByFields(
            companyUser.CompanyId,
            conditions,
            offset,
            limit,
            sortBy,
            order,
            (companyUser.RoleType != StaffUserRole.Admin) ? (long?) companyUser.Id : null,
            channelNameList,
            channelIdList,
            companyUser.RoleType,
            true);

        _logger.LogInformation(
            "[DbUserProfileService SearchUserProfileAsync] " +
            "[companyId: {CompanyId}]" +
            " processing searched user profiles response",
            companyUser.CompanyId);

        var selectedUserProfileResponse = new SearchUserContactsResponse
        {
            UserProfiles = _mapper.Map<List<UserProfileNoCompanyResponse>>(userProfileResult.UserProfiles),
            TotalResult = userProfileResult.TotalResult
        };

        var isCompanyShowCollaborators = companyUser.Company?.CompanySetting?.IsShowCollaboratorsOn;

        var collaborators = new List<UserProfileContactCollaboratorViewModel>();

        var userProfileIds = userProfileResult.UserProfiles
            .Select(x => x.Id)
            .ToList();

        if (isCompanyShowCollaborators != null
            && (bool) isCompanyShowCollaborators)
        {
            collaborators = await _userProfileService.GetContactCollaboratorsByUserProfileIds(
                companyUser.CompanyId,
                userProfileIds);
        }

        // Get conversation id for each user profile
        var userProfileConversationMap = await dbContext.Conversations
            .Where(
                conversation => conversation.CompanyId == companyUser.CompanyId &&
                                userProfileIds.Contains(conversation.UserProfileId))
            .Select(
                x => new
                {
                    ConversationId = x.Id,
                    x.UserProfileId
                })
            .ToListAsync();

        // Get conversation hashtags for each conversation
        var groupedHashtags = await dbContext.ConversationHashtags
            .Include(x => x.Hashtag)
            .Where(x => userProfileConversationMap.Select(uc => uc.ConversationId).Contains(x.ConversationId))
            .GroupBy(x => x.ConversationId)
            .Select(
                g => new
                {
                    ConversationId = g.Key,
                    Hashtags = g
                        .OrderByDescending(hashtag => hashtag.Id)
                        .ToList()
                })
            .ToListAsync();

        // Get contact lists for each user profile
        var groupedUserProfileList = await dbContext.CompanyImportContactHistories
            .Join(
                dbContext.CompanyImportedUserProfiles,
                listRecord => listRecord.Id,
                listContactMap => listContactMap.ImportContactHistoryId,
                (listRecord, listContactMap) => new
                {
                    ListId = listRecord.Id,
                    ListName = listRecord.ImportName,
                    UserProfileId = listContactMap.UserProfileId,
                    listRecord.IsBookmarked,
                    listRecord.Order,
                    listRecord.CreatedAt
                })
            .Where(x => x.UserProfileId != null && userProfileIds.Contains(x.UserProfileId))
            .GroupBy(x => x.UserProfileId)
            .Select(g => new
            {
                UserProfileId = g.Key,
                Lists = g.Select(x => new
                {
                    Id = x.ListId,
                    ListName = x.ListName,
                    x.IsBookmarked,
                    x.Order,
                    x.CreatedAt
                }).ToList()
            })
            .ToListAsync();

        foreach (var userProfile in selectedUserProfileResponse.UserProfiles)
        {
            if (isCompanyShowCollaborators != null
                && (bool) isCompanyShowCollaborators)
            {
                userProfile.Collaborators = collaborators
                    .FirstOrDefault(x => x.UserProfileId == userProfile.Id)?
                    .AdditionalAssignees;
            }

            if (companyUser.Company.RolePermission?.Count > 0)
            {
                var permission = companyUser.Company
                    .RolePermission
                    .FirstOrDefault(x => x.StaffUserRole == companyUser.RoleType);

                if (permission != null)
                {
                    ExecuteCompanyRolePermissions(permission, companyUser, userProfile);
                }
            }

            // Get conversation id from the map
            userProfile.ConversationId = userProfileConversationMap
                .FirstOrDefault(x => x.UserProfileId == userProfile.Id)?.ConversationId;

            // Get conversation hashtags from the map
            var conversationHashTags = groupedHashtags
                .FirstOrDefault(x => x.ConversationId == userProfile.ConversationId)?
                .Hashtags;
            userProfile.ConversationHashtags = _mapper.Map<List<ConversationHashtagResponse>>(conversationHashTags) ?? new List<ConversationHashtagResponse>();

            // Get contact lists from the map
            userProfile.ContactLists = groupedUserProfileList.FirstOrDefault(x => x.UserProfileId == userProfile.Id)
                ?.Lists
                .OrderByDescending(x => x.IsBookmarked)
                .ThenBy(x => x.Order)
                .ThenByDescending(x => x.CreatedAt)
                .DistinctBy(x => x.Id)
                .Select(
                    x => new ContactJoinedList
                    {
                        Id = x.Id,
                        ListName = x.ListName
                    }).ToList() ?? new List<ContactJoinedList>();
        }

        if (!string.IsNullOrEmpty(fields))
        {
            var split = fields.Split(',');

            if (split.Contains("lastmessage"))
            {
                foreach (var userProfile in selectedUserProfileResponse.UserProfiles)
                {
                    // It seems unused maybe it should be removed
                    var lastMessage = await dbContext.ConversationMessages
                        .Where(y => y.ConversationId == userProfile.ConversationId)
                        .OrderByDescending(y => y.CreatedAt)
                        .Include(y => y.UploadedFiles)
                        .Include(y => y.MessageAssignee.Identity)
                        .Include(y => y.EmailFrom)
                        .Include(y => y.Sender)
                        .Include(y => y.Receiver)
                        .Include(y => y.SenderDevice)
                        .Include(y => y.ReceiverDevice)
                        .Include(y => y.facebookSender)
                        .Include(y => y.facebookReceiver)
                        .Include(y => y.whatsappSender)
                        .Include(y => y.whatsappReceiver)
                        .Include(y => y.WebClientSender)
                        .Include(y => y.WebClientReceiver)
                        .Include(y => y.MessageAssignee.Identity)
                        .Include(y => y.WeChatSender)
                        .Include(y => y.WeChatReceiver)
                        .Include(y => y.LineSender)
                        .Include(y => y.LineReceiver)
                        .Include(x => x.SMSSender)
                        .Include(x => x.SMSReceiver)
                        .Take(1)
                        .ProjectTo<ConversationMessageResponseViewModel>(_mapper.ConfigurationProvider)
                        .ToListAsync();

                    userProfile.LastMessage = lastMessage;
                }
            }
        }

        return selectedUserProfileResponse;
    }

    public async Task<SearchUserConversationResponse> SearchUserProfileWithConversationAsync(
        Staff companyUser,
        List<Condition> conditions,
        int offset,
        int limit,
        string status,
        string channels,
        string channelIds,
        string assignedTo,
        long? teamId,
        string version = "1",
        CancellationToken cancellationToken = default)
    {
        var rbacService = _serviceProvider.GetRequiredService<IRbacService>();

        var isRbacEnabled = rbacService.IsRbacEnabled();

        if (isRbacEnabled)
        {
            return await RbacSearchUserProfileWithConversationAsync(
                companyUser,
                conditions,
                offset,
                limit,
                status,
                channels,
                channelIds,
                assignedTo,
                teamId,
                version,
                cancellationToken);
        }

        return await DefaultSearchUserProfileWithConversationAsync(
            companyUser,
            conditions,
            offset,
            limit,
            status,
            channels,
            channelIds,
            assignedTo,
            teamId,
            version,
            cancellationToken);
    }

    private async Task<SearchUserConversationResponse> DefaultSearchUserProfileWithConversationAsync(
        Staff companyUser,
        List<Condition> conditions,
        int offset,
        int limit,
        string status,
        string channels,
        string channelIds,
        string assignedTo,
        long? teamId,
        string version,
        CancellationToken cancellationToken)
    {
        var dbContext = _dbContextService.GetDbContext();

        var searchUserProfileWithConversationCacheKeyPattern = new SearchUserProfileWithConversationCacheKeyPattern(
            companyUser.CompanyId,
            companyUser.IdentityId,
            conditions,
            offset,
            limit,
            status,
            channels,
            channelIds,
            assignedTo,
            teamId,
            version);

        ILockService.Lock myLock = null;

        while (true)
        {
            myLock = await _lockService.AcquireLockAsync(
                $"Searching_SearchUserProfileWithConversationAsync_{searchUserProfileWithConversationCacheKeyPattern.GenerateKeyPattern()}",
                TimeSpan.FromSeconds(5));

            if (myLock == null)
            {
                await Task.Delay(TimeSpan.FromSeconds(5), cancellationToken);
            }
            else
            {
                break;
            }
        }

        var channelNameList = new List<string>();

        if (!string.IsNullOrEmpty(channels))
        {
            channelNameList = channels.Split(",").ToList();
        }

        var channelIdList = new List<string>();

        if (!string.IsNullOrEmpty(channelIds))
        {
            channelIdList = channelIds.Split(",").ToList();
        }

        if (conditions?.Count > 0)
        {
            var condLastChannel = conditions
                .FirstOrDefault(
                    x =>
                        !string.IsNullOrEmpty(x.FieldName)
                        && x.FieldName.ToLower() == "lastchannel"
                        && x.ConditionOperator == SupportedOperator.Contains);

            if (condLastChannel != null)
            {
                foreach (var value in condLastChannel.Values)
                {
                    var set = value.Split(":");

                    if (set.Count() > 1)
                    {
                        if (!channelNameList.Contains(set[0]))
                        {
                            channelNameList.Add(set[0]);
                        }

                        channelIdList.Add(value.Substring(value.IndexOf(":") + 1));
                    }
                }
            }
        }

#if DEBUG
        var stopWatch = Stopwatch.StartNew();
#endif
        var selectedUserProfile = (await _userProfileService.GetUserProfilesQueryableByFields(
                companyUser.CompanyId,
                conditions,
                null,
                null,
                null,
                null,
                (companyUser.RoleType != StaffUserRole.Admin) ? (long?) companyUser.Id : null,
                channelNameList,
                channelIdList,
                companyUser.RoleType,
                true))
            .AsNoTracking();

        IQueryable<Conversation> conversationIdsQuery = dbContext.Conversations
            .Where(
                conversation => conversation.CompanyId == companyUser.CompanyId
                                && selectedUserProfile.Select(u => u.Id).Contains(conversation.UserProfileId))
            .OrderByDescending(conversation => conversation.IsBookmarked)
            .ThenByDescending(conversation => conversation.UpdatedTime);

        var mentionQueryable = _mentionQueryableResolver.GetMentionQueryable(
            dbContext,
            companyUser.Id,
            companyUser.CompanyId);

        // DEVS-1407 Search relevant to inbox tab
        if (!string.IsNullOrEmpty(assignedTo))
        {
            switch (assignedTo)
            {
                case "all":
                    switch (companyUser.RoleType)
                    {
                        case StaffUserRole.Admin:
                        case StaffUserRole.DemoAdmin:
                            break;
                        case StaffUserRole.TeamAdmin:
                        case StaffUserRole.Staff:
                            var managedTeam = await dbContext.CompanyStaffTeams
                                .Where(x => x.Members.Any(m => m.StaffId == companyUser.Id))
                                .Select(x => x.Id)
                                .ToListAsync();


                            if (managedTeam.Count == 0)
                            {
                                break;
                            }

                            if (companyUser.RoleType == StaffUserRole.TeamAdmin)
                            {
                                // DEVS-10182: [Web] Inbox Conversation Display Enhancement
                                // Team admins should be able to search for the conversation when their teammate are collaborator
                                var staffAccessControlAggregate =
                                    await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(
                                        companyUser);
                                var teamAdminConversationSpecification =
                                    new TeamAdminConversationSpecification(staffAccessControlAggregate);

                                var predicate = PredicateBuilder.New<Conversation>();
                                predicate = predicate.Or(teamAdminConversationSpecification.ToExpression());
                                predicate = predicate.Or(c => mentionQueryable.Select(m => m.ConversationId).Contains(c.Id));
                                conversationIdsQuery = conversationIdsQuery.Where(predicate);
                            }
                            else
                            {
                                var staffAccessControlAggregate =
                                    await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(
                                        companyUser);
                                var staffConversationSpecification =
                                    new StaffConversationSpecification(staffAccessControlAggregate);

                                var predicate = PredicateBuilder.New<Conversation>();
                                predicate = predicate.Or(staffConversationSpecification.ToExpression());
                                predicate = predicate.Or(c => mentionQueryable.Select(m => m.ConversationId).Contains(c.Id));
                                conversationIdsQuery = conversationIdsQuery.Where(predicate);
                            }

                            break;
                    }

                    break;
                case "unassigned":
                    conversationIdsQuery = conversationIdsQuery.Where(conversation => conversation.AssigneeId == null);

                    switch (companyUser.RoleType)
                    {
                        case StaffUserRole.TeamAdmin:
                        case StaffUserRole.Staff:
                            var managedTeam = await dbContext.CompanyStaffTeams
                                .Where(x => x.Members.Any(m => m.StaffId == companyUser.Id))
                                .Include(x => x.Members)
                                .AsNoTracking()
                                .ToListAsync();

                            if (managedTeam.Count == 0)
                            {
                                break;
                            }

                            var teamAssigned = managedTeam
                                .Select(x => x.Id)
                                .ToList();

                            var isShowDefaultChannelMessagesOnly = false;

                            var staffPermission = await dbContext.CompanyRolePermissions
                                .FirstOrDefaultAsync(
                                    x =>
                                        x.CompanyId == companyUser.CompanyId
                                        && x.StaffUserRole == companyUser.RoleType);
                            if (staffPermission != null)
                            {
                                isShowDefaultChannelMessagesOnly =
                                    staffPermission.Permission.IsShowDefaultChannelMessagesOnly;
                            }

                            if (isShowDefaultChannelMessagesOnly)
                            {
                                conversationIdsQuery = conversationIdsQuery.Where(
                                    conversation =>
                                        conversation.AssignedTeamId.HasValue
                                        && teamAssigned.Contains(conversation.AssignedTeamId.Value));
                            }
                            else
                            {
                                conversationIdsQuery = conversationIdsQuery.Where(
                                    conversation =>
                                        !conversation.AssignedTeamId.HasValue
                                        || (conversation.AssignedTeamId.HasValue
                                            && teamAssigned.Contains(conversation.AssignedTeamId.Value)));
                            }

                            break;
                    }

                    break;
                case "mentioned":
                    conversationIdsQuery = conversationIdsQuery.Where(
                        c => mentionQueryable.Select(m => m.ConversationId).Contains(c.Id));

                    break;
                case "team":
                    switch (companyUser.RoleType)
                    {
                        case StaffUserRole.Admin:
                        case StaffUserRole.DemoAdmin:
                        case StaffUserRole.TeamAdmin:
                            conversationIdsQuery =
                                conversationIdsQuery.Where(conversation => conversation.AssignedTeamId == teamId);
                            break;
                        case StaffUserRole.Staff:
                            conversationIdsQuery = conversationIdsQuery.Where(
                                conversation => conversation.AssignedTeamId == teamId
                                                && (!conversation.AssigneeId.HasValue
                                                    || conversation.AssigneeId == companyUser.Id)
                            );

                            break;
                    }

                    break;
                case "collaborator":
                    conversationIdsQuery = conversationIdsQuery
                        .Where(
                            conversation => conversation.AdditionalAssignees.Any(x => x.AssigneeId == companyUser.Id));
                    break;
                case "bookmarked":
                    conversationIdsQuery = conversationIdsQuery
                        .Where(
                            conversation => conversation.ConversationBookmarks.Any(x => x.StaffId == companyUser.Id));
                    break;
                default:
                    var (assignedToStaffId, assignedToIdentityId) =
                        await ResolveAssignedStaffIdAsync(companyUser, assignedTo);

                    switch (companyUser.RoleType)
                    {
                        case StaffUserRole.Admin:
                        case StaffUserRole.DemoAdmin:
                            break;
                        case StaffUserRole.TeamAdmin:
                            var isManagedTeam = await dbContext.CompanyStaffTeams
                                .AsNoTracking()
                                .AsSplitQuery()
                                .AnyAsync(
                                    x =>
                                        x.Members.Any(x => x.StaffId == companyUser.Id)
                                        && x.Members.Any(x => x.Staff.Id == assignedToStaffId));
                            if (!isManagedTeam)
                            {
                                if (assignedToIdentityId != companyUser.IdentityId)
                                {
                                    throw new Exception($"{assignedToIdentityId} is not your teammates");
                                }
                            }

                            break;
                        case StaffUserRole.Staff:
                            if (assignedTo != companyUser.IdentityId)
                            {
                                throw new Exception(
                                    $"You cannot access conversations assigned to {assignedToIdentityId}");
                            }

                            break;
                    }


                    conversationIdsQuery = conversationIdsQuery
                        .WhereIf(
                            version == "2",
                            conversation => conversation.AssigneeId == assignedToStaffId);

                    break;
            }
        }

        if (status != "all")
        {
            conversationIdsQuery = conversationIdsQuery.Where(conversation => conversation.Status == status.ToLower())
                .OrderByDescending(
                    conversation => conversation.ConversationBookmarks.Any(x => x.StaffId == companyUser.Id))
                .ThenByDescending(conversation => conversation.UpdatedTime);
        }

        var conversationsIdList = await conversationIdsQuery
            .Skip(offset)
            .Take(limit)
            .Select(x => x.Id)
            .AsNoTracking()
            .ToListAsync(cancellationToken: cancellationToken);

        var conversationsList = await dbContext.Conversations
            .Where(x => conversationsIdList.Contains(x.Id) && x.CompanyId == companyUser.CompanyId)
            .Include(x => x.UserProfile)
            .Include(x => x.WhatsappUser)
            .Include(x => x.facebookUser)
            .Include(x => x.WhatsappUser)
            .Include(x => x.Assignee.Identity)
            .Include(x => x.EmailAddress)
            .Include(x => x.WebClient)
            .Include(x => x.WeChatUser)
            .Include(x => x.LineUser)
            .Include(x => x.SMSUser)
            .Include(x => x.ViberUser)
            .Include(x => x.TelegramUser)
            .Include(x => x.WhatsApp360DialogUser)
            .Include(x => x.WhatsappCloudApiUser)
            .Include(x => x.ConversationBookmarks)
            .Include(x => x.InstagramUser)
            .AsNoTracking()
            .ToListAsync(cancellationToken: cancellationToken);

        var totalResult = await conversationIdsQuery.CountAsync(cancellationToken: cancellationToken);

#if DEBUG
        stopWatch.Stop();
        _logger.LogInformation($"SearchUserProfileWithConversationAsync Conversation Query: {stopWatch.ElapsedMilliseconds}ms");
#endif

        conversationsList.ForEach(x => x.IsBookmarked = x.ConversationBookmarks.Any(y => y.StaffId == companyUser.Id));

        var rolePermission = await dbContext.CompanyRolePermissions
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyUser.CompanyId
                    && x.StaffUserRole == companyUser.RoleType,
                cancellationToken: cancellationToken);

        var filterLastMessage =
            rolePermission != null
            && rolePermission.Permission.IsShowDefaultChannelMessagesOnly;

        var associatedTeams = await dbContext.CompanyStaffTeams
            .Where(x => x.Members.Any(y => y.StaffId == companyUser.Id))
            .AsNoTracking()
            .AsSplitQuery()
            .ToListAsync(cancellationToken: cancellationToken);

        var whatsappIds = new List<string>();
        var twilioSenderIds = new List<string>();
        var whatsapp360dialogDefaultChannelIds = new List<long>();
        var whatsappCloudDefaultChannelIds = new List<string>();

        foreach (var associatedTeam in associatedTeams)
        {
            if (associatedTeam.DefaultChannels?.Count > 0)
            {
                foreach (var defaultChannel in associatedTeam.DefaultChannels
                             .Where(
                                 x =>
                                     x.channel != ChannelTypes.Whatsapp360Dialog
                                     && x.channel != ChannelTypes.WhatsappCloudApi))
                {
                    channelNameList.Add(defaultChannel.channel);

                    if (defaultChannel.ids != null)
                    {
                        foreach (var id in defaultChannel.ids)
                        {
                            var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                            whatsappIds.Add(twilioInstance[0]);

                            if (twilioInstance.Count() > 1)
                            {
                                twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                            }
                        }
                    }
                }

                foreach (var defaultChannel in associatedTeam.DefaultChannels
                             .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                {
                    foreach (var channelId in defaultChannel.ids)
                    {
                        var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);

                        if (validLong)
                        {
                            whatsapp360dialogDefaultChannelIds.Add(whatsapp360dialogChannelId);
                        }
                    }
                }

                foreach (var defaultChannel in associatedTeam.DefaultChannels
                             .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                {
                    foreach (var channelId in defaultChannel.ids)
                    {
                        whatsappCloudDefaultChannelIds.Add(channelId);
                    }
                }
            }
        }

        var mappedResult = await _conversationNoCompanyResponseViewModelMapper.ToViewModelsAsync(
            conversationsList);

        var customFields = await dbContext.UserProfileCustomFields
            .Where(
                y => mappedResult
                    .Select(x => x.UserProfile.Id)
                    .Contains(y.UserProfileId))
            .AsNoTracking()
            .AsSplitQuery()
            .ProjectTo<UserProfileCustomFieldNoOptionsViewModel>(_mapper.ConfigurationProvider)
            .ToListAsync(cancellationToken: cancellationToken);

        var hashtags = await dbContext.ConversationHashtags
            .Where(
                y => mappedResult
                    .Select(x => x.ConversationId)
                    .Contains(y.ConversationId))
            .Include(y => y.Hashtag)
            .AsNoTracking()
            .AsSplitQuery()
            .ProjectTo<ConversationHashtagResponse>(_mapper.ConfigurationProvider)
            .ToListAsync(cancellationToken: cancellationToken);

        mappedResult.ForEach(
            x =>
            {
                x.UserProfile.CustomFields = customFields
                    .Where(y => y.UserProfileId == x.UserProfile.Id)
                    .ToList();

                x.ConversationHashtags = hashtags
                    .Where(y => y.ConversationId == x.ConversationId)
                    .ToList();
            });

        // Disabled the field for performance optimizations to reduce heavy loading and prevent FE not invalidate local cache
        foreach (var result in mappedResult)
        {
            result.AdditionalAssignees = null;
        }

        var selectedUserProfileResponse = new SearchUserConversationResponse
        {
            Conversations = mappedResult,
            TotalResult = totalResult,
        };

        foreach (var conversation in selectedUserProfileResponse.Conversations)
        {
            await _conversationService.GetConversationLastMessage(
                conversation,
                filterLastMessage,
                whatsappIds,
                whatsapp360dialogDefaultChannelIds,
                channelIdList,
                channelNameList,
                twilioSenderIds,
                false,
                whatsappCloudDefaultChannelIds,
                cancellationToken);
        }

        if (myLock != null)
        {
            await _lockService.ReleaseLockAsync(myLock);
        }

        return selectedUserProfileResponse;
    }

    private async ValueTask<(long AssignedStaffId, string AssignedAspNetUserId)> ResolveAssignedStaffIdAsync(
        Staff currentStaff,
        string assignedTo)
    {
        if (assignedTo == "you" || assignedTo == currentStaff.IdentityId)
        {
            return (currentStaff.Id, currentStaff.IdentityId);
        }

        var dbContext = _dbContextService.GetDbContext();

        var assignedToStaffId = await dbContext.UserRoleStaffs
            .Where(x => x.CompanyId == currentStaff.CompanyId && x.IdentityId == assignedTo)
            .Select(x => x.Id)
            .FirstOrDefaultAsync();

        return (assignedToStaffId, currentStaff.IdentityId);
    }

    public async Task<SelectAllContactsResponse> GetAllUserProfileIdAsync(
        Staff companyUser,
        List<Condition> conditions,
        string status,
        string channels,
        string channelIds)
    {
        var dbContext = _dbContextService.GetDbContext();

        var channelNameList = new List<string>();

        if (!string.IsNullOrEmpty(channels))
        {
            channelNameList = channels.Split(",").ToList();
        }

        var channelIdList = new List<string>();

        if (!string.IsNullOrEmpty(channelIds))
        {
            channelIdList = channelIds.Split(",").ToList();
        }

        var isFromRawSql = _configuration.GetValue<bool>("SqlPerformance:FromRawSql");

        if (!isFromRawSql & conditions?.Count > 0)
        {
            var condLastChannel = conditions
                .FirstOrDefault(
                    x =>
                        !string.IsNullOrEmpty(x.FieldName)
                        && x.FieldName.ToLower() == "lastchannel"
                        && x.ConditionOperator == SupportedOperator.Contains);

            if (condLastChannel != null)
            {
                foreach (var value in condLastChannel.Values)
                {
                    var set = value.Split(":");

                    if (set.Count() > 1)
                    {
                        if (!channelNameList.Contains(set[0]))
                        {
                            channelNameList.Add(set[0]);
                        }

                        channelIdList.Add(value.Substring(value.IndexOf(":") + 1));
                    }
                }
            }
        }

        IQueryable<string> selectedUserProfileIdsQuery = null;

        switch (conditions?.Count)
        {
            case > 0 when conditions.Any(x => x.NextOperator == SupportedNextOperator.Or):
                {
                    var getUserProfilesIdsByFields = await _userProfileService.GetUserProfilesIdsByFields(
                        companyUser.CompanyId,
                        conditions,
                        null,
                        null,
                        "createdat",
                        "desc",
                        (companyUser.RoleType != StaffUserRole.Admin) ? (long?) companyUser.Id : null,
                        channelNameList,
                        channelIdList,
                        companyUser.RoleType);

                    selectedUserProfileIdsQuery = dbContext.UserProfiles
                        .Where(x => getUserProfilesIdsByFields.UserProfileIds.Contains(x.Id))
                        .Select(x => x.Id);

                    if (status != "all")
                    {
                        var conversationsQuery =
                            from conversation in dbContext.Conversations
                            where selectedUserProfileIdsQuery.Contains(conversation.UserProfileId)
                            orderby conversation.UpdatedTime descending
                            select conversation;

                        if (status != "all")
                        {
                            conversationsQuery =
                                from conversation in conversationsQuery
                                where conversation.Status == status.ToLower()
                                orderby conversation.UpdatedTime descending
                                select conversation;
                        }

                        selectedUserProfileIdsQuery = conversationsQuery.Select(x => x.UserProfileId);
                    }

                    var selectAllUserProfileResponse = new SelectAllContactsResponse
                    {
                        UserProfileIds = await selectedUserProfileIdsQuery.ToListAsync()
                    };

                    selectAllUserProfileResponse.TotalResult = selectAllUserProfileResponse.UserProfileIds.Count;

                    return selectAllUserProfileResponse;
                }
            case > 0:
                {
                    var userProfileIds = new List<string>();
                    IQueryable<UserProfile> getUserProfilesIdsByFields;

                    if (isFromRawSql)
                    {
                        var (userProfileSQLQuery, count, queryableUserProfile) =
                            await _userProfileSqlService.GetUserProfilesAsync(
                                companyUser.CompanyId,
                                conditions,
                                null,
                                null,
                                "createdat",
                                "desc",
                                (companyUser.RoleType != StaffUserRole.Admin) ? (long?) companyUser.Id : null,
                                channelNameList,
                                channelIdList,
                                companyUser.RoleType,
                                getUserProfileIdOnly: true);

                        getUserProfilesIdsByFields = queryableUserProfile;
                        userProfileIds = userProfileSQLQuery.Select(u => u.Id).ToList();
                    }
                    else
                    {
                        getUserProfilesIdsByFields = await _userProfileService
                            .GetIQueryableUserProfilesWithFilter(
                                companyId: companyUser.CompanyId,
                                conditions: conditions,
                                offset: null,
                                limit: null,
                                sortBy: "createdat",
                                order: "desc",
                                assigneeId: companyUser.RoleType != StaffUserRole.Admin ? companyUser.Id : null,
                                channels: channelNameList,
                                channelIds: channelIdList,
                                staffUserRole: companyUser.RoleType);
                    }

                    if (status != "all")
                    {
                        getUserProfilesIdsByFields = dbContext.Conversations
                            .Join(
                                getUserProfilesIdsByFields,
                                conversation => conversation.UserProfileId,
                                profile => profile.Id,
                                (conversation, profile) => new
                                {
                                    conversation,
                                    profile
                                })
                            .Where(x => x.conversation.Status == status.ToLower())
                            .Select(x => x.profile);
                    }

                    if (status != "all" || !isFromRawSql)
                    {
                        userProfileIds = await getUserProfilesIdsByFields
                            .Select(x => x.Id)
                            .ToListAsync();
                    }

                    var selectAllUserProfileResponse = new SelectAllContactsResponse
                    {
                        UserProfileIds = userProfileIds
                    };

                    selectAllUserProfileResponse.TotalResult = selectAllUserProfileResponse.UserProfileIds.Count;

                    return selectAllUserProfileResponse;
                }
            default:
                {
                    var userProfilesQ = _userProfileService
                        .GetManageableUserProfilesQueryable(companyUser)
                        .AsNoTracking();

                    var selectAllUserProfileResponse = new SelectAllContactsResponse
                    {
                        UserProfileIds = await userProfilesQ
                            .Select(x => x.Id)
                            .ToListAsync()
                    };

                    selectAllUserProfileResponse.TotalResult = selectAllUserProfileResponse.UserProfileIds.Count;

                    return selectAllUserProfileResponse;
                }
        }
    }

    public async Task<UserProfileNoCompanyResponse> UpdateUserProfileDescriptionAsync(
        Staff companyUser,
        string userProfileId,
        UserProfileDescriptionModel userProfileDescriptionModel)
    {
        var dbContext = _dbContextService.GetDbContext();

        var userProfile = await dbContext.UserProfiles
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyUser.CompanyId
                    && x.Id == userProfileId);

        userProfile.Description = userProfileDescriptionModel.Description;

        await dbContext.SaveChangesAsync();

        var response = _mapper.Map<UserProfileNoCompanyResponse>(userProfile);

        return response;
    }

    public async Task<ConversationNoCompanyResponseViewModel> GetConversationByUserProfileIdAsync(
        Staff companyUser,
        string userProfileId)
    {
        var conversation = await _userProfileService.GetConversationByUserProfileId(
            companyUser.CompanyId,
            userProfileId,
            "closed");

        var conversationResponse = await _conversationNoCompanyResponseViewModelMapper.ToViewModelAsync(
            conversation);

        var staff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(companyUser);
        var aggregatedConversation = await _accessControlAggregationService.GetAggregatedConversationAsync(conversation);

        conversationResponse.StaffConversationPermission = _rbacConversationPermissionManager.GetStaffConversationPermission(
            staff,
            aggregatedConversation);

        return conversationResponse;
    }

    public async Task<UserProfileNoCompanyResponse> GetUserProfileByIdAsync(Staff companyUser, string userProfileId)
    {
        var dbContext = _dbContextService.GetDbContext();

        _logger.LogInformation($"GetUserProfile: userProfileId {userProfileId}");

        companyUser.Company.CustomUserProfileFields = await dbContext.CompanyCustomUserProfileFields
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .Include(x => x.CustomUserProfileFieldLinguals)
            .Include(x => x.CustomUserProfileFieldOptions)
            .ThenInclude(x => x.CustomUserProfileFieldOptionLinguals)
            .ToListAsync();

        companyUser.Company.RolePermission = await dbContext.CompanyRolePermissions
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();

        var userProfiles = await dbContext.UserProfiles
            .Include(x => x.CustomFields)
            .Include(x => x.InstagramUser)
            .Include(x => x.FacebookAccount)
            .Include(x => x.WhatsAppAccount)
            .Include(x => x.EmailAddress)
            .Include(x => x.RegisteredUser.Identity)
            .Include(x => x.UserDevice)
            .Include(x => x.WebClient)
            .Include(x => x.WeChatUser)
            .Include(x => x.LineUser)
            .Include(x => x.SMSUser)
            .Include(x => x.WhatsApp360DialogUser)
            .Include(x => x.WhatsappCloudApiUser)
            .Include(x => x.TelegramUser)
            .Include(x => x.TikTokUser)
            .Include(x => x.ViberUser)
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyUser.CompanyId
                    && x.Id == userProfileId);

        companyUser.Company.CustomUserProfileFields = companyUser.Company
            .CustomUserProfileFields
            .OrderBy(z => z.Order)
            .ToList();

        companyUser.Company
            .CustomUserProfileFields
            .ForEach(
                x =>
                    x.CustomUserProfileFieldOptions = x.CustomUserProfileFieldOptions
                        .OrderBy(z => z.Order)
                        .ThenBy(z => z.Value)
                        .ToList());

        var results = _mapper.Map<UserProfileNoCompanyResponse>(userProfiles);

        // mask data
        if (companyUser.Company.RolePermission?.Count > 0)
        {
            var permission = companyUser.Company
                .RolePermission
                .FirstOrDefault(x => x.StaffUserRole == companyUser.RoleType);

            if (permission != null)
            {
                ExecuteCompanyRolePermissions(permission, companyUser, results);
            }
        }

        try
        {
            var conversation = await dbContext.Conversations
                .FirstOrDefaultAsync(convo => convo.UserProfileId == userProfiles.Id);

            if (conversation != null)
            {
                var conversationHashtags = (await dbContext.ConversationHashtags
                        .Include(X => X.Hashtag)
                        .Where(x => x.ConversationId == conversation.Id)
                        .OrderByDescending(x => x.Id)
                        .ToListAsync())
                    .DistinctBy(ch => ch.HashtagId)
                    .ToList();

                results.ConversationHashtags = _mapper.Map<List<ConversationHashtagResponse>>(conversationHashtags);
                results.ConversationId = conversation.Id;
            }

            var lists = await dbContext.CompanyImportContactHistories
                .Where(x => x.ImportedUserProfiles.Any(x => x.UserProfileId == results.Id))
                .OrderByDescending(x => x.IsBookmarked).ThenBy(X => X.Order)
                .ThenByDescending(x => x.CreatedAt)
                .ToListAsync();

            // Init ContactLists
            results.ContactLists = [];

            foreach (var list in lists)
            {
                results.ContactLists.Add(
                    new ContactJoinedList
                    {
                        Id = list.Id,
                        ListName = list.ImportName
                    });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{MethodName}] error when populating user profile {UserProfileId} conversation details and lists: {ExceptionMessage}",
                nameof(GetUserProfileByIdAsync),
                userProfileId,
                ex.Message);
        }

        return results;
    }

    public async Task<List<UserProfileCustomFieldNoOptionsViewModel>> GetUserProfileCustomFieldAsync(
        Staff companyUser,
        string userProfileId)
    {
        var dbContext = _dbContextService.GetDbContext();

        var manageableUserProfileId = await dbContext.UserProfiles
            .Where(
                x =>
                    x.CompanyId == companyUser.CompanyId
                    && x.Id == userProfileId)
            .Select(x => x.Id)
            .FirstOrDefaultAsync();

        var customFields = await dbContext.UserProfileCustomFields
            .Where(x => x.UserProfileId == manageableUserProfileId)
            .ToListAsync();

        var results = _mapper.Map<List<UserProfileCustomFieldNoOptionsViewModel>>(
            customFields);

        return results;
    }

    public async Task<UserProfileNoCompanyResponse> UpdateUserProfileCustomFieldAsync(
        Staff companyUser,
        string userProfileId,
        List<AddCustomFieldsViewModel> addCustomFieldsViewModels)
    {
        var userProfile = await _userProfileService.UpdateUserProfileCustomFields(
            companyUser.CompanyId,
            userProfileId,
            companyUser.Id,
            addCustomFieldsViewModels,
            userProfileSource: UserProfileSource.ManualEntry);

        var responseVm = _mapper.Map<UserProfileNoCompanyResponse>(userProfile);

        return responseVm;
    }

    public async Task<bool> DeleteUserProfileAsync(Staff companyUser, UserProfileIdsViewModel userprofileViewModel)
    {
        var dbContext = _dbContextService.GetDbContext();

        if (companyUser.RoleType == StaffUserRole.Staff)
        {
            throw new Exception("Unable to delete");
        }

        if (companyUser.CompanyId == "a784fd97-3a7a-47e6-ae44-480dadf641f7")
        {
            throw new Exception("Unable to delete");
        }

        // COSMAX & VITAE
        if (companyUser.CompanyId is "4c543d7e-bb9c-43f0-87b7-cb9fe90870b0"
                or "820a5a3f-1f6a-4d58-8247-cb8b81655c80"
            && companyUser.RoleType == StaffUserRole.TeamAdmin)
        {
            throw new Exception("Unable to delete");
        }

        foreach (var userProfileIdsChunk in userprofileViewModel.UserProfileIds.Chunk(100))
        {
            _logger.LogInformation(
                "Company {CompanyId}, staff {StaffIdentityId} of name {StaffUserName} is deleting user profiles: {UserProfileIds}",
                companyUser.CompanyId,
                companyUser.IdentityId,
                companyUser.Identity?.UserName,
                JsonConvert.SerializeObject(userProfileIdsChunk));

            await dbContext.Conversations
                .Where(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && userProfileIdsChunk.Contains(x.UserProfileId))
                .ExecuteUpdateAsync(
                    conversation =>
                        conversation.SetProperty(c => c.ActiveStatus, ActiveStatus.Inactive));

            await dbContext.UserProfiles
                .Where(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && userProfileIdsChunk.Contains(x.Id))
                .ExecuteUpdateAsync(
                    profile =>
                        profile.SetProperty(p => p.ActiveStatus, ActiveStatus.Inactive));
        }

        if (userprofileViewModel.UserProfileIds.Count < 100)
        {
            BackgroundJob.Enqueue<IUserProfileService>(
                x => x.DeleteUserProfiles(
                    companyUser.CompanyId,
                    userprofileViewModel,
                    new UserProfileDeletionTriggerContext(
                        UpdateUserProfileTriggerSource.StaffManual,
                        companyUser),
                    3));
        }

        return true;
    }

    public async Task<List<UserProfileNoCompanyResponse>> BulkUpdateUserProfileCustomFieldAsync(
        Staff companyUser,
        BulkUpdateCustomFieldsViewModel bulkUpdateCustomFieldsViewModel)
    {
        var userProfile = await _userProfileService.BulkUpdateUserProfiles(
            companyUser.CompanyId,
            companyUser.Id,
            bulkUpdateCustomFieldsViewModel);

        return userProfile;
    }

    public async Task<UserGroupResult> GetUserProfileListAsync(Staff companyUser, string name, int offset, int limit)
    {
        var dbContext = _dbContextService.GetDbContext();

        if (limit >= 750)
        {
            limit = 750;
        }

        var histories = dbContext.CompanyImportContactHistories
            .AsSingleQuery()
            .Include(x => x.ImportedFrom.Identity)
            .Where(
                x =>
                    x.CompanyId == companyUser.CompanyId
                    && x.Status != ImportStatus.Saved)
            .WhereIf(
                !string.IsNullOrEmpty(name),
                x => x.ImportName.Contains(name))
            .WhereIf(
                companyUser.RoleType == StaffUserRole.Staff,
                x => x.ImportedFromId == companyUser.Id)
            .OrderByDescending(x => x.IsBookmarked)
            .ThenBy(x => x.Order)
            .ThenByDescending(x => x.CreatedAt)
            .Select(
                x => new
                {
                    List = _mapper.Map<ImportContactHistoryResponse>(x),
                    UniqueContactCount = x.ImportedUserProfiles
                        .Select(up => up.UserProfileId)
                        .Distinct()
                        .Count(),
                });

        var results = await histories
            .Skip(offset)
            .Take(limit)
            .ToListAsync();

        results.ForEach(x => x.List.TotalContactCount = x.UniqueContactCount);

        var response = new UserGroupResult
        {
            userGroups = results
                .Select(x => x.List)
                .ToList(),
            TotalGroups = await histories.CountAsync()
        };

        return response;
    }

    public async Task<UserGroupBriefResult> GetUserProfileListBriefResultAsync(Staff companyUser, string name = null)
    {
        var dbContext = _dbContextService.GetDbContext();

        var histories = await dbContext.CompanyImportContactHistories
            .Where(
                x =>
                    x.CompanyId == companyUser.CompanyId
                    && x.Status != ImportStatus.Saved)
            .WhereIf(
                companyUser.RoleType == StaffUserRole.Staff,
                x => x.ImportedFromId == companyUser.Id)
            .WhereIf(
                name != null,
                x => x.ImportName.Contains(name))
            .OrderByDescending(x => x.IsBookmarked)
            .ThenBy(x => x.Order)
            .ThenByDescending(x => x.CreatedAt)
            .ProjectTo<ImportContactHistoryBriefResponse>(_mapper.ConfigurationProvider)
            .ToListAsync();

        var response = new UserGroupBriefResult
        {
            userGroups = histories,
            TotalGroups = histories.Count()
        };

        return response;
    }

    public async Task DeleteUserProfileListAsync(Staff companyUser, long staffId, GroupListViewModel groupListViewModel)
    {
        await _userProfileService.DeleteContactList(companyUser.CompanyId, staffId, groupListViewModel);
    }

    public async Task BookmarkUserProfileListAsync(Staff companyUser, List<BookmarkViewModel> listReorderViewModels)
    {
        var dbContext = _dbContextService.GetDbContext();

        foreach (var listRecorder in listReorderViewModels)
        {
            var list = await dbContext.CompanyImportContactHistories
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && x.Id == listRecorder.ListId);

            list.IsBookmarked = listRecorder.IsBookmark;
            await dbContext.SaveChangesAsync();
        }
    }

    public async Task ReorderUserProfileListAsync(Staff companyUser, List<ListReorderViewModel> listReorderViewModels)
    {
        var dbContext = _dbContextService.GetDbContext();

        foreach (var listRecorder in listReorderViewModels)
        {
            var list = await dbContext.CompanyImportContactHistories
                .FirstOrDefaultAsync(
                    x =>
                        x.CompanyId == companyUser.CompanyId
                        && x.Id == listRecorder.ListId);

            if (list == null)
            {
                continue;
            }

            list.Order = listRecorder.Order;

            await dbContext.SaveChangesAsync();
        }
    }

    public async Task<List<ImportedUserProfile>> AddUserProfileToUserProfileListAsync(
        Staff companyUser,
        long groupId,
        UserProfileIdsViewModel userprofileViewModel)
    {
        var addedUsers = await _userProfileService.AddToUserList(
            companyUser.CompanyId,
            groupId,
            userprofileViewModel,
            companyUser.Id);

        return addedUsers;
    }

    public async Task<ImportContactHistoryResponse> RemoveUserProfileFromUserProfileListAsync(
        Staff companyUser,
        long groupId,
        UserProfileIdsViewModel userprofileViewModel)
    {
        var group = await _userProfileService.RemoveFromUserList(
            companyUser.CompanyId,
            groupId,
            userprofileViewModel,
            companyUser.Id);

        var response = _mapper.Map<ImportContactHistoryResponse>(group);
        response.TotalContactCount = group.ImportedUserProfiles.Count;

        return response;
    }

    public async Task<ImportContactHistoryResponse> CreateUserProfileListAsync(
        Staff companyUser,
        UserProfileIdsViewModel userprofileViewModel)
    {
        var dbContext = _dbContextService.GetDbContext();

        List<UserProfile> userProfileList = new List<UserProfile>();

        if (userprofileViewModel.UserProfileIds?.Count > 0)
        {
            userProfileList = await dbContext.UserProfiles
                .Where(
                    x =>
                        userprofileViewModel.UserProfileIds.Contains(x.Id)
                        && x.CompanyId == companyUser.CompanyId)
                .ToListAsync();
        }

        var newImport = new ImportContactHistory
        {
            CompanyId = companyUser.CompanyId,
            ImportName = userprofileViewModel.GroupListName,
            ImportedUserProfiles = new List<ImportedUserProfile>(),
            IsImported = false,
            Status = ImportStatus.Imported,
            ImportedFrom = await dbContext.UserRoleStaffs
                .Include(x => x.Identity)
                .FirstOrDefaultAsync(x => x.Id == companyUser.Id)
        };

        foreach (var userprofile in userProfileList)
        {
            newImport.ImportedUserProfiles.Add(
                new ImportedUserProfile
                {
                    UserProfileId = userprofile.Id
                });
        }

        var olderList = await dbContext.CompanyImportContactHistories
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();

        olderList.ForEach(x => x.Order += 1);

        dbContext.CompanyImportContactHistories.Add(newImport);
        await dbContext.SaveChangesAsync();

        await _userProfileListHooks.OnUserProfileListCreatedAsync(
            newImport.Id.ToString(),
            newImport.ImportName);

        var response = _mapper.Map<ImportContactHistoryResponse>(newImport);

        return response;
    }

    public async Task<(string Csv, int TotalCount)> ExportUserProfileToCsvAsync(
        Staff companyUser,
        UserProfileIdsViewModel userprofileViewModel)
    {
        var (exportResultCsv, totalCount) = await _userProfileService.ExportUserProfileInCsv(
            companyUser.CompanyId,
            companyUser.Id,
            userprofileViewModel);

        return (exportResultCsv, totalCount);
    }

    public async Task<ImportContactHistoryResponse> GetUserProfileListCountAsync(Staff companyUser, long groupId)
    {
        var dbContext = _dbContextService.GetDbContext();

        var group = await dbContext.CompanyImportContactHistories
            .Include(x => x.ImportedFrom.Identity)
            .FirstOrDefaultAsync(
                x =>
                    x.Id == groupId
                    && x.CompanyId == companyUser.CompanyId);

        if (group == null)
        {
            throw new Exception($"GroupID: {groupId} not found.");
        }

        var response = _mapper.Map<ImportContactHistoryResponse>(group);

        response.TotalContactCount = await dbContext.CompanyImportedUserProfiles
            .CountAsync(y => y.ImportContactHistoryId == response.Id);

        return response;
    }

    public async Task<string> GetUserProfileImportSpreadsheetSampleInCsvAsync(Staff companyUser)
    {
        var dbContext = _dbContextService.GetDbContext();

        companyUser.Company.CustomUserProfileFields =
            await _contactCacheService.GetCompanyCustomUserProfileFieldsCachedAsync(companyUser.CompanyId);

        var staffs = await dbContext.UserRoleStaffs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .Include(x => x.Identity)
            .ToListAsync();

        // before your loop
        var header = "FirstName,LastName,Label,Collaborators";
        var sample =
            $"SleekFlow,Team,LabelA;LabelB,{staffs.FirstOrDefault().Identity.DisplayName};{staffs.FirstOrDefault().Identity.DisplayName}";

        // var propertyType = "Default contact property,Default contact property";
        var options = string.Empty;

        companyUser.Company.CustomUserProfileFields = companyUser.Company
            .CustomUserProfileFields
            .OrderBy(x => x.Order)
            .ToList();

        companyUser.Company.CustomUserProfileFields.ForEach(
            x =>
                x.CustomUserProfileFieldOptions = x.CustomUserProfileFieldOptions
                    .OrderBy(z => z.Order)
                    .ThenBy(z => z.Value)
                    .ToList());

        foreach (var customField in companyUser.Company
                     .CustomUserProfileFields
                     .Where(
                         x =>
                             x.IsEditable == true ||
                             x.FieldName == "LastChannel"))
        {
            header += $",{customField.FieldName}";
            sample += $",";

            switch (customField.Type)
            {
                case FieldDataType.Options:
                    options +=
                        $"\n{customField.FieldName}:,{string.Join(";", customField.CustomUserProfileFieldOptions.Select(x => x.Value))}";

                    if (customField.FieldName.ToLower() == "country")
                    {
                        sample += $"\"Hong Kong SAR\"";
                    }
                    else
                    {
                        sample += $"\"{customField.CustomUserProfileFieldOptions.FirstOrDefault()?.Value}\"";
                    }

                    break;
                case FieldDataType.Boolean:
                    sample += $"\"{true}\"";

                    break;
                case FieldDataType.PhoneNumber:
                    sample += "\"85239053590\"";

                    break;
                case FieldDataType.SingleLineText:
                    if (customField.FieldName.ToLower() == "companyname")
                    {
                        sample += "\"SleekFlow\"";
                    }

                    if (customField.FieldName.ToLower() == "jobtitle")
                    {
                        sample += "\"Developer\"";
                    }

                    break;
                case FieldDataType.Email:
                    sample += "\"<EMAIL>\"";

                    break;
                case FieldDataType.TravisUser:
                    sample += $"\"{staffs.FirstOrDefault().Identity.DisplayName}\"";

                    break;
                case FieldDataType.Channel:
                    options += $"\n{customField.FieldName}:";
                    var lastChannel = ConversationHelper.GetChannelName(ChannelTypes.WhatsappTwilio);

                    var channel = string.Empty;

                    var twilioConfigs = await dbContext.ConfigWhatsAppConfigs
                        .Where(x => x.CompanyId == companyUser.CompanyId)
                        .ToListAsync();

                    foreach (var twilioConfig in twilioConfigs)
                    {
                        options += $"{lastChannel} - {twilioConfig.Name};";

                        if (string.IsNullOrEmpty(channel))
                        {
                            channel = $"\"{lastChannel} - {twilioConfig.Name}\"";
                            sample += channel;
                        }
                    }

                    break;
                default:
                    break;
            }
        }

        var sampleCsv = $"{header}\n{sample}\n\n\n\n\n" +
                        $"Index: (You can remove this in your import))\n" +
                        $"Contact owner:,{string.Join(";", staffs.Select(x => x.Identity.DisplayName))}" +
                        $"{options}";

        return sampleCsv;
    }

    public async Task<IWorkbook> GetUserProfileImportSpreadsheetSampleInExcelAsync(Staff companyUser)
    {
        var dbContext = _dbContextService.GetDbContext();

        companyUser.Company.CustomUserProfileFields =
            await _contactCacheService.GetCompanyCustomUserProfileFieldsCachedAsync(companyUser.CompanyId);

        var staffs = await dbContext.UserRoleStaffs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .Include(x => x.Identity)
            .ToListAsync();

        companyUser.Company.CustomUserProfileFields = companyUser.Company.CustomUserProfileFields
            .OrderBy(x => x.Order)
            .ToList();

        companyUser.Company.CustomUserProfileFields.ForEach(
            x =>
                x.CustomUserProfileFieldOptions = x.CustomUserProfileFieldOptions
                    .OrderBy(z => z.Order)
                    .ThenBy(z => z.Value)
                    .ToList());

        // Sheet arrangement for Custom Fields
        var userProfileHeaders = new Dictionary<string, Type>
        {
            {
                "FirstName", typeof(string)
            },
            {
                "LastName", typeof(string)
            },
            {
                "Label", typeof(string)
            },
            {
                "Collaborators", typeof(string)
            }
        };

        var userProfileSampleData = new Dictionary<string, object>()
        {
            {
                "FirstName", "SleekFlow"
            },
            {
                "LastName", "Team"
            },
            {
                "Label", "LabelA;LabelB"
            },
            {
                "Collaborators",
                $"{staffs.FirstOrDefault().Identity.DisplayName};{staffs.FirstOrDefault().Identity.DisplayName}"
            }
        };

        // Sheet arrangement for Options
        var customFieldOptionHeaders = new Dictionary<string, Type>
        {
            {
                "Custom Field Name", typeof(string)
            },
        };

        var customFieldOptionSampleData = new List<IDictionary<string, object>>();

        // Add Contact Owner
        AppendOptionsToExcelColumn(
            customFieldOptionHeaders,
            customFieldOptionSampleData,
            "ContactOwner",
            staffs
                .Select(x => x.Identity.DisplayName)
                .ToList());

        foreach (var customField in companyUser.Company.CustomUserProfileFields
                     .Where(
                         x =>
                             x.IsEditable == true ||
                             x.FieldName == "LastChannel"))
        {
            if (userProfileHeaders.Keys.Contains(customField.FieldName))
            {
                continue;
            }

            userProfileHeaders.Add(customField.FieldName, typeof(string));
            userProfileSampleData.Add(customField.FieldName, string.Empty);

            switch (customField.Type)
            {
                case FieldDataType.Options:
                    AppendOptionsToExcelColumn(
                        customFieldOptionHeaders,
                        customFieldOptionSampleData,
                        customField.FieldName,
                        customField.CustomUserProfileFieldOptions
                            .Select(x => x.Value)
                            .ToList());

                    if (customField.FieldName.ToLower() == "country")
                    {
                        userProfileSampleData[customField.FieldName] = "Hong Kong SAR";
                    }
                    else
                    {
                        userProfileSampleData[customField.FieldName] =
                            $"{customField.CustomUserProfileFieldOptions.FirstOrDefault()?.Value}";
                    }

                    break;
                case FieldDataType.Boolean:
                    userProfileSampleData[customField.FieldName] = $"{true}";

                    break;
                case FieldDataType.PhoneNumber:
                    userProfileSampleData[customField.FieldName] = "85239053590";

                    break;
                case FieldDataType.SingleLineText:
                    if (customField.FieldName.ToLower() == "companyname")
                    {
                        userProfileSampleData[customField.FieldName] = "SleekFlow";
                    }

                    if (customField.FieldName.ToLower() == "jobtitle")
                    {
                        userProfileSampleData[customField.FieldName] = "Developer";
                    }

                    break;
                case FieldDataType.Email:
                    userProfileSampleData[customField.FieldName] = "<EMAIL>";

                    break;
                case FieldDataType.TravisUser:
                    userProfileSampleData[customField.FieldName] = $"{staffs.FirstOrDefault().Identity.DisplayName}";

                    break;
                case FieldDataType.Channel:
                    var lastChannel = ConversationHelper.GetChannelName(ChannelTypes.WhatsappTwilio);

                    var channelOptions = new List<string>();
                    var channel = string.Empty;
                    var channelList = string.Empty;

                    var twilioConfigs = await dbContext.ConfigWhatsAppConfigs
                        .Where(x => x.CompanyId == companyUser.CompanyId)
                        .ToListAsync();

                    foreach (var twilioConfig in twilioConfigs)
                    {
                        channelOptions.Add($"{lastChannel} - {twilioConfig.Name}");

                        if (string.IsNullOrEmpty(channel))
                        {
                            channel = $"{lastChannel} - {twilioConfig.Name}";
                            channelList += channel;
                        }
                    }

                    userProfileSampleData[customField.FieldName] = channelList;

                    AppendOptionsToExcelColumn(
                        customFieldOptionHeaders,
                        customFieldOptionSampleData,
                        customField.FieldName,
                        channelOptions);

                    break;
                default:
                    break;
            }
        }

        // Generate Excel File
        IWorkbook workbook = new XSSFWorkbook();

        workbook.CreateSheetFromList(
            "Contacts",
            userProfileHeaders,
            new List<IDictionary<string, object>>
            {
                userProfileSampleData
            });

        workbook.CreateSheetFromList("Index", customFieldOptionHeaders, customFieldOptionSampleData, true);

        return workbook;
    }

    /// <summary>
    /// Return the sample CSV file as string for express import.
    /// </summary>
    /// <param name="companyId">SleekFlow Company ID.</param>
    /// <returns>Sample CSV file as string.</returns>
    public async Task<string> GetUserProfileBulkImportSpreadsheetSampleInCsvAsync(string companyId)
    {
        var dbContext = _dbContextService.GetDbContext();

        var header = "FirstName,LastName";
        var sample = "SleekFlow,Team";
        var options = string.Empty;

        var companyCustomUserProfileFields =
            await _contactCacheService.GetCompanyCustomUserProfileFieldsCachedAsync(companyId);

        var staffs = await dbContext.UserRoleStaffs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId)
            .Include(x => x.Identity)
            .ToListAsync();

        companyCustomUserProfileFields = companyCustomUserProfileFields
            .OrderBy(x => x.Order)
            .ToList();

        companyCustomUserProfileFields.ForEach(
            x =>
                x.CustomUserProfileFieldOptions = x.CustomUserProfileFieldOptions
                    .OrderBy(z => z.Order)
                    .ThenBy(z => z.Value).ToList());

        foreach (var customField in companyCustomUserProfileFields.Where(x => x.IsEditable == true))
        {
            if (customField.Type == FieldDataType.Date || customField.Type == FieldDataType.DateTime)
            {
                continue;
            }

            header += $",{customField.FieldName}";
            sample += $",";

            switch (customField.Type)
            {
                case FieldDataType.Options:
                    options +=
                        $"\n{customField.FieldName}:,{string.Join(";", customField.CustomUserProfileFieldOptions.Select(x => x.Value))}";

                    if (customField.FieldName.ToLower() == "country")
                    {
                        sample += $"\"Hong Kong SAR\"";
                    }
                    else
                    {
                        sample += $"\"{customField.CustomUserProfileFieldOptions.FirstOrDefault()?.Value}\"";
                    }

                    break;
                case FieldDataType.Boolean:
                    sample += $"\"{true}\"";

                    break;
                case FieldDataType.PhoneNumber:
                    sample += "\"85239053590\"";

                    break;
                case FieldDataType.SingleLineText:
                    if (customField.FieldName.ToLower() == "companyname")
                    {
                        sample += "\"SleekFlow\"";
                    }

                    if (customField.FieldName.ToLower() == "jobtitle")
                    {
                        sample += "\"Developer\"";
                    }

                    break;
                case FieldDataType.Email:
                    sample += "\"<EMAIL>\"";

                    break;
                case FieldDataType.TravisUser:
                    sample += $"\"{staffs.FirstOrDefault().Identity.DisplayName}\"";

                    break;
                default:
                    break;
            }
        }

        var samplecsv = $"{header}\n{sample}\n\n\n\n\n" +
                        $"Index: (You can remove this in your import))\n" +
                        $"Contact owner:,{string.Join(";", staffs.Select(x => x.Identity.DisplayName))}" +
                        $"{options}";

        return samplecsv;
    }

    /// <summary>
    /// Return the sample XLSX file in proprietary Excel workbook format for express import.
    /// </summary>
    /// <param name="companyId">SleekFlow Company ID.</param>
    /// <returns>Sample XLSX file as workbook object.</returns>
    public async Task<IWorkbook> GetUserProfileBulkImportSpreadsheetSampleInExcelAsync(string companyId)
    {
        var dbContext = _dbContextService.GetDbContext();

        var companyCustomUserProfileFields =
            await _contactCacheService.GetCompanyCustomUserProfileFieldsCachedAsync(companyId);

        var staffs = await dbContext.UserRoleStaffs
            .AsNoTracking()
            .Where(x => x.CompanyId == companyId)
            .Include(x => x.Identity)
            .ToListAsync();

        companyCustomUserProfileFields = companyCustomUserProfileFields
            .OrderBy(x => x.Order)
            .ToList();

        companyCustomUserProfileFields.ForEach(
            x =>
                x.CustomUserProfileFieldOptions = x.CustomUserProfileFieldOptions
                    .OrderBy(z => z.Order)
                    .ThenBy(z => z.Value)
                    .ToList());

        // Sheet arrangement for Custom Fields
        var userProfileHeaders = new Dictionary<string, Type>
        {
            {
                "FirstName", typeof(string)
            },
            {
                "LastName", typeof(string)
            }
        };

        var userProfileSampleData = new Dictionary<string, object>()
        {
            {
                "FirstName", "SleekFlow"
            },
            {
                "LastName", "Team"
            }
        };

        // Sheet arrangement for Options
        var customFieldOptionHeaders = new Dictionary<string, Type>
        {
            {
                "Custom Field Name", typeof(string)
            },
        };

        var customFieldOptionSampleData = new List<IDictionary<string, object>>();

        // Add Contact Owner
        AppendOptionsToExcelColumn(
            customFieldOptionHeaders,
            customFieldOptionSampleData,
            "ContactOwner",
            staffs
                .Select(x => x.Identity.DisplayName)
                .ToList());

        foreach (var customField in companyCustomUserProfileFields.Where(x => x.IsEditable == true))
        {
            if (userProfileHeaders.Keys.Contains(customField.FieldName)
             || customField.Type == FieldDataType.Date
             || customField.Type == FieldDataType.DateTime)
            {
                continue;
            }

            userProfileHeaders.Add(customField.FieldName, typeof(string));
            userProfileSampleData.Add(customField.FieldName, string.Empty);

            switch (customField.Type)
            {
                case FieldDataType.Options:
                    AppendOptionsToExcelColumn(
                        customFieldOptionHeaders,
                        customFieldOptionSampleData,
                        customField.FieldName,
                        customField.CustomUserProfileFieldOptions
                            .Select(x => x.Value)
                            .ToList());

                    if (customField.FieldName.ToLower() == "country")
                    {
                        userProfileSampleData[customField.FieldName] = "Hong Kong SAR";
                    }
                    else
                    {
                        userProfileSampleData[customField.FieldName] =
                            $"{customField.CustomUserProfileFieldOptions.FirstOrDefault()?.Value}";
                    }

                    break;
                case FieldDataType.Boolean:
                    userProfileSampleData[customField.FieldName] = $"{true}";

                    break;
                case FieldDataType.PhoneNumber:
                    userProfileSampleData[customField.FieldName] = "85239053590";

                    break;
                case FieldDataType.SingleLineText:
                    if (customField.FieldName.ToLower() == "companyname")
                    {
                        userProfileSampleData[customField.FieldName] = "SleekFlow";
                    }

                    if (customField.FieldName.ToLower() == "jobtitle")
                    {
                        userProfileSampleData[customField.FieldName] = "Developer";
                    }

                    break;
                case FieldDataType.Email:
                    userProfileSampleData[customField.FieldName] = "<EMAIL>";

                    break;
                case FieldDataType.TravisUser:
                    userProfileSampleData[customField.FieldName] = $"{staffs.FirstOrDefault().Identity.DisplayName}";

                    break;

                default:
                    break;
            }
        }

        // Generate Excel File
        IWorkbook workbook = new XSSFWorkbook();

        workbook.CreateSheetFromList(
            "Contacts",
            userProfileHeaders,
            new List<IDictionary<string, object>>
            {
                userProfileSampleData
            });

        workbook.CreateSheetFromList("Index", customFieldOptionHeaders, customFieldOptionSampleData, true);

        return workbook;
    }

    public async Task ImportUserProfileAsync(Staff companyUser, ImportSpreadsheetViewModel importSpreadsheetViewModel)
    {
        var dbContext = _dbContextService.GetDbContext();

        var importSpreadsheet = await _importSpreadsheetService.SerializeSpreadsheet(
            companyUser.Company,
            importSpreadsheetViewModel);

        if (!string.IsNullOrEmpty(importSpreadsheetViewModel.ColumnsMap))
        {
            try
            {
                var columnMaps = JsonConvert.DeserializeObject<List<ColumnMap>>(
                    importSpreadsheetViewModel.ColumnsMap);

                foreach (var columnMap in columnMaps)
                {
                    var header = importSpreadsheet.headers
                        .FirstOrDefault(x => x.CsvFileColumnNumber == columnMap.csvFileColumnNumber);

                    header.HeaderName = columnMap.name;
                    header.importAction = columnMap.ImportAction;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Map Column Error");
            }
        }

        var newImport = new ImportContactHistory
        {
            CompanyId = companyUser.CompanyId,
            ImportName = importSpreadsheet.ImportName,
            ImportedUserProfiles = new List<ImportedUserProfile>(),
            IsImported = true,
            Status = ImportStatus.Importing,
            ImportedFromId = companyUser.Id
        };

        var olderList = await dbContext.CompanyImportContactHistories
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();

        olderList.ForEach(x => x.Order += 1);

        await dbContext.CompanyImportContactHistories.AddAsync(newImport);
        await dbContext.SaveChangesAsync();

        _logger.LogInformation(
            "[ImportUserProfileAsync] Company ID: {CompanyId}. User ID: {UserId}. Import ID: {ImportId}. Import Name: {ImportName}. Record Count: {RecordCount}",
            companyUser.CompanyId,
            companyUser.Id,
            newImport.Id,
            newImport.ImportName,
            importSpreadsheet.records.Count);

        BackgroundJob.Enqueue<IUserProfileImportService>(
            x => x.ImportUserProfile(
                newImport.Id,
                companyUser.CompanyId,
                companyUser.Id,
                importSpreadsheet,
                importSpreadsheetViewModel.IsTriggerAutomation,
                false));
    }

    public async Task<BackgroundTask> ImportUserProfileInBackgroundAsync(
        Staff companyUser,
        ImportSpreadsheetViewModel importSpreadsheetViewModel)
    {
        var dbContext = _dbContextService.GetDbContext();

        var importSpreadsheet = await _importSpreadsheetService.SerializeSpreadsheet(
            companyUser.Company,
            importSpreadsheetViewModel);

        if (importSpreadsheet.records.Count > 60000)
        {
            throw new Exception($"To protect the system, reject import more than 60000 contacts");
        }

        if (!string.IsNullOrEmpty(importSpreadsheetViewModel.ColumnsMap))
        {
            try
            {
                var columnMaps = JsonConvert.DeserializeObject<List<ColumnMap>>(
                    importSpreadsheetViewModel.ColumnsMap);

                foreach (var columnMap in columnMaps)
                {
                    var header = importSpreadsheet.headers
                        .FirstOrDefault(x => x.CsvFileColumnNumber == columnMap.csvFileColumnNumber);

                    header.HeaderName = columnMap.name;
                    header.importAction = columnMap.ImportAction;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Map Column Error");
            }
        }

        var newImport = new ImportContactHistory
        {
            CompanyId = companyUser.CompanyId,
            ImportName = importSpreadsheet.ImportName,
            ImportedUserProfiles = new List<ImportedUserProfile>(),
            IsImported = true,
            Status = ImportStatus.Importing,
            ImportedFromId = companyUser.Id
        };

        var olderList = await dbContext.CompanyImportContactHistories
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();

        olderList.ForEach(x => x.Order += 1);

        await dbContext.CompanyImportContactHistories.AddAsync(newImport);
        await dbContext.SaveChangesAsync();

        _logger.LogInformation(
            "[ImportUserProfileAsync] Company ID: {CompanyId}. User ID: {UserId}. Import ID: {ImportId}. Import Name: {ImportName}. Record Count: {RecordCount}",
            companyUser.CompanyId,
            companyUser.Id,
            newImport.Id,
            newImport.ImportName,
            importSpreadsheet.records.Count);

        var backgroundTask = await _backgroundTaskService.EnqueueImportContactsTask(
            companyUser.IdentityId,
            companyUser.Id,
            companyUser.CompanyId,
            newImport.Id,
            false,
            importSpreadsheetViewModel.IsTriggerAutomation,
            importSpreadsheet);

        return backgroundTask;
    }

    public async Task<BackgroundTask> ImportUserProfileToListInBackgroundAsync(
        Staff companyUser,
        ImportIntoListSpreadsheetViewModel importIntoListSpreadsheetViewModel)
    {
        var dbContext = _dbContextService.GetDbContext();

        var importSpreadsheetViewModel = new ImportSpreadsheetViewModel
        {
            ColumnsMap = importIntoListSpreadsheetViewModel.ColumnsMap,
            files = importIntoListSpreadsheetViewModel.Files,
            IsTriggerAutomation = importIntoListSpreadsheetViewModel.IsTriggerAutomation
        };

        var importSpreadsheet = await _importSpreadsheetService.SerializeSpreadsheet(
            companyUser.Company,
            importSpreadsheetViewModel);

        var targetImport = await dbContext.CompanyImportContactHistories
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyUser.CompanyId
                    && x.Id == importIntoListSpreadsheetViewModel.ListId);

        importSpreadsheet.ImportName = targetImport.ImportName;

        if (!string.IsNullOrEmpty(importSpreadsheetViewModel.ColumnsMap))
        {
            try
            {
                var columnMaps = JsonConvert.DeserializeObject<List<ColumnMap>>(
                    importSpreadsheetViewModel.ColumnsMap);

                foreach (var columnMap in columnMaps)
                {
                    var header = importSpreadsheet.headers.FirstOrDefault(
                        x => x.CsvFileColumnNumber == columnMap.csvFileColumnNumber);

                    header.HeaderName = columnMap.name;
                    header.importAction = columnMap.ImportAction;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Map Column Error");
            }
        }

        var importContactToListRecord = new ImportContactToListRecord
        {
            ImportIndex = 0
        };

        await dbContext.CompanyImportContactToListRecords.AddAsync(importContactToListRecord);
        await dbContext.SaveChangesAsync();

        _logger.LogInformation(
            "[ImportUserProfileToListInBackgroundAsync] Company ID: {CompanyId}. User ID: {UserId}. Import ID: {ImportId}. Import Name: {ImportName}. Record Count: {RecordCount}",
            companyUser.CompanyId,
            companyUser.Id,
            targetImport.Id,
            targetImport.ImportName,
            importSpreadsheet.records.Count);

        var backgroundTask = await _backgroundTaskService.EnqueueImportContactsTask(
            companyUser.IdentityId,
            companyUser.Id,
            companyUser.CompanyId,
            targetImport.Id,
            true,
            importSpreadsheetViewModel.IsTriggerAutomation,
            importSpreadsheet,
            importContactToListRecord.Id);

        return backgroundTask;
    }

    public async Task<BackgroundTask> BulkImportUserProfileInBackgroundAsync(
        Staff companyUser,
        ImportSpreadsheetViewModel importSpreadsheetViewModel)
    {
        var dbContext = _dbContextService.GetDbContext();

        var importSpreadsheet = await _importSpreadsheetService.SerializeSpreadsheet(
            companyUser.Company,
            importSpreadsheetViewModel);

        if (importSpreadsheet.records.Count > 60000)
        {
            throw new Exception($"To protect the system, reject import more than 60000 contacts");
        }

        if (!string.IsNullOrEmpty(importSpreadsheetViewModel.ColumnsMap))
        {
            try
            {
                var columnMaps = JsonConvert.DeserializeObject<List<ColumnMap>>(
                    importSpreadsheetViewModel.ColumnsMap);

                foreach (var columnMap in columnMaps)
                {
                    var header = importSpreadsheet.headers
                        .FirstOrDefault(x => x.CsvFileColumnNumber == columnMap.csvFileColumnNumber);

                    header.HeaderName = columnMap.name;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Map Column Error");
            }
        }

        var newImport = new ImportContactHistory
        {
            CompanyId = companyUser.CompanyId,
            ImportName = importSpreadsheet.ImportName,
            ImportedUserProfiles = new List<ImportedUserProfile>(),
            IsImported = true,
            Status = ImportStatus.Importing,
            ImportedFromId = companyUser.Id,
        };

        var olderList = await dbContext.CompanyImportContactHistories
            .Where(x => x.CompanyId == companyUser.CompanyId)
            .ToListAsync();

        olderList.ForEach(x => x.Order += 1);

        await dbContext.CompanyImportContactHistories.AddAsync(newImport);
        await dbContext.SaveChangesAsync();

        _logger.LogInformation(
            "[BulkImportUserProfileInBackgroundAsync] Company ID: {CompanyId}. User ID: {UserId}. Import ID: {ImportId}. Import Name: {ImportName}. Record Count: {RecordCount}",
            companyUser.CompanyId,
            companyUser.Id,
            newImport.Id,
            newImport.ImportName,
            importSpreadsheet.records.Count);

        var backgroundTask = await _backgroundTaskService.CreateBulkImportContactsTask(
            companyUser.IdentityId,
            companyUser.Id,
            companyUser.CompanyId,
            newImport.Id,
            isImportIntoList: false,
            isTriggerAutomation: false,
            importSpreadsheet);

        return backgroundTask;
    }

    public async Task<BackgroundTask> BulkImportUserProfileToListInBackgroundAsync(
        Staff companyUser,
        ImportIntoListSpreadsheetViewModel importIntoListSpreadsheetViewModel)
    {
        var dbContext = _dbContextService.GetDbContext();

        var importSpreadsheetViewModel = new ImportSpreadsheetViewModel
        {
            ColumnsMap = importIntoListSpreadsheetViewModel.ColumnsMap,
            files = importIntoListSpreadsheetViewModel.Files,
            IsTriggerAutomation = importIntoListSpreadsheetViewModel.IsTriggerAutomation
        };

        var importSpreadsheet = await _importSpreadsheetService.SerializeSpreadsheet(
            companyUser.Company,
            importSpreadsheetViewModel);

        var targetImport = await dbContext.CompanyImportContactHistories
            .FirstOrDefaultAsync(
                x =>
                    x.CompanyId == companyUser.CompanyId
                    && x.Id == importIntoListSpreadsheetViewModel.ListId);

        importSpreadsheet.ImportName = targetImport.ImportName;

        if (!string.IsNullOrEmpty(importSpreadsheetViewModel.ColumnsMap))
        {
            try
            {
                var columnMaps = JsonConvert.DeserializeObject<List<ColumnMap>>(
                    importSpreadsheetViewModel.ColumnsMap);

                foreach (var columnMap in columnMaps)
                {
                    var header = importSpreadsheet.headers.FirstOrDefault(
                        x => x.CsvFileColumnNumber == columnMap.csvFileColumnNumber);

                    header.HeaderName = columnMap.name;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Map Column Error");
            }
        }

        var importContactToListRecord = new ImportContactToListRecord
        {
            ImportIndex = 0
        };

        await dbContext.CompanyImportContactToListRecords.AddAsync(importContactToListRecord);
        await dbContext.SaveChangesAsync();

        _logger.LogInformation(
            "[BulkImportUserProfileToListInBackgroundAsync] Company ID: {CompanyId}. User ID: {UserId}. Import ID: {ImportId}. Import Name: {ImportName}. Record Count: {RecordCount}",
            companyUser.CompanyId,
            companyUser.Id,
            targetImport.Id,
            targetImport.ImportName,
            importSpreadsheet.records.Count);

        var backgroundTask = await _backgroundTaskService.CreateBulkImportContactsTask(
            companyUser.IdentityId,
            companyUser.Id,
            companyUser.CompanyId,
            targetImport.Id,
            true,
            importSpreadsheetViewModel.IsTriggerAutomation,
            importSpreadsheet,
            importContactToListRecord.Id);

        return backgroundTask;
    }

    public async Task<ImportSpreadSheetValidationResult> ValidateImportSpreadsheetAsync(
        Staff companyUser,
        ImportSpreadsheetViewModel importSpreadsheetViewModel)
    {
        var importSpreadSheet = await _importSpreadsheetService.SerializeSpreadsheet(companyUser.Company, importSpreadsheetViewModel);

        var importSpreadSheetValidationResult = await _contactImportUsageService.GetUsageImportSpreadsheetAsync(companyUser.CompanyId, importSpreadSheet);

        return importSpreadSheetValidationResult;
    }

    public async Task<ImportSpreadsheet> PreviewImportSpreadsheet(
        Staff companyUser,
        ImportSpreadsheetViewModel importSpreadsheetViewModel)
    {
        var importSpreadsheet = await _importSpreadsheetService.SerializeSpreadsheet(
            companyUser.Company,
            importSpreadsheetViewModel);

        if (!string.IsNullOrEmpty(importSpreadsheetViewModel.ColumnsMap))
        {
            try
            {
                var columnMaps = JsonConvert.DeserializeObject<List<ColumnMap>>(
                    importSpreadsheetViewModel.ColumnsMap);

                foreach (var columnMap in columnMaps)
                {
                    var header = importSpreadsheet.headers
                        .FirstOrDefault(x => x.CsvFileColumnNumber == columnMap.csvFileColumnNumber);

                    header.HeaderName = columnMap.name;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Map Column Error");
            }
        }

        return importSpreadsheet;
    }

    public async Task<UserProfileNoCompanyResponse> SwitchMessagingChannelAsync(
        Staff companyUser,
        SwitchMessagingChannelRequest request)
    {
        var userProfile = await _userProfileService.SwitchUserProfileMessagingChannel(
            companyUser.CompanyId,
            request.UserProfileId,
            request.ChannelType,
            request.ChannelIdentityId);

        var response = _mapper.Map<UserProfileNoCompanyResponse>(userProfile);

        return response;
    }

    public async Task<UserProfileNoCompanyResponse> SwitchWhatsAppTwilioChannel(
        Staff companyUser,
        string userProfileId,
        ChangeChatAPIInstance chatAPIInstance)
    {
        var userProfile = await _userProfileService.SwitchWhatsappChannel(
            userProfileId,
            chatAPIInstance,
            companyUser.IdentityId);

        var response = _mapper.Map<UserProfileNoCompanyResponse>(userProfile);

        return response;
    }

    public async Task<UserProfileNoCompanyResponse> SwitchWhatsapp360DialogChannelAsync(
        Staff companyUser,
        SwitchChannelChannelRequest request)
    {
        var userProfile = await _userProfileService.SwitchWhatsapp360DialogChannel(
            companyUser.CompanyId,
            request.userProfileId,
            request.ConfigId);

        var response = _mapper.Map<UserProfileNoCompanyResponse>(userProfile);

        return response;
    }

    public async Task<UserProfileNoCompanyResponse> SwitchWhatsappCloudApiChannelAsync(
        Staff companyUser,
        SwitchWhatsappCloudApiChannelRequest request)
    {
        var userProfile = await _userProfileService.SwitchWhatsappCloudApiChannel(
            companyUser.CompanyId,
            request.UserProfileId,
            request.WhatsappChannelPhoneNumber);

        var response = _mapper.Map<UserProfileNoCompanyResponse>(userProfile);

        return response;
    }

    public async Task<List<RemarkResponse>> GetUserProfileAuditLogsAsync(
        Staff companyUser,
        string userProfileId,
        int offset,
        int limit)
    {
        var dbContext = _dbContextService.GetDbContext();

        if (!await dbContext.UserProfiles
                .AnyAsync(
                    x =>
                        x.Id == userProfileId
                        && x.CompanyId == companyUser.CompanyId))
        {
            throw new Exception($"UserProfile: {userProfileId} Not found");
        }

        var auditLogResponses = await _auditHubAuditLogService.GetUserProfileAuditLogsAsync(
            companyUser.CompanyId,
            userProfileId,
            offset,
            limit);

        // var auditLogResponses = await _dbAuditLogService.GetUserProfileRemarksAsync(companyUser.CompanyId, userProfileId, offset, limit);
        return auditLogResponses;
    }

    public async Task<RemarkResponse> AddUserProfileActivityAsync(
        Staff companyUser,
        string userProfileId,
        RemarkViewModel conversationRemarkViewModel)
    {
        var dbContext = _dbContextService.GetDbContext();

        if (!await dbContext.UserProfiles
                .AnyAsync(
                    x =>
                        x.Id == userProfileId
                        && x.CompanyId == companyUser.CompanyId))
        {
            throw new Exception("userProfile not found");
        }

        var responseVm = await _auditHubAuditLogService.CreateStaffManualAddedLogAsync(
            companyUser.CompanyId,
            userProfileId,
            companyUser.IdentityId,
            conversationRemarkViewModel.Remarks,
            null);

        return responseVm;
    }

    // public async Task<RemarkResponse> UpdateUserProfileActivityAsync(Staff companyUser, string remarkId, RemarkViewModel remarkViewModel)
    // {
    //     var userProfileRemark = await dbContext.UserProfileRemarks.FirstOrDefaultAsync(x => x.UserProfile.CompanyId == companyUser.CompanyId && x.RemarkId == remarkId);
    //
    //     if (userProfileRemark == null)
    //         throw new Exception("remark not found");
    //
    //     userProfileRemark.Remarks = remarkViewModel.Remarks;
    //     userProfileRemark.CreatedAt = DateTime.UtcNow;
    //     await dbContext.SaveChangesAsync();
    //
    //     var responseVm = _mapper.Map<RemarkResponse>(userProfileRemark);
    //     await _signalRService.SignalROnRemarksReceived(responseVm);
    //
    //     return responseVm;
    // }
    public async Task<WebClientInfoResponse> GetWebClientTrackingInfoAsync(Staff companyUser, string webClientUuid)
    {
        var dbContext = _dbContextService.GetDbContext();

        var webclientSender = await dbContext.SenderWebClientSenders
            .Include(x => x.IPAddressInfos)
            .FirstOrDefaultAsync(
                x =>
                    x.WebClientUUID == webClientUuid
                    && x.CompanyId == companyUser.CompanyId);

        var response = new WebClientInfoResponse
        {
            OnlineStatus = webclientSender.OnlineStatus.ToString(),
            Results = webclientSender.IPAddressInfos,
            Total = await dbContext.SenderWebClientIPAddressInfos
                .CountAsync(x => x.WebClientSenderId == webclientSender.Id)
        };

        return response;
    }

    public async Task<ShopifyOrderResponse> GetShopifyOrderByUserProfileIdAsync(
        Staff companyUser,
        string userProfileId,
        int offset,
        int limit)
    {
        var dbContext = _dbContextService.GetDbContext();

        var manageableUserProfileId = await dbContext.UserProfiles
            .Where(
                x =>
                    x.CompanyId == companyUser.CompanyId
                    && x.Id == userProfileId)
            .Select(x => x.Id)
            .FirstOrDefaultAsync();

        if (manageableUserProfileId == null)
        {
            throw new Exception($"Unable to get userProfile: {userProfileId}");
        }

        var shopifyOrders = await dbContext.UserProfileShopifyOrders
            .Where(x => x.UserProfileId == manageableUserProfileId)
            .OrderByDescending(x => x.CreatedAt)
            .Skip(offset)
            .Take(limit)
            .ToListAsync();

        var response = new ShopifyOrderResponse
        {
            Results = shopifyOrders,
            Total = await dbContext.UserProfileShopifyOrders
                .CountAsync(x => x.UserProfileId == userProfileId)
        };

        return response;
    }

    public async Task<ShopifyAbandonedCart> GetShopifyAbandonedCartByUserProfileIdAsync(
        Staff companyUser,
        string userProfileId)
    {
        var dbContext = _dbContextService.GetDbContext();

        var manageableUserProfileId = await dbContext.UserProfiles
            .Where(
                x =>
                    x.CompanyId == companyUser.CompanyId
                    && x.Id == userProfileId)
            .Select(x => x.Id)
            .FirstOrDefaultAsync();

        if (manageableUserProfileId == null)
        {
            throw new Exception($"Not found");
        }

        var shopifyOrders = await dbContext.UserProfileShopifyAbandonedCarts
            .Where(
                x =>
                    x.UserProfileId == userProfileId
                    && x.Status == ConversionStatus.NoStatus)
            .OrderByDescending(x => x.Date)
            .FirstOrDefaultAsync();

        return shopifyOrders;
    }

    #region Private method

    private class ImportUserProfileObject
    {
        [JsonIgnore]
        private Dictionary<string, int> HeaderIndexDictionary { get; set; }

        public List<ImportHeader> _headers { get; set; }

        public List<string> _fields { get; set; }

        public ImportUserProfileObject(List<ImportHeader> headers, List<string> fields)
        {
            _headers = headers;
            _fields = fields;
        }

        public string GetValueFromList(string key)
        {
            try
            {
                var index = GetIndexFromHeader(key.ToLower());

                if (index == null)
                {
                    return null;
                }

                return _fields[index.Value].Trim();
            }
            catch (Exception)
            {
                return null;
            }
        }

        private int? GetIndexFromHeader(string key)
        {
            if (HeaderIndexDictionary == null)
            {
                InitHeaderIndexDictionary();
            }

            if (HeaderIndexDictionary.TryGetValue(key, out var index))
            {
                return index;
            }

            return null;
        }

        private void InitHeaderIndexDictionary()
        {
            HeaderIndexDictionary = new();

            if (_headers == null)
            {
                return;
            }

            for (var index = 0; index < _headers.Count; index++)
            {
                var headerName = _headers[index]?.HeaderName?.ToLower();

                if (headerName != null && !HeaderIndexDictionary.ContainsKey(headerName))
                {
                    HeaderIndexDictionary.Add(headerName, index);
                }
            }
        }
    }

    private void AppendOptionsToExcelColumn(
        IDictionary<string, Type> header,
        IList<IDictionary<string, object>> optionSampleDataset,
        string fieldName,
        IReadOnlyList<string> data)
    {
        var optionSampleData = new Dictionary<string, object>
        {
            {
                "Custom Field Name", fieldName
            }
        };

        for (var index = 0; index < data.Count; index++)
        {
            if (index == 0)
            {
                const string firstHeader = "Options";

                if (!optionSampleData.ContainsKey(firstHeader))
                {
                    header[firstHeader] = typeof(string);
                }

                optionSampleData[firstHeader] = data[index];
            }
            else
            {
                var blankHeader = new string(' ', index);

                if (!optionSampleData.ContainsKey(blankHeader))
                {
                    header[blankHeader] = typeof(string);
                }

                optionSampleData[blankHeader] = data[index];
            }
        }

        optionSampleDataset.Add(optionSampleData);
    }

    private void ExecuteCompanyRolePermissions(RolePermission permission, Staff companyUser, UserProfileNoCompanyResponse userProfile)
    {
        if (permission.Permission.IsMaskPhoneNumber)
        {
            var phoneNumber =
                companyUser.Company.CustomUserProfileFields.FirstOrDefault(x => x.FieldName.ToLower() == "phonenumber");

            if (phoneNumber != null)
            {
                var phoneNumberValue = userProfile.CustomFields
                    .FirstOrDefault(x => x.CompanyDefinedFieldId == phoneNumber.Id);

                if (phoneNumberValue != null)
                {
                    phoneNumberValue.Value = PhoneNumberHelper.MaskingPhoneNumber(phoneNumberValue.Value);
                }

                if (!string.IsNullOrEmpty(userProfile.WhatsAppAccount?.phone_number))
                {
                    userProfile.WhatsAppAccount.phone_number = PhoneNumberHelper.MaskingPhoneNumber(userProfile.WhatsAppAccount.phone_number);
                    userProfile.WhatsAppAccount.name = PhoneNumberHelper.MaskingPhoneNumber(userProfile.WhatsAppAccount.name);
                }
            }
        }

        if (permission.Permission.IsMaskEmail)
        {
            var email = companyUser.Company.CustomUserProfileFields.FirstOrDefault(
                x => x.FieldName.ToLower() == "email");

            if (email != null)
            {
                var emailValue = userProfile.CustomFields
                    .FirstOrDefault(x => x.CompanyDefinedFieldId == email.Id);

                if (emailValue != null)
                {
                    emailValue.Value = PhoneNumberHelper.MaskingEmail(emailValue.Value);
                }

                if (!string.IsNullOrEmpty(userProfile.EmailAddress?.Email))
                {
                    userProfile.EmailAddress.Email = PhoneNumberHelper.MaskingEmail(userProfile.EmailAddress.Email);
                    userProfile.EmailAddress.Name = PhoneNumberHelper.MaskingEmail(userProfile.EmailAddress.Name);
                }
            }
        }
    }

    #endregion

    #region rbac methods

    private async Task<SearchUserConversationResponse> RbacSearchUserProfileWithConversationAsync(
        Staff companyUser,
        List<Condition> conditions,
        int offset,
        int limit,
        string status,
        string channels,
        string channelIds,
        string assignedTo,
        long? teamId,
        string version,
        CancellationToken cancellationToken)
    {
        var dbContext = _dbContextService.GetDbContext();

        var searchUserProfileWithConversationCacheKeyPattern = new SearchUserProfileWithConversationCacheKeyPattern(
            companyUser.CompanyId,
            companyUser.IdentityId,
            conditions,
            offset,
            limit,
            status,
            channels,
            channelIds,
            assignedTo,
            teamId,
            version);

        ILockService.Lock myLock = null;

        while (true)
        {
            myLock = await _lockService.AcquireLockAsync(
                $"Searching_SearchUserProfileWithConversationAsync_{searchUserProfileWithConversationCacheKeyPattern.GenerateKeyPattern()}",
                TimeSpan.FromSeconds(5));

            if (myLock == null)
            {
                await Task.Delay(TimeSpan.FromSeconds(5), cancellationToken);
            }
            else
            {
                break;
            }
        }

        var channelNameList = new List<string>();

        if (!string.IsNullOrEmpty(channels))
        {
            channelNameList = channels.Split(",").ToList();
        }

        var channelIdList = new List<string>();

        if (!string.IsNullOrEmpty(channelIds))
        {
            channelIdList = channelIds.Split(",").ToList();
        }

        if (conditions?.Count > 0)
        {
            var condLastChannel = conditions
                .FirstOrDefault(
                    x =>
                        !string.IsNullOrEmpty(x.FieldName)
                        && x.FieldName.ToLower() == "lastchannel"
                        && x.ConditionOperator == SupportedOperator.Contains);

            if (condLastChannel != null)
            {
                foreach (var value in condLastChannel.Values)
                {
                    var set = value.Split(":");

                    if (set.Count() > 1)
                    {
                        if (!channelNameList.Contains(set[0]))
                        {
                            channelNameList.Add(set[0]);
                        }

                        channelIdList.Add(value.Substring(value.IndexOf(":") + 1));
                    }
                }
            }
        }

#if DEBUG
        var stopWatch = Stopwatch.StartNew();
#endif
        var condPhoneNumber = string.Empty;
        var condFullName = string.Empty;

        if (conditions?.Count > 0)
        {
            condPhoneNumber = conditions
                .Where(x => !string.IsNullOrEmpty(x.FieldName))
                .Where(x => x.FieldName.Equals("phonenumber", StringComparison.OrdinalIgnoreCase))
                .Where(x => x.ConditionOperator == SupportedOperator.Contains)
                .Select(x => x.Values.FirstOrDefault())
                .FirstOrDefault();

            condFullName = conditions
                .Where(x => !string.IsNullOrEmpty(x.FieldName))
                .Where(x => x.FieldName.Equals("displayname", StringComparison.OrdinalIgnoreCase))
                .Where(x => x.ConditionOperator == SupportedOperator.Contains)
                .Select(x => x.Values.FirstOrDefault())
                .FirstOrDefault();
        }

        var staff = await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(companyUser);
        var tryingToAccessStaff = InboxAssignedTos.IsUserIdentityIdFilter(assignedTo)
            ? await _accessControlAggregationService.GetStaffAccessControlAggregateAsync(assignedTo, staff.CompanyId)
            : null;

        var conversationIdsQuery = new SearchConversationByContactQueryableBuilder(dbContext, _mentionQueryableResolver)
            .Build(
                companyId: companyUser.CompanyId,
                assignedTo: assignedTo,
                phoneNumber: condPhoneNumber,
                fullName: condFullName,
                status: status,
                teamId: teamId,
                behaviourVersion: version,
                staff,
                tryingToAccessStaff);

        var conversationsIdList = await conversationIdsQuery
            .Skip(offset)
            .Take(limit)
            .Select(x => x.Id)
            .AsNoTracking()
            .ToListAsync(cancellationToken: cancellationToken);

        // Get the mapping related data and map to the model.
        var conversationsList = await dbContext.Conversations
            .Where(x => conversationsIdList.Contains(x.Id) && x.CompanyId == companyUser.CompanyId)
            .Include(x => x.UserProfile)
            .Include(x => x.WhatsappUser)
            .Include(x => x.facebookUser)
            .Include(x => x.WhatsappUser)
            .Include(x => x.Assignee.Identity)
            .Include(x => x.EmailAddress)
            .Include(x => x.WebClient)
            .Include(x => x.WeChatUser)
            .Include(x => x.LineUser)
            .Include(x => x.SMSUser)
            .Include(x => x.ViberUser)
            .Include(x => x.TelegramUser)
            .Include(x => x.TikTokUser)
            .Include(x => x.WhatsApp360DialogUser)
            .Include(x => x.WhatsappCloudApiUser)
            .Include(x => x.ConversationBookmarks)
            .Include(x => x.InstagramUser)
            .AsNoTracking()
            .ToListAsync(cancellationToken: cancellationToken);

        var totalResult = await conversationIdsQuery.CountAsync(cancellationToken: cancellationToken);

#if DEBUG
        stopWatch.Stop();
        _logger.LogInformation($"SearchUserProfileWithConversationAsync Conversation Query: {stopWatch.ElapsedMilliseconds}ms");
#endif

        conversationsList.ForEach(x => x.IsBookmarked = x.ConversationBookmarks.Any(y => y.StaffId == companyUser.Id));

        var filterLastMessage = staff.HasDefaultChannelSettingEnabled();

        var associatedTeams = await dbContext.CompanyStaffTeams
            .Where(x => x.Members.Any(y => y.StaffId == companyUser.Id))
            .AsNoTracking()
            .AsSplitQuery()
            .ToListAsync(cancellationToken: cancellationToken);

        var whatsappIds = new List<string>();
        var twilioSenderIds = new List<string>();
        var whatsapp360dialogDefaultChannelIds = new List<long>();
        var whatsappCloudDefaultChannelIds = new List<string>();

        foreach (var associatedTeam in associatedTeams)
        {
            if (associatedTeam.DefaultChannels?.Count > 0)
            {
                foreach (var defaultChannel in associatedTeam.DefaultChannels
                             .Where(
                                 x =>
                                     x.channel != ChannelTypes.Whatsapp360Dialog
                                     && x.channel != ChannelTypes.WhatsappCloudApi))
                {
                    channelNameList.Add(defaultChannel.channel);

                    if (defaultChannel.ids != null)
                    {
                        foreach (var id in defaultChannel.ids)
                        {
                            var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                            whatsappIds.Add(twilioInstance[0]);

                            if (twilioInstance.Count() > 1)
                            {
                                twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                            }
                        }
                    }
                }

                foreach (var defaultChannel in associatedTeam.DefaultChannels
                             .Where(x => x.channel == ChannelTypes.Whatsapp360Dialog))
                {
                    foreach (var channelId in defaultChannel.ids)
                    {
                        var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);

                        if (validLong)
                        {
                            whatsapp360dialogDefaultChannelIds.Add(whatsapp360dialogChannelId);
                        }
                    }
                }

                foreach (var defaultChannel in associatedTeam.DefaultChannels
                             .Where(x => x.channel == ChannelTypes.WhatsappCloudApi))
                {
                    foreach (var channelId in defaultChannel.ids)
                    {
                        whatsappCloudDefaultChannelIds.Add(channelId);
                    }
                }
            }
        }

        var mappedResult = await _conversationNoCompanyResponseViewModelMapper.ToViewModelsAsync(
            conversationsList);

        var conversationsDict =
            (await _accessControlAggregationService.GetAggregatedConversationsAsync(conversationsList)).ToDictionary(x => x.Id);

        var customFields = await dbContext.UserProfileCustomFields
            .Where(
                y => mappedResult
                    .Select(x => x.UserProfile.Id)
                    .Contains(y.UserProfileId))
            .AsNoTracking()
            .AsSplitQuery()
            .ProjectTo<UserProfileCustomFieldNoOptionsViewModel>(_mapper.ConfigurationProvider)
            .ToListAsync(cancellationToken: cancellationToken);

        var hashtags = await dbContext.ConversationHashtags
            .Where(
                y => mappedResult
                    .Select(x => x.ConversationId)
                    .Contains(y.ConversationId))
            .Include(y => y.Hashtag)
            .AsNoTracking()
            .AsSplitQuery()
            .ProjectTo<ConversationHashtagResponse>(_mapper.ConfigurationProvider)
            .ToListAsync(cancellationToken: cancellationToken);

        mappedResult.ForEach(
            x =>
            {
                x.UserProfile.CustomFields = customFields
                    .Where(y => y.UserProfileId == x.UserProfile.Id)
                    .ToList();

                x.ConversationHashtags = hashtags
                    .Where(y => y.ConversationId == x.ConversationId)
                    .ToList();
            });

        // Disabled the field for performance optimizations to reduce heavy loading and prevent FE not invalidate local cache
        foreach (var result in mappedResult)
        {
            result.AdditionalAssignees = null;
            result.StaffConversationPermission = _rbacConversationPermissionManager.GetStaffConversationPermission(
                staff,
                conversationsDict[result.ConversationId]);
        }

        var selectedUserProfileResponse = new SearchUserConversationResponse
        {
            Conversations = mappedResult,
            TotalResult = totalResult,
        };

        foreach (var conversation in selectedUserProfileResponse.Conversations)
        {
            await _conversationService.GetConversationLastMessage(
                conversation,
                filterLastMessage,
                whatsappIds,
                whatsapp360dialogDefaultChannelIds,
                channelIdList,
                channelNameList,
                twilioSenderIds,
                false,
                whatsappCloudDefaultChannelIds,
                cancellationToken);
        }

        if (myLock != null)
        {
            await _lockService.ReleaseLockAsync(myLock);
        }

        return selectedUserProfileResponse;
    }

    #endregion
}