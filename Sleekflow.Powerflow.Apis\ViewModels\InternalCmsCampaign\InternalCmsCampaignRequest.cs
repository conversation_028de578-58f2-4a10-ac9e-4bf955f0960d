using Newtonsoft.Json;

namespace Sleekflow.Powerflow.Apis.ViewModels.InternalCmsCampaign;

public class GetSentBroadcastCampaignsStatisticsRequest
{
    [JsonProperty("company_ids")]
    public List<string> CompanyIds { get; set; }

    [JsonProperty("company_name")]
    public string CompanyName { get; set; }

    [JsonProperty("start_date")]
    public DateTimeOffset? StartDate { get; set; }

    [JsonProperty("end_date")]
    public DateTimeOffset? EndDate { get; set; }

    [JsonProperty("channels")]
    public List<string> Channels { get; set; }

    [JsonProperty("countries")]
    public List<string> Countries { get; set; }

    [JsonProperty("industries")]
    public List<string> Industries { get; set; }

    [JsonProperty("hubSpotIndustry")]
    public string HubSpotIndustry { get; set; }

    [JsonProperty("offset")]
    public int Offset { get; set; }

    [JsonProperty("limit")]
    public int Limit { get; set; } = 100000;

    [JsonProperty("sort_by")]
    public string SortBy { get; set; } = "sentAt";

    [JsonProperty("sort_order")]
    public string SortOrder { get; set; } = "DESC";
}

