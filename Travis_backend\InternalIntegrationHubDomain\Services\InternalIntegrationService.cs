#nullable enable

using System;
using System.Threading.Tasks;
using Sleekflow.Apis.InternalIntegrationHub.Api;
using Sleekflow.Apis.InternalIntegrationHub.Model;

namespace Travis_backend.InternalIntegrationHubDomain.Services;

public interface IInternalIntegrationService
{
    public Task CreateCustomerAsync(
        string companyId,
        string companyName,
        string companyCountry,
        string? companyOwnerEmail,
        string? companyOwnerPhone,
        string? salesRepEmail);

    public Task UpdateCustomerAsync(
        string companyId,
        string companyName,
        string companyCountry,
        string? companyOwnerEmail,
        string? companyOwnerPhone,
        string? salesRepEmail);

    public Task<string> CreateInvoiceAsync(
        string externalId,
        string companyId,
        decimal subscriptionFee,
        decimal oneTimeSetupFee,
        decimal whatsappCreditAmount,
        DateTime? subscriptionStartDate,
        DateTime? subscriptionEndDate,
        int? paymentTerm,
        string currency = "USD",
        decimal? paidAmount = null,
        DateTime? createdDate = null);

    public Task<string> CreateResellerTopUpInvoiceAsync(
        string externalId,
        string companyId,
        decimal topUpAmount,
        string currency,
        DateTime? createdDate);

    public Task<string> CreateTwilioTopUpInvoiceAsync(
        string externalId,
        string companyId,
        decimal topUpAmount,
        string currency,
        DateTime? createdDate);

    public Task<string> CreatePaymentRecordByInvoiceIdAsync(
        int invoiceId,
        long billRecordId,
        string companyId);
}

public class InternalIntegrationService : IInternalIntegrationService
{
    private readonly INetSuiteInternalApi _netSuiteApi;

    public InternalIntegrationService(INetSuiteInternalApi netSuiteApi)
    {
        _netSuiteApi = netSuiteApi;
    }

    public async Task CreateCustomerAsync(
        string companyId,
        string companyName,
        string companyCountry,
        string? companyOwnerEmail,
        string? companyOwnerPhone,
        string? salesRepEmail)
    {
        var input = new CreateCustomerInput(
            companyId,
            companyName,
            companyCountry,
            companyOwnerEmail,
            companyOwnerPhone,
            salesRepEmail);
        await _netSuiteApi.NetSuiteInternalCreateCustomerPostAsync(createCustomerInput: input);
    }

    public async Task UpdateCustomerAsync(
        string companyId,
        string companyName,
        string companyCountry,
        string? companyOwnerEmail,
        string? companyOwnerPhone,
        string? salesRepEmail)
    {
        var input = new UpdateCustomerInput(
            companyId,
            companyName,
            companyCountry,
            companyOwnerEmail,
            companyOwnerPhone,
            salesRepEmail);
        await _netSuiteApi.NetSuiteInternalUpdateCustomerPostAsync(updateCustomerInput: input);
    }

    public async Task<string> CreateInvoiceAsync(
        string externalId,
        string companyId,
        decimal subscriptionFee,
        decimal oneTimeSetupFee,
        decimal whatsappCreditAmount,
        DateTime? subscriptionStartDate,
        DateTime? subscriptionEndDate,
        int? paymentTerm,
        string currency = "USD",
        decimal? paidAmount = null,
        DateTime? createdDate = null)
    {
        var input = new CreateInvoiceInput(
            externalId,
            companyId,
            subscriptionFee,
            string.Empty,
            oneTimeSetupFee,
            string.Empty,
            whatsappCreditAmount,
            string.Empty,
            subscriptionStartDate,
            subscriptionEndDate,
            paymentTerm,
            currency,
            (double?) paidAmount,
            createdDate);
        var output = await _netSuiteApi.NetSuiteInternalCreateInvoicePostAsync(createInvoiceInput: input);
        return output.Data.ExternalId;
    }

    public async Task<string> CreateResellerTopUpInvoiceAsync(
        string externalId,
        string companyId,
        decimal topUpAmount,
        string currency,
        DateTime? createdDate)
    {
        var input = new CreateResellerTopUpInvoiceInput(
            externalId,
            companyId,
            topUpAmount,
            currency,
            createdDate);
        var output =
            await _netSuiteApi.NetSuiteInternalCreateResellerTopUpInvoicePostAsync(
                createResellerTopUpInvoiceInput: input);
        return output.Data.Message;
    }

    public async Task<string> CreateTwilioTopUpInvoiceAsync(
        string externalId,
        string companyId,
        decimal topUpAmount,
        string currency,
        DateTime? createdDate)
    {
        var input = new CreateTwilioTopUpInvoiceInput(
            externalId,
            companyId,
            topUpAmount,
            currency,
            createdDate);
        var output =
            await _netSuiteApi.NetSuiteInternalCreateTwilioTopUpInvoicePostAsync(createTwilioTopUpInvoiceInput: input);
        return output.Data.Message;
    }

    public async Task<string> CreatePaymentRecordByInvoiceIdAsync(
        int invoiceId,
        long billRecordId,
        string companyId)
    {
        var input = new CreatePaymentRecordByInvoiceIdInput(invoiceId, billRecordId, companyId);
        var output =
            await _netSuiteApi.NetSuiteInternalCreatePaymentRecordByInvoiceIdPostAsync(
                createPaymentRecordByInvoiceIdInput: input);
        return output.Data.Message;
    }
}