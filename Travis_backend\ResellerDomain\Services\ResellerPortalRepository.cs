using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Hangfire;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Stripe.Checkout;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.CommonDomain.Services;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.CoreDomain.Models;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Helpers;
using Travis_backend.InternalDomain.Services;
using Travis_backend.ResellerDomain.Constants;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ResellerDomain.ViewModels;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.TenantHubDomain.Services;

namespace Travis_backend.ResellerDomain.Services;

public interface IResellerPortalRepository
{
    Task<ResponseWrapper> GetUserIdentityReseller(ApplicationUser userIdentity);

    Task<ResellerCompanyProfile> GetResellerOwnProfile(string resellerProfileId);

    Task<ResponseWrapper> CreateClientCompany(
        ApplicationUser userIdentity,
        RegisterClientCompanyViewModel registerCompanyViewModel);

    Task<ResponseWrapper> CreateClientAccount(
        ApplicationUser resellerUser,
        RegisterClientAccountViewModel registerClientAccountViewModel);

    Task<GetResellerClientCompanyDto> GetResellerClientCompanies(string resellerProfileId, GetCompaniesViewModel request);

    Task<ResellerClientCompanyUsageDetails> GetResellerCompanyById(string resellerProfileId, string clientCompanyId);

    Task TopUp(
        string resellerCompanyProfileId,
        string resellerTopUpLogId,
        decimal topUpAmount,
        Session stripeCheckoutSession = null);

    Task<TransactionLogsResponse> GetAllCmsTransactionLogs(string resellerProfileId);

    Task<List<ResellerClientStaffInformation>> GetResellerClientStaffs(string resellerProfileId, string clientCompanyId);

    string GetResellerSubscriptionPlanId(
        SubscriptionTier subscriptionTier,
        ResellerCompanyProfile resellerProfile,
        string changeAddOnType = null);
}

public class ResellerPortalRepository : IResellerPortalRepository
{
    private readonly ApplicationDbContext _appDbContext;
    private readonly IMapper _mapper;
    private readonly ILogger<ResellerPortalRepository> _logger;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly ICompanyUsageService _companyUsageService;
    private readonly ICountryService _countryService;
    private readonly IResellerBaseService _resellerBaseService;
    private readonly IEnabledFeaturesService _enabledFeaturesService;

    private readonly IBillRecordRevenueCalculatorService _billRecordRevenueCalculatorService;

    public ResellerPortalRepository(
        ApplicationDbContext appDbContext,
        IMapper mapper,
        ILogger<ResellerPortalRepository> logger,
        UserManager<ApplicationUser> userManager,
        IAzureBlobStorageService azureBlobStorageService,
        ICompanyUsageService companyUsageService,
        ICountryService countryService,
        IResellerBaseService resellerBaseService,
        IEnabledFeaturesService enabledFeaturesService,
        IBillRecordRevenueCalculatorService billRecordRevenueCalculatorService)
    {
        _appDbContext = appDbContext;
        _mapper = mapper;
        _logger = logger;
        _userManager = userManager;
        _azureBlobStorageService = azureBlobStorageService;
        _companyUsageService = companyUsageService;
        _countryService = countryService;
        _resellerBaseService = resellerBaseService;
        _enabledFeaturesService = enabledFeaturesService;
        _billRecordRevenueCalculatorService = billRecordRevenueCalculatorService;
    }

    public async Task<ResponseWrapper> GetUserIdentityReseller(ApplicationUser userIdentity)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        var resellerCompanyProfileId = await _appDbContext.ResellerStaffs
            .Where(x => x.IdentityId == userIdentity.Id)
            .Select(x => x.ResellerCompanyProfileId)
            .FirstOrDefaultAsync();

        if (string.IsNullOrEmpty(resellerCompanyProfileId))
        {
            response.ErrorMsg = "Identity does not belong to any reseller company staff";
            return response;
        }

        response.IsSuccess = true;
        response.Data = resellerCompanyProfileId;

        return response;
    }

    public async Task<ResellerCompanyProfile> GetResellerOwnProfile(string resellerProfileId)
    {
        return await _appDbContext.ResellerCompanyProfiles
            .AsNoTracking()
            .Include(x => x.ResellerProfileLogo)
            .Include(x => x.Company)
            .Include(x => x.ClientCompanyProfiles)
            .FirstOrDefaultAsync(x => x.Id == resellerProfileId);
    }

    // TODO: Refactor this method after multi data center is implemented
    public async Task<ResponseWrapper> CreateClientCompany(
        ApplicationUser userIdentity,
        RegisterClientCompanyViewModel registerCompanyViewModel)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        var resellerStaff = await _appDbContext.ResellerStaffs
            .Include(x => x.ResellerCompanyProfile)
            .ThenInclude(x => x.Company)
            .FirstOrDefaultAsync(
                x => x.IdentityId == userIdentity.Id &&
                     x.ResellerCompanyProfile.Company.CompanyType == CompanyType.Reseller);

        if (resellerStaff == null)
        {
            response.ErrorMsg = "Identity does not belong to any Reseller Company";

            return response;
        }

        // Check if the reseller has a country tier and subscription plan config, if not then update it.
        resellerStaff.ResellerCompanyProfile =
            await _resellerBaseService.CheckAndUpdateResellerSubscriptionPlanConfig(
                resellerStaff.ResellerCompanyProfile);

        // Check if the subscription plan is valid
        if (registerCompanyViewModel.SubscriptionPlanId != resellerStaff.ResellerCompanyProfile
                .ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId] &&
            registerCompanyViewModel.SubscriptionPlanId != resellerStaff.ResellerCompanyProfile
                .ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionProPlanId] &&
            registerCompanyViewModel.SubscriptionPlanId != resellerStaff.ResellerCompanyProfile
                .ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionPremiumPlanId])
        {
            response.ErrorMsg = "Invalid subscription Id";

            return response;
        }

        // Check if the subscription plan is exist
        var subscriptionPlan = await _appDbContext.CoreSubscriptionPlans
            .Where(x => x.Id == registerCompanyViewModel.SubscriptionPlanId).FirstOrDefaultAsync();

        if (subscriptionPlan == null)
        {
            response.ErrorMsg = "Subscription Plan Not Found";

            return response;
        }

        var fee = Convert.ToDecimal(subscriptionPlan.Amount);
        fee = Math.Round(fee * (1 - resellerStaff.ResellerCompanyProfile.ResellerDiscount), 0) *
              registerCompanyViewModel.Duration;

        if (resellerStaff.ResellerCompanyProfile.Balance - fee <
            resellerStaff.ResellerCompanyProfile.BalanceMinimumLimit)
        {
            response.ErrorMsg =
                $"Insufficient Balance to pay subscription plan id {registerCompanyViewModel.SubscriptionPlanId}, You has reached the Minimum Balance Limit. " +
                $"Balance: {resellerStaff.ResellerCompanyProfile.Balance:0.00}, " +
                $"Minimum Balance Limit: {resellerStaff.ResellerCompanyProfile.BalanceMinimumLimit:0.00}.";

            return response;
        }

        var periodEnd = DateTime.UtcNow.AddMonths(registerCompanyViewModel.Duration);

        var billRecord = new BillRecord
        {
            SubscriptionPlanId = subscriptionPlan.Id,
            PayAmount = Convert.ToDouble(fee),
            Status = BillStatus.Active,
            PaymentStatus = PaymentStatus.Paid,
            PeriodStart = DateTime.UtcNow,
            PeriodEnd = periodEnd,
            PaidByReseller = true,
            quantity = 1
        };

        var company = new Company
        {
            CompanyName = registerCompanyViewModel.CompanyName,
            BillRecords = new List<BillRecord>
            {
                billRecord
            },
            IsFreeTrial = false,
            TimeZoneInfoId = "GMT Standard Time",
            CompanyType = registerCompanyViewModel.CompanyType
        };
        company.StorageConfig = new StorageConfig()
        {
            ContainerName = company.Id
        };

        var customFields = new List<CompanyCustomField>
        {
            new ()
            {
                Category = "CompanyInfo",
                FieldName = "CompanySize",
                Type = FieldDataType.SingleLineText,
                CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                {
                    new ()
                    {
                        DisplayName = "公司人數", Language = "zh-HK"
                    },
                    new ()
                    {
                        DisplayName = "Company Size", Language = "en"
                    }
                },
                Value = registerCompanyViewModel.CompanySize
            },
            new ()
            {
                Category = "CompanyInfo",
                FieldName = "PhoneNumber",
                Type = FieldDataType.SingleLineText,
                CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                {
                    new ()
                    {
                        DisplayName = "公司電話", Language = "zh-HK"
                    },
                    new ()
                    {
                        DisplayName = "Company Phone", Language = "en"
                    }
                },
                Value = registerCompanyViewModel.PhoneNumber
            },
            new ()
            {
                Category = "CompanyInfo",
                FieldName = "CompanyWebsite",
                Type = FieldDataType.SingleLineText,
                CompanyCustomFieldFieldLinguals = new List<CompanyCustomFieldFieldLingual>
                {
                    new ()
                    {
                        DisplayName = "公司網頁", Language = "zh-HK"
                    },
                    new ()
                    {
                        DisplayName = "Company Website", Language = "en"
                    }
                },
                Value = registerCompanyViewModel.CompanyWebsite
            }
        };

        company.CompanyCustomFields = customFields;

        company.SignalRGroupName = company.Id;

        company = AddDefaultUserFiels(company);
        company = AddDefaultQuickReply(company);
        company = AddDefaultLabel(company);
        company = AddDefaultCompanyList(company);

        try
        {
            var phoneNumberUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
            var phoneNumber = phoneNumberUtil.Parse($"+{userIdentity.PhoneNumber}", null);
            var countCode = PhoneNumberHelper.GetCountryCode(phoneNumber);
            var countryName = _countryService.GetCountryEnglishNameByCountryCode(countCode);

            company.CompanyCountry = countryName;
        }
        catch (Exception ex)
        {
            _logger.LogInformation(ex, "Country Ignore: {ErrorMessage}", ex.Message);
        }

        _appDbContext.CompanyCompanies.Add(company);
        await _appDbContext.SaveChangesAsync();

        await _resellerBaseService.SetSubscriptionPlanMaximumUsage(company);

        var resellerClientCompanyProfile = new ResellerClientCompanyProfile()
        {
            ResellerCompanyProfile = resellerStaff.ResellerCompanyProfile,
            ClientCompanyId = company.Id,
            ContactOwnerIdentityId = userIdentity.Id
        };
        await _appDbContext.ResellerClientCompanyProfiles.AddAsync(resellerClientCompanyProfile);
        await _appDbContext.SaveChangesAsync();

        var responseWrapper = await _resellerBaseService.AddResellerStaffsToClientCompanies(
            resellerStaff.ResellerCompanyProfile,
            clientCompany: company);

        if (responseWrapper.IsSuccess)
        {
            var resellerClientStaves = (List<Staff>) responseWrapper.Data;
            var resellerClientStaff = resellerClientStaves.First(x => x.IdentityId == userIdentity.Id);

            var assignmentRule = new AssignmentRule
            {
                AssignmentRuleName = "Default",
                AssignmentType = AssignmentType.SpecificPerson,
                AssignedStaff = resellerClientStaff,
                CompanyId = company.Id
            };
            await _appDbContext.CompanyAssignmentRules.AddAsync(assignmentRule);
        }

        await _appDbContext.SaveChangesAsync();

        resellerStaff.ResellerCompanyProfile.Debited += fee;

        var transactionLog = new ResellerTransactionLog()
        {
            ResellerCompanyProfileId = resellerStaff.ResellerCompanyProfileId,
            Amount = fee,
            Currency = resellerStaff.ResellerCompanyProfile.Currency,
            BillRecordId = billRecord.Id,
            ClientCompanyId = company.Id,
            UserIdentityId = userIdentity.Id,
            TransactionMode = TransactionMode.Debit,
            TransactionCategory = ResellerTransactionCategory.Subscription,
            TransactionAction = "Switch " + _resellerBaseService.SetTransactionAction(subscriptionPlan)
        };

        await _appDbContext.ResellerTransactionLogs.AddAsync(transactionLog);
        await _appDbContext.SaveChangesAsync();

        var clientUserId = string.Empty;

        if (registerCompanyViewModel.RegisterClientCompanyAccountViewModel != null)
        {
            var registerClientAccountViewModel = new RegisterClientAccountViewModel
            {
                ClientCompanyId = company.Id,
                DisplayName = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.DisplayName,
                Email = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.Email,
                FirstName = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.FirstName,
                LastName = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.LastName,
                PhoneNumber = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.PhoneNumber,
                RoleType = StaffUserRole.Admin,
                Username = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.Username,
                Password = registerCompanyViewModel.RegisterClientCompanyAccountViewModel.Password
            };

            var resellerStaffResponse =
                await CreateClientAccount(
                    userIdentity,
                    registerClientAccountViewModel);

            if (!resellerStaffResponse.IsSuccess)
            {
                _logger.LogWarning(
                    "Reseller Client Company {ResellerClientCompanyId} Staff {ResellerClientUsername} creation error: {ErrorMessage}",
                    company.Id,
                    registerCompanyViewModel.RegisterClientCompanyAccountViewModel.Username,
                    resellerStaffResponse.ErrorMsg);
            }

            clientUserId = resellerStaffResponse.Data.ToString();
        }

        // Feature Flag related
        try
        {
            await _enabledFeaturesService.UpdateDefaultFeatureFlagForCompanyBasedOnSubscriptionTier(
                registerCompanyViewModel.SubscriptionPlanId,
                company.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating feature flag for company");
        }

        // HubSpot
        BackgroundJob.Schedule<IInternalHubSpotService>(
            x => x.CreateNewResellerSignupCompanyAndStaff(
                company.Id),
            TimeSpan.FromMinutes(2));

        // Sync PartnerStack Customer Key From HubSpot Contact
        BackgroundJob.Schedule<IInternalPartnerStackService>(
            x => x.SyncPartnerStackCustomerKeyFromHubSpotContact(
                company.Id,
                registerCompanyViewModel.RegisterClientCompanyAccountViewModel.Email),
            TimeSpan.FromMinutes(4));

        response.IsSuccess = true;

        response.Data = new ResellerClientInformation
        {
            ResellerClientId = resellerClientCompanyProfile.Id,
            ResellerProfileId = resellerStaff.ResellerCompanyProfileId,
            ClientCompanyId = company.Id,
            ClientCompanyName = company.CompanyName,
            ClientUserId = clientUserId,
            SubscriptionPlans = _mapper.Map<List<SubscriptionPlanDto>>(
                new List<SubscriptionPlan>
                {
                    await _appDbContext.CoreSubscriptionPlans.FirstOrDefaultAsync(
                        x => x.Id == billRecord.SubscriptionPlanId)
                })
        };

        return response;
    }

    // TODO: Refactor this method after multi data center is implemented
    public async Task<ResponseWrapper> CreateClientAccount(
        ApplicationUser resellerUser,
        RegisterClientAccountViewModel registerClientAccountViewModel)
    {
        var response = new ResponseWrapper()
        {
            IsSuccess = false
        };

        var resellerCompany = await _appDbContext.ResellerStaffs.Include(x => x.ResellerCompanyProfile)
            .ThenInclude(x => x.Company).Where(x => x.IdentityId == resellerUser.Id)
            .Select(x => x.ResellerCompanyProfile.Company).FirstOrDefaultAsync();

        var clientCompanyProfile = await _appDbContext.ResellerClientCompanyProfiles
            .Include(x => x.ClientCompany)
            .FirstOrDefaultAsync(
                x => x.ClientCompanyId == registerClientAccountViewModel.ClientCompanyId
                     && resellerCompany.Id == x.ResellerCompanyProfile.CompanyId);

        if (clientCompanyProfile == null)
        {
            response.ErrorMsg = "Client Company Profile not found";

            return response;
        }

        var existingUser = await _userManager.FindByEmailAsync(registerClientAccountViewModel.Email);
        if (existingUser != null)
        {
            response.ErrorMsg = "user email already taken";

            return response;
        }

        existingUser = await _userManager.FindByNameAsync(registerClientAccountViewModel.Username);
        if (existingUser != null)
        {
            response.ErrorMsg = "username already taken";

            return response;
        }

        if (!_resellerBaseService.ValidatePassword(registerClientAccountViewModel.Password))
        {
            response.ErrorMsg =
                "Password must be at least 8 characters long, contain at least one uppercase letter, one lowercase letter, one number and one special character";

            return response;
        }

        // Check the maximum agents limit is reached or not
        var resellerCompanyProfileId = await _appDbContext.ResellerStaffs
            .AsNoTracking()
            .Where(x => x.IdentityId == resellerUser.Id)
            .Select(x => x.ResellerCompanyProfileId)
            .FirstOrDefaultAsync();

        var totalAgents =
            await _appDbContext.UserRoleStaffs.CountAsync(
                staff => staff.CompanyId == clientCompanyProfile.ClientCompanyId && !_appDbContext.ResellerStaffs
                    .Where(
                        rs => rs.ResellerCompanyProfileId ==
                              resellerCompanyProfileId)
                    .Select(rs => rs.IdentityId).ToList().Contains(staff.IdentityId));

        var companyUsage = await _companyUsageService.GetCompanyUsage(registerClientAccountViewModel.ClientCompanyId);

        if (totalAgents >= companyUsage.MaximumAgents)
        {
            response.ErrorMsg = "Maximum agents reached";
            return response;
        }

        var userIdentity = _mapper.Map<ApplicationUser>(registerClientAccountViewModel);

        if (string.IsNullOrEmpty(userIdentity.UserName))
        {
            userIdentity.UserName = registerClientAccountViewModel.Email;
        }

        userIdentity.UserRole = "staff";
        userIdentity.EmailConfirmed = true;

        var identityResult = await _userManager.CreateAsync(userIdentity, registerClientAccountViewModel.Password);

        if (identityResult.Succeeded)
        {
            await _appDbContext.UserRoleStaffs.AddAsync(
                new Staff
                {
                    IdentityId = userIdentity.Id,
                    Company = clientCompanyProfile.ClientCompany,
                    Locale = "zh-hk",
                    NotificationSettingId = 1,
                    RoleType = registerClientAccountViewModel.RoleType
                });

            await _appDbContext.SaveChangesAsync();

            await _resellerBaseService.AddResellerActivityLog(
                new ResellerActivityLog
                {
                    ResellerCompanyProfileId = resellerCompanyProfileId,
                    CompanyId = registerClientAccountViewModel.ClientCompanyId,
                    CreatedByUserId = resellerUser.Id,
                    Category = ResellerActivityLogCategory.User,
                    Action = $"Create New User - {registerClientAccountViewModel.Username}"
                });

            // HubSpot
            BackgroundJob.Enqueue<IInternalHubSpotService>(
                x => x.SyncCompanyStaffs(registerClientAccountViewModel.ClientCompanyId));

            response.IsSuccess = true;
            response.Data = userIdentity.Id;

            return response;
        }

        _logger.LogError(
            "Client Account username {Username}, email {Email} cannot be created with error : {CreateUserErrors}",
            registerClientAccountViewModel.Username,
            registerClientAccountViewModel.Email,
            JsonConvert.SerializeObject(identityResult.Errors));

        if (identityResult.Errors.Any(
                x => x.Code == _userManager.ErrorDescriber.DuplicateUserName(userIdentity.UserName).Code))
        {
            response.ErrorMsg = "username already taken";
            return response;
        }

        if (identityResult.Errors.Any(
                x => userIdentity.Email != null && x.Code == _userManager.ErrorDescriber.DuplicateEmail(userIdentity.Email).Code))
        {
            response.ErrorMsg = "user email already taken";
            return response;
        }

        response.ErrorMsg = $"Client Account cannot be created with error : {identityResult.Errors}";
        return response;
    }

    public async Task<TransactionLogsResponse> GetAllCmsTransactionLogs(string resellerProfileId)
    {
        var resellerProfile = await _appDbContext.ResellerCompanyProfiles
            .AsNoTracking()
            .AsSplitQuery()
            .Include(x => x.ClientCompanyProfiles)
            .FirstOrDefaultAsync(x => x.Id == resellerProfileId);

        if (resellerProfile == null)
        {
            return null;
        }

        var resellerTransactionLogs = await _appDbContext.ResellerTransactionLogs
            .AsNoTracking()
            .OrderByDescending(x => x.CreatedAt)
            .Where(x => x.ResellerCompanyProfileId == resellerProfileId)
            .ToListAsync();

        var billRecordIds = resellerTransactionLogs.Where(x => x.BillRecordId != null).Select(x => x.BillRecordId)
            .ToList();
        var billRecords = await _appDbContext.CompanyBillRecords
            .Where(x => billRecordIds.Contains(x.Id))
            .ProjectTo<BillRecordDto>(_mapper.ConfigurationProvider)
            .ToListAsync();

        var clientCompanyIds = resellerTransactionLogs.Select(x => x.ClientCompanyId).Distinct().ToList();
        var clientCompanies = await _appDbContext.CompanyCompanies
            .Where(x => clientCompanyIds.Contains(x.Id))
            .Select(
                x => new
                {
                    x.Id,
                    x.CompanyName
                })
            .ToListAsync();

        var userIdentityIds = resellerTransactionLogs.Where(x => x.UserIdentityId != null).Select(x => x.UserIdentityId)
            .ToList();
        var userInfoResponses = await _appDbContext.Users.Where(x => userIdentityIds.Contains(x.Id))
            .ProjectTo<UserInfoResponse>(_mapper.ConfigurationProvider).ToListAsync();

        var transactionLogDtos = new List<TransactionLogDto>();
        foreach (var transactionLog in resellerTransactionLogs)
        {
            var transactionLogDto = _mapper.Map<TransactionLogDto>(transactionLog);

            if (transactionLog.BillRecordId != null)
            {
                transactionLogDto.BillRecordDto = billRecords.Find(x => x.Id == transactionLog.BillRecordId);
            }

            if (!string.IsNullOrEmpty(transactionLog.ClientCompanyId))
            {
                transactionLogDto.ClientCompanyName =
                    clientCompanies.Find(x => x.Id == transactionLog.ClientCompanyId).CompanyName;
            }

            if (!string.IsNullOrEmpty(transactionLog.UserIdentityId))
            {
                transactionLogDto.UserIdentity =
                    userInfoResponses.Find(x => x.Id == transactionLog.UserIdentityId);
            }

            transactionLogDtos.Add(transactionLogDto);
        }

        var transactionLogResponse = new TransactionLogsResponse()
        {
            TransactionLogs = transactionLogDtos,
            TransactionLogsNumber = resellerTransactionLogs.Count
        };

        return transactionLogResponse;
    }

    public async Task<GetResellerClientCompanyDto> GetResellerClientCompanies(
        string resellerProfileId,
        GetCompaniesViewModel request)
    {
        var getCompanyDto = new GetResellerClientCompanyDto
        {
            ClientCompanyNumber = 0,
            ClientCompanies = []
        };

        var clientCompanyIds = await _appDbContext.ResellerCompanyProfiles
            .AsNoTracking()
            .Include(x => x.ClientCompanyProfiles)
            .Where(x => x.Id == resellerProfileId)
            .Select(x => x.ClientCompanyProfiles.Select(ccp => ccp.ClientCompanyId).ToList())
            .FirstOrDefaultAsync();

        if (clientCompanyIds.Count == 0)
        {
            return getCompanyDto;
        }

        var resellerStaffs = _appDbContext.ResellerStaffs
            .AsNoTracking()
            .Include(x => x.ProfilePicture)
            .Where(x => x.ResellerCompanyProfileId == resellerProfileId);

        var usageList = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .AsSplitQuery()
            .Include(x => x.BillRecords)
            .Include(x => x.Staffs)
            .ThenInclude(x => x.Identity)
            .Include(x => x.UserProfiles)
            .Where(company => clientCompanyIds.Contains(company.Id))
            .OrderBy(x => x.CreatedAt)
            .Select(
                x => new ResellerClientCompanyUsageDetails
                {
                    ClientCompanyId = x.Id,
                    ClientCompanyName = x.CompanyName,
                    CreatedAt = x.CreatedAt,
                    CurrentSubscriptionPlan = x.BillRecords
                        .OrderByDescending(billRecord => billRecord.created)
                        .ThenByDescending(billRecord => billRecord.PayAmount)
                        .Where(
                            billRecord => ValidSubscriptionPlan.SubscriptionPlan.Contains(
                                              billRecord.SubscriptionPlanId) &&
                                          billRecord.Status != BillStatus.Inactive &&
                                          billRecord.Status != BillStatus.Terminated &&
                                          billRecord.PeriodStart <= DateTime.UtcNow &&
                                          billRecord.PeriodEnd > DateTime.UtcNow)
                        .Select(billRecord => _mapper.Map<SubscriptionPlanDto>(billRecord.SubscriptionPlan))
                        .FirstOrDefault(),
                    TotalAgents =
                        x.Staffs.Count(
                            staff => !resellerStaffs.Select(rs => rs.IdentityId).Contains(staff.IdentityId)),
                    TotalContacts =
                        x.UserProfiles.Count(
                            userProfile => userProfile.ActiveStatus == ActiveStatus.Active),
                    TotalWhatsappInstance = x.WhatsappCloudApiConfigs.Count,
                    ContactOwner = x.Staffs
                        .Where(
                            staff => staff.RoleType == StaffUserRole.Admin &&
                                     !resellerStaffs.Select(rs => rs.IdentityId).Contains(staff.IdentityId))
                        .Select(
                            staff => new ResellerClientStaffInformation()
                            {
                                Username = staff.Identity.UserName,
                                Email = staff.Identity.Email,
                                PhoneNumber = staff.Identity.PhoneNumber,
                                FirstName = staff.Identity.FirstName,
                                LastName = staff.Identity.LastName,
                                DisplayName = staff.Identity.DisplayName,
                                RoleType = staff.RoleType,
                                CreatedAt = staff.Identity.CreatedAt
                            })
                        .FirstOrDefault(),
                    Assignee = resellerStaffs
                        .Where(
                            rs =>
                                rs.IdentityId == x.ResellerClientCompanyProfile.AssigneeIdentityId)
                        .Select(
                            rs => new ResellerStaffInformation()
                            {
                                Id = rs.Id,
                                IdentityId = rs.IdentityId,
                                UserInfo =
                                    _mapper.Map<UserInfoResponse>(
                                        _appDbContext.Users.FirstOrDefault(
                                            user => user.Id == rs.IdentityId)),
                                ProfilePictureURL = rs.ProfilePicture != null
                                    ? _azureBlobStorageService.GetAzureBlobSasUri(
                                        rs.ProfilePicture.Filename,
                                        rs.ProfilePicture.BlobContainer,
                                        1)
                                    : null
                            })
                        .FirstOrDefault(),
                    CompanyStatus = x.IsDeleted ? "Deleted" : "Active",
                    MonthlyRecurringRevenue = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                        x.BillRecords.Where(b => b.PeriodStart <= DateTime.UtcNow && b.Status != BillStatus.Inactive)
                            .OrderByDescending(br => br.created)
                            .ToList(),
                        x.TimeZoneInfoId,
                        default),
                    TimeZoneInfoId = x.TimeZoneInfoId
                })
            .ToListAsync();

        // Renew the subscription plan if it is expired and the company is not deleted
        foreach (var companyUsageDetail in usageList.Where(companyUsageDetail => companyUsageDetail.CurrentSubscriptionPlan == null &&
                     companyUsageDetail.CompanyStatus != "Deleted"))
        {
            await _companyUsageService.GetBillingPeriodUsages(companyUsageDetail.ClientCompanyId);

            var billRecords = _appDbContext.CompanyBillRecords
                .AsNoTracking()
                .OrderByDescending(billRecord => billRecord.created)
                .ThenByDescending(billRecord => billRecord.PayAmount)
                .Where(
                    billRecord => billRecord.CompanyId == companyUsageDetail.ClientCompanyId);

            companyUsageDetail.CurrentSubscriptionPlan =
                await billRecords
                    .Where(
                        billRecord => ValidSubscriptionPlan.SubscriptionPlan.Contains(
                                          billRecord.SubscriptionPlanId) &&
                                      billRecord.Status != BillStatus.Inactive &&
                                      billRecord.Status != BillStatus.Terminated &&
                                      billRecord.PeriodStart <= DateTime.UtcNow &&
                                      billRecord.PeriodEnd > DateTime.UtcNow)
                    .Select(billRecord => _mapper.Map<SubscriptionPlanDto>(billRecord.SubscriptionPlan))
                    .FirstOrDefaultAsync();

            companyUsageDetail.MonthlyRecurringRevenue = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                await billRecords
                    .Where(
                        br => br.PeriodStart <= DateTime.UtcNow &&
                              br.Status != BillStatus.Inactive)
                    .OrderByDescending(br => br.created)
                    .ToListAsync(),
                companyUsageDetail.TimeZoneInfoId);
        }

        getCompanyDto.ClientCompanyNumber = usageList.Count;

        usageList = request.Limit == -1
            ? usageList
            : usageList.Skip(request.Offset).Take(request.Limit).ToList();

        getCompanyDto.ClientCompanies = usageList;
        return getCompanyDto;
    }

    public async Task<ResellerClientCompanyUsageDetails> GetResellerCompanyById(string resellerProfileId, string clientCompanyId)
    {
        var companyUsage = await _companyUsageService.GetCompanyUsage(clientCompanyId);

        var resellerStaffs = _appDbContext.ResellerStaffs
            .AsNoTracking()
            .Include(x => x.ProfilePicture)
            .Where(x => x.ResellerCompanyProfileId == resellerProfileId);

        var usage = await _appDbContext.CompanyCompanies
            .AsNoTracking()
            .AsSplitQuery()
            .Include(x => x.Staffs)
            .ThenInclude(x => x.Identity)
            .Include(x => x.UserProfiles)
            .Include(x => x.BillRecords)
            .ThenInclude(x => x.SubscriptionPlan)
            .Include(x => x.ResellerClientCompanyProfile)
            .Include(x => x.CompanyIconFile)
            .Where(x => x.Id == clientCompanyId && x.CompanyType == CompanyType.ResellerClient)
            .Select(
                x => new ResellerClientCompanyUsageDetails
                {
                    ClientCompanyId = x.Id,
                    ClientCompanyName = x.CompanyName,
                    ClientProfileId = x.ResellerClientCompanyProfile.Id,
                    ClientCompanyLogoLink =
                        x.CompanyIconFile != null
                            ? _azureBlobStorageService.GetAzureBlobSasUri(
                                x.CompanyIconFile.Filename,
                                x.CompanyIconFile.BlobContainer,
                                24)
                            : null,
                    CreatedAt = x.CreatedAt,
                    CurrentSubscriptionPlan =
                        x.BillRecords
                            .OrderByDescending(billRecord => billRecord.created)
                            .ThenByDescending(billRecord => billRecord.PayAmount)
                            .Where(
                                billRecord => ValidSubscriptionPlan.SubscriptionPlan.Contains(
                                                  billRecord.SubscriptionPlanId) &&
                                              billRecord.Status != BillStatus.Inactive &&
                                              billRecord.Status != BillStatus.Terminated &&
                                              billRecord.PeriodStart <= DateTime.UtcNow &&
                                              billRecord.PeriodEnd > DateTime.UtcNow)
                            .Select(billRecord => _mapper.Map<SubscriptionPlanDto>(billRecord.SubscriptionPlan))
                            .FirstOrDefault(),
                    BillRecords = _mapper.Map<List<BillRecordDto>>(x.BillRecords),
                    TotalAgents =
                        x.Staffs.Count(
                            staff => !resellerStaffs.Select(rs => rs.IdentityId).Contains(staff.IdentityId)),
                    TotalContacts =
                        x.UserProfiles.Count(
                            userProfile => userProfile.ActiveStatus == ActiveStatus.Active),
                    TotalWhatsappInstance = x.WhatsappCloudApiConfigs.Count,
                    WhatsAppConfigCount = x.WhatsAppConfigs.Count,
                    WhatsApp360DialogConfigCount = x.WhatsApp360DialogConfigs.Count,
                    InstagramConfigCount = x.InstagramConfigs.Count,
                    FacebookConfigCount = x.FacebookConfigs.Count,
                    WebClientSenderCount = x.WebClientSenders.Any() ? 1 : 0,
                    LineConfigCount = x.LineConfigs.Count,
                    SMSConfigCount = x.SMSConfigs.Count,
                    TelegramConfigCount = x.TelegramConfigs.Count,
                    ViberConfigCount = x.ViberConfigs.Count,
                    WeChatConfigCount = x.WeChatConfig != null ? 1 : 0,
                    EmailConfigCount = x.EmailConfig != null ? 1 : 0,
                    ShoplineConfigCount = x.ShoplineConfigs.Count,
                    ShopifyConfigCount = x.ShopifyConfigs.Count,
                    MaximumContacts = companyUsage.MaximumContacts,
                    MaximumAgents = companyUsage.MaximumAgents,
                    MaximumWhatsappInstance = companyUsage.MaximumNumberOfWhatsappCloudApiChannels,
                    ContactOwner = x.Staffs
                        .Where(
                            staff => staff.RoleType == StaffUserRole.Admin &&
                                     !resellerStaffs.Select(rs => rs.IdentityId).Contains(staff.IdentityId))
                        .Select(
                            staff => new ResellerClientStaffInformation()
                            {
                                Username = staff.Identity.UserName,
                                Email = staff.Identity.Email,
                                PhoneNumber = staff.Identity.PhoneNumber,
                                FirstName = staff.Identity.FirstName,
                                LastName = staff.Identity.LastName,
                                DisplayName = staff.Identity.DisplayName,
                                RoleType = staff.RoleType,
                                CreatedAt = staff.Identity.CreatedAt
                            })
                        .FirstOrDefault(),
                    Assignee = resellerStaffs
                        .Where(
                            resellerStaff =>
                                resellerStaff.IdentityId == x.ResellerClientCompanyProfile.AssigneeIdentityId).Select(
                            resellerStaff => new ResellerStaffInformation()
                            {
                                Id = resellerStaff.Id,
                                IdentityId = resellerStaff.IdentityId,
                                UserInfo =
                                    _mapper.Map<UserInfoResponse>(
                                        _appDbContext.Users.FirstOrDefault(
                                            user => user.Id == resellerStaff.IdentityId)),
                                ProfilePictureURL = resellerStaff.ProfilePicture != null
                                    ? _azureBlobStorageService.GetAzureBlobSasUri(
                                        resellerStaff.ProfilePicture.Filename,
                                        resellerStaff.ProfilePicture.BlobContainer,
                                        1)
                                    : null
                            })
                        .FirstOrDefault(),
                    CompanyStatus = x.IsDeleted ? "Deleted" : "Active",
                    MonthlyRecurringRevenue = _billRecordRevenueCalculatorService.SumMonthlyRecurringRevenueWithTimezone(
                        x.BillRecords.Where(b => b.PeriodStart <= DateTime.UtcNow && b.Status != BillStatus.Inactive)
                            .OrderByDescending(br => br.created)
                            .ToList(),
                        x.TimeZoneInfoId,
                        default)
                })
            .FirstOrDefaultAsync();

        return usage;
    }

    public async Task TopUp(
        string resellerCompanyProfileId,
        string resellerTopUpLogId,
        decimal topUpAmount,
        Session stripeCheckoutSession = null)
    {
        var isValid = long.TryParse(resellerTopUpLogId, out var logId);

        if (!isValid)
        {
            return;
        }

        var transactionLog = await _appDbContext.ResellerTransactionLogs
            .Include(x => x.ResellerCompanyProfile)
            .FirstOrDefaultAsync(
                x => x.Id == logId && x.ResellerCompanyProfileId == resellerCompanyProfileId &&
                     x.TopUpMethod == ResellerTopUpMethod.Stripe);

        if (transactionLog == null)
        {
            return;
        }

        if (transactionLog.TopupStatus is TopupStatus.Redeemed or TopupStatus.Cancelled)
        {
            return;
        }

        transactionLog.Amount = topUpAmount;
        transactionLog.InvoiceId = stripeCheckoutSession?.Id;
        transactionLog.CustomerEmail = stripeCheckoutSession?.CustomerEmail;
        transactionLog.CustomerId = stripeCheckoutSession?.CustomerId;
        transactionLog.TopupStatus = TopupStatus.Redeemed;
        transactionLog.UpdatedAt = DateTime.UtcNow;
        transactionLog.ResellerCompanyProfile.TopUp += topUpAmount;
        transactionLog.ResellerCompanyProfile.UpdatedAt = DateTime.UtcNow;
        await _appDbContext.SaveChangesAsync();

        await _resellerBaseService.AddResellerActivityLog(
            new ResellerActivityLog
            {
                ResellerCompanyProfileId = transactionLog.ResellerCompanyProfileId,
                CompanyId = stripeCheckoutSession?.Metadata["resellerCompanyId"],
                CreatedByUserId = stripeCheckoutSession?.Metadata["resellerUserId"],
                Category = ResellerActivityLogCategory.Balance,
                Action = $"Top Up Adjustment: {topUpAmount}"
            });
    }

    public async Task<List<ResellerClientStaffInformation>> GetResellerClientStaffs(
        string resellerProfileId,
        string clientCompanyId)
    {
        var resellerStaffIds = await _appDbContext.ResellerStaffs
            .Where(x => x.ResellerCompanyProfileId == resellerProfileId)
            .Select(x => x.IdentityId)
            .ToListAsync();

        var clientCompanyStaffs = await _appDbContext.UserRoleStaffs
            .Where(x => x.CompanyId == clientCompanyId && !resellerStaffIds.Contains(x.IdentityId))
            .Include(x => x.Identity)
            .ToListAsync();

        var resellerClientStaffs = clientCompanyStaffs.Where(x => x.Identity != null)
            .Select(
                clientCompanyStaff => new ResellerClientStaffInformation()
                {
                    Username = clientCompanyStaff.Identity.UserName,
                    Email = clientCompanyStaff.Identity.Email,
                    PhoneNumber = clientCompanyStaff.Identity.PhoneNumber,
                    FirstName = clientCompanyStaff.Identity.FirstName,
                    LastName = clientCompanyStaff.Identity.LastName,
                    DisplayName = clientCompanyStaff.Identity.DisplayName,
                    RoleType = clientCompanyStaff.RoleType,
                    CreatedAt = clientCompanyStaff.Identity.CreatedAt
                })
            .ToList();

        return resellerClientStaffs;
    }

    private async Task<List<CustomUserProfileFieldOption>> GetCountryList()
    {
        var result = await _appDbContext.CoreCountries.ToListAsync();

        if (result.Count > 0)
        {
            var response = _mapper.Map<List<CustomUserProfileFieldOption>>(result);

            return response;
        }
        else
        {
            List<Country> cultureList = new List<Country>();

            CultureInfo[] getCultureInfo = CultureInfo.GetCultures(CultureTypes.SpecificCultures);

            foreach (CultureInfo getCulture in getCultureInfo)
            {
                try
                {
                    RegionInfo getRegionInfo = new RegionInfo(getCulture.LCID);

                    if (!cultureList.Select(x => x.EnglishName).Contains(getRegionInfo.EnglishName))
                    {
                        cultureList.Add(
                            new Country
                            {
                                Id = getRegionInfo.TwoLetterISORegionName,
                                EnglishName = getRegionInfo.EnglishName,
                                DisplayName = getRegionInfo.DisplayName
                            });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogInformation(ex, "Error Message: {ErrorMessage}", ex.Message);
                }
            }

            await _appDbContext.CoreCountries.AddRangeAsync(cultureList);
            await _appDbContext.SaveChangesAsync();

            var response = _mapper.Map<List<CustomUserProfileFieldOption>>(cultureList);

            return response;
        }
    }

    private Company AddDefaultLabel(Company company)
    {
        company.CompanyHashtags = new List<CompanyHashtag>();

        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "New customer",
                HashTagColor = HashTagColor.Cyan
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "New order",
                HashTagColor = HashTagColor.Yellow
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "Pending payment",
                HashTagColor = HashTagColor.Pink
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "Paid",
                HashTagColor = HashTagColor.Purple
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "Order complete",
                HashTagColor = HashTagColor.Green
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "VIP",
                HashTagColor = HashTagColor.Blue
            });
        company.CompanyHashtags.Add(
            new CompanyHashtag
            {
                Hashtag = "Issue",
                HashTagColor = HashTagColor.Red
            });

        return company;
    }

    private Company AddDefaultUserFiels(Company company)
    {
        List<CompanyCustomUserProfileField> companyCustomUserProfiles = new List<CompanyCustomUserProfileField>()
        {
            new CompanyCustomUserProfileField
            {
                FieldName = "Labels",
                Type = FieldDataType.Labels,
                Order = 0,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Labels", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "標籤", Language = "zh-hk"
                    }
                },
                IsDeletable = false,
                IsEditable = false,
                IsDefault = true,
                FieldsCategory = FieldsCategory.Segmentation
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Lists",
                Type = FieldDataType.Lists,
                Order = 1,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Lists", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "名單", Language = "zh-hk"
                    }
                },
                IsDeletable = false,
                IsEditable = false,
                IsDefault = true,
                FieldsCategory = FieldsCategory.Segmentation
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Email",
                Type = FieldDataType.Email,
                Order = 2,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Email", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "電郵地址", Language = "zh-hk"
                    }
                },
                IsDeletable = false
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "PhoneNumber",
                Type = FieldDataType.PhoneNumber,
                Order = 3,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Phone Number", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "電話號碼", Language = "zh-hk"
                    }
                },
                IsDeletable = false
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "CompanyName",
                Type = FieldDataType.SingleLineText,
                Order = 4,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Company Name", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "公司名稱", Language = "zh-hk"
                    }
                }
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "JobTitle",
                Type = FieldDataType.SingleLineText,
                Order = 5,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Job Title", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "職稱", Language = "zh-hk"
                    }
                }
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "ContactOwner",
                Type = FieldDataType.TravisUser,
                Order = 6,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Contact Owner", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "聯絡負責人", Language = "zh-hk"
                    }
                },
                IsDeletable = false,
                IsEditable = true,
                IsDefault = true,
                FieldsCategory = FieldsCategory.SleekFlowUser
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LeadStage",
                Type = FieldDataType.Options,
                Order = 7,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Lead Stage", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "階段", Language = "zh-hk"
                    }
                },
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
                {
                    new CustomUserProfileFieldOption
                    {
                        Value = "Prospect",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Prospect", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Prospect", Language = "zh-HK"
                            }
                        },
                        Order = 0
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Contacted",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Contacted", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Contacted", Language = "zh-HK"
                            }
                        },
                        Order = 1
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Lead",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Lead", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Lead", Language = "zh-HK"
                            }
                        },
                        Order = 2
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Opportunity",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Opportunity", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Opportunity", Language = "zh-HK"
                            }
                        },
                        Order = 3
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Customer",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Customer", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Customer", Language = "zh-HK"
                            }
                        },
                        Order = 4
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Lost",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Lost", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Lost", Language = "zh-HK"
                            }
                        },
                        Order = 5
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Inactive",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Inactive / to be reviewed", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Inactive / to be reviewed", Language = "zh-HK"
                            }
                        },
                        Order = 6
                    }
                },
                IsDeletable = false,
                IsEditable = true,
                IsDefault = true
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LeadSource",
                Type = FieldDataType.Options,
                Order = 8,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Lead Source", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "資料來源", Language = "zh-hk"
                    }
                },
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
                {
                    new CustomUserProfileFieldOption
                    {
                        Value = "Organic search",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Organic search", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "網站", Language = "zh-HK"
                            }
                        },
                        Order = 0
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Referrals",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Referrals", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "推薦", Language = "zh-HK"
                            }
                        },
                        Order = 1
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Social media",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Social media", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "社交媒體", Language = "zh-HK"
                            }
                        },
                        Order = 2
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Email marketing",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Email marketing", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "社交媒體", Language = "zh-HK"
                            }
                        },
                        Order = 3
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Paid search",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Paid search", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "付費搜索", Language = "zh-HK"
                            }
                        },
                        Order = 4
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Paid social",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Paid social", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "付費社交媒體", Language = "zh-HK"
                            }
                        },
                        Order = 5
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Direct traffic",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Direct traffic", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "直接流量", Language = "zh-HK"
                            }
                        },
                        Order = 6
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Events and seminar",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Events and seminar", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "研討會", Language = "zh-HK"
                            }
                        },
                        Order = 7
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Other offline sources",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Other offline sources", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "其他活動", Language = "zh-HK"
                            }
                        },
                        Order = 8
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Others",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Others", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "其他", Language = "zh-HK"
                            }
                        },
                        Order = 9
                    }
                },
                IsDeletable = false,
                IsEditable = true,
                IsDefault = true
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Priority",
                Type = FieldDataType.Options,
                Order = 9,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Priority", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "優先度", Language = "zh-hk"
                    }
                },
                CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>()
                {
                    new CustomUserProfileFieldOption
                    {
                        Value = "High",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "High", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "高", Language = "zh-HK"
                            }
                        },
                        Order = 0
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Medium",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Medium", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "中", Language = "zh-HK"
                            }
                        },
                        Order = 1
                    },
                    new CustomUserProfileFieldOption
                    {
                        Value = "Low",
                        CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>()
                        {
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "Low", Language = "en"
                            },
                            new CustomUserProfileFieldOptionLingual
                            {
                                DisplayName = "低", Language = "zh-HK"
                            }
                        },
                        Order = 2
                    }
                }
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Country",
                Type = FieldDataType.Options,
                Order = 10,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Country", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "國家", Language = "zh-hk"
                    },
                },
                CustomUserProfileFieldOptions = GetCountryList().Result,
                IsDefault = true,
                IsDeletable = false,
                IsEditable = false
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "Subscriber",
                Type = FieldDataType.Boolean,
                Order = 11,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Subscriber", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "訂閱者", Language = "zh-hk"
                    }
                },
                IsDeletable = false,
                IsEditable = true,
                IsDefault = true
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LastChannel",
                Type = FieldDataType.Channel,
                Order = 12,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Last Channel", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "最後聯繫頻道", Language = "zh-hk"
                    }
                },
                IsEditable = false,
                IsDeletable = false,
                FieldsCategory = FieldsCategory.Message
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LastContact",
                Type = FieldDataType.DateTime,
                Order = 13,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Last Contact From You", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "最後聯絡時間", Language = "zh-hk"
                    }
                },
                IsEditable = false,
                IsDeletable = false,
                FieldsCategory = FieldsCategory.Message
            },
            new CompanyCustomUserProfileField
            {
                FieldName = "LastContactFromCustomers",
                Type = FieldDataType.DateTime,
                Order = 14,
                CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                {
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "Last Contact From Customers", Language = "en"
                    },
                    new CustomUserProfileFieldLingual
                    {
                        DisplayName = "客戶的最後聯絡時間", Language = "zh-hk"
                    }
                },
                IsEditable = false,
                IsDeletable = false,
                FieldsCategory = FieldsCategory.Message
            }
        };

        company.CustomUserProfileFields = new List<CompanyCustomUserProfileField>();

        foreach (var customFeild in companyCustomUserProfiles)
        {
            company.CustomUserProfileFields.Add(customFeild);
        }

        return company;
    }

    private Company AddDefaultQuickReply(Company company)
    {
        company.QuickReplies = new List<CompanyQuickReply>()
        {
            new CompanyQuickReply
            {
                Value = "Welcome",
                CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                {
                    new CompanyQuickReplyLingual
                    {
                        Language = "en", Value = "Hello. Thank you for contacting. How may we help you today?"
                    }
                }
            },
            new CompanyQuickReply
            {
                Value = "Thanks",
                CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                {
                    new CompanyQuickReplyLingual
                    {
                        Language = "en",
                        Value = "Thank you for your business! We look forward to working with you again."
                    }
                }
            },
            new CompanyQuickReply
            {
                Value = "Away",
                CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                {
                    new CompanyQuickReplyLingual
                    {
                        Language = "en",
                        Value =
                            "Hello. Thank you for your message. We’re not here right now, but will respond as soon as we return."
                    }
                }
            },
            new CompanyQuickReply
            {
                Value = "Close",
                CompanyQuickReplyLinguals = new List<CompanyQuickReplyLingual>
                {
                    new CompanyQuickReplyLingual
                    {
                        Language = "en",
                        Value = "Let me know if you have any further questions. Our team is always here for you."
                    }
                }
            }
        };

        return company;
    }

    private Company AddDefaultCompanyList(Company company)
    {
        company.ImportContactHistories = new List<ImportContactHistory>();

        company.ImportContactHistories.Add(
            new ImportContactHistory
            {
                ImportName = "[Template] Leads"
            });
        company.ImportContactHistories.Add(
            new ImportContactHistory
            {
                ImportName = "[Template] Customers"
            });
        company.ImportContactHistories.Add(
            new ImportContactHistory
            {
                ImportName = "[Template] Pending"
            });
        company.ImportContactHistories.Add(
            new ImportContactHistory
            {
                ImportName = "[Template] VIP"
            });

        return company;
    }

    public string GetResellerSubscriptionPlanId(
        SubscriptionTier subscriptionTier,
        ResellerCompanyProfile resellerProfile,
        string changeAddOnType = null)
    {
        if (changeAddOnType == null)
        {
            return subscriptionTier switch
            {
                SubscriptionTier.Pro => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionProPlanId],
                SubscriptionTier.Premium => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionPremiumPlanId],
                SubscriptionTier.Free => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId],
                _ => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId]
            };
        }

        switch (subscriptionTier)
        {
            case SubscriptionTier.Free:
                return changeAddOnType switch
                {
                    ChangeAddOnType.Agent => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnAgentProPlanId],
                    ChangeAddOnType.AdditionalContact => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnCustomerContactProPlanId],
                    ChangeAddOnType.WhatsAppPhoneNumber => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnWhatsAppPhoneNumberProPlanId],
                    _ => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId]
                };
            case SubscriptionTier.Pro:
                return changeAddOnType switch
                {
                    ChangeAddOnType.Agent => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnAgentProPlanId],
                    ChangeAddOnType.AdditionalContact => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnCustomerContactProPlanId],
                    ChangeAddOnType.WhatsAppPhoneNumber => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnWhatsAppPhoneNumberProPlanId],
                    _ => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId]
                };
            case SubscriptionTier.Premium:
                return changeAddOnType switch
                {
                    ChangeAddOnType.Agent => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnAgentPremiumPlanId],
                    ChangeAddOnType.AdditionalContact => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnCustomerContactPremiumPlanId],
                    ChangeAddOnType.WhatsAppPhoneNumber => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.AddOnWhatsAppPhoneNumberPremiumPlanId],
                    _ => resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId]
                };
            case SubscriptionTier.Enterprise:
            case SubscriptionTier.AddOn:
            case SubscriptionTier.Agent:
            case SubscriptionTier.MarkUpLog:
            default:
                return resellerProfile.ResellerSubscriptionPlanConfig[ResellerSubscriptionPlanIds.SubscriptionFreePlanId];
        }
    }
}