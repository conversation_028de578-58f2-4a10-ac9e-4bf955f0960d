﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using GraphApi.Client.Helpers;
using Newtonsoft.Json;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.ChannelDomain.Models.LiveChatV2;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.Constants;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices.ViewModels;
using Travis_backend.FileDomain.Models;
using Travis_backend.MessageDomain.Models.Interfaces;
using Travis_backend.MessageDomain.ViewModels;
using WABA360Dialog.ApiClient.Payloads.Models.MessageObjects.ContactObjects;
using WABA360Dialog.ApiClient.Payloads.Models.MessageObjects.InteractiveObjects;
using WABA360Dialog.ApiClient.Payloads.Models.WebhookObjects;

namespace Travis_backend.MessageDomain.Models
{
    public class ConversationProcessedMessage
    {
        [Key]
        public string ProcessedMessageId { get; set; }
    }

    public enum MessageChannelType
    {
        naive,
        whatsapp,
        facebook,
        line,
        wechat,
        web,
        sms,
        email,
        viber,
        telegram,
        note = 99
    }

    public class ConversationMessage
    {
        public long Id { get; set; }

        public string CompanyId { get; set; }

        // public Company Company { get; set; }
        public string ConversationId { get; set; }

        public Conversation Conversation { get; set; }

        public string MessageUniqueID { get; set; }

        public long? BroadcastHistoryId { get; set; }

        public string MessageChecksum { get; set; }

        public string Channel { get; set; } = "naive";

        // public MessageChannelType MessageChannel { get; set; }
        public string SenderDeviceUUID { get; set; }

        public long? SenderDeviceId { get; set; }

        public UserDevice SenderDevice { get; set; }

        public string ReceiverDeviceUUID { get; set; }

        public long? ReceiverDeviceId { get; set; }

        public UserDevice ReceiverDevice { get; set; }

        public string SenderId { get; set; }

        public ApplicationUser Sender { get; set; }

        public string ReceiverId { get; set; }

        public ApplicationUser Receiver { get; set; }

        public FacebookSender facebookSender { get; set; }

        public long? facebookSenderId { get; set; }

        public FacebookSender facebookReceiver { get; set; }

        public long? facebookReceiverId { get; set; }

        public WhatsAppSender whatsappSender { get; set; }

        public long? whatsappSenderId { get; set; }

        public WhatsAppSender whatsappReceiver { get; set; }

        public long? whatsappReceiverId { get; set; }

        [ForeignKey(nameof(Whatsapp360DialogSenderId))]
        public WhatsApp360DialogSender Whatsapp360DialogSender { get; set; }

        public long? Whatsapp360DialogSenderId { get; set; }

        [ForeignKey(nameof(Whatsapp360DialogReceiverId))]
        public WhatsApp360DialogSender Whatsapp360DialogReceiver { get; set; }

        public long? Whatsapp360DialogReceiverId { get; set; }

        public WeChatSender WeChatSender { get; set; }

        public long? WeChatSenderId { get; set; }

        public WeChatSender WeChatReceiver { get; set; }

        public long? WeChatReceiverId { get; set; }

        public LineSender LineSender { get; set; }

        public long? LineSenderId { get; set; }

        public LineSender LineReceiver { get; set; }

        public long? LineReceiverId { get; set; }

        public ViberSender ViberSender { get; set; }

        public long? ViberSenderId { get; set; }

        public ViberSender ViberReceiver { get; set; }

        public long? ViberReceiverId { get; set; }

        public TelegramSender TelegramSender { get; set; }

        public long? TelegramSenderId { get; set; }

        public TelegramSender TelegramReceiver { get; set; }

        public long? TelegramReceiverId { get; set; }

        public SMSSender SMSReceiver { get; set; }

        public long? SMSReceiverId { get; set; }

        public SMSSender SMSSender { get; set; }

        public long? SMSSenderId { get; set; }

        // public ChatAPIWhatsAppSender chatAPIWhatsAppSender { get; set; }
        // public ChatAPIWhatsAppSender chatAPIWhatsAppReceiver { get; set; }

        // public string WebClientSenderWebClientUUID { get; set; }
        public long? WebClientSenderId { get; set; }

        public WebClientSender WebClientSender { get; set; }

        // public string WebClientReceiverWebClientUUID { get; set; }
        public long? WebClientReceiverId { get; set; }

        public WebClientSender WebClientReceiver { get; set; }

        public long? InstagramSenderId { get; set; }

        public InstagramSender InstagramSender { get; set; }

        public long? InstagramReceiverId { get; set; }

        public InstagramSender InstagramReceiver { get; set; }

        // Email
        public EmailSender EmailFrom { get; set; }

        public long? EmailFromId { get; set; }

        public string EmailTo { get; set; }

        public string EmailCC { get; set; }

        public string Subject { get; set; }

        [MaxLength(450)]
        public string Visibility { get; set; } = "public";

        public Staff MessageAssignee { get; set; }

        public long? MessageAssigneeId { get; set; }

        public string MessageType { get; set; }

        public string MessageContent { get; set; }

        public DeliveryType DeliveryType { get; set; }

        public List<TranslationResult> TranslationResults { get; set; }

        public IList<UploadedFile> UploadedFiles { get; set; } = new List<UploadedFile>();

        // public ConversationTicket ConversationTicket { get; set; }
        public string ChannelStatusMessage { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public long Timestamp { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

        public long? FrondendTimestamp { get; set; }

        public long? LocalTimestamp { get; set; }

        public MessageStatus Status { get; set; }

        public bool IsSentFromSleekflow { get; set; }

        public string QuotedMsgBody { get; set; }

        [MaxLength(450)]
        public string QuotedMsgId { get; set; }

        public bool IsSandbox { get; set; }

        public string MessageTag { get; set; }

        // public double? Price { get; set; }
        public string StoryURL { get; set; }

        public DateTime? ScheduleSentAt { get; set; }

        public string JobId { get; set; }

        // Sandbox
        // public SandboxSender SandboxSender { get; set; }
        // public long? SandboxSenderId { get; set; }
        // public SandboxSender SandboxReceiver { get; set; }
        // public long? SandboxReceiverId { get; set; }

        // For Default Channel
        [MaxLength(400)]
        public string ChannelIdentityId { get; set; }

        #region Dynamic Channel Sender

        public ConversationMessageDynamicChannelSender DynamicChannelSender { get; set; }

        [NotMapped]
        public WhatsappCloudApiSender WhatsappCloudApiSender
        {
            get => !IsSentFromSleekflow ? DynamicChannelSender : null;
            set => DynamicChannelSender = value;
        }

        [NotMapped]
        public WhatsappCloudApiSender WhatsappCloudApiReceiver
        {
            get => IsSentFromSleekflow ? DynamicChannelSender : null;
            set => DynamicChannelSender = value;
        }

        [NotMapped]
        public TikTokSender TikTokSender
        {
            get => !IsSentFromSleekflow ? DynamicChannelSender : null;
            set => DynamicChannelSender = value;
        }

        [NotMapped]
        public TikTokSender TikTokReceiver
        {
            get => IsSentFromSleekflow ? DynamicChannelSender : null;
            set => DynamicChannelSender = value;
        }

        #endregion

        public Whatsapp360DialogExtendedMessagePayload Whatsapp360DialogExtendedMessagePayload { get; set; }

        public bool IsFromImport { get; set; }

        #region SleekPay

        public long? SleekPayRecordId { get; set; }

        // public StripePaymentRecord SleekPayRecord { get; set; }
        #endregion

        public ExtendedMessagePayload ExtendedMessagePayload { get; set; }

        public List<string> AnalyticTags { get; set; }

        public Dictionary<string, object> Metadata { get; set; }

        public IMessagingChannelUser GetIMessagingChannelUser()
        {
            return Channel switch
            {
                ChannelTypes.WhatsappTwilio => IsSentFromSleekflow
                    ? whatsappReceiver
                    : whatsappSender,
                ChannelTypes.Whatsapp360Dialog => IsSentFromSleekflow
                    ? Whatsapp360DialogReceiver
                    : Whatsapp360DialogSender,
                ChannelTypes.WhatsappCloudApi => IsSentFromSleekflow
                    ? WhatsappCloudApiReceiver
                    : WhatsappCloudApiSender,
                ChannelTypes.Facebook => IsSentFromSleekflow
                    ? facebookReceiver
                    : facebookSender,
                ChannelTypes.Sms => IsSentFromSleekflow
                    ? SMSReceiver
                    : SMSSender,
                ChannelTypes.Instagram => IsSentFromSleekflow
                    ? InstagramReceiver
                    : InstagramSender,
                ChannelTypes.Wechat => IsSentFromSleekflow
                    ? WeChatReceiver
                    : WeChatSender,
                ChannelTypes.Line => IsSentFromSleekflow
                    ? LineReceiver
                    : LineSender,
                ChannelTypes.Viber => IsSentFromSleekflow
                    ? ViberReceiver
                    : ViberSender,
                ChannelTypes.Telegram => IsSentFromSleekflow
                    ? TelegramReceiver
                    : TelegramSender,
                ChannelTypes.Tiktok => IsSentFromSleekflow
                    ? TikTokReceiver
                    : TikTokSender,
                ChannelTypes.Email => EmailFrom,
                // [LiveChatV2]
                ChannelTypes.LiveChatV2 => IsSentFromSleekflow
                    ? WebClientReceiver
                    : WebClientSender,
                _ => null
            };
        }
    }

    public class Whatsapp360DialogExtendedMessagePayload
    {
        [Key]
        public long Id { get; set; }

        [ForeignKey(nameof(ConversationMessageId))]
        public ConversationMessage ConversationMessage { get; set; }

        public long ConversationMessageId { get; set; }

        public Whatsapp360DialogTemplateMessageViewModel Whatsapp360DialogTemplateMessage { get; set; }

        public InteractiveObject Whatsapp360DialogInteractiveObject { get; set; }

        public Whatsapp360DialogExtendedMessageReplyPayload ReplyPayload { get; set; }
    }

    public class Whatsapp360DialogExtendedMessageReplyPayload
    {
        public WebhookInteractiveReplyObject InteractiveReply { get; set; }

        public WebhookTemplateButtonReplyObject TemplateButtonReply { get; set; }

        public WebhookLocationObject Location { get; set; }

        public List<ContactObject> Contacts { get; set; }

        public WebhookOrderObject Order { get; set; }
    }

    public class ExtendedMessagePayload
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [ForeignKey(nameof(ConversationMessageId))]
        public ConversationMessage ConversationMessage { get; set; }

        public long ConversationMessageId { get; set; }

        [MaxLength(50)]
        public string Channel { get; set; }

        public ExtendedMessageType ExtendedMessageType { get; set; }

        [MaxLength(50)]
        public string FacebookOTNTopicId { get; set; }

        public ExtendedMessagePayloadDetail ExtendedMessagePayloadDetail { get; set; }

        public void SetExtendedMessagePayloadDetailWithType(ExtendedMessagePayloadDetail value)
        {
            ExtendedMessagePayloadDetail = value;

            if (value.WhatsappCloudApiTemplateMessageObject != null)
            {
                ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiTemplateMessage;
            }

            if (value.WhatsappCloudApiInteractiveObject != null)
            {
                ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiInteractiveMessage;
            }

            if (value.WhatsappCloudApiContactsObject != null)
            {
                ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiContactsMessage;
            }

            if (value.WhatsappCloudApiLocationObject != null)
            {
                ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiLocationMessage;
            }

            if (value.WhatsappCloudApiReactionObject != null)
            {
                ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiReactionMessage;
            }

            if (value.WhatsappCloudApiInteractiveReply != null)
            {
                ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiInteractiveReplyMessage;
            }

            if (value.WhatsappCloudApiTemplateButtonReply != null)
            {
                ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiTemplateButtonReplyMessage;
            }

            if (value.WhatsappCloudApiLocationReply != null)
            {
                ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiLocationReplyMessage;
            }

            if (value.WhatsappCloudApiContactsReply != null)
            {
                ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiContactsReplyMessage;
            }

            if (value.WhatsappCloudApiOrderObject != null)
            {
                ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiOrderMessage;
            }

            if (value.WhatsappCloudApiReactionReply != null)
            {
                ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiReactionReplyMessage;
            }

            if (value.WhatsappCloudApiReferral != null)
            {
                ExtendedMessageType = ExtendedMessageType.WhatsappCloudApiReferralMessage;
            }

            if (value.TikTokMessageObject != null)
            {
                if (value.TikTokMessageObject.SharePost != null)
                {
                    ExtendedMessageType = ExtendedMessageType.TikTokSharePostMessage;
                }
                else if (value.TikTokMessageObject.Referral != null)
                {
                    ExtendedMessageType = ExtendedMessageType.TikTokReferralMessage;
                }
            }
        }
    }

    public class ExtendedMessagePayloadDetail
    {
        public MessageData FacebookMessagePayload { get; set; }

        public FacebookAdsClickToMessengerObject FacebookAdClickToMessengerObject { get; set; }

        #region cloud api

        public WhatsappCloudApiTemplateMessageViewModel WhatsappCloudApiTemplateMessageObject { get; set; }

        public WhatsappCloudApiInteractiveObject WhatsappCloudApiInteractiveObject { get; set; }

        public List<WhatsappCloudApiContactObject> WhatsappCloudApiContactsObject { get; set; }

        public WhatsappCloudApiLocationObject WhatsappCloudApiLocationObject { get; set; }

        public WhatsappCloudApiReactionObject WhatsappCloudApiReactionObject { get; set; }

        public WhatsappCloudApiOrderDetailObject WhatsappCloudApiOrderObject { get; set; }

        public WhatsappCloudApiWebhookInteractiveReplyObject WhatsappCloudApiInteractiveReply { get; set; }

        public WhatsappCloudApiWebhookTemplateButtonReplyObject WhatsappCloudApiTemplateButtonReply { get; set; }

        public WhatsappCloudApiWebhookLocationObject WhatsappCloudApiLocationReply { get; set; }

        public List<WhatsappCloudApiContactObject> WhatsappCloudApiContactsReply { get; set; }

        public WhatsappCloudApiWebhookOrderObject WhatsappCloudApiOrderReply { get; set; }

        public WhatsappCloudApiReactionObject WhatsappCloudApiReactionReply { get; set; }

        public WhatsappCloudApiWebhookReferralObject WhatsappCloudApiReferral { get; set; }
        #endregion

        #region TwilioContentApi

        public WhatsappTwilioContentApiObject WhatsappTwilioContentApiObject { get; set; }

        #endregion

        #region TikTok

        [JsonProperty("tikTokMessageObject")]
        public TikTokMessageObject TikTokMessageObject { get; set; }

        #endregion
        #region liveChatV2

        [JsonProperty("liveChatV2MessageObject")]
        public LiveChatV2MessageObject LiveChatV2MessageObject { get; set; }

        #endregion

    }

    public class WhatsappCloudApiOrderDetailObject : WhatsappCloudApiWebhookOrderObject
    {
        [JsonProperty("catalog_name")]
        public string CatalogName { get; set; }

        [JsonProperty("product_items")]
        public List< Travis_backend.MessageDomain.Models.WhatsappCloudApiWebhookProductItemsDetailsObject> ProductItems { get; set; }

        public WhatsappCloudApiOrderDetailObject(WhatsappCloudApiWebhookOrderObject whatsappCloudApiWebhookOrderObject)
        {
            if (whatsappCloudApiWebhookOrderObject != null)
            {
                CatalogId = whatsappCloudApiWebhookOrderObject.CatalogId;
                Text = whatsappCloudApiWebhookOrderObject.Text;
                ProductItems = new List<WhatsappCloudApiWebhookProductItemsDetailsObject>();

                foreach (var productItem in whatsappCloudApiWebhookOrderObject.ProductItems)
                {
                    ProductItems.Add(new WhatsappCloudApiWebhookProductItemsDetailsObject(productItem));
                }
            }
        }
    }

    public class WhatsappCloudApiWebhookProductItemsDetailsObject : WhatsappCloudApiWebhookProductItemsObject
    {
        /// <summary>
        /// Name of the product. From graph api ({product-catalog-id}/products)
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// URL of the product image. From graph api ({product-catalog-id}/products)
        /// </summary>
        [JsonProperty("image_url")]
        public string ImageUrl { get; set; }

        /// <summary>
        /// Description of product From graph api ({product-catalog-id}/products)
        /// </summary>
        [JsonProperty("description")]
        public string Description { get; set; }

        /// <summary>
        /// enum {in stock, out of stock, preorder, available for order, discontinued, pending}
        /// Availability of product From graph api ({product-catalog-id}/products)
        /// </summary>
        [JsonProperty("availability")]
        public string Availability { get; set; }

        /// <summary>
        /// Inventory of product From graph api ({product-catalog-id}/products)
        /// </summary>
        [JsonProperty("inventory")]
        public int? Inventory { get; set; }

        public WhatsappCloudApiWebhookProductItemsDetailsObject(
            WhatsappCloudApiWebhookProductItemsObject whatsappCloudApiWebhookProductItemsObject)
        {
            if (whatsappCloudApiWebhookProductItemsObject != null)
            {
                ProductRetailerId = whatsappCloudApiWebhookProductItemsObject.ProductRetailerId;
                Quantity = whatsappCloudApiWebhookProductItemsObject.Quantity;
                ItemPrice = whatsappCloudApiWebhookProductItemsObject.ItemPrice;
                Currency = whatsappCloudApiWebhookProductItemsObject.Currency;
            }
        }
    }

    public class WhatsappCloudApiFlowSubmissionObject : Dictionary<string, object>
    {
        private const string FlowToken = "flow_token";

        [JsonProperty("flow_submission_data")]
        public Dictionary<string, object> WhatsappCloudApiFlowSubmissionData;

        public WhatsappCloudApiFlowSubmissionObject(Dictionary<string, object> dictionary)
        {
            if (dictionary == null)
            {
                return;
            }

            foreach (var (key, value) in dictionary)
            {
                if (key == FlowToken && value is string flowTokenValStr)
                {
                    var flowTokenData = FlowTokenHelper.ParseFlowToken(flowTokenValStr);
                    TryAdd("waba_id", flowTokenData.WabaId);
                    TryAdd("flow_id", flowTokenData.FlowId);
                    TryAdd("timestamp", flowTokenData.Timestamp);
                }
                else
                {
                    WhatsappCloudApiFlowSubmissionData ??= new Dictionary<string, object>();

                    WhatsappCloudApiFlowSubmissionData.TryAdd(key, value);
                }
            }

            TryAdd("flow_submission_data", WhatsappCloudApiFlowSubmissionData);
        }
    }

    public class WhatsappTwilioContentApiObject
    {
        public string ContentSid { get; set; }

        public Dictionary<string, object> ContentVariables { get; set; }

        public string ContentVariablesJson
        {
            get
            {
                ContentVariables ??= new Dictionary<string, object>();
                return JsonConvert.SerializeObject(ContentVariables, Formatting.Indented);
            }
        }

        public CreateTemplateWithContentApiResponse TwilioContentObject { get; set; }
    }

    public enum ExtendedMessageType
    {
        WhatsappCloudApiTemplateMessage = 101,
        WhatsappCloudApiInteractiveMessage = 102,
        WhatsappCloudApiContactsMessage = 103,
        WhatsappCloudApiLocationMessage = 104,
        WhatsappCloudApiReactionMessage = 105,
        WhatsappCloudApiOrderMessage = 106,

        WhatsappCloudApiTemplateButtonReplyMessage = 111,
        WhatsappCloudApiInteractiveReplyMessage = 112,
        WhatsappCloudApiContactsReplyMessage = 113,
        WhatsappCloudApiLocationReplyMessage = 114,
        WhatsappCloudApiOrderReplyMessage = 115,
        WhatsappCloudApiReactionReplyMessage = 116,
        WhatsappCloudApiFlowSubmissionMessage = 117,
        WhatsappCloudApiReferralMessage = 120,

        FacebookOTNRequest = 201,
        FacebookOTNText = 202,
        FacebookOTNFile = 203,
        FacebookOTNInteractive = 204,
        FacebookInteractiveMessage = 205,
        FacebookAdClickToMessenger = 206,
        InstagramInteractiveMessage = 301,

        TwilioContentApi = 401,

        LiveChatV2ContactFormMessage = 501,
        LiveChatV2ButtonMessage = 502,
        LiveChatV2LinkMessage = 503,

        TikTokSharePostMessage = 601,
        TikTokReferralMessage = 602
    }

    public enum DeliveryType
    {
        Normal = 0,
        Broadcast = 1,
        AutomatedMessage = 2, // Automation, API (Optin, Platform API, Zapier, Make.com, Salesforce, etc.)
        ReadMore = 3,
        QuickReply = 4,
        FlowHubAction = 5, // Flow Builder Send Message Command
        AiAgentAction = 6, // AI Agent Send Message Command

        // Payment Link
        PaymentLink = 90
    }

    public enum MessageStatus
    {
        Sending = 0,
        Sent,
        Received,
        Read,
        Failed,
        Undelivered,
        Deleted,
        OutOfCredit,
        Scheduled,

        // Payment Link
        PaymentLinkPending = 90,
        PaymentLinkPaid = 91,
        PaymentLinkCanceled = 92
    }

    public class ExtendedMessagePayloadFile : RecordTime
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [MaxLength(50)]
        public string CompanyId { get; set; }

        [MaxLength(50)]
        public string Channel { get; set; }

        public ExtendedMessageType ExtendedMessageType { get; set; }

        [MaxLength(1000)]
        public string BlobContainer { get; set; }

        [MaxLength(1000)]
        public string BlobFilePath { get; set; }

        [MaxLength(1000)]
        public string Filename { get; set; }

        [MaxLength(255)]
        public string MIMEType { get; set; }

        [MaxLength(1000)]
        public string Url { get; set; }

        public long FileSize { get; set; }

        [MaxLength(1000)]
        public string DisplayName { get; set; }

        [MaxLength(20)]
        public string MediaType { get; set; }
    }

    public class ConversationUnreadRecord : DateAuditedEntity<long>
    {
        public string CompanyId { get; set; }

        public string ConversationId { get; set; }

        public long MessageId { get; set; }

        public long StaffId { get; set; }

        [MaxLength(450)]
        public string StaffIdentityId { get; set; }

        public NotificationDeliveryType NotificationDeliveryType { get; set; }

        public NotificationDeliveryStatus NotificationDeliveryStatus { get; set; }

        public DateTime SentAt { get; set; } = DateTime.UtcNow;

        public DateTime? DeliveredAt { get; set; }

        public DateTime? ReadAt { get; set; }

        public ConversationUnreadRecord()
        {
        }

        public ConversationUnreadRecord(
            ConversationMessage conversationMessage,
            long staffId,
            string staffIdentityId,
            NotificationDeliveryType notificationDeliveryType)
        {
            CompanyId = conversationMessage.CompanyId;
            ConversationId = conversationMessage.ConversationId;
            MessageId = conversationMessage.Id;
            NotificationDeliveryType = notificationDeliveryType;
            StaffId = staffId;
            StaffIdentityId = staffIdentityId;
        }
    }

    public enum NotificationDeliveryType
    {
        ContactOwner = 0,
        TeamMemberAsContactOwner = 7,

        Collaborator = 1,
        TeamMemberAsCollaborator = 6,

        TeamAssigned = 2,
        TeamUnassigned = 3,

        Unassigned = 4,
        Mentioned = 5,

        Assigned = 9,
        All = 8
    }

    public enum NotificationDeliveryStatus
    {
        Sent,
        Delivered,
        Read
    }

    public class LiveChatV2MessageObject
    {
        [JsonProperty("contact_form")]
        public LiveChatV2ContactFormObject ContactForm { get; set; }

        [JsonProperty("link_message")]
        public LiveChatV2LinkMessageObject LinkMessage { get; set; }
    }


    public class TikTokMessageObject
    {
        [JsonProperty("share_post")]
        public TikTokSharePostObject SharePost { get; set; }

        [JsonProperty("referral")]
        public TikTokReferralObject Referral { get; set; }
    }

    public class TikTokSharePostObject
    {
        [JsonProperty("embed_url")]
        public string EmbedUrl { get; set; }
    }

    public class TikTokReferralObject
    {
        [JsonProperty("source")]
        public string Source { get; set; } // 'ad' | 'short_link'

        [JsonProperty("ad")]
        public TikTokAdObject Ad { get; set; }

        [JsonProperty("short_link")]
        public TikTokShortLinkObject ShortLink { get; set; }
    }

    public class TikTokAdObject
    {
        [JsonProperty("advertiser_id")]
        public string AdvertiserId { get; set; }

        [JsonProperty("ad_id")]
        public string AdId { get; set; }

        [JsonProperty("timestamp")]
        public long Timestamp { get; set; }

        [JsonProperty("ad_name")]
        public string AdName { get; set; }

        [JsonProperty("embed_url")]
        public string EmbedUrl { get; set; }
    }

    public class TikTokShortLinkObject
    {
        [JsonProperty("ref")]
        public string Ref { get; set; }

        [JsonProperty("prefilled_message")]
        public string PrefilledMessage { get; set; }

        [JsonProperty("prefilled_message_audit_status")]
        public string PrefilledMessageAuditStatus { get; set; }
    }
}
