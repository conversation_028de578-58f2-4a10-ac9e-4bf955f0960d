using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using Hangfire;
using Microsoft.ApplicationInsights.AspNetCore.Extensions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Namotion.Reflection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Apis.CrmHub.Api;
using Sleekflow.Apis.CrmHub.Model;
using Sleekflow.Apis.MessagingHub.Api;
using Sleekflow.Apis.MessagingHub.Client;
using Sleekflow.Apis.MessagingHub.Model;
using Sleekflow.Apis.TenantHub.Api;
using Sleekflow.Apis.TenantHub.Model;
using Sleekflow.Apis.WebhookHub.Api;
using Sleekflow.Apis.WebhookHub.Model;
using Telegram.Bot;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AccountAuthenticationDomain.Services;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.Cache;
using Travis_backend.Cache.Models;
using Travis_backend.Cache.Models.CacheKeyPatterns;
using Travis_backend.ChannelDomain.Clients;
using Travis_backend.ChannelDomain.Exceptions;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.ChannelDomain.Services;
using Travis_backend.ChannelDomain.Services.Facebook;
using Travis_backend.ChannelDomain.Services.Instagram;
using Travis_backend.ChannelDomain.ViewModels;
using Travis_backend.CommonDomain.ViewModels;
using Travis_backend.CompanyDomain.ApiKeyResolvers;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.Constants;
using Travis_backend.ContactDomain.Services;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.ViewModels;
using Travis_backend.ConversationServices;
using Travis_backend.Database;
using Travis_backend.Enums;
using Travis_backend.Extensions;
using Travis_backend.ConversationServices.Models;
using Travis_backend.ConversationServices.ViewModels;
using Travis_backend.Externals;
using Travis_backend.FileDomain.Services;
using Travis_backend.FlowHubs;
using Travis_backend.Helpers;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.SignalR;
using Travis_backend.StripeIntegrationDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Constants;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Models.Common;
using Travis_backend.SubscriptionPlanDomain.Services;
using Twilio;
using Twilio.Rest.Api.V2010;
using RegisterWebhookInput = Sleekflow.Apis.WebhookHub.Model.RegisterWebhookInput;
using Travis_backend.ContactDomain.Repositories;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.Services.Cache;
using Travis_backend.FacebookInstagramIntegrationDomain.Services;
using Travis_backend.Infrastructures.Attributes;
using Travis_backend.TenantHubDomain.Services;
using CreateTikTokConnectionInput = Travis_backend.ChannelDomain.ViewModels.CreateTikTokConnectionInput;
using DeleteTikTokConnectionInput = Travis_backend.ChannelDomain.ViewModels.DeleteTikTokConnectionInput;
using ProfilePictureFile = Travis_backend.CompanyDomain.Models.ProfilePictureFile;
using SleekflowStaff = Sleekflow.Apis.MessagingHub.Model.SleekflowStaff;
using Staff = Travis_backend.CompanyDomain.Models.Staff;
using StaffWithoutCompanyResponse = Travis_backend.ConversationDomain.ViewModels.StaffWithoutCompanyResponse;
using UpdateTikTokConnectionInput = Travis_backend.ChannelDomain.ViewModels.UpdateTikTokConnectionInput;
using User = Telegram.Bot.Types.User;

#if OBSOLETE_AUTH
using Travis_backend.AccountAuthenticationDomain.ViewModels;
using Travis_backend.SubscriptionPlanDomain.ViewModels;
#endif

namespace Travis_backend.Controllers.CompanyManagementControllers
{
    [Authorize]
    public class CompanyController : Controller
    {
        private readonly ApplicationDbContext _appDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IMapper _mapper;
        private readonly ILogger<CompanyController> _logger;
        private readonly IConfiguration _configuration;
        private readonly IConversationMessageService _conversationMessageService;
        private readonly IUploadService _uploadService;
        private readonly IAzureBlobStorageService _azureBlobStorageService;
        private readonly ISignalRService _signalRService;
        private readonly IEmailNotificationService _emailNotificationService;
        private readonly ICompanyService _companyService;
        private readonly ICompanyUsageService _companyUsageService;
        private readonly ICompanyTeamService _companyTeamService;
        private readonly ICompanyOfferService _companyOfferService;
        private readonly ITwilioService _twilioService;
        private readonly ICacheManagerService _cacheManagerService;
        private readonly ICompanyInfoCacheService _companyInfoCacheService;
        private readonly ICoreService _coreService;
        private readonly IUserProfileService _userProfileService;
        private readonly IFacebookService _facebookService;
        private readonly IInstagramService _instagramService;
        private readonly IProvidersApi _providersApi;
        private readonly IAutomationRuleService _automationRuleService;
        private readonly IWhatsappCloudApiService _whatsappCloudApiService;
        private readonly ICompanySubscriptionService _companySubscriptionService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IPlanDefinitionService _planDefinitionService;

        private readonly bool _isEnableTenantHubLogic;
        private readonly IInstagramChannelService _instagramChannelService;
        private readonly IFacebookChannelService _facebookChannelService;
        private readonly IExternalFileService _externalFileService;
        private readonly ICompanyAssignmentQueueService _companyAssignmentQueueService;
        private readonly ICompanyTeamAssignmentQueueService _companyTeamAssignmentQueueService;
        private readonly ApiKeyResolverFactory _apiKeyResolverFactory;
        private readonly IStaffHooks _staffHooks;

        private readonly ICompanyStaffOverviewService _companyStaffOverviewService;
        private readonly IFacebookWebhooksApi _facebookWebhooksApi;

        private readonly IUserProfileDuplicationConfigRepository _userProfileDuplicationConfigRepository;

        private readonly ICompanyStaffControllerService _companyStaffControllerService;

        private readonly ISubscriptionCountryTierDeterminer _subscriptionCountryTierDeterminer;

        private readonly IHttpContextAccessor _httpContextAccessor;

        private readonly IManagementRbacApi _managementRbacApi;

        private readonly IRbacService _rbacService;

        private readonly IBusinessHourConfigService _businessHourConfigService;

        private readonly IManagementCompaniesApi _managementCompaniesApi;

        private readonly IManagementRolesApi _managementRolesApi;

        private readonly IContactCacheService _contactCacheService;
        private readonly IChannelsApi _channelsApi;

        public CompanyController(
            ApplicationDbContext appDbContext,
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            IMapper mapper,
            IConfiguration configuration,
            ILogger<CompanyController> logger,
            IConversationMessageService messagingService,
            IUploadService uploadService,
            IAzureBlobStorageService azureBlobStorageService,
            ISignalRService signalRService,
            IEmailNotificationService emailNotificationService,
            ICompanyService companyService,
            ICompanyUsageService companyUsageService,
            ICompanyTeamService companyTeamService,
            ICompanyOfferService companyOfferService,
            ITwilioService twilioService,
            ICacheManagerService cacheManagerService,
            ICompanyInfoCacheService companyInfoCacheService,
            ICoreService coreService,
            IUserProfileService userProfileService,
            IProvidersApi providersApi,
            IFacebookService facebookService,
            IInstagramService instagramService,
            IAutomationRuleService automationRuleService,
            IWhatsappCloudApiService whatsappCloudApiService,
            IHttpClientFactory httpClientFactory,
            IPlanDefinitionService planDefinitionService,
            ICompanySubscriptionService companySubscriptionService,
            IInstagramChannelService instagramChannelService,
            IFacebookChannelService facebookChannelService,
            IExternalFileService externalFileService,
            ICompanyAssignmentQueueService companyAssignmentQueueService,
            ICompanyTeamAssignmentQueueService companyTeamAssignmentQueueService,
            ApiKeyResolverFactory apiKeyResolverFactory,
            ICompanyStaffOverviewService companyStaffOverviewService,
            IFacebookWebhooksApi facebookWebhooksApi,
            IUserProfileDuplicationConfigRepository userProfileDuplicationConfigRepository,
            IStaffHooks staffHooks,
            ICompanyStaffControllerService companyStaffControllerService,
            ISubscriptionCountryTierDeterminer subscriptionCountryTierDeterminer,
            IManagementRbacApi managementRbacApi,
            IRbacService rbacService,
            IHttpContextAccessor httpContextAccessor,
            IBusinessHourConfigService businessHourConfigService,
            IManagementCompaniesApi managementCompaniesApi,
            IManagementRolesApi managementRolesApi,
            IContactCacheService contactCacheService,
            IChannelsApi channelsApi)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _appDbContext = appDbContext;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
            _conversationMessageService = messagingService;
            _uploadService = uploadService;
            _azureBlobStorageService = azureBlobStorageService;
            _signalRService = signalRService;
            _emailNotificationService = emailNotificationService;
            _companyService = companyService;
            _companyUsageService = companyUsageService;
            _companyTeamService = companyTeamService;
            _companyOfferService = companyOfferService;
            _twilioService = twilioService;
            _cacheManagerService = cacheManagerService;
            _companyInfoCacheService = companyInfoCacheService;
            _coreService = coreService;
            _userProfileService = userProfileService;
            _providersApi = providersApi;

            _facebookService = facebookService;
            _instagramService = instagramService;
            _automationRuleService = automationRuleService;
            _whatsappCloudApiService = whatsappCloudApiService;
            _companySubscriptionService = companySubscriptionService;
            _httpClientFactory = httpClientFactory;
            _planDefinitionService = planDefinitionService;

            _isEnableTenantHubLogic = _configuration.GetValue<bool>("TenantHub:IsEnableTenantLogic");
            _instagramChannelService = instagramChannelService;
            _facebookChannelService = facebookChannelService;
            _externalFileService = externalFileService;
            _companyAssignmentQueueService = companyAssignmentQueueService;
            _companyTeamAssignmentQueueService = companyTeamAssignmentQueueService;
            _apiKeyResolverFactory = apiKeyResolverFactory;
            _companyStaffOverviewService = companyStaffOverviewService;
            _facebookWebhooksApi = facebookWebhooksApi;
            _userProfileDuplicationConfigRepository = userProfileDuplicationConfigRepository;
            _staffHooks = staffHooks;

            _companyStaffControllerService = companyStaffControllerService;
            _subscriptionCountryTierDeterminer = subscriptionCountryTierDeterminer;
            _httpContextAccessor = httpContextAccessor;
            _businessHourConfigService = businessHourConfigService;
            _managementCompaniesApi = managementCompaniesApi;
            _managementRolesApi = managementRolesApi;
            _rbacService = rbacService;
            _managementRbacApi = managementRbacApi;
            _contactCacheService = contactCacheService;
            _channelsApi = channelsApi;
        }

        /// <summary>
        /// Get Company Info.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("Company")]
        public async Task<ActionResult<CompanyResponse>> GetCompany()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var key = $"GetCompnayInfo_{companyUser.CompanyId}_{companyUser.IdentityId}_{companyUser.RoleType}";
            var getCompanyInfoCacheKeyPattern = new GetCompanyInfoCacheKeyPattern(
                companyUser.CompanyId,
                companyUser.IdentityId,
                companyUser.RoleType);

            var data = await _cacheManagerService.GetCacheAsync(getCompanyInfoCacheKeyPattern);

            if (!string.IsNullOrEmpty(data))
            {
                return Ok(JsonConvert.DeserializeObject<CompanyResponse>(data));
            }

            var companyId = companyUser.CompanyId;
            var ipAddress = _httpContextAccessor?.HttpContext?.GetRequestIpAddress();

            companyUser.Company = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == companyUser.CompanyId && !x.IsDeleted)

                // .Include(x => x.FacebookConfigs)
                // .Include(x => x.WhatsAppConfigs)
                // .Include(x => x.EmailConfig)
                // .Include(x => x.WeChatConfig)
                // .Include(x => x.LineConfigs)
                // .Include(x => x.ViberConfigs)
                // .Include(x => x.TelegramConfigs)
                // .Include(x => x.CompanyHashtags)
                // .Include(x => x.SMSConfigs)
                // .Include(x => x.ShopifyConfigs)
                // .Include(x => x.ShoplineConfigs)
                // .Include(x => x.InstagramConfigs)
                .Include(x => x.CompanyIconFile)
                .Include(x => x.RolePermission)
                .Include(x => x.TwilioUsageRecords).FirstOrDefaultAsync();

            if (companyUser.Company == null)
            {
                return Unauthorized();
            }

            // if (companyUser.Id != 1)
            // {
            //    companyUser.Identity.LastLoginAt = DateTime.UtcNow;
            //    await _appDbContext.SaveChangesAsync();
            // }
            companyUser.Company.FacebookConfigs = await _appDbContext.ConfigFacebookConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);
            companyUser.Company.WhatsAppConfigs = await _appDbContext.ConfigWhatsAppConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);
            companyUser.Company.EmailConfig = await _appDbContext.ConfigEmailConfigs
                .Where(x => x.Id == companyUser.Company.EmailConfigId).FirstOrDefaultAsync(HttpContext.RequestAborted);
            companyUser.Company.WeChatConfig = await _appDbContext.ConfigWeChatConfigs
                .Where(x => x.Id == companyUser.Company.WeChatConfigId).FirstOrDefaultAsync(HttpContext.RequestAborted);
            companyUser.Company.LineConfigs = await _appDbContext.ConfigLineConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);
            companyUser.Company.ViberConfigs = await _appDbContext.ConfigViberConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);
            companyUser.Company.TelegramConfigs = await _appDbContext.ConfigTelegramConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);
            companyUser.Company.TikTokConfigs = await _appDbContext.ConfigTikTokConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);

            var companyHashtagsContinueQuery = true;
            var offset = 0;
            do
            {
                var tags = await _appDbContext.CompanyDefinedHashtags
                    .Where(x => x.CompanyId == companyUser.CompanyId).Skip(offset).Take(10000).AsNoTracking().OrderBy(x => x.Id).ToListAsync(HttpContext.RequestAborted);

                companyUser.Company.CompanyHashtags ??= new List<CompanyHashtag>();

                if (tags.Count is 0 or < 10000)
                {
                    companyHashtagsContinueQuery = false;
                }

                offset += tags.Count;

                companyUser.Company.CompanyHashtags.AddRange(tags);

            }
            while (companyHashtagsContinueQuery);

            companyUser.Company.SMSConfigs = await _appDbContext.ConfigSMSConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);
            companyUser.Company.ShopifyConfigs = await _appDbContext.ConfigShopifyConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);
            companyUser.Company.ShoplineConfigs = await _appDbContext.ConfigShoplineConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);
            companyUser.Company.InstagramConfigs = await _appDbContext.ConfigInstagramConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);
            companyUser.Company.WhatsApp360DialogConfigs = await _appDbContext.ConfigWhatsApp360DialogConfigs
                .AsNoTracking().Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);
            companyUser.Company.WhatsApp360DialogUsageRecords = await _appDbContext.CompanyWhatsApp360DialogUsageRecords
                .AsNoTracking()
                .Where(x => x.CompanyId == companyUser.CompanyId && x.TopUpMode == TopUpMode.PartnerPayment && !x.IsDeleted)
                .ToListAsync(HttpContext.RequestAborted); // Temp

            companyUser.Company.WhatsappCloudApiConfigs = await _appDbContext.ConfigWhatsappCloudApiConfigs
                .AsNoTracking().Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);

            // This code block is used to prevent any kind of exceptions make company controller fail
            try
            {
                companyUser.Company.WhatsappCloudApiUsageRecords =
                    (await _whatsappCloudApiService.GetBusinessBalances(companyUser.CompanyId)).BusinessBalances;
            }
            catch (SleekflowErrorCodeException ex)
            {
                _logger.LogError(
                    ex,
                    "Messaging Hub exception for getting WhatsappCloudApiUsageRecords:{CompanyId}, message:{ExceptionMessage}",
                    companyUser.CompanyId,
                    ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Unexpected exception for getting WhatsappCloudApiUsageRecords:{CompanyId}, message:{ExceptionMessage}",
                    companyUser.CompanyId,
                    ex.Message);
            }

            companyUser.Company.BlastMessageConfig = await _appDbContext.BlastMessageConfigs.AsNoTracking()
                .Where(x => x.CompanyId == companyUser.CompanyId).FirstOrDefaultAsync(HttpContext.RequestAborted);

            companyUser.Company.CustomUserProfileFields = await _appDbContext.CompanyCustomUserProfileFields
                .Where(x => x.CompanyId == companyUser.CompanyId).Include(x => x.CustomUserProfileFieldOptions)
                .ThenInclude(ling => ling.CustomUserProfileFieldOptionLinguals)
                .Include(x => x.CustomUserProfileFieldLinguals).ToListAsync(HttpContext.RequestAborted);
            companyUser.Company.CompanyCustomFields = await _appDbContext.CompanyCompanyCustomFields
                .Where(x => x.CompanyId == companyUser.CompanyId).Include(x => x.CompanyCustomFieldFieldLinguals)
                .ToListAsync(HttpContext.RequestAborted);

            companyUser.Company.CustomUserProfileFields =
                companyUser.Company.CustomUserProfileFields.OrderBy(x => x.Order).ToList();
            companyUser.Company.CustomUserProfileFields.ForEach(
                x => x.CustomUserProfileFieldOptions = x.CustomUserProfileFieldOptions.OrderBy(z => z.Order)
                    .ThenBy(z => z.Value).ToList());

            // Default Channel Id Handling
            var assoicatedTeams = await _appDbContext.CompanyStaffTeams
                .Where(x => x.Members.Any(y => y.StaffId == companyUser.Id))
                .ToListAsync(HttpContext.RequestAborted);
            var whatsappIds = new List<string>();
            var twilioSenderIds = new List<string>();
            var whatsapp360DialogIds = new List<long>();
            var whatsappCloudDefaultChannelIds = new List<string>();
            foreach (var assoicatedTeam in assoicatedTeams)
            {
                if (assoicatedTeam.DefaultChannels?.Count > 0)
                {
                    foreach (var defaultchannel in assoicatedTeam.DefaultChannels)
                    {
                        if (defaultchannel.ids != null)
                        {
                            foreach (var id in defaultchannel.ids)
                            {
                                var twilioInstance = id.Split(";", StringSplitOptions.RemoveEmptyEntries);
                                whatsappIds.Add(twilioInstance[0]);
                                if (twilioInstance.Count() > 1)
                                {
                                    twilioSenderIds.Add(twilioInstance[1].Replace(": ", ":+"));
                                }
                            }
                        }
                    }

                    // 360dialog default channels
                    foreach (var defaultchannel in assoicatedTeam.DefaultChannels.Where(
                                 x => x.channel == ChannelTypes.Whatsapp360Dialog))
                    {
                        foreach (var channelId in defaultchannel.ids)
                        {
                            var validLong = long.TryParse(channelId, out var whatsapp360dialogChannelId);
                            if (validLong &&
                                companyUser.Company.WhatsApp360DialogConfigs.Any(
                                    x => x.Id == whatsapp360dialogChannelId))
                            {
                                whatsapp360DialogIds.Add(whatsapp360dialogChannelId);
                            }
                        }
                    }

                    // Cloud API default channels
                    foreach (var defaultChannel in assoicatedTeam.DefaultChannels.Where(
                                 x => x.channel == ChannelTypes.WhatsappCloudApi))
                    {
                        foreach (var channelId in defaultChannel.ids)
                        {
                            whatsappCloudDefaultChannelIds.Add(channelId);
                        }
                    }
                }
            }

            // Filter out default channels
            if (companyUser.RoleType != StaffUserRole.Admin
                && (whatsappIds.Count > 0
                    || twilioSenderIds.Count > 0
                    || whatsapp360DialogIds.Count > 0
                    || whatsappCloudDefaultChannelIds.Count > 0)
                && (
                    // Team Admin Show Default Channel Messages Only
                    (companyUser.RoleType == StaffUserRole.TeamAdmin && companyUser.Company.RolePermission.Any(
                        x => x.StaffUserRole == StaffUserRole.TeamAdmin &&
                             x.StoredPermission.IsShowDefaultChannelMessagesOnly))
                    ||
                    // Staff Show Default Channel Messages Only
                    (companyUser.RoleType == StaffUserRole.Staff && companyUser.Company.RolePermission.Any(
                        x => x.StaffUserRole == StaffUserRole.Staff &&
                             x.StoredPermission.IsShowDefaultChannelMessagesOnly))))
            {
                companyUser.Company.WhatsAppConfigs = companyUser.Company.WhatsAppConfigs.Where(
                        x => whatsappIds.Contains(x.TwilioAccountId) && twilioSenderIds.Contains(x.WhatsAppSender))
                    .ToList();
                companyUser.Company.FacebookConfigs = companyUser.Company.FacebookConfigs
                    .Where(x => whatsappIds.Contains(x.PageId)).ToList();
                companyUser.Company.LineConfigs = companyUser.Company.LineConfigs
                    .Where(x => whatsappIds.Contains(x.ChannelID)).ToList();
                companyUser.Company.WhatsApp360DialogConfigs = companyUser.Company.WhatsApp360DialogConfigs
                    .Where(x => whatsapp360DialogIds.Contains(x.Id)).ToList();
                companyUser.Company.WhatsappCloudApiConfigs = companyUser.Company.WhatsappCloudApiConfigs
                    .Where(x => whatsappCloudDefaultChannelIds.Contains(x.WhatsappPhoneNumber)).ToList();
                companyUser.Company.InstagramConfigs = companyUser.Company.InstagramConfigs
                    .Where(x => whatsappIds.Contains(x.InstagramPageId)).ToList();
            }

            try
            {
                await PopulateChannelFieldCustomUserProfileFieldOptions(companyUser);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Channel error {CompanyId}",
                    companyUser.CompanyId);
            }

            try
            {
                var staffs = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyUser.CompanyId)
                    .Include(x => x.Identity)
                    .ToListAsync(HttpContext.RequestAborted);

                PopulateTravisUserTypeFieldOptions(companyUser, staffs);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Populate Travis user field options error {CompanyId}",
                    companyUser.CompanyId);
            }

            try
            {
                var teams = await _appDbContext.CompanyStaffTeams.Where(x => x.CompanyId == companyUser.CompanyId)
                    .OrderBy(x => x.TeamName).ToListAsync(HttpContext.RequestAborted);
                var assignedTeam = companyUser.Company.CustomUserProfileFields.Where(x => x.FieldName == "AssignedTeam")
                    .FirstOrDefault();
                if (assignedTeam != null)
                {
                    assignedTeam.CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>();
                    foreach (var team in teams)
                    {
                        assignedTeam.CustomUserProfileFieldOptions.Add(
                            new CustomUserProfileFieldOption
                            {
                                Value = team.Id.ToString(),
                                CompanyCustomUserProfileFieldId = assignedTeam.Id,
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        Language = "en", DisplayName = team.TeamName
                                    }
                                }
                            });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Populate assigned team field options error {CompanyId}",
                    companyUser.CompanyId);
            }

            var companyVM = _mapper.Map<CompanyResponse>(companyUser.Company);
            var messenger = companyUser.Company.FacebookConfigs.Where(x => x.SubscribedFields.Contains("messages"))
                .ToList();
            var leadads = companyUser.Company.FacebookConfigs.Where(x => x.SubscribedFields.Contains("leadgen"))
                .ToList();

            try
            {
                var baseSubscriptionBillRecord = await _companySubscriptionService.GetCurrentBaseSubscriptionBillRecord(companyVM.Id);

                if (baseSubscriptionBillRecord.StripeSubscriptionCancelDateTime != null)
                {
                    companyVM.BaseSubscriptionEndDateTime = baseSubscriptionBillRecord.StripeSubscriptionCancelDateTime;
                }
                else if (baseSubscriptionBillRecord.Status == BillStatus.Canceled
                         && baseSubscriptionBillRecord.StripeSubscriptionCancelDateTime == null)
                {
                    //// Handle for cancel subscription immediately in Stripe
                    companyVM.BaseSubscriptionEndDateTime = baseSubscriptionBillRecord.PeriodEnd;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Unable to set BaseSubscriptionEndDateTime. Message: {Message}", ex.Message);
            }

            try
            {
                var getProviderConfigsOutputOutput = await _providersApi.ProvidersGetProviderConfigsPostAsync(
                    getProviderConfigsInput: new GetProviderConfigsInput(companyUser.CompanyId));
                var getProviderConfigsOutput = getProviderConfigsOutputOutput.Data;

                companyVM.CrmHubProviderConfigs = getProviderConfigsOutput.ProviderConfigs;
            }
            catch (Exception e)
            {
                companyVM.CrmHubProviderConfigs = new List<ProviderConfigDto>();

                _logger.LogError(
                    e,
                    "Unable to get ProviderConfigs from CrmHub. {ExceptionMessage}",
                    e.Message);
            }

            companyVM.LeadAdsFacebookConfigs = _mapper.Map<List<FacebookConfigViewModel>>(leadads);
            companyVM.FacebookConfigs = _mapper.Map<List<FacebookConfigViewModel>>(messenger);

            // companyVM.CompanyHashtags.ForEach(x => x.Count = _appDbContext.ConversationHashtags.Count(y => y.Hashtag.Id == x.Id));
            companyVM.CompanyHashtags = companyVM.CompanyHashtags.OrderBy(x => x.HashTagColor).ThenBy(x => x.Hashtag)
                .ThenByDescending(x => x.Count).ToList();

            if (!string.IsNullOrEmpty(companyUser.Company.TimeZoneInfoId))
            {
                companyVM.TimeZoneInfo = TimeZoneHelper.GetTimeZoneById(companyUser.Company.TimeZoneInfoId);
            }

            var validBills = await _appDbContext.CompanyBillRecords
                .Where(
                    x => x.CompanyId == companyVM.Id &&
                         x.Status != BillStatus.Inactive &&
                         x.Status != BillStatus.Terminated &&
                         (
                             ValidSubscriptionPlan.AllPaidPlans.Contains(x.SubscriptionPlanId) ||
                             SubscriptionPlansId.FlowBuilderFlowEnrolmentsIncentiveAddOnsForAddOnPurchase.Contains(x.SubscriptionPlanId)
                         ) &&
                         x.PeriodStart < DateTime.UtcNow &&
                         x.PeriodEnd.AddMonths(1) > DateTime.UtcNow)
                .Include(x => x.PurchaseStaff.Identity)
                .Include(x => x.SubscriptionPlan)
                .OrderByDescending(x => x.created)
                .ThenByDescending(x => x.PayAmount)
                .ToListAsync(HttpContext.RequestAborted);

            companyVM.BillRecords = _mapper.Map<List<BillRecordResponse>>(validBills);

            if (companyUser.Company.CompanyType == CompanyType.ResellerClient)
            {
                companyVM.CurrentAgents = await _appDbContext.UserRoleStaffs
                    .AsSplitQuery()
                    .AsNoTracking()
                    .CountAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId &&
                            !_appDbContext.ResellerStaffs
                                .Select(y => y.IdentityId)
                                .Contains(x.IdentityId));
            }
            else
            {
                companyVM.CurrentAgents =
                    await _appDbContext.UserRoleStaffs.Where(x => x.CompanyId == companyVM.Id).CountAsync();
            }

            try
            {
                companyVM.IsSubscriptionActive = validBills.Any(
                    x => ValidSubscriptionPlan.AllSubscriptionPlan.Contains(x.SubscriptionPlanId) &&
                         x.Status == BillStatus.Active);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] error when checking IsSubscriptionActive for company {CompanyId}: {ExceptionMessage}",
                    nameof(GetCompany),
                    companyUser.CompanyId,
                    ex.Message);
            }

            try
            {
                // Duplicated in CompanyUsageService.GetCompanyUsage, but FE is in use
                if (companyUser.Company.MaximumWhatsappInstance == 0 &&
                    validBills.Any(x => ValidSubscriptionPlan.WhatsAppPhoneNumberAddOns.Contains(x.SubscriptionPlanId)))
                {
                    var count = await _companyUsageService
                        .GetCompanyPurchasedWhatsappNumberCount(companyUser.CompanyId);

                    if (count > companyUser.Company.MaximumWhatsappInstance)
                    {
                        await _appDbContext.CompanyCompanies
                            .Where(x => x.Id == companyUser.CompanyId)
                            .ExecuteUpdateAsync(
                                calls => calls.SetProperty(
                                    p => p.MaximumWhatsappInstance, count));

                        companyUser.Company.MaximumWhatsappInstance = count;
                        companyVM.MaximumWhatsappInstance = count;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Error checking and updating maximum WhatsApp instance count for company {CompanyId}",
                    nameof(GetCompany),
                    companyUser.CompanyId);
            }

            try
            {
                companyVM.EnableSensitiveSetting = await _appDbContext.CompanyBillRecords.AnyAsync(
                    x => x.CompanyId == companyUser.CompanyId && x.Status == BillStatus.Active &&
                         (ValidSubscriptionPlan.DataMaskingAddOns.Contains(x.SubscriptionPlanId) ||
                          ValidSubscriptionPlan.EnterpriseTier.Contains(x.SubscriptionPlanId)) && DateTime.UtcNow < x.PeriodEnd);
                companyVM.IsExceededTwilioDailyLimit = false;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] error when checking EnableSensitiveSetting and IsExceededTwilioDailyLimit for company {CompanyId}: {ExceptionMessage}",
                    nameof(GetCompany),
                    companyUser.CompanyId,
                    ex.Message);
            }

            companyVM.IsShowQRCodeMapping = await _appDbContext.CompanyAssignmentRules.AnyAsync(
                x => x.CompanyId == companyUser.CompanyId && x.AutomationType == AutomationType.QRCodeAssigneeMapping &&
                     x.Status == AutomationStatus.Live);
            companyVM.IsSandbox =
                await _appDbContext.CompanySandboxes.AnyAsync(x => x.CompanyId == companyUser.CompanyId);
            companyVM.IsStripePaymentEnabled = await _appDbContext.ConfigStripePaymentConfigs.AnyAsync(
                x => x.CompanyId == companyUser.CompanyId && x.Status == StripePaymentRegistrationStatus.Registered);

            if (companyUser.Company.CompanyType != CompanyType.DirectClient)
            {
                ResellerCompanyProfile resellerCompanyProfile;

                var resellerVM = new ResellerCompanyProfileViewModel();
                if (companyUser.Company.CompanyType == CompanyType.ResellerClient)
                {
                    resellerCompanyProfile = await _appDbContext.ResellerCompanyProfiles.AsNoTracking()
                        .Include(x => x.ClientCompanyProfiles).Include(x => x.ResellerProfileLogo)
                        .Include(x => x.Company).FirstOrDefaultAsync(
                            x => x.ClientCompanyProfiles.Any(y => y.ClientCompanyId == companyUser.CompanyId));
                    resellerVM.ContactEmail = resellerCompanyProfile?.Email;
                }
                else
                {
                    resellerCompanyProfile = await _appDbContext.ResellerCompanyProfiles.AsNoTracking()
                        .Include(x => x.ResellerProfileLogo).Include(x => x.Company)
                        .FirstOrDefaultAsync(x => x.CompanyId == companyUser.CompanyId);
                }

                if (resellerCompanyProfile != null)
                {
                    resellerVM.CompanyProfileId = resellerCompanyProfile.Id;
                    resellerVM.CompanyName = resellerCompanyProfile.Company.CompanyName;
                    if (resellerCompanyProfile.ResellerProfileLogo != null)
                    {
                        resellerVM.LogoLink = _azureBlobStorageService.GetAzureBlobSasUri(
                            resellerCompanyProfile.ResellerProfileLogo.Filename,
                            resellerCompanyProfile.ResellerProfileLogo.BlobContainer,
                            24);
                    }
                }

                companyVM.Reseller = resellerVM;
            }

            if (await _appDbContext.UserRoleStaffs
                    .AnyAsync(
                        x =>
                            x.IdentityId == companyUser.IdentityId
                            && x.CompanyId != companyUser.CompanyId))
            {
                // if in more than one company
                var companyIds = _appDbContext.UserRoleStaffs
                    .Where(x => x.IdentityId == companyUser.IdentityId)
                    .Select(x => x.CompanyId);

                companyVM.AssociatedCompaniesList = await _appDbContext.CompanyCompanies
                    .Where(x => companyIds.Contains(x.Id))
                    .Include(x => x.CompanyIconFile)
                    .ProjectTo<AssociatedCompany>(_mapper.ConfigurationProvider)
                    .ToListAsync(HttpContext.RequestAborted);

                companyVM.AssociatedCompaniesList.ForEach(
                    x => x.CompanyIconURL = x.CompanyIconFile != null
                        ? _azureBlobStorageService.GetAzureBlobSasUri(
                            x.CompanyIconFile?.Filename,
                            x.CompanyIconFile?.BlobContainer,
                            24)
                        : null);
                companyVM.AssociatedCompaniesList.ForEach(x => x.CompanyIconFile = null);
            }

            var billingPeriodUsages =
                await _companyUsageService.GetBillingPeriodUsages(companyUser.CompanyId, false);
            var currentSubscriptionPlan = billingPeriodUsages.FirstOrDefault()?.BillRecord.SubscriptionPlan;

            var currentAddOns = await _appDbContext.CompanyBillRecords.Where(
                    b =>
                        b.CompanyId == companyUser.CompanyId
                        && (ValidSubscriptionPlan.AddOn.Contains(b.SubscriptionPlanId)
                            || ValidSubscriptionPlan.AgentPlan.Contains(b.SubscriptionPlanId))
                        && b.PeriodStart < DateTime.UtcNow
                        && b.PeriodEnd > DateTime.UtcNow
                        && b.Status == BillStatus.Active)
                .ToListAsync(HttpContext.RequestAborted);

            // Apply Usage Offset
            companyVM.MaximumAgents += companyUser.Company?.UsageLimitOffsetProfile?.AgentsLimitOffset ?? 0;
            companyVM.MaximumWhatsappInstance +=
                companyUser.Company?.UsageLimitOffsetProfile?.WhatsappInstanceLimitOffset ?? 0;
            companyVM.MaximumNumberOfChannel += companyUser.Company?.UsageLimitOffsetProfile?.ChannelLimitOffset ?? 0;
            companyVM.MaximumAutomations += companyUser.Company?.UsageLimitOffsetProfile?.AutomationsLimitOffset ?? 0;

            try
            {
                var (countryTier, _) = await _subscriptionCountryTierDeterminer.DetermineAsync(companyId, ipAddress);
                companyVM.SubscriptionCountryTier = countryTier.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName}] Unable to retrieve subscription country tier. CompanyId: {CompanyId}, IPAddress: {IPAddress}, ErrorMessage: {ErrorMessage}",
                    nameof(GetCompany),
                    companyId,
                    ipAddress,
                    ex.Message);
            }

            companyVM.IsEntitleToFlowEnrollmentIncentive = await _companyOfferService.IsEntitleToFlowEnrollmentAddOnPurchaseIncentiveAsync(companyUser.CompanyId);
            companyVM.V10SubscriptionMigrationStatus = companyUser.Company.SubscriptionMigrationStatus;

            var planDefinitionFromCurrentSubscriptionPlan = await _planDefinitionService.GetPlanDefinitionAsync(currentSubscriptionPlan.Id);
            var planDefinitionsFromCurrentAddons = await _planDefinitionService.GetPlanDefinitionsAsync(currentAddOns.Select(a => a.SubscriptionPlanId).ToList());

            if (ValidSubscriptionPlan.EnterpriseTier.Contains(currentSubscriptionPlan.Id))
            {
                var isHubspotIntegrationEnabled =
                    planDefinitionFromCurrentSubscriptionPlan.FeatureQuantities.Any(x => x.FeatureId == FeatureId.HubSpotIntegration)
                    || planDefinitionsFromCurrentAddons.Any(x => x.PlanType == PlanTypes.AddOns && x.FeatureQuantities.Any(y => y.FeatureId == FeatureId.HubSpotIntegration));

                companyVM.AddonStatus = new AddonStatus
                {
                    IsAdditionalStaffEnabled = true,
                    IsAdditionalContactsEnabled = true,
                    IsUnlimitedContactEnabled = true,
                    IsUnlimitedChannelEnabled = true,
                    IsEnterpriseContactMaskingEnabled = true,
                    IsWhatsappQrCodeEnabled = true,
                    IsShopifyIntegrationEnabled = true,
                    IsHubspotIntegrationEnabled = isHubspotIntegrationEnabled,
                    IsPaymentIntegrationEnabled = true,
                    IsSalesforceCrmEnabled = IsSalesforceCrmEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                    IsSalesforceMarketingCloudEnabled = IsSalesforceMarketingCloudEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                    IsSalesforceCommerceCloudEnabled = IsSalesforceCommerceCloudEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                    IsOnboardingSupportActivated = true,
                    IsPrioritySupportActivated = true,
                    IsChatbotSetupSupportActivated = true,
                    IsHubspotIntegrationFreeTrialEligible =
                        await _companySubscriptionService.IsHubspotIntegrationFreeTrialEligible(companyUser.CompanyId),
                    IsSalesforceCrmFreeTrialEligible =
                        await _companySubscriptionService.IsSalesforceCrmFreeTrialEligible(companyUser.CompanyId),
                    IsMicrosoftDynamics365IntegrationEnabled = IsMicrosoftDynamics365IntegrationEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                    IsFacebookLeadAdsEnabled = IsFacebookLeadAdsEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                    IsPlatformApiEnabled = IsPlatformApiEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                    IsMakeIntegrationEnabled = IsMakeIntegrationEnabledEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                    IsZapierIntegrationEnabled = IsZapierIntegrationEnabledEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                    IsPiiMaskingEnabled = true
                };
            }
            else
            {
                try
                {
                    companyVM.AddonStatus = new AddonStatus
                    {
                        IsAdditionalStaffEnabled =
                            _isEnableTenantHubLogic
                                ? planDefinitionsFromCurrentAddons.Any(
                                    p => p.PlanType == PlanTypes.AddOns && p.FeatureQuantities.Any(
                                        fq => fq.FeatureId == "agents"))
                                : currentAddOns.Any(b => ValidSubscriptionPlan.AgentPlan.Contains(b.SubscriptionPlanId)),
                        IsAdditionalContactsEnabled =
                            _isEnableTenantHubLogic
                                ? planDefinitionsFromCurrentAddons.Any(
                                    p => p.PlanType == PlanTypes.AddOns && p.FeatureQuantities.Any(
                                        fq => fq.FeatureId == "contacts"))
                                : currentAddOns.Any(
                                    b => ValidSubscriptionPlan.AdditionalContactAddOns.Contains(b.SubscriptionPlanId) &&
                                         b.SubscriptionPlanId.Contains("additional")),
                        IsUnlimitedContactEnabled =
                            _isEnableTenantHubLogic
                                ? planDefinitionsFromCurrentAddons.Any(
                                    p => p.PlanType == PlanTypes.AddOns && p.FeatureQuantities.Any(
                                        fq => fq.FeatureId == "contacts" && fq.Quantity >= 1000000))
                                : currentAddOns.Any(
                                    b => ValidSubscriptionPlan.AdditionalContactAddOns.Contains(b.SubscriptionPlanId) &&
                                         b.SubscriptionPlanId.Contains("unlimited")),
                        IsUnlimitedChannelEnabled =
                            _isEnableTenantHubLogic
                                ? planDefinitionsFromCurrentAddons.Any(
                                    p => p.PlanType == PlanTypes.AddOns && p.FeatureQuantities.Any(
                                        fq => fq.FeatureId == "channels" && fq.Quantity >= 999))
                                : currentAddOns.Any(
                                    b => ValidSubscriptionPlan.AdditionalChannelAddOns.Contains(b.SubscriptionPlanId)),
                        IsEnterpriseContactMaskingEnabled = IsEnterpriseContactMaskingEnabled(companyVM.EnableSensitiveSetting, planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                        IsWhatsappQrCodeEnabled = IsWhatsAppQrCodeEnabled(companyVM.IsShowQRCodeMapping, planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                        IsShopifyIntegrationEnabled = IsShopifyIntegrationEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                        IsHubspotIntegrationEnabled = IsHubSpotIntegrationEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                        IsPaymentIntegrationEnabled = IsStripeIntegrationEnabled(companyVM.IsStripePaymentEnabled, planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                        IsSalesforceCrmEnabled = IsSalesforceCrmEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                        IsSalesforceMarketingCloudEnabled = IsSalesforceMarketingCloudEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                        IsSalesforceCommerceCloudEnabled = IsSalesforceCommerceCloudEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                        IsOnboardingSupportActivated =
                            _isEnableTenantHubLogic
                                ? planDefinitionsFromCurrentAddons.Any(
                                      p => p.PlanType == PlanTypes.AddOns && p.FeatureQuantities.Any(
                                          fq => fq.FeatureId == "onboarding_support"))
                                  || planDefinitionFromCurrentSubscriptionPlan.FeatureQuantities.Any(fq => fq.FeatureId == "onboarding_support")
                                : currentAddOns.Any(
                                      b => ValidSubscriptionPlan.OnboardingSupportAddOns.Contains(
                                          b.SubscriptionPlanId)) ||
                                  (currentSubscriptionPlan.Version == 9 &&
                                   currentSubscriptionPlan.Id.Contains("yearly") &&
                                   (currentSubscriptionPlan.SubscriptionTier ==
                                    SubscriptionTier.Premium ||
                                    currentSubscriptionPlan.SubscriptionTier ==
                                    SubscriptionTier.Pro)),
                        IsPrioritySupportActivated =
                            _isEnableTenantHubLogic
                                ? planDefinitionsFromCurrentAddons.Any(
                                         p => p.PlanType == PlanTypes.AddOns && p.FeatureQuantities.Any(
                                             fq => fq.FeatureId == "priority_support"))
                                  || planDefinitionFromCurrentSubscriptionPlan.FeatureQuantities.Any(fq => fq.FeatureId == "priority_support")
                                : currentAddOns.Any(
                                      b => ValidSubscriptionPlan.PrioritySupportAddOns.Contains(
                                          b.SubscriptionPlanId)) ||
                                  (currentSubscriptionPlan.Version == 9 &&
                                   currentSubscriptionPlan.SubscriptionTier ==
                                   SubscriptionTier.Premium),
                        IsChatbotSetupSupportActivated =
                            _isEnableTenantHubLogic
                                ? planDefinitionsFromCurrentAddons.Any(
                                    p => p.PlanType == PlanTypes.AddOns && p.FeatureQuantities.Any(
                                        fq => fq.FeatureId.Contains("chatbot_automation_support")))
                                : currentAddOns.Any(
                                    b => ValidSubscriptionPlan.ChatbotAutomationSupportAddOns
                                        .Contains(b.SubscriptionPlanId)),
                        IsHubspotIntegrationFreeTrialEligible =
                            await _companySubscriptionService.IsHubspotIntegrationFreeTrialEligible(
                                companyUser.CompanyId),
                        IsSalesforceCrmFreeTrialEligible =
                            await _companySubscriptionService.IsSalesforceCrmFreeTrialEligible(companyUser.CompanyId),
                        IsMicrosoftDynamics365IntegrationEnabled = IsMicrosoftDynamics365IntegrationEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                        IsFacebookLeadAdsEnabled = IsFacebookLeadAdsEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                        IsPlatformApiEnabled = IsPlatformApiEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                        IsMakeIntegrationEnabled = IsMakeIntegrationEnabledEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                        IsZapierIntegrationEnabled = IsZapierIntegrationEnabledEnabled(planDefinitionFromCurrentSubscriptionPlan, planDefinitionsFromCurrentAddons, currentAddOns),
                    };
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[{MethodName} endpoint] Pricing revamp error for company {CompanyId}: {ExceptionMessage}",
                        nameof(GetCompany),
                        companyUser.CompanyId,
                        ex.Message);
                }
            }

            // await _cacheService.SaveCacheData(key, JsonConvert.SerializeObject(companyVM), TimeSpan.FromHours(12));
            await _cacheManagerService.SaveCacheAsync(getCompanyInfoCacheKeyPattern, companyVM);
            return Ok(companyVM);
        }

        private static void PopulateTravisUserTypeFieldOptions(Staff companyUser, List<Staff> staffs)
        {
            // Custom user profile fields that requires mapping staff info according to the identity id
            // stored in the field value
            var staffUserFields = companyUser.Company.CustomUserProfileFields
                .Where(x => x.Type == FieldDataType.TravisUser)
                .ToList();

            if (staffUserFields.Any())
            {
                foreach (var staffUserField in staffUserFields)
                {
                    var staffUserFieldOptions = staffs.Select(
                        staff => new CustomUserProfileFieldOption
                        {
                            Value = staff.IdentityId,
                            CompanyCustomUserProfileFieldId = staffUserField.Id,
                            CustomUserProfileFieldOptionLinguals = new()
                            {
                                new ()
                                {
                                    Language = "en",
                                    DisplayName = !string.IsNullOrEmpty(staff.Identity.DisplayName)
                                        ?
                                        staff.Identity.DisplayName
                                        : !string.IsNullOrEmpty(staff.Identity.FirstName)
                                            ? staff.Identity.FirstName
                                            :
                                            staff.Identity.UserName
                                }
                            }
                        });

                    // Always instantiate a new list so that it is the company latest staffs list
                    // Ignore those options loaded from DB for company staff field type
                    staffUserField.CustomUserProfileFieldOptions = new();
                    staffUserField.CustomUserProfileFieldOptions.AddRange(staffUserFieldOptions);
                }
            }
        }

        [HttpPost]
        [Route("Company/Update")]
        public async Task<IActionResult> Update([FromBody] UpdateCompanyNameViewModel updateCompanyNameViewModel)
        {
            if (User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized(
                        new ResponseViewModel
                        {
                            message = "no permission"
                        });
                }

                var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == companyUser.CompanyId)
                    .FirstOrDefaultAsync();
                if (!string.IsNullOrEmpty(updateCompanyNameViewModel.CompanyName))
                {
                    company.CompanyName = updateCompanyNameViewModel.CompanyName;
                }

                if (!string.IsNullOrEmpty(updateCompanyNameViewModel.TimeZoneInfoId))
                {
                    if (TimeZoneHelper.GetTimeZoneByIdOrDefault(updateCompanyNameViewModel.TimeZoneInfoId) == null)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = $"Invalid TimeZoneInfoId: {updateCompanyNameViewModel.TimeZoneInfoId}"
                            });
                    }

                    company.TimeZoneInfoId = updateCompanyNameViewModel.TimeZoneInfoId;
                }

                await _appDbContext.SaveChangesAsync();

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                var responseVM = _mapper.Map<CompanyResponse>(company);
                return Ok(responseVM);
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "error"
                });
        }

#if OBSOLETE_AUTH
        [Obsolete]
        [HttpGet]
        [Route("Company/redeem")]
        public async Task<IActionResult> GetRedeemHistory()
        {

            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
                return Unauthorized(new ResponseViewModel { message = "no permission" });

            try
            {
                var histories = await _coreService.GetRedeemHistory(companyUser.CompanyId);
                var response = _mapper.Map<List<RedeemPromotionRecordResponse>>(histories);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseViewModel { message = ex.Message });
            }
        }

        [Obsolete]
        [HttpPost]
        [Route("Company/redeem")]
        public async Task<ActionResult<ResponseViewModel>> RedeemPromotionCode([FromBody] ApplyPromotionCodeViewModel applyPromotionCodeViewModel)
        {

            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
                return Unauthorized(new ResponseViewModel { message = "no permission" });

            try
            {
                await _coreService.RedeemPromotionCode(applyPromotionCodeViewModel, companyUser.CompanyId);
                return Ok(new ResponseViewModel { message = "success" });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseViewModel { message = ex.Message });
            }
        }
#endif

        [HttpGet]
        [Route("Company/usage")]
        public async Task<IActionResult> GetCompanyUsage()
        {
            if (User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized(
                        new ResponseViewModel
                        {
                            message = "no permission"
                        });
                }

                var response = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);

                return Ok(response);
            }

            return BadRequest();
        }

#if OBSOLETE_AUTH
        [Obsolete]
        [HttpGet]
        [Route("Company/OnboardingProgress")]
        public async Task<IActionResult> GetOnboardingProgress()
        {
            if (User.Identity.IsAuthenticated)
            {

                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                    return Unauthorized(new ResponseViewModel { message = "no permission" });

                var company =
 await _appDbContext.CompanyCompanies.AsSplitQuery().Where(x => x.Id == companyUser.CompanyId).Include(x => x.FacebookConfigs).Include(x => x.WhatsAppConfigs).Include(x => x.LineConfigs).Include(x => x.WeChatConfig).Include(x => x.EmailConfig).Include(x => x.SMSConfigs).Include(x => x.CompanyCustomFields).FirstOrDefaultAsync();

                var onboardingStatus = new OnboardingStatus();
                onboardingStatus.IsMessagingChannelAdded =
 (company.FacebookConfigs?.Count > 0 || company.WhatsAppConfigs?.Count > 0 || company.WeChatConfig != null || company.LineConfigs?.Count > 0 || company.SMSConfigs?.Count > 0 || company.EmailConfig != null);
                onboardingStatus.IsWebWidgetCustomized =
 (company.CompanyCustomFields.Where(x => x.CompanyId == company.Id && x.Category.ToLower() == "messaging").Count() > 0);
                onboardingStatus.IsWebWidgetAdded =
 (_appDbContext.SenderWebClientSenders.Where(x => x.CompanyId == company.Id).Count() > 0);
                onboardingStatus.IsInvitedTeammate =
 (_appDbContext.UserRoleStaffs.Where(x => x.CompanyId == company.Id).Count() > 1);
                onboardingStatus.IsAssignmentRuleAdded =
 (_appDbContext.CompanyNotificationRecords.Where(x => x.CompanyId == company.Id && (x.EventName == "OnAssignmentRuleChanged" || x.EventName == "OnAssignmentRuleAdded")).Count() > 0);
                onboardingStatus.IsBroadcastMessageSent =
 (_appDbContext.CompanyNotificationRecords.Where(x => x.CompanyId == company.Id && x.EventName == "OnBroadcastCampaignSent").Count() > 0);

                return Ok(onboardingStatus);
            }
            return BadRequest();
        }
#endif

        [HttpGet]
        [Route("v2/Company/OnboardingProgress")]
        public async Task<IActionResult> GetV2OnboardingProgress()
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized(
                        new ResponseViewModel
                        {
                            message = "no permission"
                        });
                }

                var defaultQuickReplies = new string[]
                {
                    "Welcome",
                    "Thanks",
                    "Away",
                    "Close"
                };
                var onboardingStatus = await _appDbContext.CompanyCompanies.Where(x => x.Id == companyUser.CompanyId)
                    .Select(
                        x => new NewOnboardingStatus
                        {
                            IsMessagingChannelConnected =
                                x.FacebookConfigs.Any() || x.WhatsAppConfigs.Any() || x.WeChatConfig != null ||
                                 x.LineConfigs.Any() ||
                                 x.SMSConfigs.Any() || x.EmailConfig != null || x.ViberConfigs.Any() ||
                                 x.ShoplineConfigs.Any() || x.ShopifyConfigs.Any() || x.InstagramConfigs.Any() ||
                                 x.TelegramConfigs.Any() || x.TikTokConfigs.Any() || x.WhatsApp360DialogConfigs.Any(),
                            IsQuickReplyAdded =
                                x.QuickReplies.Where(y => !defaultQuickReplies.Contains(y.Value)).Any(),
                            IsInvitedTeammate = x.Staffs.Count > 1,
                        }).FirstOrDefaultAsync();
                onboardingStatus.IsWhatsappConsultationBooked = await _appDbContext.UserRoleStaffs.AnyAsync(
                    y => y.CompanyId == companyUser.CompanyId && y.IsWhatsappConsultationBooked);
                onboardingStatus.IsInboxDemoCompleted = await _appDbContext.UserRoleStaffs.AnyAsync(
                    y => y.CompanyId == companyUser.CompanyId && y.IsInboxDemoCompleted);
                onboardingStatus.IsInboxInUse =
                    await _appDbContext.Conversations.AnyAsync(y => y.CompanyId == companyUser.CompanyId);
                onboardingStatus.IsAutomationRuleAdded = await _appDbContext.CompanyAssignmentRules.AnyAsync(
                    y => y.CompanyId == companyUser.CompanyId && y.AssignmentRuleName != "Default");
                onboardingStatus.IsContactListCreated = await _appDbContext.CompanyImportContactHistories.AnyAsync(
                    y => y.CompanyId == companyUser.CompanyId &&
                         (y.Status == ImportStatus.Imported || y.Status == ImportStatus.Saved));
                onboardingStatus.IsCampaignCreated = await _appDbContext.CompanyMessageTemplates.AnyAsync(
                    y => y.CompanyId == companyUser.CompanyId && (y.SentAt != null || y.ScheduledAt != null));
                onboardingStatus.IsWebWidgetAdded =
                    await _appDbContext.SenderWebClientSenders.AnyAsync(x => x.CompanyId == companyUser.CompanyId);

                return Ok(onboardingStatus);
            }

            return BadRequest();
        }

        [HttpGet]
        [Route("Company/Staff")]
        public async Task<IActionResult> GetCompanyStaff(
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 20)
        {
            if (User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Company).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                List<Staff> staffs = new List<Staff>();

                if (companyUser.Company.CompanyType != CompanyType.ResellerClient)
                {
                    staffs = await _appDbContext.UserRoleStaffs
                        .Where(x => x.CompanyId == companyUser.CompanyId && x.Id != 1).Include(x => x.Identity)
                        .Include(x => x.ProfilePicture).OrderBy(x => x.Order).ThenBy(x => x.Id).Skip(offset).Take(limit)
                        .ToListAsync(HttpContext.RequestAborted);

                    foreach (var staff in staffs)
                    {
                        staff.QRCodeChannel = await _companyService.GetTargetedChannel(staff);
                    }
                }
                else
                {
                    var allStaffs = await _appDbContext.UserRoleStaffs.AsNoTracking()
                        .Where(x => x.CompanyId == companyUser.CompanyId && x.Id != 1).Include(x => x.Identity)
                        .Include(x => x.ProfilePicture).ToListAsync(HttpContext.RequestAborted);
                    var resellerStaffIdentityIds = await _appDbContext.ResellerStaffs.AsNoTracking()
                        .Include(x => x.ResellerCompanyProfile).ThenInclude(x => x.ClientCompanyProfiles).Where(
                            x => x.ResellerCompanyProfile.ClientCompanyProfiles.Any(
                                y => y.ClientCompanyId == companyUser.CompanyId)).Select(x => x.IdentityId)
                        .ToListAsync(HttpContext.RequestAborted);
                    staffs = allStaffs.Where(x => !resellerStaffIdentityIds.Contains(x.IdentityId))
                        .OrderBy(x => x.Order).ThenBy(x => x.Id).Skip(offset).Take(limit).ToList();

                    foreach (var staff in staffs)
                    {
                        staff.QRCodeChannel = await _companyService.GetTargetedChannel(staff);
                    }
                }

                // Adhoc for SCMP
                if (companyUser.CompanyId is "c39b40f9-38c3-4e2a-bc53-af46389363a9"
                    or "5edfbcc3-2179-405f-b860-0afd7d2356e3" or "c3c8fa8c-c64f-4f94-873d-250845bf71cf")
                {
                    switch (companyUser.RoleType)
                    {
                        case StaffUserRole.TeamAdmin:
                        case StaffUserRole.Staff:
                            var teamMembers = await _appDbContext.CompanyTeamMembers
                                .Where(
                                    x =>
                                        _appDbContext.CompanyTeamMembers
                                            .Where(x => x.StaffId == companyUser.Id)
                                            .Select(x => x.CompanyTeamId)
                                            .Contains(x.CompanyTeamId))
                                .Select(x => x.StaffId)
                                .ToListAsync(HttpContext.RequestAborted);

                            staffs = staffs
                                .Where(x => teamMembers.Contains(x.Id))
                                .ToList();

                            break;
                    }
                }

                var staffsVM = _mapper.Map<List<StaffWithoutCompanyResponse>>(staffs);
                staffsVM.ForEach(x => x.TimeZoneInfo = TimeZoneHelper.GetTimeZoneById(x.TimeZoneInfoId));

                return Ok(staffsVM);
            }

            return BadRequest();
        }

        [AuthoriseUser]
        [HttpGet("Company/StaffOverviews")]
        [Consumes("application/json")]
        [Produces("application/json")]
        public async Task<ActionResult<List<StaffOverviewResponse>>> GetCompanyStaffOverviews(
            [FromQuery]
            GetCompanyStaffRequestQuery query,
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 20)
        {
            var staff = _httpContextAccessor.HttpContext.GetRequestCompanyStaff();

            // Validate sort parameters if needed
            if (!string.IsNullOrEmpty(query.SortBy))
            {
                var sortFields = query.GetSortFields();
                if (sortFields.Contains(StaffSortField.None))
                {
                    return BadRequest(new
                    {
                        message =
                            $"Invalid sort_by value: {query.SortBy}. Valid values are: display_name, email, role, status, position, created_at, invite_expired, invite_pending"
                    });
                }
            }

            var result =
                await _companyStaffControllerService.GetCompanyStaffAsync(
                    staff.CompanyId,
                    staff.Company.CompanyType,
                    query,
                    offset,
                    limit);

            return Ok(result.Results);
        }

        [AuthoriseUser]
        [HttpGet("Company/StaffOverviews/Count")]
        public async Task<IActionResult> GetCompanyStaffOverviewCount([FromQuery] GetCompanyStaffRequestQuery query)
        {
            var staff = _httpContextAccessor.HttpContext.GetRequestCompanyStaff();

            var count = await _companyStaffControllerService.CountCompanyStaffAsync(staff.CompanyId, staff.Company.CompanyType, query);

            return Ok(count);
        }

        [HttpGet]
        [Route("v2/company/Staff")]
        public async Task<IActionResult> GetV2CompanyStaff(
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 20,
            [FromQuery(Name = "staffName")]
            string staffName = null)
        {
            if (User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Company).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var staffs = await _appDbContext.UserRoleStaffs
                    .Where(
                        x => x.CompanyId == companyUser.CompanyId && x.Id != 1 && staffName != null &&
                            (x.Identity.DisplayName.Contains(staffName) || x.Identity.FirstName.Contains(staffName) ||
                             x.Identity.LastName.Contains(staffName))).Include(x => x.Identity)
                    .Include(x => x.ProfilePicture).OrderBy(x => x.Order).ThenBy(x => x.Id).Skip(offset).Take(limit)
                    .ToListAsync(HttpContext.RequestAborted);

                // Adhoc for SCMP
                if (companyUser.CompanyId is "c39b40f9-38c3-4e2a-bc53-af46389363a9"
                    or "5edfbcc3-2179-405f-b860-0afd7d2356e3" or "c3c8fa8c-c64f-4f94-873d-250845bf71cf")
                {
                    switch (companyUser.RoleType)
                    {
                        case StaffUserRole.TeamAdmin:
                        case StaffUserRole.Staff:
                            var teamMembers = await _appDbContext.CompanyTeamMembers
                                .Where(
                                    x =>
                                        _appDbContext.CompanyTeamMembers
                                            .Where(x => x.StaffId == companyUser.Id)
                                            .Select(x => x.CompanyTeamId)
                                            .Contains(x.CompanyTeamId))
                                .Select(x => x.StaffId)
                                .ToListAsync(HttpContext.RequestAborted);

                            staffs = staffs
                                .Where(x => teamMembers.Contains(x.Id))
                                .ToList();
                            break;
                    }
                }

                var staffsVM = _mapper.Map<List<StaffWithoutCompanyResponse>>(staffs);

                staffsVM.ForEach(x => x.TimeZoneInfo = TimeZoneHelper.GetTimeZoneById(x.TimeZoneInfoId));

                foreach (var staff in staffsVM)
                {
                    var assoicatedTeams = await _appDbContext.CompanyStaffTeams
                        .Where(x => x.Members.Any(y => y.Staff.IdentityId == staff.UserInfo.Id)).ToListAsync(HttpContext.RequestAborted);
                    var response = _mapper.Map<List<CompanyTeamResponse>>(assoicatedTeams);
                    staff.AssociatedTeams = response;
                }

                return Ok(staffsVM);
            }

            return BadRequest();
        }

        [HttpPost]
        [Route("company/staff/qrcode/{staffId}")]
        public async Task<IActionResult> UpdateStaffQRCodeSetting(
            string staffId,
            [FromBody]
            StaffInfoViewModel staffInfoViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "data error"
                    });
            }

            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Company).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser.RoleType == StaffUserRole.Staff && staffId != companyUser.IdentityId)
            {
                return Unauthorized(
                    new ResponseViewModel
                    {
                        message = "Permission denied"
                    });
            }

            var staff = await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == companyUser.CompanyId && x.IdentityId == staffId).FirstOrDefaultAsync();

            if (staffInfoViewModel.QRCodeIdentity != null)
            {
                // Check for duplicate QRCodeIdentity in the same company
                var existingStaffWithSameQRCode = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyUser.CompanyId
                            && x.QRCodeIdentity == staffInfoViewModel.QRCodeIdentity
                            && x.IdentityId != staffId) // Exclude current staff
                    .FirstOrDefaultAsync();

                if (existingStaffWithSameQRCode != null)
                {
                    return BadRequest(new ResponseViewModel
                    {
                        message = "QR Code Identity is already in use by another staff member in your company"
                    });
                }

                staff.QRCodeIdentity = staffInfoViewModel.QRCodeIdentity;
            }

            if (staffInfoViewModel.QRCodeChannel != null)
            {
                staff.QRCodeChannel = staffInfoViewModel.QRCodeChannel;
            }

            await _appDbContext.SaveChangesAsync();

            if (companyUser.RoleType == StaffUserRole.Admin || companyUser.RoleType == StaffUserRole.TeamAdmin)
            {
                await _companyTeamService.AddOrRemoveTeam(companyUser.CompanyId, staff.Id, staffInfoViewModel.TeamIds);
            }

            var staffsVM = _mapper.Map<StaffWithoutCompanyResponse>(staff);
            return Ok(staffsVM);
        }

        [HttpGet]
        [Route("Company/Staff/Qrcode")]
        public async Task<IActionResult> GetCompanyStaffQrcode()
        {
            if (User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Identity).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Ok();
                }

                try
                {
                    dynamic response = await GetStaffQRCode(companyUser.CompanyId, companyUser);

                    return Ok(response);
                }
                catch (Exception ex)
                {
                    return BadRequest(ex);
                }
            }

            return BadRequest();
        }

        [HttpGet]
        [Route("Company/Staff/Qrcode/{userId}")]
        public async Task<IActionResult> GetStaffQrcode(string userId)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Ok();
                }

                var staff = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyUser.CompanyId && x.IdentityId == userId)
                    .Include(x => x.Identity).FirstOrDefaultAsync();

                dynamic response = await GetStaffQRCode(companyUser.CompanyId, staff);

                return Ok(response);
            }

            return BadRequest();
        }

        private async Task<dynamic> GetStaffQRCode(string companyId, Staff staff)
        {
            var (url, qrCodeUrl) = await _companyService.GetStaffQrcode(staff);

            var context = await _externalFileService.DownloadFileFromUrlAsync(qrCodeUrl);
            var qrCodeImage = context.FileContent.ToArray();
            var stream = new MemoryStream(qrCodeImage);

            var qrCodeFilename = $"qrcode/staff/{staff.QRCodeIdentity}.jpg";
            await _uploadService.UploadFileBySteam(companyId, qrCodeFilename, stream);

            dynamic response = new JObject();
            response.url = url;
            response.qrcodeBase64 = Convert.ToBase64String(qrCodeImage);
            response.qrcodeUrl = qrCodeUrl;
            return response;
        }

        [HttpGet]
        [Route("Company/Staff/{userId}")]
        public async Task<IActionResult> GetCompanyStaff(string userId)
        // Note: The parameter is named 'userId' because it refers to the staff's identity ID
        // This endpoint retrieves staff information by their identity ID (Id in AspNetUsers), not by staff ID
        {
            var user = await _userManager.GetUserAsync(User);
            var companyUser = await _coreService.GetCompanyStaff(user);

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var staffs = await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == companyUser.CompanyId && x.IdentityId == userId)
                .Include(x => x.Identity)
                .Include(x => x.ProfilePicture)
                .ToListAsync(HttpContext.RequestAborted);

            foreach (var staff in staffs)
            {
                staff.QRCodeChannel = await _companyService.GetTargetedChannel(staff);
            }

            var staffsVM = _mapper.Map<List<StaffWithoutCompanyResponse>>(staffs);

            staffsVM.ForEach(x => x.TimeZoneInfo = TimeZoneHelper.GetTimeZoneById(x.TimeZoneInfoId));

            foreach (var staff in staffsVM)
            {
                var associatedTeams = await _appDbContext.CompanyStaffTeams
                    .Where(x => x.Members.Any(y => y.Staff.IdentityId == staff.UserInfo.Id)).ToListAsync(HttpContext.RequestAborted);
                var response = _mapper.Map<List<CompanyTeamResponse>>(associatedTeams);
                staff.AssociatedTeams = response;
            }

            if (companyUser.IsNewlyRegistered)
            {
                await _coreService.UpdateStaffIsNewlyRegistered(user, false);
            }

            return Ok(staffsVM);
        }

        [HttpGet]
        [Route("v2/Company/Staff/{staffId}")]
        public async Task<IActionResult> GetV2CompanyStaff(string staffId)
        {
            if (User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Company).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser != null)
                {
                    var staffs = await _appDbContext.UserRoleStaffs
                        .Where(x => x.CompanyId == companyUser.CompanyId && x.IdentityId == staffId)
                        .Include(x => x.Identity).Include(x => x.ProfilePicture).ToListAsync(HttpContext.RequestAborted);

                    foreach (var staff in staffs)
                    {
                        staff.QRCodeChannel = await _companyService.GetTargetedChannel(staff);
                    }

                    var staffsVM = _mapper.Map<List<StaffWithoutCompanyResponse>>(staffs);

                    staffsVM.ForEach(x => x.TimeZoneInfo = TimeZoneHelper.GetTimeZoneById(x.TimeZoneInfoId));

                    foreach (var staff in staffsVM)
                    {
                        var staffRoles = await _rbacService.GetRbacUserRoles(companyUser.CompanyId, staff.StaffId.ToString());

                        var assoicatedTeams = await _appDbContext.CompanyStaffTeams.Where(
                                x => x.Members.Where(y => y.Staff.IdentityId == staff.UserInfo.Id).Count() > 0)
                            .ToListAsync(HttpContext.RequestAborted);
                        var response = _mapper.Map<List<CompanyTeamResponse>>(assoicatedTeams);

                        staff.RbacRoles = staffRoles.Roles;
                        staff.AssociatedTeams = response;
                        staff.IsCompanyOwner = await _companyStaffControllerService.IsCompanyOwnerAsync(companyUser.CompanyId, staffId);
                    }

                    return Ok(staffsVM);
                }
            }

            return BadRequest();
        }

#if OBSOLETE_AUTH
        [Obsolete]
        [HttpPost]
        [Route("Company/Staff/ResetPassword/{staffId}")]
        public async Task<ActionResult<StaffWithoutCompanyResponse>> ChangeStaffPassword(string staffId, [FromBody] StaffChangePassword updatePassword)
        {
            if (User.Identity.IsAuthenticated)
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);


                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Company).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser.RoleType == StaffUserRole.Staff && staffId != companyUser.IdentityId)
                    return Unauthorized(new ResponseViewModel { message = "Permission denied" });

                var staffs =
 await _appDbContext.UserRoleStaffs.Where(x => x.CompanyId == companyUser.CompanyId && x.IdentityId == staffId).Include(x => x.RegisteredSessions).FirstOrDefaultAsync();
                staffs.RegisteredSessions.Where(x => x.UUID != updatePassword.UUID).ToList().ForEach(x => x.SessionStatus
 = SessionStatus.AutoLogout);
                foreach (var device in staffs.RegisteredSessions.Where(x => x.UUID != updatePassword.UUID))
                    await _signalRService.SignalRAutoLogout(device.UUID, _mapper.Map<ActiveSessionResponse>(device));
                await _appDbContext.SaveChangesAsync();

                var user = await _userManager.FindByIdAsync(staffs.IdentityId);

                if (!string.IsNullOrEmpty(updatePassword.OldPassword))
                {
                    //Check old password
                    if (updatePassword.OldPassword == updatePassword.Password)
                        return BadRequest(new ResponseViewModel { message =
 "New password and old password cannot be same" });

                    var loginResult =
 await _signInManager.PasswordSignInAsync(user, updatePassword.OldPassword, true, false);
                    if (!loginResult.Succeeded)
                        return BadRequest(new ResponseViewModel { message = "Incorrect old password" });
                }

                var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                await _userManager.ResetPasswordAsync(user, token, updatePassword.Password);

                var staffsVM = _mapper.Map<StaffWithoutCompanyResponse>(staffs);
                return Ok(staffsVM);
            }
            return BadRequest();
        }
#endif

        [HttpPost]
        [Route("Company/Staff/{staffId}")]
        public async Task<ActionResult<StaffWithoutCompanyResponse>> UpdateCompanyStaffInfo(
            string staffId,
            [FromBody]
            StaffInfoViewModel staffInfoViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "data error"
                    });
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser.RoleType == StaffUserRole.Staff && staffId != companyUser.IdentityId)
            {
                return Unauthorized(
                    new ResponseViewModel
                    {
                        message = "Permission denied"
                    });
            }

            var targetStaff = await _appDbContext.UserRoleStaffs
                .Where(x => x.CompanyId == companyUser.CompanyId && x.IdentityId == staffId)
                .Include(x => x.Identity).Include(x => x.ProfilePicture)
                .FirstOrDefaultAsync();

            if (staffInfoViewModel.Position != null)
            {
                var positionOfContactOwner = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(x => x.CompanyId == companyUser.CompanyId && x.FieldName == "PositionOfContactOwner")
                    .FirstOrDefaultAsync();

                if (positionOfContactOwner == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyUser.CompanyId,
                        FieldName = "PositionOfContactOwner",
                        Type = FieldDataType.ContactOwnerField,
                        Order = 99,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Position of Contact Owner", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "聯絡負責人職位", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = false,
                        IsVisible = false,
                    };
                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }

                targetStaff.Position = staffInfoViewModel.Position;
            }

            if (staffInfoViewModel.StaffName != null)
            {
                targetStaff.Identity.FirstName = staffInfoViewModel.StaffName;
                targetStaff.Identity.DisplayName = staffInfoViewModel.StaffName;
            }

            if (!string.IsNullOrEmpty(staffInfoViewModel.TimeZoneInfoId))
            {
                targetStaff.TimeZoneInfoId = staffInfoViewModel.TimeZoneInfoId;
            }

            if (staffInfoViewModel.PhoneNumber != null)
            {
                targetStaff.Identity.PhoneNumber = staffInfoViewModel.PhoneNumber;
            }

            if (staffInfoViewModel.FirstName != null)
            {
                targetStaff.Identity.FirstName = staffInfoViewModel.FirstName;
                targetStaff.Identity.DisplayName = staffInfoViewModel.FirstName;
            }

            if (staffInfoViewModel.LastName != null)
            {
                targetStaff.Identity.LastName = staffInfoViewModel.LastName;

                if (!string.IsNullOrEmpty(staffInfoViewModel.LastName))
                {
                    targetStaff.Identity.DisplayName += $" {staffInfoViewModel.LastName}";
                }
                else
                {
                    targetStaff.Identity.DisplayName = targetStaff.Identity.FirstName;
                }
            }

            if (staffInfoViewModel.StaffRole.HasValue && companyUser.RoleType == StaffUserRole.Admin)
            {
                targetStaff.RoleType = staffInfoViewModel.StaffRole.Value;

                await _staffHooks.OnStaffRoleUpdatedAsync(
                    targetStaff.CompanyId,
                    targetStaff.Id,
                    targetStaff.RoleType);
            }

            if (staffInfoViewModel.Status.HasValue)
            {
                targetStaff.Status = staffInfoViewModel.Status.Value;

                if (staffInfoViewModel.Status == StaffStatus.Away)
                {
                    var companyAssignmentQueue =
                        await _companyAssignmentQueueService.GetCompanyAssignmentQueueByCompanyIdAsync(
                            targetStaff.CompanyId);

                    await _companyAssignmentQueueService.RemoveStaffFromCompanyAssignmentQueueAsync(
                        targetStaff.IdentityId,
                        companyAssignmentQueue);

                    var targetStaffTeams = await _companyTeamService.GetCompanyteamFromStaffId(
                        targetStaff.CompanyId,
                        targetStaff.IdentityId);

                    foreach (var targetStaffTeam in targetStaffTeams)
                    {
                        var companyTeamAssignmentQueue =
                            await _companyTeamAssignmentQueueService.GetCompanyTeamAssignmentQueueByTeamIdAsync(
                                targetStaffTeam.Id);

                        await _companyTeamAssignmentQueueService.RemoveStaffFromCompanyTeamAssignmentQueueAsync(
                            targetStaff.IdentityId,
                            companyTeamAssignmentQueue);
                    }
                }

                await _staffHooks.OnStaffStatusUpdatedAsync(
                    targetStaff.CompanyId,
                    targetStaff.Id,
                    targetStaff.Status);

                _logger.LogInformation(
                    "Staff {StaffIdentityId} status changed to {Status}",
                    staffId,
                    staffInfoViewModel.Status);
            }

            if (!string.IsNullOrEmpty(staffInfoViewModel.Username) &&
                (companyUser.RoleType == StaffUserRole.Admin || staffId != companyUser.IdentityId) &&
                staffInfoViewModel.Username != targetStaff.Identity.UserName)
            {
                var existing = await _userManager.FindByNameAsync(staffInfoViewModel.Username);

                if (existing != null)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "Username has been used"
                        });
                }

                targetStaff.Identity.UserName = staffInfoViewModel.Username;
                targetStaff.Identity.NormalizedUserName = staffInfoViewModel.Username.ToUpper();
            }

            if (staffInfoViewModel.IsShowName.HasValue &&
                companyUser.CompanyId != "6f7da3bb-06ea-49e0-a119-36a77076816d")
            {
                targetStaff.IsShowName = staffInfoViewModel.IsShowName.Value;
            }

            if (!string.IsNullOrEmpty(staffInfoViewModel.Message))
            {
                var positionOfContactOwner = await _appDbContext.CompanyCustomUserProfileFields
                    .Where(x => x.CompanyId == companyUser.CompanyId && x.FieldName == "UniqueLink")
                    .FirstOrDefaultAsync();

                if (positionOfContactOwner == null)
                {
                    var newTeamField = new CompanyCustomUserProfileField
                    {
                        CompanyId = companyUser.CompanyId,
                        FieldName = "UniqueLink",
                        Type = FieldDataType.ContactOwnerField,
                        Order = 99,
                        CustomUserProfileFieldLinguals = new List<CustomUserProfileFieldLingual>()
                        {
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "Unique Link", Language = "en"
                            },
                            new CustomUserProfileFieldLingual
                            {
                                DisplayName = "負責鏈接", Language = "zh-hk"
                            }
                        },
                        IsDeletable = false,
                        IsEditable = false,
                        IsVisible = false,
                    };
                    _appDbContext.CompanyCustomUserProfileFields.Add(newTeamField);
                    await _appDbContext.SaveChangesAsync();
                }

                targetStaff.Message = staffInfoViewModel.Message;
            }

            if (companyUser.RbacRole == StaffUserRole.Admin || companyUser.RbacRole == StaffUserRole.TeamAdmin)
            {
                await _companyTeamService.AddOrRemoveTeam(
                    companyUser.CompanyId,
                    targetStaff.Id,
                    staffInfoViewModel.TeamIds);
            }

            if (staffInfoViewModel.DefaultCurrency != null)
            {
                targetStaff.DefaultCurrency = staffInfoViewModel.DefaultCurrency;
            }

            List<string> updateRoleIds = null;
            var isRbacEnabled = _rbacService.IsRbacEnabled();

            if (isRbacEnabled)
            {
                updateRoleIds = staffInfoViewModel?.RoleIds ?? null;
            }
            else
            {
                if (staffInfoViewModel.StaffRole != null)
                {
                    var defaultRoles =
                        (await _managementRolesApi.ManagementRolesGetAllRolesPostAsync(body: new object()))
                        ?.Data;

                    var defaultRoleId = defaultRoles.Roles
                        .Find(r => r.Name == staffInfoViewModel.StaffRole.ToString())?.Id;
                    updateRoleIds = new List<string>();

                    if (defaultRoleId != null)
                    {
                        updateRoleIds.Add(defaultRoleId);
                    }
                }
            }

            var isVerifyRbac =
                _configuration.TryGetPropertyValue<bool>("Rbac:IsMiddlewareVerificationEnabled");

            // If isVerifyRbac is true, that's mean the RbacMiddleware has done the rbac checking
            if (updateRoleIds is { Count: > 0 } && !isVerifyRbac)
            {
                var allowToUpdateRole = isRbacEnabled
                    ? await _rbacService.CheckPermission(companyUser, HttpContext)
                    : (companyUser.RbacRole == StaffUserRole.Admin);

                if (!allowToUpdateRole)
                {
                    return Unauthorized(
                        new ResponseViewModel
                        {
                            message = "Access denied to update staff roles"
                        });
                }
            }

            await _appDbContext.SaveChangesAsync();

            // Invalidate cache
            var getCompanyInfoCacheKeyPattern = new GetCompanyStaffCacheKeyPattern(targetStaff.IdentityId);
            await _cacheManagerService.DeleteCacheAsync(getCompanyInfoCacheKeyPattern);

            var result = await _managementCompaniesApi.ManagementCompaniesOnUpdateStaffInformationPostAsync(
                managementOnUpdateStaffInformationInput: new ManagementOnUpdateStaffInformationInput(
                    companyId: companyUser?.CompanyId,
                    staffId: targetStaff.Id.ToString(),
                    teamIds: staffInfoViewModel.TeamIds?
                        .Select(x => x.ToString()).ToList(),
                    roleIds: updateRoleIds,
                    displayName: targetStaff.Identity.DisplayName,
                    firstName: staffInfoViewModel.FirstName ?? targetStaff.Identity.FirstName,
                    lastName: staffInfoViewModel.LastName ?? targetStaff.Identity.LastName,
                    phoneNumber: staffInfoViewModel.PhoneNumber ?? targetStaff.Identity.PhoneNumber));

            var staffsVM = _mapper.Map<StaffWithoutCompanyResponse>(targetStaff);
            await _signalRService.SignalROnStaffUpdated(targetStaff.IdentityId, staffsVM);

            return Ok(staffsVM);
        }

        [HttpPost]
        [Route("Company/Staff/ProfilePicture/{staffId}")]
        public async Task<IActionResult> AddProfilePicture(
            string staffId,
            [FromForm]
            ProfilePictureViewModel attachmentViewModel)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                // var companyUser = _appDbContext.UserRoleStaffs.Include(x => x.Company.StorageConfig).Include(x => x.Identity).Where(x => x.IdentityId == senderId).FirstOrDefault();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (string.IsNullOrEmpty(companyUser.CompanyId))
                {
                    return BadRequest();
                }

                var targetUploadProfileStaff = await _appDbContext.UserRoleStaffs.Include(x => x.ProfilePicture)
                    .FirstOrDefaultAsync(x => x.IdentityId == staffId && x.CompanyId == companyUser.CompanyId);

                if (targetUploadProfileStaff == null)
                {
                    return BadRequest();
                }

                foreach (IFormFile file in attachmentViewModel.files)
                {
                    var fileName = $"ProfilePicture/{staffId}/{DateTime.UtcNow.ToString("o")}/{file.FileName}";
                    var uploadFileResult = await _uploadService.UploadImage(companyUser.CompanyId, fileName, file);

                    if (uploadFileResult?.Url == null)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = "upload failure"
                            });
                    }

                    var newuUploadedFile = new ProfilePictureFile();
                    newuUploadedFile.Filename = fileName;
                    newuUploadedFile.CompanyId = companyUser.CompanyId;
                    newuUploadedFile.BlobContainer = companyUser.CompanyId;
                    newuUploadedFile.Url = uploadFileResult.Url;
                    newuUploadedFile.MIMEType = file.ContentType;
                    _appDbContext.UserStaffProfilePictures.Add(newuUploadedFile);

                    targetUploadProfileStaff.ProfilePicture = newuUploadedFile;

                    break;
                }

                await _appDbContext.SaveChangesAsync();

                var staffResponse = _mapper.Map<StaffWithoutCompanyResponse>(targetUploadProfileStaff);
                return Ok(staffResponse);
            }

            return BadRequest();
        }

        [HttpDelete]
        [Route("Company/Staff/ProfilePicture/{staffId}")]
        public async Task<IActionResult> DeleteProfilePicture(string staffId)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                // var senderId = _userManager.GetUserId(User);
                // var companyUser = _appDbContext.UserRoleStaffs.Include(x => x.Company.StorageConfig).Where(x => x.IdentityId == senderId).FirstOrDefault();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
                if (companyUser == null)
                {
                    return Unauthorized();
                }

                companyUser.Company.StorageConfig = await _appDbContext.ConfigStorageConfigs
                    .Where(x => x.CompanyId == companyUser.CompanyId).FirstOrDefaultAsync();

                var targetUploadProfileStaff = await _appDbContext.UserRoleStaffs.Include(x => x.ProfilePicture)
                    .Include(x => x.Identity).FirstOrDefaultAsync(
                        x => x.IdentityId == staffId && x.CompanyId == companyUser.CompanyId);

                if (targetUploadProfileStaff == null)
                {
                    return BadRequest();
                }

                if (targetUploadProfileStaff.ProfilePicture != null)
                {
                    try
                    {
                        var result = await _azureBlobStorageService.DeleteFromAzureBlob(
                            targetUploadProfileStaff.ProfilePicture.Filename,
                            targetUploadProfileStaff.ProfilePicture.BlobContainer);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[{MethodName}] Error deleting profile picture for staff {StaffId} from blob storage. {ExceptionMessage}",
                            nameof(DeleteProfilePicture),
                            staffId,
                            ex.Message);
                    }

                    _appDbContext.UserStaffProfilePictures.Remove(targetUploadProfileStaff.ProfilePicture);
                    await _appDbContext.SaveChangesAsync();
                }

                var staffResponse = _mapper.Map<StaffWithoutCompanyResponse>(targetUploadProfileStaff);
                return Ok(staffResponse);
            }

            return BadRequest();
        }

        [HttpDelete]
        [Route("Company/Staff/{staffId}")]
        public async Task<IActionResult> DeleteStaff(string staffId)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Include(x => x.Company.StorageConfig).Where(x => x.IdentityId == senderId).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                if (companyUser.RoleType != StaffUserRole.Admin)
                {
                    return Unauthorized(
                        new ResponseViewModel
                        {
                            message = "Permission denied"
                        });
                }

                var staffIdTobeDeleted = await _appDbContext.UserRoleStaffs
                    .Where(x => x.CompanyId == companyUser.CompanyId && x.IdentityId == staffId).Select(x => x.Id)
                    .FirstOrDefaultAsync();
                var isRemoved = await _coreService.RemoveStaffData(companyUser.CompanyId, staffIdTobeDeleted);

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);
                await _staffHooks.OnStaffDeletedAsync(companyUser.CompanyId, staffIdTobeDeleted);
                return Ok(
                    new ResponseViewModel
                    {
                        message = "Deleted"
                    });
            }

            return BadRequest();
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("comapny/profilepicture/{filenameId}")]
        public async Task<IActionResult> GetAzureBlob(string filenameId)
        {
            var file = await _appDbContext.UserStaffProfilePictures
                .FirstOrDefaultAsync(x => x.ProfilePictureId == filenameId);

            var stream = await _azureBlobStorageService.DownloadFromAzureBlob(file.Filename, file.BlobContainer);

            return File(
                stream.ToArray(),
                string.IsNullOrEmpty(file.MIMEType) ? "application/octet-stream" : file.MIMEType,
                file.Filename);
        }

        [HttpPost]
        [Route("Company/UserProfileFields")]
        public async Task<IActionResult> AddUserProfileField(
            [FromBody]
            List<AddUserProfileCustomFieldViewModel> addUserProfileCustomFieldsViewModels)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var customFields = _mapper.Map<List<CompanyCustomUserProfileField>>(addUserProfileCustomFieldsViewModels);
            companyUser.Company.CustomUserProfileFields =
                await _companyService.UpdateCustomUserProfileFields(companyUser.CompanyId, customFields);

            var companyVm = _mapper.Map<CompanyResponse>(companyUser.Company);
            await _signalRService.SiganlROnUserProfileFieldFormatChanged(
                companyUser.Company,
                companyVm.CustomUserProfileFields);

            companyVm.CustomUserProfileFields = companyVm.CustomUserProfileFields.OrderBy(x => x.Order).ToList();

            // Invalidate
            await _contactCacheService.InvalidateCompanyCustomUserProfileFieldsCacheAsync(companyUser.CompanyId);

            return Ok(companyVm);
        }

        [HttpDelete]
        [Route("Company/UserProfileField")]
        public async Task<IActionResult> DeleteUserProfileFields(
            [FromBody]
            DeleteCustomUserFieldViewModel deleteCustomUserFieldViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            foreach (var userProfileFieldId in deleteCustomUserFieldViewModel.UserProfileFieldIds)
            {
                var customFieldsTobeDeleted = await _appDbContext.CompanyCustomUserProfileFields
                    .Include(x => x.CustomUserProfileFieldLinguals)
                    .Include(x => x.CustomUserProfileFieldOptions)
                    .ThenInclude(x => x.CustomUserProfileFieldOptionLinguals)
                    .FirstOrDefaultAsync(
                        x =>
                            x.Id == userProfileFieldId
                            && x.CompanyId == companyUser.CompanyId);

                if (customFieldsTobeDeleted != null)
                {
                    if (!customFieldsTobeDeleted.IsDeletable.Value
                        || customFieldsTobeDeleted.IsDefault.Value)
                    {
                        continue;
                    }

                    _appDbContext.UserProfileCustomFields.RemoveRange(
                        await _appDbContext.UserProfileCustomFields
                            .Where(x => x.CompanyDefinedFieldId == userProfileFieldId)
                            .ToListAsync());

                    foreach (var option in customFieldsTobeDeleted.CustomUserProfileFieldOptions)
                    {
                        _appDbContext.CompanyCustomUserProfileFieldOptionLinguals.RemoveRange(
                            option.CustomUserProfileFieldOptionLinguals);
                    }

                    _appDbContext.CompanyCustomUserProfileFieldOptions.RemoveRange(
                        customFieldsTobeDeleted.CustomUserProfileFieldOptions);
                    _appDbContext.CompanyCustomUserProfileFieldLinguals.RemoveRange(
                        customFieldsTobeDeleted.CustomUserProfileFieldLinguals);
                    _appDbContext.CompanyCustomUserProfileFields.RemoveRange(customFieldsTobeDeleted);

                    await _appDbContext.SaveChangesAsync();

                    var company = await _appDbContext.CompanyCompanies.Include(x => x.CustomUserProfileFields)
                        .ThenInclude(cust => cust.CustomUserProfileFieldLinguals)
                        .Include(x => x.CustomUserProfileFields).ThenInclude(x => x.CustomUserProfileFieldOptions)
                        .ThenInclude(x => x.CustomUserProfileFieldOptionLinguals)
                        .FirstOrDefaultAsync(x => x.Id == companyUser.CompanyId);
                    var companyVM = _mapper.Map<CompanyResponse>(company);
                    await _signalRService.SiganlROnUserProfileFieldFormatChanged(
                        company,
                        companyVM.CustomUserProfileFields);
                    await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                    // Invalidate
                    await _contactCacheService.InvalidateCompanyCustomUserProfileFieldsCacheAsync(companyUser.CompanyId);
                }
                else
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "not found"
                        });
                }
            }

            return Ok(
                new ResponseViewModel
                {
                    message = "success"
                });
        }

        [HttpGet]
        [Route("Company/CompanyFields")]
        public async Task<IActionResult> GetCompanyFields()
        {
            if (User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var companyCustomFields = await _appDbContext.CompanyCompanyCustomFields
                    .Where(x => x.CompanyId == companyUser.CompanyId).Include(x => x.CompanyCustomFieldFieldLinguals)
                    .ToListAsync(HttpContext.RequestAborted);
                var response = _mapper.Map<List<CompanyCustomFieldViewModel>>(companyCustomFields);

                return Ok(response);
            }

            return BadRequest();
        }

        [HttpGet]
        [Route("Company/CompanyFields/{fieldName}")]
        public async Task<IActionResult> GetCompanyFields(string fieldName)
        {
            if (User.Identity.IsAuthenticated)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var companyCustomField =
                    await _companyService.GetCustomFieldByFieldName(companyUser.CompanyId, fieldName);
                var response = _mapper.Map<CompanyCustomFieldViewModel>(companyCustomField);

                return Ok(response);
            }

            return BadRequest();
        }

        [HttpPost]
        [Route("Company/CompanyFields")]
        public async Task<IActionResult> AddCompanyFields(
            [FromBody]
            List<AddCompanyCustomFieldViewModel> addCompanyCustomFieldViewModels)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var customFields = _mapper.Map<List<CompanyCustomField>>(addCompanyCustomFieldViewModels);

            foreach (var customFeild in customFields)
            {
                customFeild.CompanyId = companyUser.CompanyId;

                if (!await _appDbContext.CompanyCompanyCustomFields
                        .AnyAsync(
                            x =>
                                x.CompanyId == companyUser.CompanyId
                                && x.FieldName == customFeild.FieldName))
                {
                    _appDbContext.CompanyCompanyCustomFields.Add(customFeild);
                }
                else
                {
                    var existing = await _appDbContext.CompanyCompanyCustomFields
                        .Include(x => x.CompanyCustomFieldFieldLinguals).FirstOrDefaultAsync(
                            x => x.CompanyId == companyUser.CompanyId && x.FieldName == customFeild.FieldName);
                    _appDbContext.CompanyCustomFieldFieldLinguals.RemoveRange(existing.CompanyCustomFieldFieldLinguals);
                    existing.Category = customFeild.Category;
                    existing.CompanyCustomFieldFieldLinguals = customFeild.CompanyCustomFieldFieldLinguals;
                    existing.Value = customFeild.Value;
                    existing.IsEditable = customFeild.IsEditable;
                    existing.IsVisible = customFeild.IsVisible;
                    existing.Type = customFeild.Type;
                }
            }

            await _appDbContext.SaveChangesAsync();

            var company = await _appDbContext.CompanyCompanies.Include(x => x.CompanyCustomFields)
                .ThenInclude(x => x.CompanyCustomFieldFieldLinguals)
                .FirstOrDefaultAsync(x => x.Id == companyUser.CompanyId);
            var companyVM = _mapper.Map<CompanyResponse>(company);

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            return Ok(companyVM);
        }

        [HttpDelete]
        [Route("Company/CompanyFields")]
        public async Task<ActionResult<ResponseViewModel>> DeleteCompanyFields(
            [FromBody]
            DeleteCompanyCustomFieldViewModel deleteCompanyCustomFieldViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            foreach (var fieldName in deleteCompanyCustomFieldViewModel.CompanyCustomFieldNames)
            {
                var customFieldsTobeDeleted = await _appDbContext.CompanyCompanyCustomFields
                    .Include(x => x.CompanyCustomFieldFieldLinguals).FirstOrDefaultAsync(
                        x => x.FieldName.ToLower() == fieldName.ToLower() && x.CompanyId == companyUser.CompanyId);

                if (customFieldsTobeDeleted != null)
                {
                    _appDbContext.CompanyCompanyCustomFields.RemoveRange(customFieldsTobeDeleted);
                    _appDbContext.CompanyCustomFieldFieldLinguals.RemoveRange(
                        customFieldsTobeDeleted.CompanyCustomFieldFieldLinguals);

                    await _appDbContext.SaveChangesAsync();
                    await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                    return Ok(
                        new ResponseViewModel
                        {
                            message = "success"
                        });
                }
                else
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = "not found"
                        });
                }
            }

            return BadRequest(
                new ResponseViewModel
                {
                    message = "not found"
                });
        }

        [HttpPost]
        [Route("Company/AssignmentRule/reorder")]
        public async Task<ActionResult<ResponseViewModel>> ReorderAssignmentRule(
            [FromBody]
            List<AssignmentReorderViewModel> assignmentReorderViewModels)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            foreach (var assignmentRecorder in assignmentReorderViewModels)
            {
                await _appDbContext.CompanyAssignmentRules.Where(x => x.AssignmentId == assignmentRecorder.AssignmentId)
                    .ExecuteUpdateAsync(
                        calls => calls.SetProperty(
                            p => p.Order, assignmentRecorder.Order));
            }

            return Ok(
                new ResponseViewModel
                {
                    message = "success"
                });
        }

        [HttpPost]
        [Route("Company/AssignmentRule/status")]
        public async Task<ActionResult<ResponseViewModel>> ReorderAssignmentRule(
            [FromBody]
            List<AssignmentStatusViewModel> assignmentStatusViewModels)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            foreach (var assignmentRecorder in assignmentStatusViewModels)
            {
                await _appDbContext.CompanyAssignmentRules.Where(x => x.AssignmentId == assignmentRecorder.AssignmentId)
                        .ExecuteUpdateAsync(
                            calls => calls.SetProperty(
                                p => p.Status, assignmentRecorder.Status));
            }

            return Ok(
                new ResponseViewModel
                {
                    message = "success"
                });
        }

        [HttpPost]
        [Route("Company/AssignmentRule")]
        public async Task<ActionResult<List<AssignmentRuleResponse>>> AddAssignmentRule(
            [FromBody]
            List<AssignmentRuleViewModel> assignmentRuleViewModels)
        {
            try
            {
                if (!User.Identity.IsAuthenticated)
                {
                    return Unauthorized();
                }

                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
                var response = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                // await _cacheService.RemoveCompanyInfoCache(companyUser.CompanyId, "AssignmentRuleInfo");
                var replyCommentsExceedCharLimit = assignmentRuleViewModels.Where(
                    x => (x.AutomationType == AutomationType.FacebookPostComment && x.AutomationActions.Any(
                             y => y.AutomatedTriggerType == AutomatedTriggerType.FacebookReplyComment &&
                                  y.MessageContent != null && y.FbIgAutoReply.MessageContent.Length > 8000)) ||
                         (x.AutomationType == AutomationType.InstagramMediaComment && x.AutomationActions.Any(
                             y => y.AutomatedTriggerType == AutomatedTriggerType.InstagramReplyComment &&
                                  y.MessageContent != null && y.FbIgAutoReply.MessageContent.Length > 2200))).ToList();
                if (replyCommentsExceedCharLimit.Count > 0)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message =
                                $"FB Comment Max Limit: 8000 characters and IG Comment Max Limit: 2200 characters, list: {JsonConvert.SerializeObject(replyCommentsExceedCharLimit)}"
                        });
                }

                foreach (var assignmentVM in assignmentRuleViewModels)
                {
                    _logger.LogInformation(
                        "[POST /Company/AssignmentRule] Company {CompanyId} user {UserId} {Action} assignment rule, payload: {Payload}",
                        companyUser.CompanyId,
                        companyUser.IdentityId,
                        string.IsNullOrWhiteSpace(assignmentVM.AssignmentId) ? "adding" : "updating",
                        JsonConvert.SerializeObject(assignmentVM));

                    var assignmentRule = _mapper.Map<AssignmentRule>(assignmentVM);

                    // Hoc fix for QRcode maping
                    switch (assignmentRule.AutomationType)
                    {
                        case AutomationType.QRCodeAssigneeMapping:
                        case AutomationType.QRCodeAssignTeamMapping:
                            assignmentRule.AssignmentId = await _appDbContext.CompanyAssignmentRules
                                .Where(
                                    x => x.CompanyId == companyUser.CompanyId &&
                                         x.AutomationType == assignmentRule.AutomationType).Select(x => x.AssignmentId)
                                .FirstOrDefaultAsync();
                            assignmentVM.AssignmentId = assignmentRule.AssignmentId;
                            break;
                        case AutomationType.RecurringJob:
                            if (assignmentVM.Conditions.IsNullOrEmpty() ||
                                assignmentVM.Conditions.Any(
                                    c => string.IsNullOrEmpty(c.FieldName) && string.IsNullOrEmpty(c.ContainHashTag)))
                            {
                                return BadRequest(
                                    "RecurringJob must have conditions and condition field name must not be empty");
                            }

                            break;
                    }

                    // if (assignmentRule.AutomationType == AutomationType.ContactAdded)
                    //    assignmentRule.AutomationType = AutomationType.FieldValueChanged;

                    // if (assignmentVM.AutomationType == AutomationType.MessageReceived)
                    // {
                    //    var lastContact = assignmentVM.Conditions?.Where(x => x.FieldName == "LastContact" && x.ConditionOperator == SupportedOperator.DateBeforeDayAgo).FirstOrDefault();
                    //    if (lastContact == null || lastContact?.Values.FirstOrDefault() == "0")
                    //        return BadRequest(new ResponseViewModel { message = "AutomationType: MessageReceived much set [LastContact] and set DateBeforeDayAgo higher then 0" });
                    // }
                    if (assignmentVM.AutomationType == AutomationType.FacebookPostComment ||
                        assignmentVM.AutomationType == AutomationType.InstagramMediaComment)
                    {
                        var responseWrapper =
                            await _facebookService.AddOrUpdateFbIgCommentRule(assignmentVM, companyUser);

                        if (!responseWrapper.IsSuccess)
                        {
                            return BadRequest(responseWrapper.ErrorMsg);
                        }

                        continue;
                    }

                    if (string.IsNullOrEmpty(assignmentVM.AssignmentId))
                    {
                        if (await _appDbContext.CompanyAssignmentRules
                                .CountAsync(
                                    x =>
                                        x.CompanyId == companyUser.CompanyId &&
                                        x.Status == AutomationStatus.Live &&
                                        x.AssignmentRuleName != "Default" &&
                                        !x.AssignmentRuleName.ToLower().Contains("zaiper")) >
                            response.MaximumAutomations)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    errorId = "ERR_RULES_LIMIT_BY_PLAN",
                                    message =
                                        $"You've reached the maximum number of automation, your plan: {response.billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan.SubscriptionName} can set {response.MaximumAutomations} automations."
                                });
                        }

                        if (!string.IsNullOrEmpty(assignmentVM.StaffId))
                        {
                            assignmentRule.AssignedStaff = await _appDbContext.UserRoleStaffs
                                .Where(
                                    x => x.IdentityId == assignmentVM.StaffId && x.CompanyId == companyUser.CompanyId)
                                .Include(x => x.Identity).FirstOrDefaultAsync();
                        }
                        else
                        {
                            assignmentRule.AssignedStaff = null;
                        }

                        if (assignmentVM.TeamId.HasValue)
                        {
                            assignmentRule.AssignedTeam = await _appDbContext.CompanyStaffTeams
                                .Where(x => x.Id == assignmentVM.TeamId.Value && x.CompanyId == companyUser.CompanyId)
                                .FirstOrDefaultAsync();
                        }
                        else
                        {
                            assignmentRule.AssignedTeamId = null;
                        }

                        if (assignmentVM.AutomationActions?.Count > 0)
                        {
                            assignmentRule.AutomationActions = new List<AutomationAction>();
                            foreach (var automation in assignmentVM.AutomationActions)
                            {
                                var automationAction = new AutomationAction
                                {
                                    CompanyId = companyUser.CompanyId,
                                    AssignmentType = automation.AssignmentType,
                                    AutomatedTriggerType = automation.AutomatedTriggerType,
                                    MessageContent = automation.MessageContent,
                                    WhatsApp360DialogExtendedAutomationMessages =
                                        automation.WhatsApp360DialogExtendedAutomationMessages,
                                    ExtendedAutomationMessage = automation.ExtendedAutomationMessage,
                                    MessageParams = automation.MessageParams,
                                    ActionAddConversationHashtags = automation.ActionAddConversationHashtags,
                                    ActionAddConversationRemarks = automation.ActionAddConversationRemarks,
                                    ActionUpdateCustomFields = automation.ActionUpdateCustomFields,
                                    ActionAddedToGroupIds = automation.ActionAddedToGroupIds,
                                    ActionRemoveFromGroupIds = automation.ActionRemoveFromGroupIds,
                                    ActionWait = automation.ActionWait,
                                    ActionWaitDays = automation.ActionWaitDays,
                                    ChangeConversationStatus = automation.ChangeConversationStatus,
                                    Order = automation.Order,
                                    TeamAssignmentType = automation.TeamAssignmentType,
                                    TargetedChannelWithIds = automation.TargetedChannelWithIds,
                                    AddAdditionalAssigneeIds = automation.AddAdditionalAssigneeIds,
                                    WebhookURL = automation.WebhookURL,
                                    DialogflowServiceAccountConfigId = automation.DialogflowServiceAccountConfigId,
                                    DialogflowLanguageCode = automation.DialogflowLanguageCode
                                };
                                if (!string.IsNullOrEmpty(automation.StaffId))
                                {
                                    automationAction.AssignedStaff = await _appDbContext.UserRoleStaffs
                                        .Where(
                                            x => x.IdentityId == automation.StaffId &&
                                                 x.CompanyId == companyUser.CompanyId).Include(x => x.Identity)
                                        .FirstOrDefaultAsync();
                                }
                                else
                                {
                                    automationAction.AssignedStaffId = null;
                                }

                                if (automation.TeamId.HasValue)
                                {
                                    automationAction.AssignedTeam = await _appDbContext.CompanyStaffTeams
                                        .Where(
                                            x => x.Id == automation.TeamId.Value &&
                                                 x.CompanyId == companyUser.CompanyId).FirstOrDefaultAsync();
                                }
                                else
                                {
                                    automationAction.AssignedTeamId = null;
                                }

                                assignmentRule.AutomationActions.Add(automationAction);
                            }
                        }

                        assignmentRule.Order = 0;
                        assignmentRule.CompanyId = companyUser.CompanyId;
                        _appDbContext.CompanyAssignmentRules.Add(assignmentRule);

                        var olderAssignment = await _appDbContext.CompanyAssignmentRules
                            .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync();
                        olderAssignment.ForEach(x => x.Order += 1);

                        await _signalRService.SignalROnAssignmentRuleAdded(companyUser.Company, assignmentRule);
                    }
                    else
                    {
                        if (await _appDbContext.CompanyAssignmentRules
                                .CountAsync(
                                    x =>
                                        x.CompanyId == companyUser.CompanyId &&
                                        x.Status == AutomationStatus.Live &&
                                        x.AssignmentRuleName != "Default" &&
                                        !x.AssignmentRuleName.ToLower().Contains("zaiper")) >
                            response.MaximumAutomations)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    errorId = "ERR_RULES_LIMIT_BY_PLAN",
                                    message =
                                        $"You've reached the maximum number of automation, your plan: {response.billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan.SubscriptionName} can set {response.MaximumAutomations} automations."
                                });
                        }

                        assignmentRule = await _appDbContext.CompanyAssignmentRules
                            .Include(x => x.AssignedStaff)
                            .Include(x => x.AutomationActions)
                            .Include(x => x.AssociatedList)
                            .FirstOrDefaultAsync(x => x.AssignmentId == assignmentVM.AssignmentId);

                        if (assignmentRule == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = "assignment rule not found."
                                });
                        }

                        if (!string.IsNullOrEmpty(assignmentVM.StaffId))
                        {
                            assignmentRule.AssignedStaff = await _appDbContext.UserRoleStaffs
                                .Where(
                                    x => x.CompanyId == companyUser.CompanyId && x.IdentityId == assignmentVM.StaffId)
                                .Include(x => x.Identity)
                                .FirstOrDefaultAsync();
                        }
                        else
                        {
                            assignmentRule.AssignedStaff = null;
                        }

                        if (assignmentVM.TeamId.HasValue)
                        {
                            assignmentRule.AssignedTeamId = assignmentVM.TeamId.Value;
                            assignmentRule.AssignedTeam = await _appDbContext.CompanyStaffTeams
                                .Where(x => x.Id == assignmentVM.TeamId && x.CompanyId == companyUser.CompanyId)
                                .FirstOrDefaultAsync();
                        }
                        else if (!assignmentVM.TeamId.HasValue && assignmentVM.AssignmentRuleName == "Default")
                        {
                            var teamId = assignmentVM.AutomationActions.FirstOrDefault()?.TeamId;

                            assignmentRule.AssignedTeamId = teamId;
                            assignmentRule.AssignedTeam = await _appDbContext.CompanyStaffTeams
                                .Where(x => x.Id == teamId && x.CompanyId == companyUser.CompanyId)
                                .FirstOrDefaultAsync();

                            var teamMemberIdList = await _appDbContext.CompanyTeamMembers
                                .Where(x => x.CompanyTeamId == teamId)
                                .Select(x => x.StaffId)
                                .ToListAsync();

                            var queueStaffIds = await _appDbContext.UserRoleStaffs
                                .Where(
                                    staff =>
                                        (teamId.HasValue && teamMemberIdList.Any(x => x == staff.Id)) ||
                                        (!teamId.HasValue && staff.CompanyId == companyUser.CompanyId))
                                .Select(x => x.IdentityId)
                                .ToListAsync();

                            var existingQueue = await _appDbContext.CompanyAssignmentQueues
                                .Where(x => x.CompanyId == companyUser.CompanyId)
                                .FirstOrDefaultAsync();

                            if (existingQueue == null)
                            {
                                await _appDbContext.CompanyAssignmentQueues.AddAsync(
                                    new AssignmentQueue
                                    {
                                        CompanyId = companyUser.CompanyId,
                                        StaffIds = queueStaffIds
                                    });
                            }
                            else
                            {
                                existingQueue.StaffIds = queueStaffIds;
                            }

                            await _appDbContext.SaveChangesAsync();
                        }

                        assignmentRule.AssignmentRuleName = assignmentVM.AssignmentRuleName;
                        assignmentRule.Conditions = assignmentVM.Conditions;
                        assignmentRule.Order = assignmentVM.Order;
                        assignmentRule.AssignmentType = assignmentVM.AssignmentType;
                        assignmentRule.TeamAssignmentType = assignmentVM.TeamAssignmentType;
                        assignmentRule.TargetedChannels = assignmentVM.TargetedChannels;
                        assignmentRule.TargetedChannelWithIds = assignmentVM.TargetedChannelWithIds;
                        assignmentRule.AutomationType = assignmentVM.AutomationType;
                        assignmentRule.RecurringSetting = assignmentVM.RecurringSetting;
                        assignmentRule.Status = assignmentVM.Status;
                        assignmentRule.WebhookVariables = assignmentVM.WebhookVariables;
                        assignmentRule.IsContinue = assignmentVM.IsContinue;

                        if (assignmentRule.AssociatedList != null)
                        {
                            assignmentRule.AssociatedList.ImportName = assignmentRule.AssignmentRuleName;
                            assignmentRule.AssociatedList.Status = ImportStatus.Importing;
                        }

                        // if (assignmentVM.AutomationActions?.Count > 0)
                        // {
                        var mylist = assignmentVM.AutomationActions;

                        if (assignmentVM.AutomationActions != null)
                        {
                            var removeList = new List<AutomationAction>();
                            foreach (var automation in assignmentRule.AutomationActions)
                            {
                                var automationVM = assignmentVM.AutomationActions.Where(x => x.Id == automation.Id)
                                    .FirstOrDefault();
                                if (automationVM == null)
                                {
                                    removeList.Add(automation);
                                }
                                else
                                {
                                    automation.AssignmentType = automationVM.AssignmentType;
                                    automation.AutomatedTriggerType = automationVM.AutomatedTriggerType;
                                    automation.MessageContent = automationVM.MessageContent;
                                    automation.WhatsApp360DialogExtendedAutomationMessages =
                                        automationVM.WhatsApp360DialogExtendedAutomationMessages;
                                    automation.ExtendedAutomationMessage = automationVM.ExtendedAutomationMessage;
                                    automation.MessageParams = automationVM.MessageParams;
                                    automation.ActionAddConversationHashtags =
                                        automationVM.ActionAddConversationHashtags;
                                    automation.ActionAddConversationRemarks = automationVM.ActionAddConversationRemarks;
                                    automation.ActionUpdateCustomFields = automationVM.ActionUpdateCustomFields;
                                    automation.ActionAddedToGroupIds = automationVM.ActionAddedToGroupIds;
                                    automation.ActionRemoveFromGroupIds = automationVM.ActionRemoveFromGroupIds;
                                    automation.ActionWait = automationVM.ActionWait;
                                    automation.ActionWaitDays = automationVM.ActionWaitDays;
                                    automation.Order = automationVM.Order;
                                    automation.TeamAssignmentType = automationVM.TeamAssignmentType;
                                    automation.ChangeConversationStatus = automationVM.ChangeConversationStatus;
                                    automation.TargetedChannelWithIds = automationVM.TargetedChannelWithIds;
                                    automation.AddAdditionalAssigneeIds = automationVM.AddAdditionalAssigneeIds;
                                    automation.WebhookURL = automationVM.WebhookURL;
                                    automation.DialogflowServiceAccountConfigId =
                                        automationVM.DialogflowServiceAccountConfigId;
                                    automation.DialogflowLanguageCode = automationVM.DialogflowLanguageCode;

                                    if (!string.IsNullOrEmpty(automationVM.StaffId))
                                    {
                                        automation.AssignedStaff = await _appDbContext.UserRoleStaffs
                                            .Where(
                                                x => x.IdentityId == automationVM.StaffId &&
                                                     x.CompanyId == companyUser.CompanyId).Include(x => x.Identity)
                                            .FirstOrDefaultAsync();
                                    }
                                    else
                                    {
                                        automation.AssignedStaffId = null;
                                    }

                                    if (automationVM.TeamId.HasValue)
                                    {
                                        automation.AssignedTeam = await _appDbContext.CompanyStaffTeams
                                            .Where(
                                                x => x.Id == automationVM.TeamId.Value &&
                                                     x.CompanyId == companyUser.CompanyId).FirstOrDefaultAsync();
                                    }
                                    else
                                    {
                                        automation.AssignedTeamId = null;
                                    }
                                }
                            }

                            foreach (var automation in removeList)
                            {
                                assignmentRule.AutomationActions.Remove(automation);
                            }

                            var newAutomationRule = assignmentVM.AutomationActions.Where(x => x.Id == null).ToList();
                            foreach (var automation in newAutomationRule)
                            {
                                var automationAction = new AutomationAction
                                {
                                    CompanyId = companyUser.CompanyId,
                                    AssignmentType = automation.AssignmentType,
                                    AutomatedTriggerType = automation.AutomatedTriggerType,
                                    MessageContent = automation.MessageContent,
                                    WhatsApp360DialogExtendedAutomationMessages =
                                        automation.WhatsApp360DialogExtendedAutomationMessages,
                                    ExtendedAutomationMessage = automation.ExtendedAutomationMessage,
                                    MessageParams = automation.MessageParams,
                                    ActionAddConversationHashtags = automation.ActionAddConversationHashtags,
                                    ActionAddConversationRemarks = automation.ActionAddConversationRemarks,
                                    ActionUpdateCustomFields = automation.ActionUpdateCustomFields,
                                    ActionAddedToGroupIds = automation.ActionAddedToGroupIds,
                                    ActionRemoveFromGroupIds = automation.ActionRemoveFromGroupIds,
                                    ActionWait = automation.ActionWait,
                                    ActionWaitDays = automation.ActionWaitDays,
                                    Order = automation.Order,
                                    TeamAssignmentType = automation.TeamAssignmentType,
                                    ChangeConversationStatus = automation.ChangeConversationStatus,
                                    TargetedChannelWithIds = automation.TargetedChannelWithIds,
                                    AddAdditionalAssigneeIds = automation.AddAdditionalAssigneeIds,
                                    WebhookURL = automation.WebhookURL,
                                    DialogflowServiceAccountConfigId = automation.DialogflowServiceAccountConfigId,
                                    DialogflowLanguageCode = automation.DialogflowLanguageCode
                                };
                                if (!string.IsNullOrEmpty(automation.StaffId))
                                {
                                    automationAction.AssignedStaff = await _appDbContext.UserRoleStaffs
                                        .Where(
                                            x => x.IdentityId == automation.StaffId &&
                                                 x.CompanyId == companyUser.CompanyId).Include(x => x.Identity)
                                        .FirstOrDefaultAsync();
                                }
                                else
                                {
                                    automationAction.AssignedStaffId = null;
                                }

                                if (automation.TeamId.HasValue)
                                {
                                    automationAction.AssignedTeam = await _appDbContext.CompanyStaffTeams
                                        .Where(
                                            x => x.Id == automation.TeamId.Value &&
                                                 x.CompanyId == companyUser.CompanyId).FirstOrDefaultAsync();
                                }
                                else
                                {
                                    automationAction.AssignedTeamId = null;
                                }

                                assignmentRule.AutomationActions.Add(automationAction);
                            }
                        }
                        else
                        {
                            _appDbContext.CompanyAutomationActions.RemoveRange(assignmentRule.AutomationActions);
                            assignmentRule.AutomationActions = null;
                        }

                        // }
                        await _signalRService.SignalROnAssignmentRuleChanged(companyUser.Company, assignmentRule);
                    }

                    if (assignmentRule.AutomationType == AutomationType.RecurringJob)
                    {
                        string subscriptionPlanId = response.billingPeriodUsages.FirstOrDefault()?.BillRecord.SubscriptionPlan.Id;

                        if (!ValidSubscriptionPlan.ProTier.Contains(subscriptionPlanId) &&
                            !ValidSubscriptionPlan.PremiumTier.Contains(subscriptionPlanId) &&
                            !ValidSubscriptionPlan.EnterpriseTier.Contains(subscriptionPlanId))
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = "Your subscription plan could not use RecurringJob",
                                    errorId = "ERR_RULES_LIMIT_RECURRING"
                                });
                        }

                        if (assignmentRule.RecurringSetting == null)
                        {
                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = "AutomationType: Scheduled much set ScheduleSetting",
                                    errorId = "ERR_RULES_MISSING_RECURRING_SETTING"
                                });
                        }

                        var dayOfWeek = "*";
                        var dayOfMonth = "*";
                        var month = "*";

                        if (assignmentRule.RecurringSetting.dayOfWeek?.Count > 0)
                        {
                            dayOfWeek = string.Join(",", assignmentRule.RecurringSetting.dayOfWeek?.ToArray());
                        }

                        if (assignmentRule.RecurringSetting.dayOfMonth?.Count > 0)
                        {
                            dayOfMonth = string.Join(",", assignmentRule.RecurringSetting.dayOfMonth?.ToArray());
                        }

                        if (assignmentRule.RecurringSetting.month?.Count > 0)
                        {
                            month = string.Join(",", assignmentRule.RecurringSetting.month?.ToArray());
                        }

                        // if (string.IsNullOrEmpty(companyUser.TimeZoneInfoId))
                        //    companyUser.TimeZoneInfoId = companyUser.Company.TimeZoneInfoId;

                        // var timezone = Helper.TimeZoneHelper.GetTimeZoneById(companyUser.TimeZoneInfoId);
                        // assignmentRule.RecurringSetting.hours = assignmentRule.RecurringSetting.hours + (int)(timezone.BaseUtcOffsetInHour * -1);
                        // assignmentRule.RecurringSetting.hours = assignmentRule.RecurringSetting.hours % 24;
                        if (assignmentRule.Status == AutomationStatus.Live)
                        {
                            var cronExpression =
                                $"{assignmentRule.RecurringSetting.minutes} {assignmentRule.RecurringSetting.hours} {dayOfMonth} {month} {dayOfWeek}";
                            RecurringJob.AddOrUpdate<IAutomationService>(
                                assignmentRule.AssignmentId,
                                x => x.ScheduledAutomation(assignmentRule.AssignmentId),
                                cronExpression);
                        }
                        else
                        {
                            RecurringJob.RemoveIfExists(assignmentRule.AssignmentId);
                        }
                    }

                    assignmentRule.UpdatedAt = DateTime.UtcNow;
                    assignmentRule.SavedById = companyUser.Id;
                }

                await _appDbContext.SaveChangesAsync();

                BackgroundJob.Enqueue<IAutomationRuleService>(
                    svc =>
                        svc.OnRulesActivatedAsync(
                            companyUser.CompanyId,
                            assignmentRuleViewModels
                                .Where(rule => rule.Status == AutomationStatus.Live)
                                .Select(rule => rule.AssignmentId)
                                .ToArray()));

                var assignmentRules = await _appDbContext.CompanyAssignmentRules
                    .Where(x => x.CompanyId == companyUser.CompanyId && x.Status != AutomationStatus.Saved)
                    .Include(x => x.AutomationActions).ThenInclude(x => x.UploadedFiles)
                    .Include(x => x.AssignedStaff.Identity).Include(x => x.AssignedTeam.Members)
                    .ThenInclude(x => x.Staff.Identity).Include(x => x.AutomationActions)
                    .ThenInclude(x => x.FbIgAutoReply).ThenInclude(x => x.FbIgAutoReplyFiles).OrderBy(x => x.Order)
                    .ToListAsync();
                var assignmentRulesResponse = _mapper.Map<List<AssignmentRuleResponse>>(assignmentRules);
                assignmentRulesResponse.ForEach(
                    x => x.AutomationActions = x.AutomationActions.OrderBy(y => y.Order).ThenBy(y => y.Id).ToList());
                return Ok(assignmentRulesResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Upsert assignment rule error: {ExceptionMessage}",
                    ex.Message);
            }

            return BadRequest(ModelState);
        }

        [HttpPost]
        [Route("Company/AssignmentRule/{assignmentRuldId}/AutomationAction/Blank")]
        public async Task<ActionResult<AutomationActionResponse>> AddBlankAutomationRule(string assignmentRuldId)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var assignmentRule = await _appDbContext.CompanyAssignmentRules
                .Where(x => x.AssignmentId == assignmentRuldId).Include(x => x.AutomationActions).FirstOrDefaultAsync();
            var automationAction = new AutomationAction
            {
                CompanyId = companyUser.CompanyId,
                Order = 0
            };
            assignmentRule.AutomationActions.Add(automationAction);

            await _appDbContext.SaveChangesAsync();

            // await _cacheService.RemoveCompanyInfoCache(companyUser.CompanyId, "AssignmentRuleInfo");
            var automationResponse = _mapper.Map<AutomationActionResponse>(automationAction);
            return Ok(automationResponse);
        }

        [HttpGet]
        [Route("Company/AssignmentRule/{assignmentRuldId}/AutomationAction/Blank")]
        public async Task<ActionResult<List<AutomationActionResponse>>> GetBlankAutomationRule(string assignmentRuldId)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var assignmentRule = await _appDbContext.CompanyAssignmentRules
                .Where(x => x.AssignmentId == assignmentRuldId).Include(x => x.AutomationActions).FirstOrDefaultAsync();

            // await _cacheService.RemoveCompanyInfoCache(companyUser.CompanyId, "AssignmentRuleInfo");
            var automationResponse = _mapper.Map<List<AutomationActionResponse>>(assignmentRule.AutomationActions);
            return Ok(automationResponse);
        }

        [HttpPost]
        [Route("Company/AssignmentRule/Blank")]
        public async Task<ActionResult<AssignmentRuleResponse>> AddBlankAssignmentRule(
            [FromBody]
            AssignmentRuleViewModel assignmentRuleViewModel = null)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            var response = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);

            if (companyUser == null)
            {
                return Unauthorized();
            }

            if (await _appDbContext.CompanyAssignmentRules
                    .CountAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId &&
                            x.Status == AutomationStatus.Live &&
                            x.AssignmentRuleName != "Default" &&
                            !x.AssignmentRuleName.ToLower().Contains("zaiper")) > response.MaximumAutomations)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        errorId = "ERR_RULES_LIMIT_BY_PLAN",
                        message =
                            $"You've reached the maximum number of automation, your plan: {response.billingPeriodUsages.FirstOrDefault().BillRecord.SubscriptionPlan.SubscriptionName} can set {response.MaximumAutomations} automations."
                    });
            }

            var assignmentRule = new AssignmentRule
            {
                Status = AutomationStatus.Saved,
                CompanyId = companyUser.CompanyId,
                Order = 0
            };
            if (assignmentRuleViewModel != null)
            {
                assignmentRule = _mapper.Map<AssignmentRule>(assignmentRuleViewModel);
                assignmentRule.Status = AutomationStatus.Saved;
                assignmentRule.CompanyId = companyUser.CompanyId;
                assignmentRule.Order = 0;

                if (assignmentRuleViewModel.CreateAutomationList != null)
                {
                    assignmentRule.AssociatedList = new ImportContactHistory
                    {
                        CompanyId = assignmentRule.CompanyId,
                        ImportName = assignmentRule.AssignmentRuleName,
                        Alias = assignmentRuleViewModel.CreateAutomationList.Alias,
                        ContactListType = assignmentRuleViewModel.CreateAutomationList.ContactListType,
                        Status = ImportStatus.Saved
                    };
                }
            }

            // var assignmentRule = new AssignmentRule { Status = AutomationStatus.Saved, CompanyId = companyUser.CompanyId, Order = 0 };
            _appDbContext.CompanyAssignmentRules.Add(assignmentRule);

            var olderAssignment = await _appDbContext.CompanyAssignmentRules
                .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync();
            olderAssignment.ForEach(x => x.Order += 1);

            await _appDbContext.SaveChangesAsync();
            var assignmentRelesResponse = _mapper.Map<AssignmentRuleResponse>(assignmentRule);
            return Ok(assignmentRelesResponse);
        }

        [HttpGet]
        [Route("Company/AssignmentRule/Blank")]
        public async Task<ActionResult<List<AssignmentRuleResponse>>> GetBlankAssignmentRule()
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            var assignmentRules = await _appDbContext.CompanyAssignmentRules
                .Where(x => x.CompanyId == companyUser.CompanyId && x.Status == AutomationStatus.Saved)
                .Include(x => x.AssociatedList).ToListAsync(HttpContext.RequestAborted);
            var assignmentRelesResponse = _mapper.Map<List<AssignmentRuleResponse>>(assignmentRules);
            return Ok(assignmentRelesResponse);
        }

        [HttpPost]
        [Route("Company/AutomationAction/Attachment/{automationActionId}")]
        public async Task<ActionResult<AssignmentUploadedFile>> UploadFileForAutomations(
            long automationActionId,
            [FromForm]
            AssignmentAttachmentViewModel attachmentViewModel)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (string.IsNullOrEmpty(companyUser.CompanyId))
                {
                    return Unauthorized();
                }

                var automationAction = await _appDbContext.CompanyAutomationActions
                    .Where(x => x.Id == automationActionId && x.CompanyId == companyUser.CompanyId)
                    .Include(x => x.UploadedFiles).FirstOrDefaultAsync();

                foreach (IFormFile file in attachmentViewModel.files)
                {
                    var fileName = $"Automation/{automationActionId}/{DateTime.UtcNow.ToString("o")}/{file.FileName}";
                    var uploadFileResult = await _uploadService.UploadFile(companyUser.CompanyId, fileName, file);

                    if (uploadFileResult?.Url == null)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = "upload failure"
                            });
                    }

                    var newuUploadedFile = new AssignmentUploadedFile();
                    newuUploadedFile.Filename = fileName;
                    newuUploadedFile.BlobContainer = companyUser.CompanyId;
                    newuUploadedFile.Url = uploadFileResult.Url;
                    newuUploadedFile.MIMEType = file.ContentType;
                    if (automationAction.UploadedFiles == null)
                    {
                        automationAction.UploadedFiles = new List<AssignmentUploadedFile>();
                    }

                    automationAction.UploadedFiles.Add(newuUploadedFile);

                    await _appDbContext.SaveChangesAsync();

                    // await _cacheService.RemoveCompanyInfoCache(companyUser.CompanyId, "AssignmentRuleInfo");
                    var response = _mapper.Map<AssignmentUploadedFile>(newuUploadedFile);
                    return Ok(response);
                }
            }

            return BadRequest();
        }

        /// <summary>
        /// To delete attachment in automation action.
        /// </summary>
        /// <param name="automationActionId">ID of the automation action.</param>
        /// <param name="attachmentId">ID of the attachment.</param>
        /// <returns>N/A.</returns>
        [HttpDelete]
        [Route("Company/AutomationAction/{automationActionId:long}/Attachment/{attachmentId:long}")]
        public async Task<IActionResult> DeleteFileForAutomations(
            long automationActionId,
            long attachmentId)
        {
            var companyUser = await _coreService.GetCompanyStaff(
                await _userManager.GetUserAsync(User));

            if (string.IsNullOrEmpty(companyUser?.CompanyId))
            {
                return Unauthorized();
            }

            var automationAction = await _appDbContext.CompanyAutomationActions
                .Where(
                    x =>
                        x.Id == automationActionId
                        && x.CompanyId == companyUser.CompanyId)
                .Include(x => x.UploadedFiles)
                .FirstOrDefaultAsync();

            var attachmentToDelete = automationAction?.UploadedFiles
                .FirstOrDefault(x => x.Id == attachmentId);

            if (attachmentToDelete is null)
            {
                return NotFound();
            }

            automationAction.UploadedFiles.Remove(attachmentToDelete);
            await _appDbContext.SaveChangesAsync();

            await _azureBlobStorageService.DeleteFromAzureBlob(
                attachmentToDelete.Filename,
                attachmentToDelete.BlobContainer);

            return NoContent();
        }

        /// <summary>
        /// Get automation rule with filter.
        /// </summary>
        /// <param name="offset">For pagination.</param>
        /// <param name="limit">For pagination.</param>
        /// <param name="automationType">Filter by automation type.</param>
        /// <param name="name">Filter by automation name.</param>
        /// <returns></returns>
        [HttpGet]
        [Route("Company/AssignmentRule")]
        public async Task<ActionResult<List<AssignmentRuleResponse>>> GetAssignmentRules(
            [FromQuery(Name = "offset")]
            int offset = 0,
            [FromQuery(Name = "limit")]
            int limit = 1000,
            [FromQuery(Name = "triggerType")]
            AutomationType? automationType = null,
            [FromQuery(Name = "name")]
            string name = null)
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            // var key = $"AssignmentRuleInfo_{companyUser.CompanyId}_{companyUser.IdentityId}_{automationType}";
            // var data = await _cacheService.GetCacheData(key);
            //
            // if (!string.IsNullOrEmpty(data))
            //     return Ok(JsonConvert.DeserializeObject<List<AssignmentRuleResponse>>(data));
            var excludeType = new List<AutomationType>()
            {
                AutomationType.QRCodeAssigneeMapping,
                AutomationType.QRCodeAssignTeamMapping,
                AutomationType.ZapierContactUpdated,
                AutomationType.ZapierNewIncomingMessage,
                AutomationType.CrmHubOnEntityCreated,
                AutomationType.CrmHubContactUpdated,
                AutomationType.CrmHubOnEntityFieldsChanged,
            };

            var assignmentRules = await _appDbContext.CompanyAssignmentRules
                .Where(
                    x => x.CompanyId == companyUser.CompanyId &&
                         x.Status != AutomationStatus.Saved)
                .WhereIf(!automationType.HasValue, x => !excludeType.Contains(x.AutomationType))
                .WhereIf(automationType.HasValue, x => x.AutomationType == automationType)
                .WhereIf(!string.IsNullOrEmpty(name), x => x.AssignmentRuleName.Contains(name))
                .Include(x => x.AssignedStaff.Identity).Include(x => x.AutomationActions)
                .ThenInclude(x => x.AssignedTeam.Members).Include(x => x.AutomationActions)
                .ThenInclude(x => x.AssignedStaff.Identity).Include(x => x.AutomationActions)
                .ThenInclude(x => x.UploadedFiles).Include(x => x.SavedBy.Identity).Include(x => x.AssignedTeam.Members)
                .ThenInclude(x => x.Staff.Identity).Include(x => x.AssociatedList).Include(x => x.AutomationActions)
                .ThenInclude(x => x.FbIgAutoReply).ThenInclude(x => x.FbIgAutoReplyFiles)
                .OrderBy(x => x.Order).Skip(offset).Take(limit).ToListAsync(HttpContext.RequestAborted);
            var assignmentRelesResponse = _mapper.Map<List<AssignmentRuleResponse>>(
                assignmentRules.Where(
                    x => x.AutomationType != AutomationType.ZapierContactUpdated &&
                         x.AutomationType != AutomationType.CrmHubContactUpdated));
            assignmentRelesResponse.ForEach(
                x => x.AutomationActions = x.AutomationActions.OrderBy(y => y.Order).ThenBy(y => y.Id).ToList());

            // assignmentRelesResponse.ForEach(x => x.TriggeredCounter = _appDbContext.CompanyAutomationHistories.Count(y => y.TargetAssignmentRuleId == x.AssignmentId));
            // await _cacheService.SaveCacheData(key, JsonConvert.SerializeObject(assignmentRelesResponse), TimeSpan.FromMinutes(1));
            return Ok(assignmentRelesResponse);
        }

        [HttpGet]
        [Route("Company/AssignmentRule/{assignmentId}")]
        public async Task<ActionResult<List<AssignmentRuleResponse>>> GetAssignmentRuleById(string assignmentId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var assignmentRules = await _appDbContext.CompanyAssignmentRules
                .Where(x => x.CompanyId == companyUser.CompanyId && x.AssignmentId == assignmentId)
                .Include(x => x.AutomationActions).ThenInclude(x => x.AssignedTeam.Members)
                .Include(x => x.AutomationActions).ThenInclude(x => x.AssignedStaff.Identity)
                .Include(x => x.AutomationActions).ThenInclude(x => x.UploadedFiles)
                .Include(x => x.AssignedStaff.Identity).Include(x => x.AssignedTeam.Members)
                .ThenInclude(x => x.Staff.Identity).Include(x => x.AssociatedList).Include(x => x.AutomationActions)
                .ThenInclude(x => x.FbIgAutoReply).ThenInclude(x => x.FbIgAutoReplyFiles).OrderBy(x => x.Order)
                .ToListAsync(HttpContext.RequestAborted);
            var assignmentRulesResponse = _mapper.Map<List<AssignmentRuleResponse>>(assignmentRules);
            assignmentRulesResponse.ForEach(
                x => x.AutomationActions = x.AutomationActions.OrderBy(y => y.Order).ThenBy(y => y.Id).ToList());

            return Ok(assignmentRulesResponse);
        }

        [HttpPost]
        [Route("Company/AssignmentRule/duplicate/{assignmentId}")]
        public async Task<ActionResult<List<AssignmentRuleResponse>>> DuplicateAssignmentRuleById(string assignmentId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            await _automationRuleService.DuplicateAutomationRules(
                companyUser,
                new List<string>
                {
                    assignmentId
                });

            var _assignmentRules = await _appDbContext.CompanyAssignmentRules
                .Where(x => x.CompanyId == companyUser.CompanyId && x.Status != AutomationStatus.Saved)
                .Include(x => x.AssignedStaff.Identity).Include(x => x.AssignedTeam.Members)
                .ThenInclude(x => x.Staff.Identity).OrderBy(x => x.Order).ToListAsync();
            var assignmentRuleResponses = _mapper.Map<List<AssignmentRuleResponse>>(_assignmentRules);

            // await _cacheService.RemoveCompanyInfoCache(companyUser.CompanyId, "AssignmentRuleInfo");
            return Ok(assignmentRuleResponses);
        }

        /// <summary>
        /// Bulk duplicate automation rule.
        /// </summary>
        /// <param name="duplicateAutomationRuleViewModel">A list of automation rule.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Company/AssignmentRule/duplicate")]
        public async Task<ActionResult<List<AssignmentRuleResponse>>> DuplicateAssignmentRule(
            [FromBody]
            DuplicateAutomationRuleViewModel duplicateAutomationRuleViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            await _automationRuleService.DuplicateAutomationRules(
                companyUser,
                duplicateAutomationRuleViewModel.AutomationRuleIds);

            var assignmentRules = await _appDbContext.CompanyAssignmentRules
                .Where(x => x.CompanyId == companyUser.CompanyId && x.Status != AutomationStatus.Saved)
                .Include(x => x.AssignedStaff.Identity).Include(x => x.AssignedTeam.Members)
                .ThenInclude(x => x.Staff.Identity).OrderBy(x => x.Order).ToListAsync();
            var assignmentRuleResponses = _mapper.Map<List<AssignmentRuleResponse>>(assignmentRules);

            return Ok(assignmentRuleResponses);
        }

        [HttpDelete]
        [Route("Company/AssignmentRule")]
        public async Task<ActionResult<ResponseViewModel>> DeleteAssignmentRules(
            [FromBody]
            DeleteAssignmentRuleViewModel deleteAssignmentRuleViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var assignmentRuleTobeDeleted = await _appDbContext.CompanyAssignmentRules
                .Where(x => deleteAssignmentRuleViewModel.AssignmentRuleIds.Contains(x.AssignmentId))
                .Include(x => x.AssignedTeam.Members).Include(x => x.AssociatedList).ToListAsync();

            _logger.LogInformation(
                "[{MethodName}] Company {CompanyId} user {UserId} deletes assignment rule ids: {AssignmentRuleIds}",
                nameof(DeleteAssignmentRules),
                companyUser.CompanyId,
                companyUser.IdentityId,
                JsonConvert.SerializeObject(
                    assignmentRuleTobeDeleted
                        .Select(x => x.AssignmentId)
                        .ToList()));

            // var deleteLists = assignmentRuleTobeDeleted.Where(x => x.AssociatedListId.HasValue).Select(x => x.AssociatedListId).ToList();
            foreach (var scheduledRule in assignmentRuleTobeDeleted.Where(
                         x => x.AutomationType == AutomationType.RecurringJob))
            {
                RecurringJob.RemoveIfExists(scheduledRule.AssignmentId);
            }

            // do not remove the rule, just update to save status
            assignmentRuleTobeDeleted.ForEach(x => x.Status = AutomationStatus.Saved);
            await _appDbContext.SaveChangesAsync();

            // var listToBeDeleted = new List<long>();
            // foreach (var id in deleteLists)
            //     listToBeDeleted.Add(id.Value);
            //
            // await _userProfileService.DeleteContactList(companyUser.CompanyId, new GroupListViewModel { ListIds = listToBeDeleted });
            await _signalRService.SignalROnAssignmentRuleDelete(companyUser.Company, assignmentRuleTobeDeleted);

            return Ok(
                new ResponseViewModel
                {
                    message = "success"
                });
        }

        [HttpGet]
        [Authorize]
        [Route("Company/Instagram/ConnectionURL")]
        public async Task<IActionResult> GetInstagramConnectionURL([FromQuery] string appDomainName)
        {
            appDomainName = string.IsNullOrEmpty(appDomainName) ? _configuration.GetValue<string>("Values:AppDomainName") : appDomainName;
            if (!AuthorizedAppDomainNames.AllAuthorizedAppDomainNames.Contains(appDomainName))
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "app domain name is not in the list"
                    });
            }

            var facebookAppID = _configuration.GetValue<string>("Facebook:ClientId");

            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            // instagram_basic,instagram_manage_messages"
            var permission =
                "pages_read_engagement,instagram_basic,instagram_manage_messages,pages_manage_metadata,pages_messaging,instagram_manage_comments";
            if (await _appDbContext.ConfigFacebookConfigs
                    .AnyAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.SubscribedFields.Contains("leadgen")))
            {
                permission +=
                    ",pages_read_engagement,pages_read_user_content,ads_management,leads_retrieval,pages_show_list,pages_manage_ads";
            }

            if (await _appDbContext.ConfigFacebookConfigs
                    .AnyAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.SubscribedFields.Contains("messages")))
            {
                permission += ",pages_read_engagement,pages_read_user_content,pages_messaging,public_profile,email";
            }

            dynamic jObject = new JObject();
            jObject.Url = $"https://www.facebook.com/dialog/oauth?scope={permission}" +
                          $"&client_id={facebookAppID}" +

                          // $"&redirect_uri=https://83778d292e9e.ngrok.io/facebook/connect&state={companyUser.CompanyId}&response_type=code";
                          $"&redirect_uri={appDomainName}/instagram/connect&state={companyUser.CompanyId}&response_type=code";

            return Ok(jObject);
        }

        [HttpGet]
        [Authorize]
        [Route("Company/Facebook/ConnectionURL")]
        public async Task<IActionResult> GetFacebookConnectionURL([FromQuery] string appDomainName)
        {
            appDomainName = string.IsNullOrEmpty(appDomainName)
                ? _configuration.GetValue<string>("Values:AppDomainName")
                : appDomainName;

            if (!AuthorizedAppDomainNames.AllAuthorizedAppDomainNames.Contains(appDomainName))
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "app domain name is not in the list"
                    });
            }

            var facebookAppID = _configuration.GetValue<string>("Facebook:ClientId");

            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            // instagram_basic,instagram_manage_messages"
            var permission =
                "pages_manage_metadata,pages_read_engagement,pages_read_user_content,pages_messaging,public_profile,email,pages_manage_engagement";
            if (await _appDbContext.ConfigFacebookConfigs
                    .AnyAsync(
                        x =>
                            x.CompanyId == companyUser.CompanyId
                            && x.SubscribedFields.Contains("leadgen")))
            {
                permission += ",ads_management,leads_retrieval,pages_show_list,pages_manage_ads";
            }

            if (await _appDbContext.ConfigInstagramConfigs
                    .AnyAsync(x => x.CompanyId == companyUser.CompanyId))
            {
                permission += ",pages_read_engagement,instagram_basic,instagram_manage_messages";
            }

            dynamic jObject = new JObject();
            jObject.Url = $"https://www.facebook.com/dialog/oauth?scope={permission}" +
                          $"&client_id={facebookAppID}" +
                          $"&redirect_uri={appDomainName}/facebook/connect&state={companyUser.CompanyId}&response_type=code";

            return Ok(jObject);
        }

        [HttpGet]
        [Route("Company/Facebook/status/{pageId}")]
        public async Task<IActionResult> GetFacebookStatus(string pageId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser is null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "User: {CompanyUserEmail} is getting status facebook: {FacebookConfigPageId}",
                companyUser.Identity?.Email,
                pageId);

            var facebookConfigs = await _appDbContext.ConfigFacebookConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId && x.PageId == pageId).FirstOrDefaultAsync();

            if (facebookConfigs is null)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = "not found"
                    });
            }

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var subscribleResponse = await httpClient.GetAsync(
                $"https://graph.facebook.com/{facebookConfigs.PageId}/conversations?" +
                $"access_token={facebookConfigs.PageAccessToken}");

            if (subscribleResponse.IsSuccessStatusCode)
            {
                if (facebookConfigs.Status != FacebookStatus.Syncing &&
                    facebookConfigs.Status != FacebookStatus.Authenticated)
                {
                    facebookConfigs.Status = FacebookStatus.Authenticated;
                    await _appDbContext.SaveChangesAsync();
                    await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);
                }

                var response = _mapper.Map<FacebookConfigViewModel>(facebookConfigs);
                return Ok(response);
            }
            else
            {
                if (facebookConfigs.Status != FacebookStatus.Invalid)
                {
                    facebookConfigs.Status = FacebookStatus.Invalid;
                    await _appDbContext.SaveChangesAsync();
                    await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);
                }

                var response = _mapper.Map<FacebookConfigViewModel>(facebookConfigs);
                return Ok(response);
            }

        }

        [HttpGet]
        [Route("company/facebook/sync/{pageId}")]
        public async Task<IActionResult> SyncWFacebookHistory(string pageId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser is null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "User: {CompanyUserEmail} is syncing history {FacebookConfigPageId}",
                companyUser.Identity.Email,
                pageId);

            var facebookConfig = await _appDbContext.ConfigFacebookConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId && x.PageId == pageId).FirstOrDefaultAsync();

            if (facebookConfig is null)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = "not found"
                    });
            }

            BackgroundJob.Enqueue<IFacebookInstagramMessageService>(
                x => x.FetchConversationsBackground(pageId, 100, false));

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            var response = _mapper.Map<FacebookConfigViewModel>(facebookConfig);
            return Ok(response);

        }

        [HttpPost]
        [Route("Company/Facebook/Subscrible")]
        [Route("Company/Facebook/Subscribe")]
        public async Task<IActionResult> SubscribeApp([FromBody] ConnectFacebookViewModel connectFacebookViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser is null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "Company Id: {CompanyId}, Staff Identity Id: {IdentityId} initiated a request to connect a Facebook channel. Payload: {Payload}",
                companyUser.CompanyId,
                companyUser.IdentityId,
                JsonConvert.SerializeObject(connectFacebookViewModel));

            try
            {
                var response = await _facebookChannelService.ConnectFacebookChannelAsync(
                    companyUser.CompanyId,
                    connectFacebookViewModel.id,
                    connectFacebookViewModel.page_name,
                    connectFacebookViewModel.access_token,
                    companyUser.IdentityId,
                    connectFacebookViewModel.business_integration_system_user_access_token);

                var webhookUrl = _configuration["Values:DomainName"] + DefaultWebhookUrls.FacebookWebhookHandlingUrl;

                try
                {
                    await _facebookWebhooksApi.FacebookWebhooksRegisterWebhookPostAsync(registerWebhookInput: new RegisterWebhookInput(
                        companyUser.CompanyId, webhookUrl, connectFacebookViewModel.id));
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Facebook Webhook Registration]  [companyId]: {companyId} failed to register, error: {ExceptionString}",
                        companyUser.CompanyId,
                        ex.ToString());
                }

                return Ok(response);
            }
            catch (ExceedingMaximumNumberOfChannelsException exceedingMaximumNumberOfChannelsException)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = exceedingMaximumNumberOfChannelsException.Message
                    });
            }
        }

        [HttpPost]
        [Route("Company/Instagram/Subscribe")]
        public async Task<IActionResult> InstagramSubscribeApp(
            [FromBody]
            ConnectFacebookViewModel connectFacebookViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser is null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "Company Id: {CompanyId}, Staff Identity Id: {IdentityId} initiated a request to connect a Instagram channel. Payload: {Payload}",
                companyUser.CompanyId,
                companyUser.IdentityId,
                JsonConvert.SerializeObject(connectFacebookViewModel));

            try
            {
                var response = await _instagramChannelService.ConnectInstagramChannelAsync(
                        companyUser.CompanyId,
                        connectFacebookViewModel.id,
                        connectFacebookViewModel.page_name,
                        connectFacebookViewModel.access_token,
                        companyUser.IdentityId,
                        connectFacebookViewModel.business_integration_system_user_access_token
                    );

                var webhookUrl = _configuration["Values:DomainName"] + DefaultWebhookUrls.InstagramWebhookHandlingUrl;

                try
                {
                    await _facebookWebhooksApi.FacebookWebhooksRegisterWebhookPostAsync(registerWebhookInput: new RegisterWebhookInput(
                        companyUser.CompanyId, webhookUrl, connectFacebookViewModel.id));
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Instagram Webhook Registration]  [companyId]: {companyId} failed to register, error: {ExceptionString}",
                        companyUser.CompanyId,
                        ex.ToString());
                }

                return Ok(response);
            }
            catch (ExceedingMaximumNumberOfChannelsException exceedingMaximumNumberOfChannelsException)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = exceedingMaximumNumberOfChannelsException.Message
                    });
            }
        }

        // PLEASE DON'T DELETE THIS ENDPOINT EVEN IF NO ONE USE IT
        [HttpPost]
        [Route("Company/Facebook/rename/{pageId}")]
        [Route("Company/Facebook/update/{pageId}")]
        public async Task<IActionResult> RenameFacebookChannel(
            string pageId,
            [FromBody]
            ChannelRenameViewModel channelRenameViewModel)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser is null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "Company Id: {CompanyId}, Staff Identity Id: {IdentityId} initiated a request to update a facebook channel. Page Id: {pageId}. Payload: {Payload}",
                companyUser.CompanyId,
                companyUser.IdentityId,
                pageId,
                JsonConvert.SerializeObject(channelRenameViewModel));

            try
            {
                var response = await _facebookChannelService.UpdateFacebookChannelAsync(
                    companyUser.CompanyId,
                    pageId,
                    channelRenameViewModel);

                return Ok(response);
            }
            catch (ChannelNotFoundException channelNotFoundException)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = channelNotFoundException.Message
                    });
            }
        }

        [HttpDelete]
        [Route("Company/Facebook/{pageId}")]
        public async Task<IActionResult> RemoveFacebookChannel(string pageId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser is null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "Company Id: {CompanyId}, Staff Identity Id: {IdentityId} initiated a request to remove a facebook channel. Page Id: {pageId}.",
                companyUser.CompanyId,
                companyUser.IdentityId,
                pageId);

            try
            {
                await _facebookChannelService.RemoveFacebookChannelAsync(
                    companyUser.CompanyId,
                    pageId);
                try
                {
                    var webhookUrl = _configuration["Values:DomainName"] + DefaultWebhookUrls.FacebookWebhookHandlingUrl;
                    await _facebookWebhooksApi.FacebookWebhooksDeregisterWebhookPostAsync(deregisterWebhookInput: new DeregisterWebhookInput(
                        companyUser.CompanyId, webhookUrl, pageId));
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Facebook Webhook Deregistration]  [companyId]: {companyId} failed to register, error: {ExceptionString}",
                        companyUser.CompanyId,
                        ex.ToString());
                }

                return Ok(
                    new ResponseViewModel
                    {
                        message = "success"
                    });
            }
            catch (ChannelNotFoundException channelNotFoundException)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = channelNotFoundException.Message
                    });
            }
        }

        [HttpDelete]
        [Route("Company/instagram/{instagramPageId}")]
        public async Task<IActionResult> RemoveInstagramChannel(string instagramPageId)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser is null)
            {
                return Unauthorized();
            }

            try
            {
                _logger.LogInformation(
                    "Company Id: {CompanyId}, Staff Identity Id: {IdentityId} initiated a request to remove a instagram channel instagram page Id: {instagramPageId}.",
                    companyUser.CompanyId,
                    companyUser.IdentityId,
                    instagramPageId);

                await _instagramChannelService.RemoveInstagramChannelAsync(
                    companyUser.CompanyId,
                    instagramPageId);

                try
                {
                    var webhookUrl = _configuration["Values:DomainName"] + DefaultWebhookUrls.InstagramWebhookHandlingUrl;
                    await _facebookWebhooksApi.FacebookWebhooksDeregisterWebhookPostAsync(deregisterWebhookInput: new DeregisterWebhookInput(
                        companyUser.CompanyId, webhookUrl, instagramPageId));
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "[Instagram Webhook Deregistration]  [companyId]: {companyId} failed to register, error: {ExceptionString}",
                        companyUser.CompanyId,
                        ex.ToString());
                }

                return Ok(
                    new ResponseViewModel
                    {
                        message = "success"
                    });
            }
            catch (ChannelNotFoundException channelNotFoundException)
            {
                return NotFound(
                    new ResponseViewModel
                    {
                        message = channelNotFoundException.Message
                    });
            }
        }

        [HttpGet]
        [Route("Company/wechat/webhookURL")]
        public async Task<IActionResult> GetWeChatWebhookURL()
        {
            var domainName = _configuration.GetValue<String>("Values:DomainName");

            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            dynamic jObject = new JObject();
            jObject.Url = $"{domainName}/wechat/webhook/{companyUser.CompanyId}";

            return Ok(jObject);
        }

        [HttpPost]
        [Route("Company/wechat/qrcode/{appId}")]
        public async Task<IActionResult> QRCodeUploadWeChat(
            string appId,
            [FromForm]
            ProfilePictureViewModel connectWeChatViewModel)
        {
            if (ModelState.IsValid)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var newWeChatConfig = await _appDbContext.ConfigWeChatConfigs.Where(x => x.AppId == appId)
                    .Include(x => x.QRCode).FirstOrDefaultAsync();

                if (newWeChatConfig == null)
                {
                    return NotFound(
                        new ResponseViewModel
                        {
                            message = $"AppId:{appId} not found"
                        });
                }

                foreach (IFormFile file in connectWeChatViewModel.files)
                {
                    var fileName = $"{companyUser.CompanyId}/WeChat/{DateTime.UtcNow.ToString("o")}/{file.FileName}";
                    var uploadFileResult = await _uploadService.UploadImage(companyUser.CompanyId, fileName, file);

                    if (uploadFileResult?.Url == null)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = "upload failure"
                            });
                    }

                    var newuUploadedFile = new ProfilePictureFile();
                    newuUploadedFile.Filename = fileName;
                    newuUploadedFile.CompanyId = companyUser.CompanyId;
                    newuUploadedFile.BlobContainer = companyUser.CompanyId;
                    newuUploadedFile.Url = uploadFileResult.Url;
                    newuUploadedFile.MIMEType = file.ContentType;
                    newWeChatConfig.QRCode = newuUploadedFile;

                    var domainName = _configuration.GetValue<String>("Values:DomainName");
                    newWeChatConfig.QRCodeURL = $"{domainName}/webchat/qrcode/{newuUploadedFile.ProfilePictureId}";

                    await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                    await _appDbContext.SaveChangesAsync();

                    dynamic response = new JObject();
                    response.message = "success";
                    response.url = newWeChatConfig.QRCodeURL;

                    return Ok(response);
                }
            }

            return BadRequest(ModelState);
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("webchat/qrcode/{filenameId}")]
        public async Task<IActionResult> GetQRcodeAzureBlob(string filenameId)
        {
            var file = await _appDbContext.UserStaffProfilePictures.FirstOrDefaultAsync(
                x => x.ProfilePictureId == filenameId);
            var stream = await _azureBlobStorageService.DownloadFromAzureBlob(file.Filename, file.BlobContainer);
            return File(
                stream.ToArray(),
                string.IsNullOrEmpty(file.MIMEType) ? "application/octet-stream" : file.MIMEType,
                file.Filename);
        }

        [HttpPost]
        [Route("Company/wechat/connect")]
        public async Task<IActionResult> ConnectWeChat([FromBody] ConnectWeChatViewModel connectWeChatViewModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            var company = await _appDbContext.CompanyCompanies.Include(x => x.WeChatConfig)
                .FirstOrDefaultAsync(x => x.Id == companyUser.CompanyId);

            if (companyUser == null)
            {
                return BadRequest();
            }
            var companyUsage = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);
            if (companyUsage.totalChannelAdded > companyUsage.MaximumNumberOfChannel)
            {
                _logger.LogWarning(
                    "[{MethodName}] Company {CompanyId}, user {UserIdentityId} connect WeChat failure due to exceeding maximum number of channels: {MaximumChannelNumber}",
                    nameof(ConnectWeChat),
                    companyUser.CompanyId,
                    companyUser.IdentityId,
                    companyUsage.MaximumNumberOfChannel);

                return BadRequest(
                    new ResponseViewModel
                    {
                        message =
                            $"Your subscription plan only could add {companyUsage.MaximumNumberOfChannel} channels"
                    });
            }

            var newWeChatConfig =
                await _appDbContext.ConfigWeChatConfigs.FirstOrDefaultAsync(
                    x => x.AppId == connectWeChatViewModel.appId);
            if (newWeChatConfig == null)
            {
                newWeChatConfig = new WeChatConfig();
            }

            newWeChatConfig.Name = connectWeChatViewModel.name;
            newWeChatConfig.AppId = connectWeChatViewModel.appId;
            newWeChatConfig.ChannelIdentityId = newWeChatConfig.AppId;
            newWeChatConfig.AppSecret = connectWeChatViewModel.appSecret;
            company.WeChatConfig = newWeChatConfig;

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

            var getAccessTokenResponse = await httpClient.GetAsync(
                $"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={newWeChatConfig.AppId}&secret={newWeChatConfig.AppSecret}");
            if (getAccessTokenResponse.IsSuccessStatusCode)
            {
                var accessToken = JsonConvert.DeserializeObject<AccessTokenResponse>(
                    await getAccessTokenResponse.Content.ReadAsStringAsync());
                if (!accessToken.errcode.HasValue)
                {
                    newWeChatConfig.AccessToken = accessToken.access_token;
                    newWeChatConfig.TokenExpireAt = DateTime.UtcNow.AddSeconds(accessToken.expires_in - 30);

                    await _appDbContext.SaveChangesAsync();
                    await _signalRService.SignalROnChannelAdded(
                        company,
                        new ChannelSignal
                        {
                            ChannelName = ChannelTypes.Wechat
                        });

                    var response = _mapper.Map<WeChatConfigViewModel>(company.WeChatConfig);

                    if (await _appDbContext.CompanySandboxes
                            .AnyAsync(x => x.CompanyId == companyUser.CompanyId))
                    {
                        BackgroundJob.Enqueue<ICompanyService>(x => x.DeleteSandbox(companyUser.CompanyId));
                    }

                    await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                    var getWechatMenuResponse = await httpClient.GetAsync($"https://api.weixin.qq.com/cgi-bin/menu/get?&access_token={newWeChatConfig.AccessToken}");
                    if (getWechatMenuResponse.IsSuccessStatusCode)
                    {
                        try
                        {
                            var weChatMenuGetResponse = JsonConvert.DeserializeObject<WeChatMenuGetResponse>(
                                await getWechatMenuResponse.Content.ReadAsStringAsync());

                            if (weChatMenuGetResponse.WeChatCustomMenu == null)
                            {
                                throw new Exception();
                            }

                            newWeChatConfig.Metadata.Add("wechat_menu", weChatMenuGetResponse.WeChatCustomMenu);
                            _appDbContext.Entry(newWeChatConfig).Property(w => w.Metadata).IsModified = true;
                            await _appDbContext.SaveChangesAsync();

                            _logger.LogInformation(
                                "[{MethodName}] Company {CompanyId} Successfully stored WeChat Custom Menu. {payload}",
                                nameof(RestoreCustomMenu),
                                companyUser.CompanyId,
                                getWechatMenuResponse
                            );

                        }
                        catch (Exception e)
                        {
                            var weChatErrorResponse = JsonConvert.DeserializeObject<WeChatAPIResponse>(
                                await getWechatMenuResponse.Content.ReadAsStringAsync());

                            if (weChatErrorResponse.errcode == 46003)
                            {
                                _logger.LogInformation(
                                    "[{MethodName}] Company {CompanyId} does not have WeChat custom menu",
                                    nameof(RestoreCustomMenu),
                                    companyUser.CompanyId);
                                newWeChatConfig.Metadata.Add("wechat_menu", new object());
                                _appDbContext.Entry(newWeChatConfig).Property(w => w.Metadata).IsModified = true;
                                await _appDbContext.SaveChangesAsync();

                                return Ok(response);
                            }
                            return BadRequest();
                        }

                    }
                    return Ok(response);
                }

                return BadRequest(
                    new ResponseViewModel
                    {
                        code = (int)accessToken.errcode,
                        message = accessToken.errmsg
                    });
            }

            return BadRequest(
                new ResponseViewModel
                {
                    code = (int)getAccessTokenResponse.StatusCode,
                    message = getAccessTokenResponse.ReasonPhrase ?? String.Empty
                });

        }


        [HttpPost]
        [Route("Company/wechat/menu/restore")]
        public async Task<IActionResult> RestoreCustomMenu([FromBody] WeChatMenuCreateRequest createRequest)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            var company = await _appDbContext.CompanyCompanies
                .Where(x => x.Id == companyUser.CompanyId)
                .Include(x => x.WeChatConfig)
                .FirstOrDefaultAsync(x => x.WeChatConfig.AppId == createRequest.AppId);

            var weChatConfig = company?.WeChatConfig;


            if (companyUser == null)
            {
                return BadRequest();
            }

            if (weChatConfig == null)
            {
                return BadRequest();
            }

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);


            if (string.IsNullOrEmpty(weChatConfig.AccessToken) ||
                DateTime.UtcNow > weChatConfig.TokenExpireAt)
            {
                var getAccessTokenResponse = await httpClient.GetStringAsync(
                    $"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={weChatConfig.AppId}&secret={weChatConfig.AppSecret}");
                var accessToken = JsonConvert.DeserializeObject<AccessTokenResponse>(getAccessTokenResponse);
                weChatConfig.AccessToken = accessToken.access_token;
                weChatConfig.TokenExpireAt = DateTime.UtcNow.AddSeconds(accessToken.expires_in - 30);
                await _appDbContext.SaveChangesAsync();

                _logger.LogInformation(
                    "[{MethodName}] Company {CompanyId} refresh WeChat token",
                    nameof(RestoreCustomMenu),
                    companyUser.CompanyId);
            }

            try
            {
                var savedCustomMenu = JsonConvert.DeserializeObject<WeChatCustomMenu>(
                    JsonConvert.SerializeObject(company.WeChatConfig?.Metadata["wechat_menu"]));

                var jsonPayload = JsonConvert.SerializeObject(
                    savedCustomMenu,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    });


                var httpContent = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                _logger.LogInformation(
                    "Attempting to create/update WeChat custom menu for CompanyId {CompanyId}. URL: {RequestUrl}, Payload: {Payload}",
                    company.Id,
                    "https://api.weixin.qq.com/cgi-bin/menu/create",
                    jsonPayload);

                var responseMessage = await httpClient.PostAsync(
                    $"https://api.weixin.qq.com/cgi-bin/menu/create?&access_token={weChatConfig.AccessToken}",
                    httpContent);

                if (!responseMessage.IsSuccessStatusCode)
                {
                    return BadRequest();
                }

                var responseString = await responseMessage.Content.ReadAsStringAsync();
            }
            catch (Exception e)
            {
                return Ok();
            }

            return Ok();
        }

        [HttpPost]
        [Route("Company/wechat/rename/{appId}")]
        [Route("Company/wechat/update/{appId}")]
        public async Task<IActionResult> RenameWeChat(
            string appId,
            [FromBody]
            ChannelRenameViewModel channelRenameViewModel)
        {
            if (User.Identity.IsAuthenticated)
            {
                try
                {
                    var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                    if (companyUser == null)
                    {
                        return Unauthorized();
                    }

                    var weChatConfig =
                        await _appDbContext.ConfigWeChatConfigs.FirstOrDefaultAsync(
                            x => x.Id == companyUser.Company.WeChatConfigId);

                    if (weChatConfig?.AppId == appId)
                    {
                        try
                        {
                            var existing = await _appDbContext.ConfigWeChatConfigs
                                .Where(x => x.Id == companyUser.Company.WeChatConfigId)
                                .Include(x => x.QRCode)
                                .FirstOrDefaultAsync();
                            if (!string.IsNullOrEmpty(channelRenameViewModel.Name))
                            {
                                existing.Name = channelRenameViewModel.Name;
                            }

                            if (channelRenameViewModel.IsShowInWidget.HasValue)
                            {
                                existing.IsShowInWidget = channelRenameViewModel.IsShowInWidget.Value;
                            }

                            await _appDbContext.SaveChangesAsync();

                            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                            var response = _mapper.Map<WeChatConfigViewModel>(existing);

                            return Ok(response);
                        }
                        catch (Exception ex)
                        {
                            return BadRequest(ex);
                        }
                    }
                    else
                    {
                        return NotFound();
                    }
                }
                catch (Exception ex)
                {
                    return BadRequest(ex);
                }
            }

            return BadRequest();
        }

        [HttpDelete]
        [Route("Company/wechat/{appId}")]
        public async Task<IActionResult> RemoveWeChat(string appId)
        {
            if (User.Identity.IsAuthenticated)
            {
                try
                {
                    var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                    if (companyUser == null)
                    {
                        return Unauthorized();
                    }

                    var company = await _appDbContext.CompanyCompanies
                        .Where(x => x.Id == companyUser.CompanyId)
                        .Include(x => x.WeChatConfig)
                        .FirstOrDefaultAsync();

                    if (company.WeChatConfig?.AppId == appId)
                    {
                        try
                        {
                            _appDbContext.ConfigWeChatConfigs.RemoveRange(company.WeChatConfig);
                            company.WeChatConfig = null;
                            await _appDbContext.SaveChangesAsync();

                            await _signalRService.SignalROnChannelDeleted(
                                company,
                                new ChannelSignal
                                {
                                    ChannelName = ChannelTypes.Wechat
                                });

                            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                            return Ok(
                                new ResponseViewModel
                                {
                                    message = "success"
                                });
                        }
                        catch (Exception ex)
                        {
                            return BadRequest(ex);
                        }
                    }
                    else
                    {
                        return NotFound();
                    }
                }
                catch (Exception ex)
                {
                    return BadRequest(ex);
                }
            }

            return BadRequest();
        }

        [HttpGet]
        [Route("Company/email/webhookURL")]
        public async Task<IActionResult> GetEmailWebhookURL()
        {
            var domainName = _configuration.GetValue<String>("Values:DomainName");

            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            dynamic jObject = new JObject();
            jObject.Url = $"{domainName}/email/webhook/{companyUser.CompanyId}";

            return Ok(jObject);
        }

        [HttpPost]
        [Route("Company/email/connect")]
        public async Task<IActionResult> ConnectEmail([FromBody] ConnectEmailViewModel connectEmailViewModel)
        {
            // var userId = _userManager.GetUserId(User);
            // var companyUser = _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Company.EmailConfig).FirstOrDefault();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return Unauthorized();
            }

            var company = await _appDbContext.CompanyCompanies.Include(x => x.EmailConfig)
                .FirstOrDefaultAsync(x => x.Id == companyUser.CompanyId);
            var domainOfTo = connectEmailViewModel.Email.Substring(connectEmailViewModel.Email.LastIndexOf('@') + 1);

            var newEmailConfig =
                await _appDbContext.ConfigEmailConfigs.FirstOrDefaultAsync(x => x.Domain == $"@{domainOfTo}");
            if (newEmailConfig == null)
            {
                newEmailConfig = new EmailConfig();
            }

            newEmailConfig.Domain = $"@{domainOfTo}";
            newEmailConfig.Email = connectEmailViewModel.Email;
            newEmailConfig.ChannelIdentityId = newEmailConfig.Email;
            newEmailConfig.SendGridKey = connectEmailViewModel.SendGridKey;
            company.EmailConfig = newEmailConfig;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnChannelAdded(
                companyUser.Company,
                new ChannelSignal
                {
                    ChannelName = "email"
                });

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            return Ok(newEmailConfig);
        }

        [HttpPost]
        [Route("Company/email/rename/{domain}")]
        [Route("Company/email/update/{domain}")]
        public async Task<IActionResult> RemoveEmail(
            string domain,
            [FromBody]
            ChannelRenameViewModel channelRenameViewModel)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var emailConfig =
                    await _appDbContext.ConfigEmailConfigs.FirstOrDefaultAsync(
                        x => x.Id == companyUser.Company.EmailConfigId);

                if (companyUser.Company.EmailConfig?.Domain == domain)
                {
                    if (!string.IsNullOrEmpty(channelRenameViewModel.Name))
                    {
                        emailConfig.Name = channelRenameViewModel.Name;
                    }

                    if (channelRenameViewModel.IsShowInWidget.HasValue)
                    {
                        emailConfig.IsShowInWidget = channelRenameViewModel.IsShowInWidget.Value;
                    }

                    await _appDbContext.SaveChangesAsync();

                    await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                    var response = _mapper.Map<EmailConfigViewModel>(emailConfig);
                    return Ok(response);
                }
            }

            return BadRequest();
        }

        [HttpDelete]
        [Route("Company/email/{domain}")]
        public async Task<IActionResult> RemoveEmail(string domain)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var emailConfig =
                    await _appDbContext.ConfigEmailConfigs.FirstOrDefaultAsync(
                        x => x.Id == companyUser.Company.EmailConfigId);

                if (emailConfig?.Domain == domain)
                {
                    _appDbContext.ConfigEmailConfigs.RemoveRange(emailConfig);
                    await _appDbContext.SaveChangesAsync();

                    await _signalRService.SignalROnChannelDeleted(
                        companyUser.Company,
                        new ChannelSignal
                        {
                            ChannelName = ChannelTypes.Email
                        });

                    await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);
                    return Ok(
                        new ResponseViewModel
                        {
                            message = "success"
                        });
                }
            }

            return BadRequest();
        }

        [HttpPost]
        [Route("Company/Request/Channel")]
        public async Task<IActionResult> RequestChannel([FromBody] RequestChannelViewModel requestChannelViewModel)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser != null)
                {
                    var request = _mapper.Map<RequestChannel>(requestChannelViewModel);
                    request.CompanyId = companyUser.CompanyId;

                    await _appDbContext.CompanyRequestChannels.AddAsync(request);
                    await _appDbContext.SaveChangesAsync();
                }

                return Ok(
                    new ResponseViewModel
                    {
                        message = "success"
                    });
            }

            return BadRequest();
        }

        [HttpGet]
        [Route("Company/line/webhookURL")]
        public async Task<IActionResult> GetLineWebhookURL()
        {
            var domainName = _configuration.GetValue<String>("Values:DomainName");

            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            dynamic jObject = new JObject();
            jObject.Url = $"{domainName}/line/webhook/{companyUser.CompanyId}";

            return Ok(jObject);
        }

        [HttpPost]
        [Route("Company/line/connect")]
        public async Task<IActionResult> ConnectLine([FromBody] ConnectLineViewModel connectLineViewModel)
        {
            if (ModelState.IsValid)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                // companyUser.Company.LineConfigs = await _appDbContext.ConfigLineConfigs.Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync();
                if (companyUser != null)
                {
                    var company = await _companyService.GetCompany(companyUser.CompanyId);
                    var response = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);

                    if (response.totalChannelAdded > response.MaximumNumberOfChannel)
                    {
                        _logger.LogWarning(
                            "Company {CompanyId}, user {UserIdentityId} connect LINE failure due to exceeding maximum number of channels: {MaximumChannelNumber}",
                            companyUser.CompanyId,
                            companyUser.IdentityId,
                            response.MaximumNumberOfChannel);

                        return BadRequest(
                            new ResponseViewModel
                            {
                                message =
                                    $"Your subscription plan only could add {response.MaximumNumberOfChannel} channels"
                            });
                    }

                    var newLineConfig = await _appDbContext.ConfigLineConfigs.FirstOrDefaultAsync(
                        x => x.CompanyId == companyUser.CompanyId && x.ChannelID == connectLineViewModel.ChannelId);
                    if (newLineConfig == null)
                    {
                        newLineConfig = new LineConfig()
                        {
                            CompanyId = companyUser.CompanyId
                        };
                        _appDbContext.ConfigLineConfigs.Add(newLineConfig);
                    }

                    newLineConfig.Name = connectLineViewModel.Name;
                    newLineConfig.ChannelID = connectLineViewModel.ChannelId;
                    newLineConfig.ChannelIdentityId = newLineConfig.ChannelID;
                    newLineConfig.ChannelSecert = connectLineViewModel.ChannelSecret;
                    newLineConfig.BasicId = connectLineViewModel.BasicId;

                    var formContent = new FormUrlEncodedContent(
                        new[]
                        {
                            new KeyValuePair<string, string>("grant_type", "client_credentials"),
                            new KeyValuePair<string, string>("client_id", newLineConfig.ChannelID),
                            new KeyValuePair<string, string>("client_secret", newLineConfig.ChannelSecert)
                        });

                    var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);

                    var getAccessTokenResponse = await httpClient.PostAsync(
                        $"https://api.line.me/v2/oauth/accessToken",
                        formContent);
                    if (getAccessTokenResponse.IsSuccessStatusCode)
                    {
                        var accessToken = JsonConvert.DeserializeObject<LineAccessTokenResult>(
                            await getAccessTokenResponse.Content.ReadAsStringAsync());
                        if (string.IsNullOrEmpty(accessToken.error))
                        {
                            newLineConfig.ChannelAccessToken = accessToken.access_token;
                            newLineConfig.TokenExpireAt = DateTime.UtcNow.AddSeconds(accessToken.expires_in - 30);

                            await _appDbContext.SaveChangesAsync();
                            await _signalRService.SignalROnChannelAdded(
                                company,
                                new ChannelSignal
                                {
                                    ChannelName = ChannelTypes.Line
                                });

                            if (await _appDbContext.CompanySandboxes
                                    .AnyAsync(x => x.CompanyId == companyUser.CompanyId))
                            {
                                BackgroundJob.Enqueue<ICompanyService>(x => x.DeleteSandbox(companyUser.CompanyId));
                            }

                            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                            return Ok(
                                new ResponseViewModel()
                                {
                                    message = "success"
                                });
                        }

                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = accessToken.error_description
                            });
                    }

                    return BadRequest(
                        new ResponseViewModel
                        {
                            code = (int)getAccessTokenResponse.StatusCode,
                            message = getAccessTokenResponse.ReasonPhrase
                        });
                }
            }

            return BadRequest(ModelState);
        }

        [HttpPost]
        [Route("Company/line/rename/{channelId}")]
        [Route("Company/line/update/{channelId}")]
        public async Task<IActionResult> RenameLine(
            string channelId,
            [FromBody]
            ChannelRenameViewModel channelRenameViewModel)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                // companyUser.Company.LineConfigs = await _appDbContext.ConfigLineConfigs.Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync();
                var lineConfig = await _appDbContext.ConfigLineConfigs.FirstOrDefaultAsync(
                    x => x.CompanyId == companyUser.CompanyId && x.ChannelID == channelId);
                if (!string.IsNullOrEmpty(channelRenameViewModel.Name))
                {
                    lineConfig.Name = channelRenameViewModel.Name;
                }

                if (channelRenameViewModel.IsShowInWidget.HasValue)
                {
                    lineConfig.IsShowInWidget = channelRenameViewModel.IsShowInWidget.Value;
                }

                await _appDbContext.SaveChangesAsync();

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                var response = _mapper.Map<LineConfigViewModel>(lineConfig);
                return Ok(response);
            }

            return BadRequest();
        }

        [HttpDelete]
        [Route("Company/line/{channelId}")]
        public async Task<IActionResult> RemoveLine(string channelId)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }


                // companyUser.Company.LineConfigs = await _appDbContext.ConfigLineConfigs.Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync();
                await _appDbContext.ConfigLineConfigs
                    .Where(x => x.CompanyId == companyUser.CompanyId && x.ChannelID == channelId).ExecuteDeleteAsync();

                var company = await _companyService.GetCompany(companyUser.CompanyId);

                await _signalRService.SignalROnChannelDeleted(
                    company,
                    new ChannelSignal
                    {
                        ChannelName = ChannelTypes.Line
                    });

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                return Ok(
                    new ResponseViewModel
                    {
                        message = "success"
                    });
            }

            return BadRequest();
        }

        #region Viber

        /// <summary>
        /// Get Viber and Configs for the Company.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("Company/viber")]
        public async Task<ActionResult<GetViberOutput>> GetViberWebhookUrl()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var viberConfigs = await _appDbContext.ConfigViberConfigs
                .AsNoTracking()
                .Where(x => x.CompanyId == companyUser.CompanyId)
                .ProjectTo<ViberConfigResponse>(_mapper.ConfigurationProvider)
                .ToListAsync(HttpContext.RequestAborted);

            var output = new GetViberOutput()
            {
                ViberConfigs = viberConfigs
            };

            return Ok(output);
        }

        /// <summary>
        /// Connect a new Viber Channel.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("Company/viber/connect")]
        public async Task<ActionResult<ViberConfigResponse>> ConnectViber([FromBody] ConnectViberInput input)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return BadRequest(ModelState);
            }

            var response = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);

            if (response.totalChannelAdded > response.MaximumNumberOfChannel)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"Your subscription plan only could add {response.MaximumNumberOfChannel} channels"
                    });
            }

            var domainName = _configuration.GetValue<string>("Values:DomainName");

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var viberBotClient = new ViberBotClient(httpClient, input.BotToken);

            var botAccountInfo = await viberBotClient.GetBotAccountInfoAsync();

            if (botAccountInfo.Status != ViberApiResponseStatus.Ok)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Invalid Viber bot token!"
                    });
            }

            if (await _appDbContext.ConfigViberConfigs.AnyAsync(x => x.ViberBotId == botAccountInfo.Id))
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "This Viber Bot is already existed!"
                    });
            }

            // Add or Update
            var viberConfig = await _appDbContext.ConfigViberConfigs.FirstOrDefaultAsync(
                x => x.CompanyId == companyUser.CompanyId && x.ViberBotId == botAccountInfo.Id);

            if (viberConfig == null)
            {
                viberConfig = new ViberConfig();
                _appDbContext.ConfigViberConfigs.Add(viberConfig);
            }

            viberConfig.DisplayName = input.DisplayName;

            viberConfig.ViberBotId = botAccountInfo.Id;
            viberConfig.ChannelIdentityId = viberConfig.ViberBotId;
            viberConfig.CompanyId = companyUser.CompanyId;
            viberConfig.ViberBotToken = input.BotToken;
            viberConfig.ViberBotName = botAccountInfo.Name;
            viberConfig.IconUrl = botAccountInfo.Icon;
            viberConfig.Uri = botAccountInfo.Uri;
            viberConfig.Category = botAccountInfo.Category;
            viberConfig.SubCategory = botAccountInfo.SubCategory;
            viberConfig.Country = botAccountInfo.Country;
            viberConfig.ConnectedDateTime = DateTime.UtcNow;
            viberConfig.ViberBotSenderName = !string.IsNullOrWhiteSpace(input.ViberBotSenderName)
                ? input.ViberBotSenderName
                : viberConfig.ViberBotName;

            await _appDbContext.SaveChangesAsync();

            var setupWebhookResponse = await viberBotClient.SetupWebhooksAsync(
                $"{domainName}/viber/webhook/{companyUser.CompanyId}?viberBotId={Uri.EscapeDataString(viberConfig.ViberBotId)}");

            if (setupWebhookResponse.Status != ViberApiResponseStatus.Ok)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Error during Viber Bot Webhook setup!"
                    });
            }

            await _signalRService.SignalROnChannelAdded(
                companyUser.Company,
                new ChannelSignal
                {
                    ChannelName = ChannelTypes.Viber
                });

            if (await _appDbContext.CompanySandboxes.AsNoTracking().AnyAsync(x => x.CompanyId == companyUser.CompanyId))
            {
                BackgroundJob.Enqueue<ICompanyService>(x => x.DeleteSandbox(companyUser.CompanyId));
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            return Ok(_mapper.Map<ViberConfigResponse>(viberConfig));
        }

        /// <summary>
        /// Connect a new Viber Channel.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("Company/viber/reconnect")]
        public async Task<ActionResult<ViberConfigResponse>> ReconnectViber([FromBody] ReconnectViberInput input)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var viberConfig = await _appDbContext.ConfigViberConfigs.FirstOrDefaultAsync(
                x => x.Id == input.ViberChannelId && x.CompanyId == companyUser.CompanyId);

            if (viberConfig == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Viber Channel Not Found."
                    });
            }

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var viberBotClient = new ViberBotClient(httpClient, viberConfig.ViberBotToken);

            var domainName = _configuration.GetValue<string>("Values:DomainName");
            var setupWebhookResponse = await viberBotClient.SetupWebhooksAsync(
                $"{domainName}/viber/webhook/{companyUser.CompanyId}?viberBotId={viberConfig.ViberBotId}");

            if (setupWebhookResponse.Status != ViberApiResponseStatus.Ok)
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Error during Viber Bot Webhook setup!"
                    });
            }

            return Ok(_mapper.Map<ViberConfigResponse>(viberConfig));
        }

        /// <summary>
        /// Rename a existing Viber Channel.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPut]
        [Route("Company/viber")]
        public async Task<ActionResult<EditViberOutput>> RenameViber([FromBody] EditViberInput input)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var viberConfig = await _appDbContext.ConfigViberConfigs.FirstOrDefaultAsync(
                x => x.Id == input.ViberChannelId && x.CompanyId == companyUser.CompanyId);

            if (viberConfig == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Viber Channel Not Found."
                    });
            }

            if (!string.IsNullOrWhiteSpace(input.DisplayName))
            {
                viberConfig.DisplayName = input.DisplayName;
            }

            if (!string.IsNullOrWhiteSpace(input.ViberBotSenderName))
            {
                viberConfig.ViberBotSenderName = input.ViberBotSenderName;
            }

            await _appDbContext.SaveChangesAsync();

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            return Ok(
                new EditViberOutput
                {
                    Message = "success",
                    ViberChannelId = viberConfig.Id
                });
        }

        /// <summary>
        /// Delete a Viber channel.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpDelete]
        [Route("Company/viber")]
        public async Task<ActionResult<DeleteViberOutput>> RemoveViber([FromBody] DeleteViberInput input)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var viberConfig = await _appDbContext.ConfigViberConfigs.FirstOrDefaultAsync(
                x => x.Id == input.ViberChannelId && x.CompanyId == companyUser.CompanyId);

            _appDbContext.ConfigViberConfigs.Remove(viberConfig);

            var httpClient = _httpClientFactory.CreateClient(HttpClientHandlerName.Default);
            var viberBotClient = new ViberBotClient(httpClient, viberConfig.ViberBotToken);
            await viberBotClient.RemoveWebhooksAsync();

            await _appDbContext.SaveChangesAsync();

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            return Ok(
                new DeleteViberOutput
                {
                    Message = "success",
                    ViberChannelId = viberConfig.Id
                });
        }

        #endregion

        #region Telegram

        /// <summary>
        /// Get Telegram and Configs for the Company.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpGet]
        [Route("Company/telegram")]
        public async Task<ActionResult<GetTelegramOutput>> GetTelegramConfig()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var telegramConfigs = await _appDbContext.ConfigTelegramConfigs
                .AsNoTracking()
                .Where(x => x.CompanyId == companyUser.CompanyId)
                .ProjectTo<TelegramConfigDto>(_mapper.ConfigurationProvider)
                .ToListAsync(HttpContext.RequestAborted);

            if (!telegramConfigs.Any())
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Telegram webhook not set!"
                    });
            }

            var output = new GetTelegramOutput()
            {
                TelegramConfigs = telegramConfigs
            };

            return Ok(output);
        }

        /// <summary>
        /// Connect a new Telegram Channel.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("Company/telegram/connect")]
        public async Task<ActionResult<TelegramConfigDto>> ConnectTelegram([FromBody] ConnectTelegramInput input)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null)
            {
                return BadRequest(ModelState);
            }

            var response = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);

            if (response.totalChannelAdded > response.MaximumNumberOfChannel)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = $"Your subscription plan only could add {response.MaximumNumberOfChannel} channels"
                    });
            }

            var domainName = _configuration.GetValue<string>("Values:DomainName");

            var telegramBotClient = new TelegramBotClient(input.TelegramBotToken);

            User telegramBotGetMe;

            try
            {
                telegramBotGetMe = await telegramBotClient.GetMeAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId} error when getting bot-self - {ChannelDisplayName}: {ExceptionTypeName} : {ExceptionMessage}",
                    nameof(ConnectTelegram),
                    companyUser.CompanyId,
                    input.DisplayName,
                    ex.GetType().Name,
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Invalid Telegram bot token!"
                    });
            }

            if (await _appDbContext.ConfigTelegramConfigs.AnyAsync(x => x.TelegramBotId == telegramBotGetMe.Id))
            {
                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "This Telegram Bot is already added!"
                    });
            }

            try
            {
                await telegramBotClient.SetWebhookAsync(
                    $"{domainName}/telegram/webhook/{companyUser.CompanyId}?telegramBotId={telegramBotGetMe.Id}");
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId} error when setting webhook - {ChannelDisplayName}: {ExceptionTypeName} : {ExceptionMessage}",
                    nameof(ConnectTelegram),
                    companyUser.CompanyId,
                    input.DisplayName,
                    ex.GetType().Name,
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Error during Telegram Bot Webhook setup!"
                    });
            }

            var telegramConfig = await _appDbContext.ConfigTelegramConfigs.FirstOrDefaultAsync(
                x => x.CompanyId == companyUser.CompanyId && x.TelegramBotId == telegramBotGetMe.Id);

            // if (telegramConfig.CanJoinGroups)
            // {
            //     return BadRequest(new ResponseViewModel() {message = "Error during Viber Bot Webhook setup! Please disable the telegram bot group before connect."});
            // }
            if (telegramConfig == null)
            {
                telegramConfig = new TelegramConfig();
                _appDbContext.ConfigTelegramConfigs.Add(telegramConfig);
            }

            telegramConfig.DisplayName = input.DisplayName;
            telegramConfig.CompanyId = companyUser.CompanyId;

            telegramConfig.TelegramBotId = telegramBotGetMe.Id;
            telegramConfig.ChannelIdentityId = telegramConfig.TelegramBotId.ToString();
            telegramConfig.TelegramBotToken = input.TelegramBotToken;
            telegramConfig.TelegramBotDisplayName = telegramBotGetMe.FirstName;
            telegramConfig.TelegramBotUserName = telegramBotGetMe.Username;
            telegramConfig.CanReadAllGroupMessages = telegramBotGetMe.CanReadAllGroupMessages ?? false;
            telegramConfig.CanJoinGroups = telegramBotGetMe.CanJoinGroups ?? false;
            telegramConfig.SupportsInlineQueries = telegramBotGetMe.SupportsInlineQueries ?? false;

            telegramConfig.ConnectedDateTime = DateTime.UtcNow;

            await _appDbContext.SaveChangesAsync();

            await _signalRService.SignalROnChannelAdded(
                companyUser.Company,
                new ChannelSignal
                {
                    ChannelName = ChannelTypes.Telegram
                });

            if (await _appDbContext.CompanySandboxes.AsNoTracking().AnyAsync(x => x.CompanyId == companyUser.CompanyId))
            {
                BackgroundJob.Enqueue<ICompanyService>(x => x.DeleteSandbox(companyUser.CompanyId));
            }

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            return Ok(_mapper.Map<TelegramConfigDto>(telegramConfig));
        }

        /// <summary>
        /// Connect a new Telegram Channel.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPost]
        [Route("Company/telegram/reconnect")]
        public async Task<ActionResult<TelegramConfigDto>> ReconnectTelegram([FromBody] ReconnectTelegramInput input)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var config = await _appDbContext.ConfigTelegramConfigs
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == input.TelegramChannelId && x.CompanyId == companyUser.CompanyId);

            if (config == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Telegram Channel Not Found."
                    });
            }

            var telegramBotClient = new TelegramBotClient(config.TelegramBotToken);

            var domainName = _configuration.GetValue<string>("Values:DomainName");
            try
            {
                await telegramBotClient.SetWebhookAsync(
                    $"{domainName}/telegram/webhook/{companyUser.CompanyId}?telegramBotId={config.TelegramBotId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "[{MethodName} endpoint] Company {CompanyId} error when setting webhook for channel id {TelegramChannelId}: {ExceptionTypeName} : {ExceptionMessage}",
                    nameof(ReconnectTelegram),
                    companyUser.CompanyId,
                    input.TelegramChannelId,
                    ex.GetType().Name,
                    ex.Message);

                return BadRequest(
                    new ResponseViewModel()
                    {
                        message = "Error during Telegram Bot Webhook Reconnect!"
                    });
            }

            return Ok(_mapper.Map<TelegramConfigDto>(config));
        }

        /// <summary>
        /// Rename a existing Telegram Channel.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpPut]
        [Route("Company/telegram")]
        public async Task<ActionResult<EditTelegramOutput>> RenameTelegramChannel([FromBody] EditTelegramInput input)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var config = await _appDbContext.ConfigTelegramConfigs
                .FirstOrDefaultAsync(x => x.Id == input.TelegramChannelId && x.CompanyId == companyUser.CompanyId);

            if (config == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Telegram Channel Not Found."
                    });
            }

            config.DisplayName = input.DisplayName;
            await _appDbContext.SaveChangesAsync();

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            return Ok(
                new EditTelegramOutput
                {
                    Message = "success",
                    TelegramChannelId = config.Id
                });
        }

        /// <summary>
        /// Delete a Telegram channel.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [HttpDelete]
        [Route("Company/telegram")]
        public async Task<ActionResult<DeleteTelegramOutput>> RemoveTelegramChannel(
            [FromBody]
            DeleteTelegramInput input)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var config = await _appDbContext.ConfigTelegramConfigs
                .FirstOrDefaultAsync(x => x.Id == input.TelegramChannelId && x.CompanyId == companyUser.CompanyId);

            if (config == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Telegram Channel Not Found."
                    });
            }

            _appDbContext.ConfigTelegramConfigs.Remove(config);

            await _appDbContext.SaveChangesAsync();

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            return Ok(
                new DeleteTelegramOutput
                {
                    Message = "success",
                    TelegramChannelId = config.Id
                });
        }

        #endregion


        #region TikTok

        [HttpGet]
        [Route("Company/tiktok")]
        public async Task<ActionResult<GetTikTokConnectionsOutputOutput>> GetTikTokConnections()
        {
            var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (staff == null)
            {
                return Unauthorized();
            }

            return await _channelsApi.ChannelsGetTikTokConnectionsPostAsync(
                getTikTokConnectionsInput: new GetTikTokConnectionsInput(
                    staff.CompanyId
                )
            );
        }

        [HttpPost]
        [Route("Company/tiktok/Connect")]
        public async Task<ActionResult<CreateTikTokConnectionOutputOutput>> CreateTikTokConnection(
            [FromBody]
            CreateTikTokConnectionInput input)
        {
            var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (staff == null)
            {
                return Unauthorized();
            }

            var result = await _channelsApi.ChannelsCreateTikTokConnectionPostAsync(
                createTikTokConnectionInput: new Sleekflow.Apis.MessagingHub.Model.CreateTikTokConnectionInput(
                    staff.CompanyId,
                    input.Code,
                    _configuration.GetValue<string>("Values:DomainName") + "/MessagingHub/Internals",
                    input.RedirectUri)
            );

            await _companyInfoCacheService.RemoveCompanyInfoCache(staff.CompanyId);

            return result;
        }

        [HttpPatch]
        [Route("Company/tiktok")]
        public async Task<ActionResult<UpdateTikTokConnectionOutputOutput>> UpdateTikTokConnection(
            [FromBody]
            UpdateTikTokConnectionInput input)
        {
            var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (staff == null)
            {
                return Unauthorized();
            }

            var result = await _channelsApi.ChannelsUpdateTikTokConnectionPostAsync(
                updateTikTokConnectionInput: new Sleekflow.Apis.MessagingHub.Model.UpdateTikTokConnectionInput(
                    staff.CompanyId,
                    input.Id,
                    input.DisplayName)
            );

            await _companyInfoCacheService.RemoveCompanyInfoCache(staff.CompanyId);

            return result;
        }

        [HttpDelete]
        [Route("Company/tiktok")]
        public async Task<ActionResult<DeleteTikTokConnectionOutputOutput>> DeleteTikTokConnection(
            [FromBody]
            DeleteTikTokConnectionInput input)
        {
            var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (staff == null)
            {
                return Unauthorized();
            }

            var result = await _channelsApi.ChannelsDeleteTikTokConnectionPostAsync(
                deleteTikTokConnectionInput: new Sleekflow.Apis.MessagingHub.Model.DeleteTikTokConnectionInput(
                    staff.CompanyId,
                    input.Id
                    )
            );

            await _companyInfoCacheService.RemoveCompanyInfoCache(staff.CompanyId);

            return result;
        }

        /// <summary>
        /// Get all message links for a TikTok connection
        /// GET /Company/tiktok/{tiktokConfigId}/messageLinks
        /// </summary>
        [HttpGet("Company/tiktok/{tiktokConfigId}/messageLinks")]
        public async Task<ActionResult<GetTikTokLinksOutputOutput>> GetMessageLinks(
            string tiktokConfigId)
        {
            var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (staff == null)
            {
                return Unauthorized();
            }

            return await _channelsApi.ChannelsGetTikTokLinksPostAsync(
                getTikTokLinksInput: new GetTikTokLinksInput(
                    staff.CompanyId,
                    tiktokConfigId
                )
            );
        }

        /// <summary>
        /// Get a specific message link for a TikTok connection
        /// GET /Company/tiktok/{tiktokConfigId}/messageLinks/{id}
        /// </summary>
        [HttpGet("Company/tiktok/{tiktokConfigId}/messageLinks/{id}")]
        public async Task<ActionResult<GetTikTokLinkOutputOutput>> GetMessageLink(
            string tiktokConfigId,
            string id)
        {
            var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (staff == null)
            {
                return Unauthorized();
            }

            return await _channelsApi.ChannelsGetTikTokLinkPostAsync(
                getTikTokLinkInput: new GetTikTokLinkInput(
                    staff.CompanyId,
                    tiktokConfigId,
                    id
                )
            );
        }

        /// <summary>
        /// Create a new message link for a TikTok connection
        /// POST /Company/tiktok/{tiktokConfigId}/messageLinks
        /// </summary>
        [HttpPost("Company/tiktok/{tiktokConfigId}/messageLinks")]
        public async Task<ActionResult<CreateTikTokLinkOutputOutput>> CreateMessageLink(
            string tiktokConfigId,
            [FromBody] CreateTikTokLinkRequest request)
        {
            var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (staff == null)
            {
                return Unauthorized();
            }

            var createdBy = new SleekflowStaff(staff.Id.ToString(), new List<string>());


            return await _channelsApi.ChannelsCreateTikTokLinkPostAsync(
                createTikTokLinkInput: new CreateTikTokLinkInput(
                    staff.CompanyId,
                    tiktokConfigId,
                    request.Title ?? string.Empty,
                    request.Ref ?? string.Empty,
                    request.Message,
                    createdBy
                )
            );
        }

        /// <summary>
        /// Update an existing message link
        /// PUT /Company/tiktok/{tiktokConfigId}/messageLinks/{id}
        /// </summary>
        [HttpPut("Company/tiktok/{tiktokConfigId}/messageLinks/{id}")]
        public async Task<ActionResult<UpdateTikTokLinkOutputOutput>> UpdateMessageLink(
            string tiktokConfigId,
            string id,
            [FromBody]
            UpdateTikTokLinkRequest request)
        {
            var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (staff == null)
            {
                return Unauthorized();
            }

            var updatedBy = new SleekflowStaff(staff.Id.ToString(), new List<string>());

            return await _channelsApi.ChannelsUpdateTikTokLinkPostAsync(
                updateTikTokLinkInput: new UpdateTikTokLinkInput(
                    staff.CompanyId,
                    tiktokConfigId,
                    id,
                    request.Title,
                    request.Ref,
                    request.Message,
                    updatedBy
                )
            );
        }

        /// <summary>
        /// Delete a message link
        /// DELETE /Company/tiktok/{tiktokConfigId}/messageLinks/{id}
        /// </summary>
        [HttpDelete("Company/tiktok/{tiktokConfigId}/messageLinks/{id}")]
        public async Task<ActionResult<DeleteTikTokLinkOutputOutput>> DeleteMessageLink(
            string tiktokConfigId,
            string id)
        {
            var staff = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (staff == null)
            {
                return Unauthorized();
            }

            return await _channelsApi.ChannelsDeleteTikTokLinkPostAsync(
                deleteTikTokLinkInput: new DeleteTikTokLinkInput(
                    staff.CompanyId,
                    tiktokConfigId,
                    id
                )
            );
        }

        #endregion
        [HttpPost]
        [Route("Company/twilio/whatsapp")]
        public async Task<IActionResult> ConnectTwilioWhatsapp([FromBody] ConnectTwilioViewModel connectTwilioViewModel)
        {
            if (ModelState.IsValid)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
                if (companyUser == null)
                {
                    return Unauthorized();
                }

                // companyUser.Company.WhatsAppConfigs = await _appDbContext.ConfigWhatsAppConfigs.Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync();
                var companyUsage = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);

                if (companyUsage.totalChannelAdded > companyUsage.MaximumNumberOfChannel)
                {
                    _logger.LogWarning(
                        "Company {CompanyId}, user {UserIdentityId} connect WhatsApp Twilio failure due to exceeding maximum number of channels: {MaximumChannelNumber}",
                        companyUser.CompanyId,
                        companyUser.IdentityId,
                        companyUsage.MaximumNumberOfChannel);

                    return BadRequest(
                        new ResponseViewModel
                        {
                            message =
                                $"Your subscription plan only could add {companyUsage.MaximumNumberOfChannel} channels"
                        });
                }

                // if (companyUser.Company.WhatsAppConfigs == null)
                //    companyUser.Company.WhatsAppConfigs = new List<WhatsAppConfig>();
                var existing = await _appDbContext.ConfigWhatsAppConfigs.FirstOrDefaultAsync(
                    x => x.CompanyId == companyUser.CompanyId &&
                         x.TwilioAccountId == connectTwilioViewModel.AccountSID &&
                         x.WhatsAppSender == connectTwilioViewModel.PhoneNumber);
                if (existing == null)
                {
                    existing = new WhatsAppConfig()
                    {
                        CompanyId = companyUser.CompanyId
                    };
                    _appDbContext.ConfigWhatsAppConfigs.Add(existing);
                }

                existing.CompanyId = companyUser.CompanyId;
                existing.Name = connectTwilioViewModel.Name;
                existing.TwilioAccountId = connectTwilioViewModel.AccountSID;
                existing.TwilioSecret = connectTwilioViewModel.AccountSecret;
                existing.WhatsAppSender = connectTwilioViewModel.PhoneNumber;
                existing.ChannelIdentityId = PhoneNumberHelper.NormalizePhoneNumber(existing.WhatsAppSender);
                existing.MessagingServiceSid = connectTwilioViewModel.MessagingServiceSid;

                TwilioClient.Init(existing.TwilioAccountId, existing.TwilioSecret);

                try
                {
                    var account = AccountResource.Fetch(pathSid: existing.TwilioAccountId);

                    if (account.Status == AccountResource.StatusEnum.Active)
                    {
                        await _appDbContext.SaveChangesAsync();

                        if (await _appDbContext.CompanySandboxes
                                .AnyAsync(x => x.CompanyId == companyUser.CompanyId))
                        {
                            BackgroundJob.Enqueue<ICompanyService>(x => x.DeleteSandbox(companyUser.CompanyId));
                        }

                        // var callbackUrl = new Uri("http://www.example.com/");
                        // const string triggerValue = "1000";
                        // var trigger = TriggerResource.Create(callbackUrl,
                        //                                     triggerValue,
                        //                                     TriggerResource.UsageCategoryEnum.Totalprice,
                        //                                     );
                        try
                        {
                            await _twilioService.AddNewTemplate(
                                existing.TwilioAccountId,
                                existing.TwilioSecret,
                                new CreateTemplateViewModel
                                {
                                    Name = "optin_1",
                                    Category = "ACCOUNT_UPDATE",
                                    Languages = new List<LanguageElement>
                                    {
                                        new LanguageElement
                                        {
                                            Language = "en",
                                            Content =
                                                $"*Notification*\nHello there! You received a new message from {companyUser.Company.CompanyName}.",
                                            Components = new List<Component>
                                            {
                                                new Component
                                                {
                                                    Type = "BUTTONS",
                                                    Buttons = new List<Button>
                                                    {
                                                        new Button
                                                        {
                                                            Text = "Read more", Type = "QUICK_REPLY"
                                                        }
                                                    }
                                                }
                                            }
                                        },
                                        new LanguageElement
                                        {
                                            Language = "zh_HK",
                                            Content = $"*新訊息通知*\n你好! 你收到來自{companyUser.Company.CompanyName}的新訊息。",
                                            Components = new List<Component>
                                            {
                                                new Component
                                                {
                                                    Type = "BUTTONS",
                                                    Buttons = new List<Button>
                                                    {
                                                        new Button
                                                        {
                                                            Text = "查看更多", Type = "QUICK_REPLY"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                });
                            await _twilioService.AddNewTemplate(
                                existing.TwilioAccountId,
                                existing.TwilioSecret,
                                new CreateTemplateViewModel
                                {
                                    Name = "optin_2",
                                    Category = "ACCOUNT_UPDATE",
                                    Languages = new List<LanguageElement>
                                    {
                                        new LanguageElement
                                        {
                                            Language = "en",
                                            Content =
                                                $"Hello there! You received an update from {companyUser.Company.CompanyName}.",
                                            Components = new List<Component>
                                            {
                                                new Component
                                                {
                                                    Type = "BUTTONS",
                                                    Buttons = new List<Button>
                                                    {
                                                        new Button
                                                        {
                                                            Text = "Read more", Type = "QUICK_REPLY"
                                                        }
                                                    }
                                                }
                                            }
                                        },
                                        new LanguageElement
                                        {
                                            Language = "zh_HK",
                                            Content = $"你好! 以下為一則來自{companyUser.Company.CompanyName}的新訊息。",
                                            Components = new List<Component>
                                            {
                                                new Component
                                                {
                                                    Type = "BUTTONS",
                                                    Buttons = new List<Button>
                                                    {
                                                        new Button
                                                        {
                                                            Text = "查看更多", Type = "QUICK_REPLY"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                });
                            await _twilioService.AddNewTemplate(
                                existing.TwilioAccountId,
                                existing.TwilioSecret,
                                new CreateTemplateViewModel
                                {
                                    Name = "optin_3",
                                    Category = "ACCOUNT_UPDATE",
                                    Languages = new List<LanguageElement>
                                    {
                                        new LanguageElement
                                        {
                                            Language = "en",
                                            Content =
                                                $"*Notification*\nHello there! You received a new message from {existing.Name}.",
                                            Components = new List<Component>
                                            {
                                                new Component
                                                {
                                                    Type = "BUTTONS",
                                                    Buttons = new List<Button>
                                                    {
                                                        new Button
                                                        {
                                                            Text = "Read more", Type = "QUICK_REPLY"
                                                        }
                                                    }
                                                }
                                            }
                                        },
                                        new LanguageElement
                                        {
                                            Language = "zh_HK",
                                            Content = $"*新訊息通知*\n你好! 你收到來自{existing.Name}的新訊息。",
                                            Components = new List<Component>
                                            {
                                                new Component
                                                {
                                                    Type = "BUTTONS",
                                                    Buttons = new List<Button>
                                                    {
                                                        new Button
                                                        {
                                                            Text = "查看更多", Type = "QUICK_REPLY"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                });
                            await _twilioService.AddNewTemplate(
                                existing.TwilioAccountId,
                                existing.TwilioSecret,
                                new CreateTemplateViewModel
                                {
                                    Name = "optin_4",
                                    Category = "ACCOUNT_UPDATE",
                                    Languages = new List<LanguageElement>
                                    {
                                        new LanguageElement
                                        {
                                            Language = "en",
                                            Content = $"Hello there! You received an update from {existing.Name}.",
                                            Components = new List<Component>
                                            {
                                                new Component
                                                {
                                                    Type = "BUTTONS",
                                                    Buttons = new List<Button>
                                                    {
                                                        new Button
                                                        {
                                                            Text = "Read more", Type = "QUICK_REPLY"
                                                        }
                                                    }
                                                }
                                            }
                                        },
                                        new LanguageElement
                                        {
                                            Language = "zh_HK",
                                            Content = $"你好! 以下為一則來自{existing.Name}的新訊息。",
                                            Components = new List<Component>
                                            {
                                                new Component
                                                {
                                                    Type = "BUTTONS",
                                                    Buttons = new List<Button>
                                                    {
                                                        new Button
                                                        {
                                                            Text = "查看更多", Type = "QUICK_REPLY"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                });
                            await _twilioService.AddNewTemplate(
                                existing.TwilioAccountId,
                                existing.TwilioSecret,
                                new CreateTemplateViewModel
                                {
                                    Name = "greetings_1",
                                    Category = "ACCOUNT_UPDATE",
                                    Languages = new List<LanguageElement>
                                    {
                                        new LanguageElement
                                        {
                                            Language = "en", Content = "Hello {{1}}"
                                        },
                                        new LanguageElement
                                        {
                                            Language = "zh_HK", Content = "你好 {{1}}"
                                        }
                                    }
                                });
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "[{MethodName} endpoint] Company {CompanyId} add default Twilio template error: {ExceptionMessage}",
                                nameof(ConnectTwilioWhatsapp),
                                companyUser.CompanyId,
                                ex.Message);
                        }

                        var response = _mapper.Map<WhatsAppConfigViewModel>(existing);
                        await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);
                        return Ok(response);
                    }

                    return BadRequest(account);
                }
                catch (Exception)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = $"Failed to authenticate"
                        });
                }
            }

            return BadRequest(ModelState);
        }

        [HttpPost]
        [Route("Company/twilio/whatsapp/rename/{SID}")]
        [Route("Company/twilio/whatsapp/update")]
        public async Task<IActionResult> RenameTwilioWhatsapp(
            [FromQuery(Name = "SID")]
            string SID,
            [FromBody]
            ChannelRenameViewModel channelRenameViewModel)
        {
            if (ModelState.IsValid)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
                if (companyUser == null)
                {
                    return Unauthorized();
                }

                // companyUser.Company.WhatsAppConfigs = await _appDbContext.ConfigWhatsAppConfigs.Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync();
                var twilioInstance = SID.Split(";", StringSplitOptions.RemoveEmptyEntries);
                var sid = twilioInstance[0];

                var existing = await _appDbContext.ConfigWhatsAppConfigs.FirstOrDefaultAsync(
                    x => x.CompanyId == companyUser.CompanyId && x.TwilioAccountId == sid &&
                         x.WhatsAppSender == twilioInstance[1].Replace(": ", ":+"));
                if (existing == null)
                {
                    return NotFound(
                        new ResponseViewModel
                        {
                            message = "not found"
                        });
                }

                if (channelRenameViewModel.Name != null)
                {
                    existing.Name = channelRenameViewModel.Name;
                }

                if (channelRenameViewModel.IsShowInWidget.HasValue)
                {
                    existing.IsShowInWidget = channelRenameViewModel.IsShowInWidget.Value;
                }

                if (channelRenameViewModel.ReadMoreTemplateMessage != null)
                {
                    existing.ReadMoreTemplateMessage = channelRenameViewModel.ReadMoreTemplateMessage;
                }

                if (channelRenameViewModel.MessagingServiceSid != null)
                {
                    existing.MessagingServiceSid = channelRenameViewModel.MessagingServiceSid;
                }

                if (channelRenameViewModel.ReadMoreMessageContentSid != null)
                {
                    existing.ReadMoreMessageContentSid = channelRenameViewModel.ReadMoreMessageContentSid;
                }

                await _appDbContext.SaveChangesAsync();

                var response = _mapper.Map<WhatsAppConfigViewModel>(existing);

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);
                return Ok(response);
            }

            return BadRequest(ModelState);
        }

        [HttpDelete]
        [Route("Company/twilio/whatsapp/{SID}")]
        [Route("Company/twilio/whatsapp")]
        public async Task<IActionResult> DeleteTwilioWhatsapp([FromQuery(Name = "SID")] string SID)
        {
            if (ModelState.IsValid)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var twilioInstance = SID.Split(";", StringSplitOptions.RemoveEmptyEntries);
                var sid = twilioInstance[0];

                var existing = await _appDbContext.ConfigWhatsAppConfigs.FirstOrDefaultAsync(
                    x => x.CompanyId == companyUser.CompanyId && x.TwilioAccountId == sid &&
                         x.WhatsAppSender == twilioInstance[1].Replace(": ", ":+"));
                if (existing == null)
                {
                    return NotFound(
                        new ResponseViewModel
                        {
                            message = "not found"
                        });
                }

                _appDbContext.ConfigWhatsAppConfigs.RemoveRange(existing);
                await _appDbContext.SaveChangesAsync();

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                return Ok(
                    new ResponseViewModel
                    {
                        message = "success"
                    });
            }

            return BadRequest(ModelState);
        }

        /// <summary>
        /// Get Company Setting.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("Company/setting")]
        public async Task<ActionResult<CompanySetting>> GetCompanySetting()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == companyUser.CompanyId);
            company.CompanySetting ??= new CompanySetting();

            return Ok(company.CompanySetting);
        }

        /// <summary>
        /// Update Company Setting.
        /// </summary>
        /// <param name="companySetting"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("Company/setting")]
        public async Task<ActionResult<CompanySetting>> UpdateCompanySetting([FromBody] CompanySetting companySetting)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == companyUser.CompanyId);
            company.CompanySetting = companySetting;
            await _appDbContext.SaveChangesAsync();

            return Ok(company.CompanySetting);
        }

        [HttpGet]
        [Route("Company/twilio/optin")]
        public async Task<ActionResult<OptinObject>> GetOptInSetting()
        {
            // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).Include(x => x.Company).FirstOrDefaultAsync();
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            var company = await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == companyUser.CompanyId);
            if (company.CompanySetting == null)
            {
                company.CompanySetting = new CompanySetting();
            }

            var twilioAccounts = await _appDbContext.ConfigWhatsAppConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId).FirstOrDefaultAsync();

            var response = new OptinObject
            {
                IsOptInOn = company.CompanySetting.IsOptInOn,
                ReadMoreTemplateId = twilioAccounts?.ReadMoreTemplateId,
                ReadMoreTemplateMessage = twilioAccounts?.ReadMoreTemplateMessage,
                ReadMoreMessageContentSid = twilioAccounts?.ReadMoreMessageContentSid
            };

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            return Ok(response);
        }

        [HttpPost]
        [Route("Company/twilio/optin")]
        public async Task<ActionResult<OptinObject>> SaveOptInSetting([FromBody] OptinObject optinObject)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var company = await _appDbContext.CompanyCompanies.Where(x => x.Id == companyUser.CompanyId)
                .FirstOrDefaultAsync();
            if (company.CompanySetting == null)
            {
                company.CompanySetting = new CompanySetting();
            }

            var companySetting = _mapper.Map<CompanySetting>(company.CompanySetting);
            company.CompanySetting = null;
            await _appDbContext.SaveChangesAsync();

            companySetting.IsOptInOn = optinObject.IsOptInOn;
            company.CompanySetting = companySetting;
            await _appDbContext.SaveChangesAsync();

            var twilioAccounts = _appDbContext.ConfigWhatsAppConfigs.Where(x => x.CompanyId == companyUser.CompanyId);
            await twilioAccounts.ForEachAsync(
                x =>
                {
                    x.ReadMoreTemplateId = optinObject.ReadMoreTemplateId;
                    x.ReadMoreTemplateMessage = optinObject.ReadMoreTemplateMessage;
                    x.ReadMoreMessageContentSid = optinObject.ReadMoreMessageContentSid;
                });
            await _appDbContext.SaveChangesAsync();

            await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

            return Ok(optinObject);
        }

        [HttpGet]
        [Route("Company/twilio/usage")]
        public async Task<IActionResult> GetTwilioUsage()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            companyUser.Company.WhatsAppConfigs = await _appDbContext.ConfigWhatsAppConfigs
                .Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync(HttpContext.RequestAborted);

            var twilioAccountIds = companyUser.Company.WhatsAppConfigs.Where(x => x.CompanyId == companyUser.CompanyId)
                .GroupBy(x => x.TwilioAccountId);

            var twilioUsages = new List<TwilioUsageRecord>();
            foreach (var twilioAccountId in twilioAccountIds)
            {
                twilioUsages.Add(await _companyUsageService.GetTwilioUsage(companyUser.CompanyId, twilioAccountId.Key));
            }

            return Ok(twilioUsages);
        }

        [HttpGet]
        [Route("Company/twilio/sms/webhookURL")]
        public IActionResult GetTwilioWebhookURL()
        {
            var domainName = _configuration.GetValue<string>("Values:DomainName");

            dynamic jObject = new JObject();
            jObject.Url = $"{domainName}/sms/twilio/webhook";

            return Ok(jObject);
        }

        [HttpPost]
        [Route("Company/twilio/sms")]
        public async Task<IActionResult> ConnectTwilioSMS([FromBody] ConnectTwilioViewModel connectTwilioViewModel)
        {
            if (ModelState.IsValid)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
                if (companyUser == null)
                {
                    return Unauthorized();
                }

                // companyUser.Company.SMSConfigs = await _appDbContext.ConfigSMSConfigs.Where(x => x.CompanyId == companyUser.CompanyId).ToListAsync();
                var existing = await _appDbContext.ConfigSMSConfigs.FirstOrDefaultAsync(
                    x => x.CompanyId == companyUser.CompanyId &&
                         x.TwilioAccountId == connectTwilioViewModel.AccountSID);
                if (existing == null)
                {
                    existing = new SMSConfig()
                    {
                        CompanyId = companyUser.CompanyId
                    };
                    _appDbContext.ConfigSMSConfigs.Add(existing);
                }

                existing.Name = connectTwilioViewModel.Name;
                existing.TwilioAccountId = connectTwilioViewModel.AccountSID;
                existing.TwilioSecret = connectTwilioViewModel.AccountSecret;
                // Bug fix DEVS-9193 Moi Moi - A new contact will be generated when user reply via SMS channel
                // Normalize phone number to ensure all the phone numbers are in the same format
                existing.SMSSender = PhoneNumberHelper.ToE164Format(connectTwilioViewModel.PhoneNumber);
                existing.ChannelIdentityId = connectTwilioViewModel.AccountSID;

                TwilioClient.Init(existing.TwilioAccountId, existing.TwilioSecret);

                try
                {
                    var account = AccountResource.Fetch(pathSid: existing.TwilioAccountId);

                    if (account.Status == AccountResource.StatusEnum.Active)
                    {
                        await _appDbContext.SaveChangesAsync();

                        var response = _mapper.Map<SMSViewConfigViewModel>(existing);
                        await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                        return Ok(response);
                    }

                    return BadRequest(account);
                }
                catch (Exception)
                {
                    return BadRequest(
                        new ResponseViewModel
                        {
                            message = $"Failed to authenticate"
                        });
                }
            }

            return BadRequest(ModelState);
        }

        [HttpPost]
        [Route("Company/twilio/sms/rename/{SID}")]
        [Route("Company/twilio/sms/update/{SID}")]
        public async Task<IActionResult> RenameTwilioSMS(
            string SID,
            [FromBody]
            ChannelRenameViewModel channelRenameViewModel)
        {
            if (ModelState.IsValid)
            {
                // var companyUser = await _appDbContext.UserRoleStaffs.Where(x => x.IdentityId == userId).FirstOrDefaultAsync();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var existing = await _appDbContext.ConfigSMSConfigs.FirstOrDefaultAsync(
                    x => x.CompanyId == companyUser.CompanyId && x.TwilioAccountId == SID);
                if (existing == null)
                {
                    return NotFound(
                        new ResponseViewModel
                        {
                            message = "not found"
                        });
                }

                if (!string.IsNullOrEmpty(channelRenameViewModel.Name))
                {
                    existing.Name = channelRenameViewModel.Name;
                }

                if (channelRenameViewModel.IsShowInWidget.HasValue)
                {
                    existing.IsShowInWidget = channelRenameViewModel.IsShowInWidget.Value;
                }

                await _appDbContext.SaveChangesAsync();

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                var response = _mapper.Map<SMSViewConfigViewModel>(existing);
                return Ok(response);
            }

            return BadRequest(ModelState);
        }

        [HttpDelete]
        [Route("Company/twilio/sms/{SID}")]
        public async Task<IActionResult> DeleteTwilioSMS(string SID)
        {
            if (ModelState.IsValid)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var existing = await _appDbContext.ConfigSMSConfigs.FirstOrDefaultAsync(
                    x => x.CompanyId == companyUser.CompanyId && x.TwilioAccountId == SID);
                if (existing == null)
                {
                    return NotFound(
                        new ResponseViewModel
                        {
                            message = "not found"
                        });
                }

                _appDbContext.ConfigSMSConfigs.Remove(existing);
                await _appDbContext.SaveChangesAsync();

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);

                return Ok(
                    new ResponseViewModel
                    {
                        message = "success"
                    });
            }

            return BadRequest(ModelState);
        }

        [HttpGet]
        [Route("Company/Request/Channel")]
        public async Task<IActionResult> GetRequestChannel()
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser != null)
                {
                    var request = await _appDbContext.CompanyRequestChannels
                        .Where(x => x.CompanyId == companyUser.CompanyId).OrderByDescending(x => x.CreatedAt)
                        .ToListAsync(HttpContext.RequestAborted);
                    return Ok(request);
                }

                return Ok(
                    new ResponseViewModel
                    {
                        message = "success"
                    });
            }

            return BadRequest();
        }

        [HttpPost]
        [Route("Company/icon")]
        public async Task<IActionResult> AddCompanyIcon([FromForm] ProfilePictureViewModel attachmentViewModel)
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                // var senderId = _userManager.GetUserId(User);
                // var companyUser = _appDbContext.UserRoleStaffs.Include(x => x.Company.StorageConfig).Include(x => x.Company.CompanyIconFile).Where(x => x.IdentityId == senderId).FirstOrDefault();
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var company = await _appDbContext.CompanyCompanies.Include(x => x.StorageConfig)
                    .Include(x => x.CompanyIconFile).FirstOrDefaultAsync(x => x.Id == companyUser.CompanyId);

                if (string.IsNullOrEmpty(companyUser.CompanyId))
                {
                    return BadRequest();
                }

                foreach (IFormFile file in attachmentViewModel.files)
                {
                    var fileName = $"Icon/{DateTime.UtcNow.ToString("o")}/{file.FileName}";
                    var uploadFileResult = await _uploadService.UploadImage(
                        company.StorageConfig.ContainerName,
                        fileName,
                        file);

                    if (uploadFileResult?.Url == null)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = "upload failure"
                            });
                    }

                    var newuUploadedFile = new CompanyIconFile();
                    newuUploadedFile.Filename = fileName;
                    newuUploadedFile.CompanyId = companyUser.CompanyId;
                    newuUploadedFile.BlobContainer = company.StorageConfig.ContainerName;
                    newuUploadedFile.Url = uploadFileResult.Url;
                    newuUploadedFile.MIMEType = file.ContentType;
                    company.CompanyIconFile = newuUploadedFile;

                    break;
                }

                await _appDbContext.SaveChangesAsync();

                var companyResponse = _mapper.Map<CompanyResponse>(companyUser.Company);
                return Ok(companyResponse);
            }

            return BadRequest();
        }

        [HttpDelete]
        [Route("Company/icon")]
        public async Task<IActionResult> DeleteCompanyIcon()
        {
            if (ModelState.IsValid && User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var company = await _appDbContext.CompanyCompanies.Include(x => x.StorageConfig)
                    .Include(x => x.CompanyIconFile).FirstOrDefaultAsync(x => x.Id == companyUser.CompanyId);

                if (string.IsNullOrEmpty(companyUser.CompanyId))
                {
                    return BadRequest();
                }

                try
                {
                    var result = await _azureBlobStorageService.DeleteFromAzureBlob(
                        company.CompanyIconFile.Filename,
                        company.CompanyIconFile.BlobContainer);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Company {CompanyId} error removing company icon from blob storage: {ExceptionMessage}",
                        companyUser.CompanyId,
                        ex.Message);
                }

                _appDbContext.CompanyIconFiles.Remove(company.CompanyIconFile);

                await _appDbContext.SaveChangesAsync();
                company.CompanyIconFile = null;

                var companyResponse = _mapper.Map<CompanyResponse>(companyUser.Company);
                return Ok(companyResponse);
            }

            return BadRequest();
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("company/Icon/{filenameId}")]
        public async Task<IActionResult> GetIcon(string filenameId)
        {
            var file = await _appDbContext.CompanyIconFiles.FirstOrDefaultAsync(x => x.ProfilePictureId == filenameId);
            var stream = await _azureBlobStorageService.DownloadFromAzureBlob(file.Filename, file.BlobContainer);
            return File(
                stream.ToArray(),
                string.IsNullOrEmpty(file.MIMEType) ? "application/octet-stream" : file.MIMEType,
                file.Filename);
        }

        static string CreateRandomString(int length = 22)
        {
            var rnd = new Random(Guid.NewGuid().GetHashCode());
            var chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

            return new string(
                Enumerable.Repeat(chars, length)
                    .Select(s => s[rnd.Next(s.Length)]).ToArray());
        }

        [HttpPost]
        [Obsolete("Please use Tenant hub to invite user")]
        [Route("company/v2/invite")]
        [ProducesResponseType(typeof(List<StaffWithoutCompanyResponse>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(BadRequestResult), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> InviteV2Teammate(
            [FromBody] InviteV2TeammateViewModel inviteV2TeammateViewModel)
        {
            if (ModelState.IsValid)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser is null)
                {
                    return BadRequest(
                        new ResponseViewModel()
                        {
                            message = "User not complete company registration"
                        });
                }

                var addedUsers = new List<StaffWithoutCompanyResponse>();
                foreach (var inviteUser in inviteV2TeammateViewModel.InviteUsers)
                {
                    var user = await _userManager.FindByEmailAsync(inviteUser.Email);
                    if (user is null)
                    {
                        var identityResult = await _userManager.CreateAsync(
                            new ApplicationUser
                            {
                                UserName = inviteUser.Email,
                                Email = inviteUser.Email,
                                FirstName = inviteUser.Firstname,
                                LastName = inviteUser.Lastname,
                                DisplayName = $"{inviteUser.Firstname} {inviteUser.Lastname}",
                                EmailConfirmed = true,
                            });

                        if (!identityResult.Succeeded)
                        {
                            _logger.LogError(
                                "[CompanyInviteV2] Company {CompanyId} create user {UserEmail} error: {Errors}",
                                companyUser.CompanyId,
                                inviteUser.Email,
                                JsonConvert.SerializeObject(identityResult.Errors));

                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = identityResult.Errors.First().Description,
                                });
                        }

                        user = await _userManager.FindByEmailAsync(inviteUser.Email);
                    }

                    var userIsRegisteredCompany =
                        await _appDbContext.UserRoleStaffs.AnyAsync(u => u.IdentityId == user.Id);
                    if (userIsRegisteredCompany)
                    {
                        return BadRequest(
                            new ResponseViewModel
                            {
                                message = $"User ${user.Email} have registered a company",
                            });
                    }

                    // If user is no company associated, set it to invite. user for later checking
                    if (!user.UserName.StartsWith("invite."))
                    {
                        var changeUserNameResult = await _userManager.SetUserNameAsync(
                            user,
                            $"invite.{CreateRandomString()}");

                        if (!changeUserNameResult.Succeeded)
                        {
                            _logger.LogError(
                                "[CompanyInviteV2] Company {CompanyId} set username failure for email {UserEmail} error: {Errors}",
                                companyUser.CompanyId,
                                user.Email,
                                JsonConvert.SerializeObject(changeUserNameResult.Errors));

                            return BadRequest(
                                new ResponseViewModel
                                {
                                    message = changeUserNameResult.Errors.First().Description,
                                });
                        }
                    }

                    var staff = new Staff
                    {
                        CompanyId = companyUser.CompanyId,
                        IdentityId = user.Id,
                        Identity = user,
                        Locale = "en",
                        RoleType = inviteUser.UserRole,
                        Position = inviteUser.Position,
                        TimeZoneInfoId = string.IsNullOrEmpty(inviteUser.TimeZoneInfoId)
                            ? companyUser.Company.TimeZoneInfoId
                            : inviteUser.TimeZoneInfoId,
                        NotificationSettingId = 1
                    };
                    await _appDbContext.UserRoleStaffs.AddAsync(staff);
                    await _appDbContext.SaveChangesAsync();

                    await _companyTeamService.AddOrRemoveTeam(
                        companyUser.CompanyId,
                        staff.Id,
                        inviteUser.TeamIds);

                    addedUsers.Add(_mapper.Map<StaffWithoutCompanyResponse>(staff));

                    var token = await _userManager.GenerateUserTokenAsync(
                        user,
                        SleekflowTokenProviderOptions.InviteTokenProviderName,
                        "InviteUserByEmail");
                    await _emailNotificationService.SendInvitationEmail(user, token, companyUser);
                    var staffId = await _appDbContext.UserRoleStaffs
                        .Where(x => x.CompanyId == companyUser.CompanyId).OrderBy(x => x.Order)
                        .ThenBy(x => x.Id).Select(x => x.IdentityId).FirstOrDefaultAsync();
                    _logger.LogInformation(
                        "User {UserEmail} has been invited to company {CompanyUserCompanyId} by {UserId}, token: {Token}",
                        user.Email,
                        companyUser.CompanyId,
                        user.Id,
                        token);
                }

                await _companyInfoCacheService.RemoveCompanyInfoCache(companyUser.CompanyId);
                return Ok(addedUsers);
            }

            return BadRequest(ModelState);
        }

        [HttpPost]
        [Obsolete("Please use Tenant hub to invite user")]
        [Route("company/resendInvitation/{staffId}")]
        public async Task<IActionResult> ResendInvitation(string staffId)
        {
            if (ModelState.IsValid)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser == null || companyUser?.RoleType == StaffUserRole.Staff)
                {
                    return Unauthorized();
                }

                var user = await _userManager.FindByIdAsync(staffId);

                var token = await _userManager.GenerateUserTokenAsync(
                    user,
                    SleekflowTokenProviderOptions.InviteTokenProviderName,
                    "InvitationResend");
                await _emailNotificationService.SendInvitationEmail(user, token, companyUser);

                return Ok(
                    new ResponseViewModel
                    {
                        message = "success"
                    });
            }

            return BadRequest(ModelState);
        }

#if OBSOLETE_AUTH
        [HttpPost]
        [Obsolete("Not trigger in v1 frontend")]
        [Route("company/invite")]
        public async Task<IActionResult> InviteTeammate([FromBody] InviteTeammateViewModel inviteTeammateViewModel)
        {
            if (ModelState.IsValid)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

                if (companyUser != null)
                {
                    foreach (var email in inviteTeammateViewModel.InviteEmails)
                    {
                        var identityResult = await _userManager.CreateAsync(new ApplicationUser { UserName =
 email, Email = email });
                        if (identityResult.Succeeded)
                        {
                            var user = await _userManager.FindByEmailAsync(email);
                            await _appDbContext.UserRoleStaffs.AddAsync(new Staff { CompanyId =
 companyUser.CompanyId, IdentityId = user.Id, Locale = "en", RoleType = StaffUserRole.Admin });
                            await _appDbContext.SaveChangesAsync();

                            var code = await _userManager.GeneratePasswordResetTokenAsync(user);
                            await _emailNotificationService.SendInvitationEmail(user, code, companyUser);
                            await _cacheService.RemoveCompanyInfoCache(companyUser.CompanyId);
                            return Ok(new ResponseViewModel { message = "success" });
                        }
                        else
                            return BadRequest(identityResult.Errors);
                    }
                }
            }
            return BadRequest(ModelState);
        }
#endif

        [HttpPost]
        [Route("company/default-inbox-order/{order}")]
        public async Task<IActionResult> UpdateDefaultInboxOrder(string order)
        {
            if (User.Identity.IsAuthenticated)
            {
                var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
                if (companyUser == null)
                {
                    return Unauthorized();
                }

                var company =
                    await _appDbContext.CompanyCompanies.FirstOrDefaultAsync(x => x.Id == companyUser.CompanyId);
                company.DefaultInboxOrder = order;

                await _appDbContext.SaveChangesAsync();

                var companyResponse = _mapper.Map<CompanyResponse>(company);
                return Ok(companyResponse);
            }

            return BadRequest();
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("company/timezone")]
        public IActionResult GetTimeZone(
            [FromQuery(Name = "Id")]
            string Id,
            [FromQuery(Name = "countryCode")]
            string countryCode,
            [FromQuery(Name = "countryName")]
            string countryName,
            [FromQuery(Name = "ianaId")]
            string ianaId)
        {
            if (!string.IsNullOrEmpty(Id))
            {
                var item = TimeZoneHelper.GetTimeZoneById(Id);
                return Ok(item);
            }
            else if (!string.IsNullOrEmpty(countryCode))
            {
                var item = TimeZoneHelper.GetTimeZoneByCountryCode(countryCode);
                return Ok(item);
            }
            else if (!string.IsNullOrEmpty(countryName))
            {
                var item = TimeZoneHelper.GetTimeZoneByCountryDisplayName(countryName);
                return Ok(item);
            }
            else if (!string.IsNullOrEmpty(ianaId))
            {
                var item = TimeZoneHelper.GetTimeZoneByIanaId(ianaId);
                return Ok(item);
            }
            else
            {
                var items = TimeZoneHelper.GetTimeZones();
                return Ok(items);
            }
        }

        private async Task PopulateChannelFieldCustomUserProfileFieldOptions(Staff companyUser)
        {
            var channelField = companyUser
                .Company
                .CustomUserProfileFields
                .Where(x => x.Type == FieldDataType.Channel)
                .FirstOrDefault();

            var channels = await _appDbContext.UserProfileCustomFields
                .Where(x => x.CompanyDefinedFieldId == channelField.Id)
                .Select(x => x.Value.ToLower())
                .Distinct()
                .ToListAsync();

            channelField.CustomUserProfileFieldOptions = new List<CustomUserProfileFieldOption>();
            foreach (var channel in channels)
            {
                if (channel == ChannelTypes.WhatsappTwilio)
                {
                    foreach (var config in companyUser.Company.WhatsAppConfigs)
                    {
                        channelField.CustomUserProfileFieldOptions.Add(
                            new CustomUserProfileFieldOption
                            {
                                Value = $"whatsapp:{config.TwilioAccountId};{config.WhatsAppSender}",
                                CompanyCustomUserProfileFieldId = channelField.Id,
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        Language = "en", DisplayName = $"WhatsApp - {config.Name}"
                                    }
                                }
                            });
                    }
                }

                if (channel == ChannelTypes.Facebook)
                {
                    foreach (var config in companyUser.Company.FacebookConfigs)
                    {
                        channelField.CustomUserProfileFieldOptions.Add(
                            new CustomUserProfileFieldOption
                            {
                                Value = $"facebook:{config.PageId}",
                                CompanyCustomUserProfileFieldId = channelField.Id,
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        Language = "en", DisplayName = $"Facebook - {config.PageName}"
                                    }
                                }
                            });
                    }
                }

                if (channel == ChannelTypes.Whatsapp360Dialog)
                {
                    foreach (var config in companyUser.Company.WhatsApp360DialogConfigs)
                    {
                        channelField.CustomUserProfileFieldOptions.Add(
                            new CustomUserProfileFieldOption
                            {
                                Value = $"whatsapp360dialog:{config.Id}",
                                CompanyCustomUserProfileFieldId = channelField.Id,
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        Language = "en", DisplayName = $"WhatsApp - {config.ChannelName}"
                                    }
                                }
                            });
                    }
                }

                if (channel == ChannelTypes.WhatsappCloudApi)
                {
                    foreach (var config in companyUser.Company.WhatsappCloudApiConfigs)
                    {
                        channelField.CustomUserProfileFieldOptions.Add(
                            new CustomUserProfileFieldOption
                            {
                                Value = $"whatsappcloudapi:{config.WhatsappPhoneNumber}",
                                CompanyCustomUserProfileFieldId = channelField.Id,
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        Language = "en", DisplayName = $"WhatsApp - {config.ChannelName}"
                                    }
                                }
                            });
                    }
                }

                if (channel == ChannelTypes.Instagram)
                {
                    foreach (var config in companyUser.Company.InstagramConfigs)
                    {
                        channelField.CustomUserProfileFieldOptions.Add(
                            new CustomUserProfileFieldOption
                            {
                                Value = $"instagram:{config.InstagramPageId}",
                                CompanyCustomUserProfileFieldId = channelField.Id,
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        Language = "en", DisplayName = $"Instagram - {config.Name}"
                                    }
                                }
                            });
                    }
                }

                if (ChannelTypes.SingleIntegratedChannelTypes.Contains(channel))
                {
                    if (!channelField.CustomUserProfileFieldOptions.Select(x => x.Value).Contains(channel))
                    {
                        channelField.CustomUserProfileFieldOptions.Add(
                            new CustomUserProfileFieldOption
                            {
                                Value = channel,
                                CompanyCustomUserProfileFieldId = channelField.Id,
                                CustomUserProfileFieldOptionLinguals = new List<CustomUserProfileFieldOptionLingual>
                                {
                                    new CustomUserProfileFieldOptionLingual
                                    {
                                        Language = "en", DisplayName = ConversationHelper.GetChannelName(channel)
                                    }
                                }
                            });
                    }
                }
            }
        }

        #region company api keys

        /// <summary>
        /// Get or generate company API key for the given API key type
        /// </summary>
        /// <param name="apiKeyType">apiKeyType.</param>
        /// <returns>CompanyApiKeyViewModel.</returns>
        [HttpPost]
        [TypeFilter(typeof(ApiKeyExceptionFilter))]
        [Route("company/apiKeys/{apiKeyType}")]
        public async Task<ActionResult<CompanyApiKeyViewModel>> IssueCompanyApiKey(
            [FromRoute]
            string apiKeyType)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "[{MethodName}] User {UserId} ({UserName}) generating API key type {ApiKeyType} for company {CompanyId}",
                nameof(IssueCompanyApiKey),
                companyUser.IdentityId,
                companyUser.Identity?.UserName,
                apiKeyType,
                companyUser.CompanyId);

            var companyId = companyUser.CompanyId;

            var apiKeyResolver = _apiKeyResolverFactory.GetApiKeyResolver(apiKeyType);

            var companyApiKey = await apiKeyResolver.GetCompanyApiKeyAsync(companyId);
            if (companyApiKey == null)
            {
                var companyUsage = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);
                companyApiKey = await apiKeyResolver.GenerateAsync(companyId, companyUsage.MaximumAPICalls);
            }

            return Ok(new CompanyApiKeyViewModel(companyApiKey));
        }

        [HttpPost]
        [TypeFilter(typeof(ApiKeyExceptionFilter))]
        [Route("company/apiKeys/{apiKeyType}/refresh")]
        public async Task<ActionResult<CompanyApiKeyViewModel>> RefreshCompanyApiKey(
            [FromRoute]
            string apiKeyType)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));
            if (companyUser == null)
            {
                return Unauthorized();
            }

            _logger.LogInformation(
                "[{MethodName}] User {UserId} ({UserName}) refreshing API key type {ApiKeyType} for company {CompanyId}",
                nameof(RefreshCompanyApiKey),
                companyUser.IdentityId,
                companyUser.Identity?.UserName,
                apiKeyType,
                companyUser.CompanyId);

            var companyId = companyUser.CompanyId;
            var companyUsage = await _companyUsageService.GetCompanyUsage(companyUser.CompanyId);

            var apiKeyResolver = _apiKeyResolverFactory.GetApiKeyResolver(apiKeyType);

            var companyApiKey = await apiKeyResolver.RefreshAsync(companyId, companyUsage.MaximumAPICalls);

            return Ok(new CompanyApiKeyViewModel(companyApiKey));
        }

        #endregion

        [HttpPost]
        [Route("Company/UpdateUserProfileDuplicationSetting")]
        public async Task<IActionResult> UpdateUserProfileDuplicationSetting(
            [FromBody]
            UpdateUserProfileDuplicationSettingViewModel vm)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null || companyUser.RoleType != StaffUserRole.Admin)
            {
                return Unauthorized();
            }

            var config = await _userProfileDuplicationConfigRepository.GetByCompanyIdAsync(companyUser.CompanyId);
            if (config == null)
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Cannot find user profile duplication setting for this company"
                    });
            }

            if (!Enum.TryParse(vm.DuplicationMode, out DuplicationMode duplicationMode))
            {
                return BadRequest(
                    new ResponseViewModel
                    {
                        message = "Invalid duplication mode"
                    });
            }

            config.UpdateDuplicationMode(duplicationMode);
            await _userProfileDuplicationConfigRepository.UpdateAsync(config);

            return Ok(new ResponseViewModel
            {
                message = "success"
            });
        }

        [HttpPost]
        [Route("Company/GetOrCreateUserProfileDuplicationSetting")]
        public async Task<IActionResult> GetOrCreateUserProfileDuplicationSetting()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser == null || companyUser.RoleType != StaffUserRole.Admin)
            {
                return Unauthorized();
            }

            var config = await _userProfileDuplicationConfigRepository.GetByCompanyIdAsync(companyUser.CompanyId);
            if (config == null)
            {
                config = new UserProfileDuplicationConfig(
                    companyUser.CompanyId,
                    DuplicationMode.StrictDuplicatePrevention);

                await _userProfileDuplicationConfigRepository.CreateAsync(config);
            }

            return Ok(new GetOrCreateUserProfileDuplicationSettingResponse
            {
                DuplicationMode = config.DuplicationMode.ToString()
            });
        }

        [HttpGet]
        [Route("Company/BusinessHourConfig")]
        public async Task<ActionResult<BusinessHourConfigViewModel>> GetBusinessHourConfig()
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            var businessHourConfig = await _businessHourConfigService.GetAsync(companyUser.CompanyId);

            if (businessHourConfig is null)
            {
                return NotFound("Business hour config not initialized");
            }

            return Ok(new BusinessHourConfigViewModel(businessHourConfig));
        }

        [HttpPost]
        [Route("Company/BusinessHourConfig")]
        public async Task<ActionResult<BusinessHourConfigViewModel>> CreateBusinessHourConfig(
            [FromBody]
            CreateBusinessHourConfigModel request)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser.RoleType != StaffUserRole.Admin && companyUser.RoleType != StaffUserRole.SuperAdmin )
            {
                return Unauthorized();
            }

            var businessHourConfig = await _businessHourConfigService.CreateAndGetConfigAsync(
                companyUser.CompanyId,
                request.IsEnabled,
                request.WeeklyHours);

            return Ok(new BusinessHourConfigViewModel(businessHourConfig));
        }

        [HttpPut]
        [Route("Company/BusinessHourConfig")]
        public async Task<ActionResult<BusinessHourConfigViewModel>> UpdateBusinessHourConfig(
            [FromBody]
            UpdateBusinessHourConfigModel request)
        {
            var companyUser = await _coreService.GetCompanyStaff(await _userManager.GetUserAsync(User));

            if (companyUser.RoleType != StaffUserRole.Admin && companyUser.RoleType != StaffUserRole.SuperAdmin )
            {
                return Unauthorized();
            }

            var businessHourConfig = await _businessHourConfigService.UpdateAndGetAsync(
                companyUser.CompanyId,
                request.IsEnabled,
                request.WeeklyHours);

            if (businessHourConfig is null)
            {
                return NotFound("Business hour config not initialized");
            }

            return Ok(new BusinessHourConfigViewModel(businessHourConfig));
        }

        private bool IsEnterpriseContactMaskingEnabled(
            bool isEnableSensitiveSetting,
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns)
        {
            return IsFeatureEnabled(
                isEnableSensitiveSetting,
                FeatureId.SensitiveDateMasking,
                currentSubscriptionPlanDefinition,
                currentAddOnsPlanDefinition,
                currentAddOns,
                ValidSubscriptionPlan.DataMaskingAddOns);
        }

        private bool IsWhatsAppQrCodeEnabled(
            bool isShowQrCodeMapping,
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns)
        {
            return IsFeatureEnabled(
                isShowQrCodeMapping,
                FeatureId.WhatsAppQrCode,
                currentSubscriptionPlanDefinition,
                currentAddOnsPlanDefinition,
                currentAddOns,
                ValidSubscriptionPlan.WhatsappQrCodeAddOns);
        }

        private bool IsShopifyIntegrationEnabled(
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns)
        {
            return IsFeatureEnabled(
                FeatureId.ShopifyIntegration,
                currentSubscriptionPlanDefinition,
                currentAddOnsPlanDefinition,
                currentAddOns,
                ValidSubscriptionPlan.ShopifyIntegrationAddOns);
        }

        private bool IsHubSpotIntegrationEnabled(
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns)
        {
            return IsFeatureEnabled(
                FeatureId.HubSpotIntegration,
                currentSubscriptionPlanDefinition,
                currentAddOnsPlanDefinition,
                currentAddOns,
                ValidSubscriptionPlan.HubspotIntegrationAddOns);
        }

        private bool IsStripeIntegrationEnabled(
            bool isStripePaymentEnabled,
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns)
        {
            return IsFeatureEnabled(
                isStripePaymentEnabled,
                FeatureId.StripePaymentIntegration,
                currentSubscriptionPlanDefinition,
                currentAddOnsPlanDefinition,
                currentAddOns,
                ValidSubscriptionPlan.PaymentIntegrationAddOns);
        }

        private bool IsSalesforceCrmEnabled(
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns)
        {
            return IsFeatureEnabled(
                FeatureId.SalesforceIntegration,
                currentSubscriptionPlanDefinition,
                currentAddOnsPlanDefinition,
                currentAddOns,
                ValidSubscriptionPlan.SalesforceCrmIntegrationAddOns
            );
        }

        private bool IsSalesforceMarketingCloudEnabled(
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns)
        {
            return IsFeatureEnabled(
                FeatureId.SalesforceMarketingCloud,
                currentSubscriptionPlanDefinition,
                currentAddOnsPlanDefinition,
                currentAddOns,
                ValidSubscriptionPlan.SalesforceMarketingCloudAddOns
            );
        }

        private bool IsSalesforceCommerceCloudEnabled(
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns)
        {
            return IsFeatureEnabled(
                FeatureId.SalesforceCommerceCloud,
                currentSubscriptionPlanDefinition,
                currentAddOnsPlanDefinition,
                currentAddOns,
                ValidSubscriptionPlan.SalesforceCommerceCloudAddOns
            );
        }

        private bool IsMicrosoftDynamics365IntegrationEnabled(
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns)
        {
            return IsFeatureEnabled(
                FeatureId.MicrosoftDynamics365Integration,
                currentSubscriptionPlanDefinition,
                currentAddOnsPlanDefinition,
                currentAddOns,
                SubscriptionPlansId.MicrosoftDynamics365AddOns);
        }

        private bool IsFacebookLeadAdsEnabled(
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns)
        {
            return IsFeatureEnabled(
                FeatureId.FacebookLeadAds,
                currentSubscriptionPlanDefinition,
                currentAddOnsPlanDefinition,
                currentAddOns,
                SubscriptionPlansId.FacebookLeadAdsAddOns
            );
        }

        private bool IsPlatformApiEnabled(
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns)
        {
            return IsFeatureEnabled(
                FeatureId.ApiCalls,
                currentSubscriptionPlanDefinition,
                currentAddOnsPlanDefinition,
                currentAddOns,
                SubscriptionPlansId.PlatformApiAddOns);
        }

        private bool IsMakeIntegrationEnabledEnabled(
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns)
        {
            return IsFeatureEnabled(
                FeatureId.MakeIntegration,
                currentSubscriptionPlanDefinition,
                currentAddOnsPlanDefinition,
                currentAddOns,
                SubscriptionPlansId.MakeIntegrationAddOns);
        }

        private bool IsZapierIntegrationEnabledEnabled(
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns)
        {
            return IsFeatureEnabled(
                FeatureId.ZapierIntegration,
                currentSubscriptionPlanDefinition,
                currentAddOnsPlanDefinition,
                currentAddOns,
                SubscriptionPlansId.ZapierIntegrationAddOns);
        }

        private bool IsFeatureEnabled(
            bool isFeatureEnabled,
            string featureId,
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns,
            IEnumerable<string> subscriptionPlanIds)
        {
            if (_isEnableTenantHubLogic)
            {
                return currentSubscriptionPlanDefinition.FeatureQuantities.Any(x => x.FeatureId == featureId)
                       || currentAddOnsPlanDefinition.Any(p => p.PlanType == PlanTypes.AddOns && p.FeatureQuantities.Any(fq => fq.FeatureId == featureId))
                       || isFeatureEnabled;
            }

            return currentAddOns.Any(x => subscriptionPlanIds.ContainsIgnoreCase(x.SubscriptionPlanId)) || isFeatureEnabled;
        }

        private bool IsFeatureEnabled(
            string featureId,
            PlanDefinition currentSubscriptionPlanDefinition,
            IEnumerable<PlanDefinition> currentAddOnsPlanDefinition,
            IEnumerable<BillRecord> currentAddOns,
            IEnumerable<string> subscriptionPlanIds)
        {
            if (_isEnableTenantHubLogic)
            {
                return currentSubscriptionPlanDefinition.FeatureQuantities.Any(x => x.FeatureId == featureId)
                       || currentAddOnsPlanDefinition.Any(p => p.PlanType == PlanTypes.AddOns && p.FeatureQuantities.Any(fq => fq.FeatureId == featureId));
            }

            return currentAddOns.Any(x => subscriptionPlanIds.ContainsIgnoreCase(x.SubscriptionPlanId));
        }
    }

    // TikTok Message Link Request DTOs
    public class CreateTikTokLinkRequest
    {
        public string Title { get; set; }
        public string Ref { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class UpdateTikTokLinkRequest
    {
        public string Title { get; set; }
        public string Ref { get; set; }
        public string Message { get; set; }
    }
}
