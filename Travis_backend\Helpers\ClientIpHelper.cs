﻿using System.Linq;
using Microsoft.AspNetCore.Http;

namespace Travis_backend.Helpers;

public static class ClientIpHelper
{
    private const string XForwardedForHeader = "X-Forwarded-For";
    private const string XAzureClientIpHeader = "X-Azure-ClientIP";
    private const string XAzureSocketIpHeader = "X-Azure-SocketIP";
    private const string XClientIpHeader = "X-Client-IP";

    /// <summary>
    /// Retrieves the client's IP address from the HTTP context, considering various proxy headers.
    /// This logic is designed to work correctly when the application is behind services like Azure Front Door and Azure App Service.
    ///
    /// Azure Front Door sends the client IP in the 'X-Azure-ClientIP' and 'X-Azure-SocketIP' headers.
    /// The 'X-Forwarded-For' header is a standard header for identifying the originating IP address of a client
    /// connecting to a web server through an HTTP proxy or a load balancer. It may contain a comma-separated list of IPs.
    ///
    /// The order of checks is important:
    /// 1. X-Forwarded-For: This is the most common header for proxies.
    /// 2. X-Azure-ClientIP: Specific to Azure, contains the client IP.
    /// 3. X-Azure-SocketIP: Also specific to Azure, contains the client socket IP.
    /// 4. X-Client-IP: A less common but sometimes used header.
    /// 5. Connection.RemoteIpAddress: As a fallback, this gives the IP of the immediate upstream connection, which might be a proxy.
    /// </summary>
    /// <param name="context">The HTTP context of the current request.</param>
    /// <returns>The client's IP address as a string, or null if it cannot be determined.</returns>
    public static string GetClientIp(HttpContext context)
    {
        if (context.Request.Headers.TryGetValue(XForwardedForHeader, out var forwardedForValue))
        {
            var ip = forwardedForValue.FirstOrDefault()?.Split(',').FirstOrDefault()?.Trim();
            if (!string.IsNullOrEmpty(ip))
            {
                return ip;
            }
        }

        if (context.Request.Headers.TryGetValue(XAzureClientIpHeader, out var azureClientIpValue))
        {
            var ip = azureClientIpValue.FirstOrDefault()?.Trim();
            if (!string.IsNullOrEmpty(ip))
            {
                return ip;
            }
        }

        if (context.Request.Headers.TryGetValue(XAzureSocketIpHeader, out var azureSocketIpValue))
        {
            var ip = azureSocketIpValue.FirstOrDefault()?.Trim();
            if (!string.IsNullOrEmpty(ip))
            {
                return ip;
            }
        }

        if (context.Request.Headers.TryGetValue(XClientIpHeader, out var clientIpValue))
        {
            var ip = clientIpValue.FirstOrDefault()?.Trim();
            if (!string.IsNullOrEmpty(ip))
            {
                return ip;
            }
        }

        return context.Connection.RemoteIpAddress?.ToString();
    }
}