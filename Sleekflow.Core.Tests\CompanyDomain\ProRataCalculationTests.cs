using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.Services;

namespace Sleekflow.Core.Tests.CompanyDomain;

/// <summary>
/// Comprehensive tests for pro-rata calculations with the 2,628,000 seconds constant,
/// focusing on precision, edge cases, and mathematical accuracy.
/// </summary>
[TestFixture]
public class ProRataCalculationTests
{
    private Mock<ILogger<TimezoneAwareMrrCalculationService>> _mockLogger = null!;
    private TimezoneAwareMrrCalculationService _service = null!;

    [SetUp]
    public void Setup()
    {
        _mockLogger = new Mock<ILogger<TimezoneAwareMrrCalculationService>>();
        _service = new TimezoneAwareMrrCalculationService(
            _mockLogger.Object);
    }

    #region Seconds Constant Precision Tests

    [Test]
    public void GetPreciseMonthDiff_WithExactSecondsConstant_ShouldReturnOneMonth()
    {
        // Arrange
        var start = new DateTime(2023, 1, 1, 0, 0, 0);
        var end = start.AddSeconds(2628000.0); // Exactly one month in seconds

        // Act
        var result = _service.GetPreciseMonthDiff(start, end);

        // Assert
        result.Should().Be(1.0m);
    }

    [Test]
    public void GetPreciseMonthDiff_WithHalfSecondsConstant_ShouldReturnHalfMonth()
    {
        // Arrange
        var start = new DateTime(2023, 1, 1, 0, 0, 0);
        var end = start.AddSeconds(1314000.0); // Half of 2,628,000 seconds

        // Act
        var result = _service.GetPreciseMonthDiff(start, end);

        // Assert
        result.Should().BeApproximately(0.5m, 0.001m);
    }

    [Test]
    public void GetPreciseMonthDiff_WithTwoSecondsConstants_ShouldReturnTwoMonths()
    {
        // Arrange
        var start = new DateTime(2023, 1, 1, 0, 0, 0);
        var end = start.AddMonths(2);

        // Act
        var result = _service.GetPreciseMonthDiff(start, end);

        // Assert
        result.Should().BeApproximately(2.0m, 0.001m);
    }

    #endregion

    #region Time Unit Precision Tests

    [Test]
    [TestCase(1, 0.001)] // Minimum enforced value
    [TestCase(60, 0.001)] // 1 minute (still below minimum)
    [TestCase(3600, 0.001)] // 1 hour (still below minimum)
    [TestCase(86400, 0.0329)] // 1 day ≈ 86400/2628000 ≈ 0.0329
    [TestCase(604800, 0.2303)] // 1 week ≈ 604800/2628000 ≈ 0.2303
    public void GetPreciseMonthDiff_WithSpecificTimeUnits_ShouldCalculateCorrectly(int seconds, double expectedMonths)
    {
        // Arrange
        var start = new DateTime(2023, 1, 1, 0, 0, 0);
        var end = start.AddSeconds(seconds);

        // Act
        var result = _service.GetPreciseMonthDiff(start, end);

        // Assert
        result.Should().BeApproximately((decimal) expectedMonths, 0.001m);
    }

    [Test]
    public void GetPreciseMonthDiff_WithPreciseHourCalculations_ShouldBeAccurate()
    {
        // Arrange
        var testCases = new[]
        {
            new
            {
                Hours = 1, ExpectedMonths = 3600m / 2628000m
            },
            new
            {
                Hours = 6, ExpectedMonths = 21600m / 2628000m
            },
            new
            {
                Hours = 12, ExpectedMonths = 43200m / 2628000m
            },
            new
            {
                Hours = 24, ExpectedMonths = 86400m / 2628000m
            },
            new
            {
                Hours = 168, ExpectedMonths = 604800m / 2628000m
            }, // 1 week
            new
            {
                Hours = 720, ExpectedMonths = 2592000m / 2628000m
            } // 30 days
        };

        foreach (var testCase in testCases)
        {
            // Arrange
            var start = new DateTime(2023, 1, 1, 0, 0, 0);
            var end = start.AddHours(testCase.Hours);

            // Act
            var result = _service.GetPreciseMonthDiff(start, end);

            // Assert
            var expected = Math.Max(testCase.ExpectedMonths, 0.001m); // Apply minimum
            result.Should().BeApproximately(
                expected,
                0.0001m,
                $"Failed for {testCase.Hours} hours");
        }
    }

    #endregion

    #region Full Month Recognition Tests

    [Test]
    [TestCase("2023-01-01 10:30:00", "2023-02-01 10:30:00", 1.0)] // Exact month
    [TestCase("2023-01-15 14:45:30", "2023-02-15 14:45:30", 1.0)] // Exact month, different time
    [TestCase("2023-01-31 23:59:59", "2023-02-28 23:59:59", 1.0)] // Month boundary adjustment
    [TestCase("2023-02-28 12:00:00", "2023-03-28 12:00:00", 1.0)] // February to March
    [TestCase("2024-02-29 12:00:00", "2024-03-29 12:00:00", 1.0)] // Leap year February
    public void GetPreciseMonthDiff_WithFullMonthScenarios_ShouldRecognizeExactMonths(
        string startStr,
        string endStr,
        decimal expectedMonths)
    {
        // Arrange
        var start = DateTime.Parse(startStr);
        var end = DateTime.Parse(endStr);

        // Act
        var result = _service.GetPreciseMonthDiff(start, end);

        // Assert
        result.Should().Be(expectedMonths);
    }

    [Test]
    public void GetPreciseMonthDiff_WithMultipleFullMonthsAndPartial_ShouldCalculateCorrectly()
    {
        // Arrange
        var start = new DateTime(2023, 1, 15, 10, 30, 0);
        var end = new DateTime(2023, 4, 20, 16, 45, 30); // 3 full months + partial

        // Act
        var result = _service.GetPreciseMonthDiff(start, end);

        // Assert
        result.Should().BeGreaterThan(3.0m);
        result.Should().BeLessThan(4.0m);

        // More precise calculation:
        // Full months: Jan 15 -> Feb 15 (1), Feb 15 -> Mar 15 (1), Mar 15 -> Apr 15 (1) = 3 months
        // Partial: Apr 15 10:30:00 to Apr 20 16:45:30 = 5 days, 6 hours, 15 minutes, 30 seconds
        // = 5*24*3600 + 6*3600 + 15*60 + 30 = 454530 seconds
        // 454530 / 2628000 ≈ 0.173 months
        // Total ≈ 3.173 months
        result.Should().BeApproximately(3.173m, 0.01m);
    }

    #endregion

    #region Edge Case Precision Tests

    [Test]
    public void GetPreciseMonthDiff_WithLeapYearTransitions_ShouldHandleCorrectly()
    {
        // Test cases around leap year boundaries
        var testCases = new[]
        {
            // Leap year 2024
            new
            {
                Start = "2024-01-29 12:00:00", End = "2024-02-29 12:00:00", Expected = 1.0m
            },
            new
            {
                Start = "2024-02-29 12:00:00", End = "2024-03-29 12:00:00", Expected = 1.0m
            },

            // Non-leap year 2023 (Feb 28 max)
            new
            {
                Start = "2023-01-29 12:00:00", End = "2023-02-28 12:00:00", Expected = 1.0m
            },
            new
            {
                Start = "2023-01-31 12:00:00", End = "2023-02-28 12:00:00", Expected = 1.0m
            },

            // Cross leap year boundary
            new
            {
                Start = "2023-12-29 12:00:00", End = "2024-01-29 12:00:00", Expected = 1.0m
            },

            new
            {
                Start = "2024-02-28 12:00:00", End = "2024-03-30 12:00:00", Expected = 1 + (2 * 86400m / 2628000m)
            },

            new
            {
                Start = "2024-02-29 12:00:00", End = "2024-05-30 12:00:00", Expected = 3 + (1 * 86400m / 2628000m)
            },

            new
            {
                Start = "2023-02-28 12:00:00", End = "2023-03-29 12:00:00", Expected = 1.0m
            },
        };

        foreach (var testCase in testCases)
        {
            // Arrange
            var start = DateTime.Parse(testCase.Start);
            var end = DateTime.Parse(testCase.End);

            // Act
            var result = _service.GetPreciseMonthDiff(start, end);

            // Assert
            result.Should().Be(
                testCase.Expected,
                $"Failed for {testCase.Start} to {testCase.End}");
        }
    }

    [Test]
    public void GetPreciseMonthDiff_WithMonthEndBoundaries_ShouldHandleProperly()
    {
        // Test month-end boundary conditions
        var testCases = new[]
        {
            // January 31 -> February 28 (non-leap year)
            new
            {
                Start = "2023-01-31 15:30:00", End = "2023-02-28 15:30:00", Expected = 1.0m
            },

            // January 31 -> February 29 (leap year)
            new
            {
                Start = "2024-01-31 15:30:00", End = "2024-02-29 15:30:00", Expected = 1.0m
            },

            // March 31 -> April 30
            new
            {
                Start = "2023-03-31 15:30:00", End = "2023-04-30 15:30:00", Expected = 1.0m
            },

            // May 31 -> June 30
            new
            {
                Start = "2023-05-31 15:30:00", End = "2023-06-30 15:30:00", Expected = 1.0m
            },

            // January 30 -> February 28 (non-leap year)
            new
            {
                Start = "2023-01-30 15:30:00", End = "2023-02-28 15:30:00", Expected = 1.0m
            },

            // January 30 -> February 29 (leap year)
            new
            {
                Start = "2024-01-30 15:30:00", End = "2024-02-29 15:30:00", Expected = 1.0m
            },

            // February 28 -> March 31
            new
            {
                Start = "2023-02-28 15:30:00", End = "2023-03-31 15:30:00", Expected = 1.0m
            },

            // February 29 -> March 31 (leap year)
            new
            {
                Start = "2024-02-29 15:30:00", End = "2024-03-31 15:30:00", Expected = 1.0m
            },

            // February 28 -> March 29
            new
            {
                Start = "2023-02-28 15:30:00", End = "2023-03-29 15:30:00", Expected = 1.0m
            },

            // February 29 -> March 29 (leap year)
            new
            {
                Start = "2024-02-29 15:30:00", End = "2024-03-29 15:30:00", Expected = 1.0m
            },

            // February 28 -> March 30
            new
            {
                Start = "2023-02-28 15:30:00", End = "2023-03-30 15:30:00", Expected = 1.0m
            },

            // February 29 -> March 30 (leap year)
            new
            {
                Start = "2024-02-29 15:30:00", End = "2024-03-30 15:30:00", Expected = 1.0m
            },
        };

        foreach (var testCase in testCases)
        {
            // Arrange
            var start = DateTime.Parse(testCase.Start);
            var end = DateTime.Parse(testCase.End);

            // Act
            var result = _service.GetPreciseMonthDiff(start, end);

            // Assert
            result.Should().Be(
                testCase.Expected,
                $"Failed for month boundary {testCase.Start} to {testCase.End}");
        }
    }

    #endregion

    #region Mathematical Precision Tests

    [Test]
    public void GetPreciseMonthDiff_WithVerySmallPeriods_ShouldEnforceMinimum()
    {
        // Test very small time periods that should enforce minimum value
        var testCases = new[]
        {
            TimeSpan.FromMilliseconds(1),
            TimeSpan.FromSeconds(1),
            TimeSpan.FromMinutes(1),
            TimeSpan.FromMinutes(30)
        };

        foreach (var timeSpan in testCases)
        {
            // Arrange
            var start = new DateTime(2023, 1, 1, 12, 0, 0);
            var end = start.Add(timeSpan);

            // Act
            var result = _service.GetPreciseMonthDiff(start, end);

            // Assert
            result.Should().Be(0.001m, $"Failed for period {timeSpan}");
        }
    }

    [Test]
    public void GetPreciseMonthDiff_WithPrecisionBoundary_ShouldCalculateAccurately()
    {
        // Test values around the precision boundary (0.001m)
        var start = new DateTime(2023, 1, 1, 0, 0, 0);

        // Calculate seconds that would give exactly 0.001 months
        var secondsForMinimum = 2628000m * 0.001m; // 2628 seconds
        var end = start.AddSeconds((double) secondsForMinimum);

        // Act
        var result = _service.GetPreciseMonthDiff(start, end);

        // Assert
        result.Should().BeApproximately(0.001m, 0.0001m);
    }

    [Test]
    public void GetPreciseMonthDiff_WithLongPeriods_ShouldMaintainAccuracy()
    {
        // Test very long periods to ensure no precision loss
        var testCases = new[]
        {
            new
            {
                Months = 12, Description = "1 year"
            },
            new
            {
                Months = 24, Description = "2 years"
            },
            new
            {
                Months = 36, Description = "3 years"
            },
            new
            {
                Months = 60, Description = "5 years"
            },
            new
            {
                Months = 120, Description = "10 years"
            }
        };

        foreach (var testCase in testCases)
        {
            // Arrange
            var start = new DateTime(2020, 1, 1, 12, 0, 0);
            var end = start.AddMonths(testCase.Months);

            // Act
            var result = _service.GetPreciseMonthDiff(start, end);

            // Assert
            result.Should().Be(
                testCase.Months,
                $"Failed for {testCase.Description} ({testCase.Months} months)");
        }
    }

    #endregion

    #region Complex Real-World Scenarios

    [Test]
    public void GetPreciseMonthDiff_WithBusinessQuarterPeriods_ShouldCalculateCorrectly()
    {
        // Test business quarter scenarios (3-month periods)
        var testCases = new[]
        {
            // Q1 2023
            new
            {
                Start = "2023-01-01 09:00:00", End = "2023-04-01 09:00:00", Expected = 3.0m
            },

            // Q2 2023
            new
            {
                Start = "2023-04-01 09:00:00", End = "2023-07-01 09:00:00", Expected = 3.0m
            },

            // Q3 2023
            new
            {
                Start = "2023-07-01 09:00:00", End = "2023-10-01 09:00:00", Expected = 3.0m
            },

            // Q4 2023
            new
            {
                Start = "2023-10-01 09:00:00", End = "2024-01-01 09:00:00", Expected = 3.0m
            }
        };

        foreach (var testCase in testCases)
        {
            // Arrange
            var start = DateTime.Parse(testCase.Start);
            var end = DateTime.Parse(testCase.End);

            // Act
            var result = _service.GetPreciseMonthDiff(start, end);

            // Assert
            result.Should().Be(
                testCase.Expected,
                $"Failed for quarter {testCase.Start} to {testCase.End}");
        }
    }

    [Test]
    public void GetPreciseMonthDiff_WithIrregularBillingPeriods_ShouldHandleCorrectly()
    {
        // Test irregular billing periods common in real-world scenarios
        var testCases = new[]
        {
            // 45-day billing period
            new
            {
                Days = 45, ExpectedMonths = 1M + (14 * 86400m / 2628000m)
            }, // 1 month + 14 days

            // 90-day billing period
            new
            {
                Days = 90, ExpectedMonths = 3M
            },

            // 365-day billing period (annual)
            new
            {
                Days = 365, ExpectedMonths = 12M
            }
        };

        foreach (var testCase in testCases)
        {
            // Arrange
            var start = new DateTime(2023, 1, 1, 10, 0, 0);
            var end = start.AddDays(testCase.Days);

            // Act
            var result = _service.GetPreciseMonthDiff(start, end);

            // Assert
            result.Should().BeApproximately(
                testCase.ExpectedMonths,
                0.01m,
                $"Failed for {testCase.Days}-day period");
        }
    }

    #endregion

    #region Timezone Integration Tests

    [Test]
    public void GetPreciseMonthDiffAsync_WithTimezoneConversion_ShouldCalculateCorrectly()
    {
        // Arrange
        var companyId = "timezone-test-company";
        var timezoneId = "Asia/Shanghai"; // UTC+8
        var company = new Company
        {
            Id = companyId, TimeZoneInfoId = timezoneId
        };

        // UTC times that span different local days
        var utcStart = new DateTime(2023, 1, 31, 20, 0, 0, DateTimeKind.Utc); // 4 AM next day in Shanghai
        var utcEnd = new DateTime(2023, 2, 28, 20, 0, 0, DateTimeKind.Utc); // 4 AM next day in Shanghai

        // Act
        var result = _service.GetPreciseMonthDiff(utcStart, utcEnd, companyId, company.TimeZoneInfoId);

        // Assert
        result.Should().BeGreaterThan(0);
        result.Should().BeLessThan(2.0m);

        // The exact value depends on timezone conversion, but should be close to 1 month
        result.Should().BeApproximately(1.0m, 0.1m);
    }

    [Test]
    public void GetPreciseMonthDiffAsync_WithDirectTimezoneId_ShouldSkipCompanyLookup()
    {
        // Arrange
        var companyId = "timezone-test-company";
        var directTimezoneId = "Pacific Standard Time"; // UTC-8
        var utcStart = new DateTime(2023, 1, 31, 20, 0, 0, DateTimeKind.Utc); // 12 PM PST
        var utcEnd = new DateTime(2023, 2, 28, 20, 0, 0, DateTimeKind.Utc); // 12 PM PST

        // Act - Use direct timezone ID, should skip company lookup
        var result = _service.GetPreciseMonthDiff(utcStart, utcEnd, companyId, directTimezoneId);

        // Assert
        result.Should().BeGreaterThan(0);
        result.Should().BeLessThan(2.0m);

        // The exact value depends on timezone conversion, but should be close to 1 month
        result.Should().BeApproximately(1.0m, 0.1m);
    }

    #endregion

    #region Performance Tests

    [Test]
    public void GetPreciseMonthDiff_PerformanceTest_ShouldBeEfficient()
    {
        // Arrange
        var testPeriods = new List<(DateTime Start, DateTime End)>();
        var baseTime = new DateTime(2023, 1, 1, 0, 0, 0);

        // Generate 10,000 random period pairs
        var random = new Random(12345); // Fixed seed for reproducible results
        for (int i = 0; i < 10000; i++)
        {
            var start = baseTime.AddDays(random.Next(0, 365));
            var end = start.AddDays(random.Next(1, 100)); // 1-100 days later
            testPeriods.Add((start, end));
        }

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        foreach (var (start, end) in testPeriods)
        {
            var result = _service.GetPreciseMonthDiff(start, end);
            result.Should().BeGreaterThan(0); // Basic sanity check
        }

        stopwatch.Stop();

        // Assert
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000); // Should complete within 5 seconds
        Console.WriteLine($"Processed 10,000 calculations in {stopwatch.ElapsedMilliseconds}ms");
    }

    #endregion
}