using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Newtonsoft.Json;
using Sleekflow.Apis.MessagingHub.Model;
using Travis_backend.AccountAuthenticationDomain.Models;
using Travis_backend.AnalyticsDomain.Models;
using Travis_backend.AutomationDomain.Models;
using Travis_backend.BlastDomain.Models;
using Travis_backend.BroadcastDomain.Models;
using Travis_backend.ChannelDomain.Models;
using Travis_backend.CommonDomain.Models;
using Travis_backend.CompanyDomain.Models;
using Travis_backend.CompanyDomain.ViewModels;
using Travis_backend.ContactDomain.Models;
using Travis_backend.ContactDomain.ViewModels;
using Travis_backend.ConversationDomain.Models;
using Travis_backend.ConversationServices.Models;
using Travis_backend.CoreDomain.Models;
using Travis_backend.DemoDomain.Models;
using Travis_backend.Enums;
using Travis_backend.FacebookInstagramIntegrationDomain.Models;
using Travis_backend.FileDomain.Models;
using Travis_backend.HubSpotIntegrationDomain.Models;
using Travis_backend.IntegrationServices.Models;
using Travis_backend.InternalDomain.Models;
using Travis_backend.InternalDomain.ViewModels;
using Travis_backend.MessageDomain.Models;
using Travis_backend.MessageDomain.ViewModels;
using Travis_backend.Models.ChatChannelConfig;
using Travis_backend.PiiMasking.Models;
using Travis_backend.ResellerDomain.Models;
using Travis_backend.ShareInvitationDomain.Models;
using Travis_backend.ShoplineIntegrationDomain.Models;
using Travis_backend.SleekflowCrmHubDomain.Models;
using Travis_backend.StripeIntegrationDomain.Models;
using Travis_backend.SubscriptionPlanDomain.Models;
using Travis_backend.SupportTicketDomain.Models;
using Travis_backend.TenantHubDomain.Models;
using Travis_backend.ZapierIntegrationDomain.Models;
using WABA360Dialog.ApiClient.Payloads.Models.MessageObjects.InteractiveObjects;

namespace Travis_backend.Database;

/// <summary>
/// A base class of the database contexts
/// </summary>
public class BaseDbContext : IdentityDbContext<ApplicationUser>
{
    public BaseDbContext(DbContextOptions options)
        : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        // Customize the ASP.NET Identity model and override the defaults if needed.
        // For example, you can rename the ASP.NET Identity table names and more.
        // Add your customizations after calling base.OnModelCreating(builder);

        // builder.Entity<ConversationTicket>().Property(e => e.Comment).HasConversion(
        //    v => JsonConvert.SerializeObject(v, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }),
        //    v => JsonConvert.DeserializeObject<RequestComment>(v, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }));
        builder.Entity<ConversationMessage>().Property(e => e.TranslationResults).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<TranslationResult>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<BroadcastHistory>().HasIndex(
            p => new
            {
                p.ConversationId, p.BroadcastCampaignId
            }).IsClustered(false);

        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.MessageUniqueID
            }).IsUnique(true);

        // builder.Entity<ConversationMessage>().HasIndex(p => new { p.Visibility }).IsUnique(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.Channel, p.Status
            }).IsUnique(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.CompanyId, p.MessageChecksum
            }).IsUnique(true);

        // builder.Entity<ConversationMessage>().HasIndex(p => new { p.Id, p.ConversationId, p.CompanyId }).IsUnique(true);
        // builder.Entity<ConversationMessage>().HasIndex(p => new { p.IsSentFromSleekflow, p.CompanyId, p.CreatedAt }).IncludeProperties(v => v.ConversationId).IsUnique(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.DeliveryType, p.CompanyId, p.CreatedAt
            }).IncludeProperties(v => v.ConversationId).IsUnique(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.ConversationId
            }).IncludeProperties(
            v => new
            {
                v.BroadcastHistoryId,
                v.Channel,
                v.CompanyId,
                v.CreatedAt,
                v.DeliveryType,
                v.EmailCC,
                v.EmailFromId,
                v.EmailTo,
                v.facebookReceiverId,
                v.facebookSenderId,
                v.IsSentFromSleekflow,
                v.LineReceiverId,
                v.LineSenderId,
                v.MessageAssigneeId,
                v.MessageChecksum,
                v.MessageContent,
                v.MessageType,
                v.MessageUniqueID,
                v.ReceiverDeviceUUID,
                v.ReceiverId,
                v.SenderDeviceUUID,
                v.SenderId,
                v.Status,
                v.Subject,
                v.TranslationResults,
                v.UpdatedAt,
                v.Visibility,
                v.WebClientReceiverId,
                v.WebClientSenderId,
                v.WeChatReceiverId,
                v.WeChatSenderId,
                v.whatsappReceiverId,
                v.whatsappSenderId,
                v.SMSReceiverId,
                v.SMSSenderId,
                v.IsSandbox
            }).IsUnique(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.ConversationId, p.UpdatedAt
            }).IncludeProperties(
            v => new
            {
                v.BroadcastHistoryId,
                v.Channel,
                v.CompanyId,
                v.CreatedAt,
                v.DeliveryType,
                v.EmailCC,
                v.EmailFromId,
                v.EmailTo,
                v.facebookReceiverId,
                v.facebookSenderId,
                v.IsSentFromSleekflow,
                v.LineReceiverId,
                v.LineSenderId,
                v.MessageAssigneeId,
                v.MessageChecksum,
                v.MessageContent,
                v.MessageType,
                v.MessageUniqueID,
                v.ReceiverDeviceUUID,
                v.ReceiverId,
                v.SenderDeviceUUID,
                v.SenderId,
                v.Status,
                v.Subject,
                v.TranslationResults,
                v.Visibility,
                v.WebClientReceiverId,
                v.WebClientSenderId,
                v.WeChatReceiverId,
                v.WeChatSenderId,
                v.whatsappReceiverId,
                v.whatsappSenderId,
                v.SMSReceiverId,
                v.SMSSenderId,
                v.IsSandbox
            }).IsClustered(false);

        // builder.Entity<ConversationMessage>().HasIndex(p => new { p.CreatedAt, p.UpdatedAt }).IsUnique(false);
        // builder.Entity<ConversationMessage>().HasIndex(p => new { p.MessageChecksum }).IsClustered(false);
        // builder.Entity<ConversationMessage>().HasIndex(p => new { p.QuotedMsgId }).IsClustered(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.ConversationId, p.DeliveryType, p.CreatedAt
            }).IncludeProperties(
            v => new
            {
                v.BroadcastHistoryId,
                v.Channel,
                v.CompanyId,
                v.EmailCC,
                v.EmailFromId,
                v.EmailTo,
                v.facebookReceiverId,
                v.facebookSenderId,
                v.IsSentFromSleekflow,
                v.LineReceiverId,
                v.LineSenderId,
                v.MessageAssigneeId,
                v.MessageChecksum,
                v.MessageContent,
                v.MessageType,
                v.MessageUniqueID,
                v.ReceiverDeviceUUID,
                v.ReceiverId,
                v.SenderDeviceUUID,
                v.SenderId,
                v.Status,
                v.Subject,
                v.TranslationResults,
                v.Visibility,
                v.WebClientReceiverId,
                v.WebClientSenderId,
                v.WeChatReceiverId,
                v.WeChatSenderId,
                v.whatsappReceiverId,
                v.whatsappSenderId,
                v.SMSReceiverId,
                v.SMSSenderId,
                v.IsSandbox,
                v.ChannelStatusMessage
            }).IsClustered(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.Id, p.ConversationId, p.CompanyId, p.CreatedAt
            }).IncludeProperties(
            v => new
            {
                v.BroadcastHistoryId,
                v.Channel,
                v.EmailCC,
                v.EmailFromId,
                v.EmailTo,
                v.facebookReceiverId,
                v.facebookSenderId,
                v.IsSentFromSleekflow,
                v.LineReceiverId,
                v.LineSenderId,
                v.MessageAssigneeId,
                v.MessageChecksum,
                v.MessageContent,
                v.MessageType,
                v.MessageUniqueID,
                v.ReceiverDeviceUUID,
                v.ReceiverId,
                v.SenderDeviceUUID,
                v.SenderId,
                v.Status,
                v.Subject,
                v.TranslationResults,
                v.Visibility,
                v.WebClientReceiverId,
                v.WebClientSenderId,
                v.WeChatReceiverId,
                v.WeChatSenderId,
                v.whatsappReceiverId,
                v.whatsappSenderId,
                v.SMSReceiverId,
                v.SMSSenderId,
                v.IsSandbox,
                v.ChannelStatusMessage
            }).IsClustered(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.Status
            }).IncludeProperties(
            v => new
            {
                v.BroadcastHistoryId
            }).IsClustered(false);

        // builder.Entity<ConversationMessage>().HasIndex(p => new { p.CreatedAt }).IsUnique(false);
        // builder.Entity<ConversationMessage>().HasIndex(p => new { p.Channel, p.CompanyId, p.MessageUniqueID, p.Status, p.CreatedAt }).IsClustered(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.Channel, p.CompanyId, p.Status, p.CreatedAt
            }).IsClustered(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.CompanyId, p.CreatedAt, p.DeliveryType
            }).IsClustered(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.ConversationId, p.IsSentFromSleekflow
            }).IncludeProperties(
            v => new
            {
                v.BroadcastHistoryId,
                v.Channel,
                v.ChannelStatusMessage,
                v.CompanyId,
                v.CreatedAt,
                v.DeliveryType,
                v.EmailCC,
                v.EmailFromId,
                v.EmailTo,
                v.facebookReceiverId,
                v.facebookSenderId,
                v.IsSandbox,
                v.LineReceiverId,
                v.LineSenderId,
                v.MessageAssigneeId,
                v.MessageChecksum,
                v.MessageContent,
                v.MessageType,
                v.MessageUniqueID,
                v.QuotedMsgBody,
                v.QuotedMsgId,
                v.ReceiverDeviceId,
                v.ReceiverDeviceUUID,
                v.ReceiverId,
                v.SenderDeviceId,
                v.SenderDeviceUUID,
                v.SenderId,
                v.SMSReceiverId,
                v.SMSSenderId,
                v.Status,
                v.Subject,
                v.TranslationResults,
                v.UpdatedAt,
                v.Visibility,
                v.WebClientReceiverId,
                v.WebClientSenderId,
                v.WeChatReceiverId,
                v.WeChatSenderId,
                v.whatsappReceiverId,
                v.whatsappSenderId
            }).IsClustered(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.ConversationId, p.Timestamp
            }).IncludeProperties(
            v => new
            {
                v.BroadcastHistoryId,
                v.Channel,
                v.ChannelStatusMessage,
                v.CompanyId,
                v.CreatedAt,
                v.DeliveryType,
                v.EmailCC,
                v.EmailFromId,
                v.EmailTo,
                v.facebookReceiverId,
                v.facebookSenderId,
                v.IsSandbox,
                v.LineReceiverId,
                v.LineSenderId,
                v.MessageAssigneeId,
                v.MessageChecksum,
                v.MessageContent,
                v.MessageType,
                v.MessageUniqueID,
                v.QuotedMsgBody,
                v.QuotedMsgId,
                v.ReceiverDeviceId,
                v.ReceiverDeviceUUID,
                v.ReceiverId,
                v.SenderDeviceId,
                v.SenderDeviceUUID,
                v.SenderId,
                v.SMSReceiverId,
                v.SMSSenderId,
                v.Status,
                v.Subject,
                v.TranslationResults,
                v.UpdatedAt,
                v.Visibility,
                v.WebClientReceiverId,
                v.WebClientSenderId,
                v.WeChatReceiverId,
                v.WeChatSenderId,
                v.whatsappReceiverId,
                v.whatsappSenderId
            }).IsClustered(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.Timestamp
            }).IsClustered(false);
        builder.Entity<ConversationMessage>().HasIndex(
            p => new
            {
                p.CompanyId, p.SleekPayRecordId
            }).IsClustered(false).IsCreatedOnline();

        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.UpdatedTime
            }).IsUnique(false);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.IsBookmarked
            }).IsUnique(false);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.ActiveStatus
            }).IsUnique(false);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.Status
            }).IsUnique(false);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.ModifiedAt
            }).IsUnique(false);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.AssigneeId
            }).IncludeProperties(v => v.UnreadMessageCount).IsUnique(false);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.ActiveStatus, p.CompanyId
            }).IncludeProperties(
            v => new
            {
                v.AssignedTeamId,
                v.AssigneeId,
                v.EmailAddressId,
                v.facebookUserId,
                v.IsBookmarked,
                v.LineUserId,
                v.MessageGroupName,
                v.ModifiedAt,
                v.NaiveUserId,
                v.SnoozeUntil,
                v.Status,
                v.UnreadMessageCount,
                v.UpdatedTime,
                v.UserDeviceId,
                v.WebClientId,
                v.WeChatUserId,
                v.WhatsappUserId,
                v.SMSUserId,
                v.IsSandbox
            }).IsClustered(false);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.ActiveStatus, p.CompanyId, p.AssigneeId
            }).IncludeProperties(
            v => new
            {
                v.AssignedTeamId,
                v.EmailAddressId,
                v.facebookUserId,
                v.IsBookmarked,
                v.LineUserId,
                v.MessageGroupName,
                v.ModifiedAt,
                v.NaiveUserId,
                v.SnoozeUntil,
                v.Status,
                v.UnreadMessageCount,
                v.UpdatedTime,
                v.UserDeviceId,
                v.WebClientId,
                v.WeChatUserId,
                v.WhatsappUserId,
                v.SMSUserId,
                v.IsSandbox,
                v.UserProfileId
            }).IsClustered(false);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.CompanyId
            }).IncludeProperties(
            v => new
            {
                v.AssignedTeamId,
                v.ActiveStatus,
                v.AssigneeId,
                v.EmailAddressId,
                v.facebookUserId,
                v.IsBookmarked,
                v.IsSandbox,
                v.LineUserId,
                v.MessageGroupName,
                v.ModifiedAt,
                v.NaiveUserId,
                v.SMSUserId,
                v.SnoozeUntil,
                v.Status,
                v.UnreadMessageCount,
                v.UpdatedTime,
                v.UserDeviceId,
                v.UserProfileId,
                v.WebClientId,
                v.WeChatUserId,
                v.WhatsappUserId
            }).IsClustered(false);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.Id, p.UserProfileId, p.CompanyId
            }).IsUnique(true);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.ActiveStatus, p.AssigneeId
            }).IncludeProperties(
            v => new
            {
                v.UnreadMessageCount
            }).IsClustered(false);

        // new 30/7
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.CompanyId, p.ActiveStatus, p.ModifiedAt
            }).IncludeProperties(
            v => new
            {
                v.AssignedTeamId,
                v.AssigneeId,
                v.EmailAddressId,
                v.facebookUserId,
                v.IsBookmarked,
                v.IsNewCreatedConversation,
                v.IsSandbox,
                v.LineUserId,
                v.MessageGroupName,
                v.NaiveUserId,
                v.SMSUserId,
                v.SnoozeUntil,
                v.Status,
                v.UnreadMessageCount,
                v.UpdatedTime,
                v.UserDeviceId,
                v.UserProfileId,
                v.WebClientId,
                v.WeChatUserId,
                v.WhatsappUserId
            }).IsClustered(false);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.CompanyId, p.ActiveStatus, p.AssigneeId, p.AssignedTeamId
            }).IncludeProperties(
            v => new
            {
                v.EmailAddressId,
                v.facebookUserId,
                v.IsBookmarked,
                v.IsNewCreatedConversation,
                v.IsSandbox,
                v.LineUserId,
                v.MessageGroupName,
                v.ModifiedAt,
                v.NaiveUserId,
                v.SMSUserId,
                v.SnoozeUntil,
                v.Status,
                v.UnreadMessageCount,
                v.UpdatedTime,
                v.UserDeviceId,
                v.UserProfileId,
                v.WebClientId,
                v.WeChatUserId,
                v.WhatsappUserId
            }).IsClustered(false);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.UserProfileId, p.ActiveStatus
            }).IsClustered(false);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.CompanyId, p.ActiveStatus
            }).IsClustered(false);

        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.CompanyId, p.UserProfileId
            }).IsUnique(true);

        // builder.Entity<Conversation>().HasIndex(p => new { p.LastMessageId }).IsClustered(false);
        // builder.Entity<Conversation>().HasIndex(p => new { p.LastMessageChannel }).IsClustered(false);
        // builder.Entity<Conversation>().HasIndex(p => new { p. })
        builder.Entity<CompanyQuickReply>().HasIndex(
            p => new
            {
                p.CompanyId, p.Value
            }).IsUnique(false);
        builder.Entity<CompanyQuickReply>().HasIndex(
            p => new
            {
                p.CompanyId, p.Type
            }).IsClustered(false).IsUnique(false).IsCreatedOnline(true);

        // builder.Entity<CompanyQuickReplyLingual>().HasIndex(p => new { p.CompanyQuickReplyId1 }).IsUnique(false);
        builder.Entity<CompanyCustomUserProfileField>().HasIndex(
            p => new
            {
                p.CompanyId
            }).IncludeProperties(
            v => new
            {
                v.FieldName,
                v.IsDefault,
                v.IsEditable,
                v.IsVisible,
                v.Order,
                v.Type
            }).IsUnique(false);
        builder.Entity<CompanyCustomUserProfileField>().HasIndex(
            p => new
            {
                p.CompanyId, p.FieldName
            }).IsClustered(false);

        builder.Entity<UserProfile>()
                .HasOne(x => x.WebClient)
                .WithMany()
                .HasForeignKey(x => x.WebClientId);

        builder.Entity<UserProfile>().HasIndex(
            p => new
            {
                p.UpdatedAt
            }).IsUnique(false);
        builder.Entity<UserProfile>().HasIndex(
            p => new
            {
                p.ActiveStatus
            }).IsUnique(false);
        builder.Entity<UserProfile>().HasIndex(
            p => new
            {
                p.CreatedAt
            }).IncludeProperties(
            v => new
            {
                v.EmailAddressId,
                v.FacebookAccountId,
                v.FirstName,
                v.IsSandbox,
                v.LastContact,
                v.LastContactFromCustomers,
                v.LastName,
                v.LineUserId,
                v.RegisteredUserId,
                v.PictureUrl,
                v.UserProfilePictureFileId,
                v.UserDeviceId,
                v.WebClientId,
                v.WeChatUserId,
                v.WhatsAppAccountId,
                v.SMSUserId
            }).IsUnique(false);

        builder.Entity<UserProfile>().HasIndex(
            p => new
            {
                p.ActiveStatus, p.CompanyId
            }).IncludeProperties(
            v => new
            {
                v.CreatedAt,
                v.EmailAddressId,
                v.FacebookAccountId,
                v.FirstName,
                v.LastName,
                v.LineUserId,
                v.PictureUrl,
                v.RegisteredUserId,
                v.SMSUserId,
                v.UpdatedAt,
                v.UserDeviceId,
                v.UserProfilePictureFileId,
                v.WebClientId,
                v.WeChatUserId,
                v.WhatsAppAccountId,
                v.IsSandbox
            }).IsClustered(false);
        builder.Entity<UserProfile>().HasIndex(
            p => new
            {
                p.CompanyId
            }).IncludeProperties(
            v => new
            {
                v.ActiveStatus,
                v.CreatedAt,
                v.EmailAddressId,
                v.FacebookAccountId,
                v.FirstName,
                v.IsSandbox,
                v.LastContact,
                v.LastContactFromCustomers,
                v.LastName,
                v.LineUserId,
                v.PictureUrl,
                v.RegisteredUserId,
                v.SMSUserId,
                v.UpdatedAt,
                v.UserDeviceId,
                v.UserProfilePictureFileId,
                v.WebClientId,
                v.WeChatUserId,
                v.WhatsAppAccountId
            }).IsClustered(false);

        builder.Entity<UserProfile>().HasIndex(
            p => new
            {
                p.ContactOwnerId
            }).IsClustered(false);
        builder.Entity<UserProfile>().HasIndex(
            p => new
            {
                p.PhoneNumber
            }).IsClustered(false);
        builder.Entity<UserProfile>().HasIndex(
            p => new
            {
                p.Email
            }).IsClustered(false);

        builder.Entity<CrmHubEntity>().HasIndex(
            p => new
            {
                p.EntityId, p.UserProfileId
            }).IsClustered(false);

        builder.Entity<NotificationRecord>().HasIndex(
            p => new
            {
                p.CompanyId, p.StaffId
            }).IncludeProperties(
            v => new
            {
                v.Count, v.CreatedAt, v.Payload, v.EventName
            }).IsClustered(false);
        builder.Entity<NotificationRecord>().HasIndex(p => p.CreatedAt).IsClustered(false);

        builder.Entity<CompanyMessageTemplate>().HasIndex(
            p => new
            {
                p.UpdatedAt
            }).IsUnique(false);
        builder.Entity<ImportContactHistory>().HasIndex(
            p => new
            {
                p.CreatedAt
            }).IsUnique(false);
        builder.Entity<CompanyHashtag>().HasIndex(
            p => new
            {
                p.Hashtag
            }).IsUnique(false);
        builder.Entity<CompanyHashtag>().HasIndex(
            p => new
            {
                p.CompanyId, p.Hashtag
            }).IsUnique(true);

        builder.Entity<CompanyCustomUserProfileField>().HasIndex(
            p => new
            {
                p.FieldName,
                p.Order,
                p.IsVisible,
                p.IsEditable,
                p.IsDefault,
                p.Type
            }).IsUnique(false);
        builder.Entity<UserProfile>().HasIndex(
            p => new
            {
                p.FirstName, p.LastName, p.CreatedAt
            }).IsUnique(false);
        builder.Entity<WebClientSender>().HasIndex(
            p => new
            {
                p.SignalRConnectionId
            }).IsUnique(false);
        builder.Entity<WebClientSender>().HasIndex(
            p => new
            {
                p.CreatedAt
            }).IsUnique(false);
        builder.Entity<UserProfilePictureFile>().HasIndex(
            p => new
            {
                p.ProfilePictureId
            }).IsUnique(true);
        builder.Entity<CampaignUploadedFile>().HasIndex(
            p => new
            {
                p.CampaignUploadedFileId
            }).IsUnique(true);
        builder.Entity<CampaignAutomationUploadedFile>().HasIndex(
            p => new
            {
                p.CampaignAutomationUploadedFileId
            }).IsUnique(false);
        builder.Entity<AssignmentUploadedFile>().HasIndex(
            p => new
            {
                p.AssignmentUploadedFileId
            }).IsUnique(false);

        builder.Entity<CompanyIconFile>().HasIndex(
            p => new
            {
                p.ProfilePictureId
            }).IsUnique(true);
        builder.Entity<ProfilePictureFile>().HasIndex(
            p => new
            {
                p.ProfilePictureId
            }).IsUnique(true);
        builder.Entity<UserProfileCustomField>().HasIndex(
            p => new
            {
                p.CompanyDefinedFieldId
            }).IncludeProperties(v => v.Value).IsUnique(false);
        builder.Entity<UserProfileCustomField>().HasIndex(
            p => new
            {
                p.UserProfileId
            }).IncludeProperties(
            v => new
            {
                v.CompanyDefinedFieldId, v.Value
            }).IsUnique(false);
        builder.Entity<UserProfileCustomField>().HasIndex(
            p => new
            {
                p.CompanyDefinedFieldId
            }).IncludeProperties(
            v => new
            {
                v.UserProfileId, v.Value
            }).IsUnique(false);

        builder.Entity<UserProfileCustomField>().HasIndex(
            p => new
            {
                p.CompanyDefinedFieldId, p.UserProfileId
            }).IncludeProperties(
            v => new
            {
                v.Value
            }).IsClustered(false);
        builder.Entity<UserProfileCustomField>().HasIndex(
            p => new
            {
                p.CompanyDefinedFieldId, p.Value
            }).IncludeProperties(
            v => new
            {
                v.UserProfileId
            }).IsClustered(false);

        builder.Entity<Conversation>()
            .HasOne(x => x.WebClient)
            .WithMany()
            .HasForeignKey(x => x.WebClientId);

        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.ActiveStatus, p.CompanyId
            }).IncludeProperties(v => v.AssigneeId).IsUnique(false);
        builder.Entity<Conversation>().HasIndex(
            p => new
            {
                p.ActiveStatus, p.UpdatedTime
            }).IncludeProperties(v => v.WebClientId).IsUnique(false);

        builder.Entity<UserDevice>().HasIndex(
            p => new
            {
                p.DeviceUUID
            }).IsUnique(true);
        builder.Entity<UploadedFile>().HasIndex(
            p => new
            {
                p.FileId
            }).IsUnique(true);
        builder.Entity<FacebookConfig>().HasIndex(
            p => new
            {
                p.PageId
            }).IsUnique(true);

        builder.Entity<TikTokConfig>()
            .HasIndex(p => new
            {
                p.CompanyId, p.ChannelIdentityId
            })
            .IsUnique(false);

        builder.Entity<TikTokConfig>()
            .HasIndex(p => new
            {
                p.TikTokConfigId
            })
            .IsUnique(false);


        builder.Entity<TikTokConfig>().Property(e => e.Metadata).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<Dictionary<string, object>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        // builder.Entity<ConversationRemark>().HasIndex(p => new { p.RemarkId }).IsUnique(true);
        // builder.Entity<UserProfileRemark>().HasIndex(p => new { p.RemarkId }).IsUnique(true);
        builder.Entity<WhatsApp360DialogSender>().HasIndex(
            p => new
            {
                p.Id, p.CompanyId
            }).IsClustered(false).IsUnique(false);
        builder.Entity<WhatsApp360DialogSender>().HasIndex(
            p => new
            {
                p.CompanyId, p.WhatsAppId
            }).IsClustered(false).IsUnique(false);

        builder.Entity<Admin>().HasIndex(
            table => new
            {
                table.IdentityId, table.CompanyId
            }).IsUnique(true);
        builder.Entity<CompanyOperator>().HasIndex(
            table => new
            {
                table.IdentityId, table.CompanyId
            }).IsUnique(true);
        builder.Entity<Staff>().HasIndex(
            table => new
            {
                table.IdentityId, table.CompanyId
            }).IsUnique(true);

        builder.Entity<Guest>().HasIndex(
            table => new
            {
                table.IdentityId, table.CompanyId
            }).IsUnique(true);
        builder.Entity<FacebookSender>().HasIndex(
            table => new
            {
                table.FacebookId, table.CompanyId, table.pageId
            }).IsUnique(true);
        builder.Entity<InstagramSender>().HasIndex(
            table => new
            {
                table.CompanyId,
                table.InstagramId,
                table.InstagramPageId,
                table.PageId,
                table.UserId
            }).IsUnique(true);

        builder.Entity<WhatsAppSender>().HasIndex(
            table => new
            {
                table.whatsAppId, table.CompanyId, table.InstanceId,
            }).IsClustered(false);

        builder.Entity<WhatsAppSender>().HasIndex(
            table => new
            {
                table.whatsAppId, table.CompanyId, table.InstanceId, table.InstaneSender
            }).IsClustered(false);

        // builder.Entity<WhatsAppSender>().HasIndex(table => new
        // {
        //     table.InstanceId,
        //     table.InstaneSender
        // }).IsClustered(false);
        builder.Entity<WhatsAppSender>().HasIndex(
            table => new
            {
                table.InstanceId
            }).IncludeProperties(
            v => new
            {
                v.CompanyId,
                v.isFetchProfileInfo,
                v.locale,
                v.name,
                v.phone_number,
                v.profile_pic,
                v.whatsAppId
            }).IsClustered(false);

        builder.Entity<WebClientSender>().Property(e => e.Metadata).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<Dictionary<string, object>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<WeChatConfig>().Property(e => e.Metadata).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<Dictionary<string, object>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<WeChatSender>().HasIndex(
            table => new
            {
                table.openid, table.CompanyId
            }).IsUnique(true);
        builder.Entity<EmailSender>().HasIndex(
            table => new
            {
                table.Email, table.CompanyId
            }).IsUnique(true);
        builder.Entity<WebClientSender>().HasIndex(
            table => new
            {
                table.WebClientUUID, table.CompanyId
            }).IsUnique(true);
        builder.Entity<LineSender>().HasIndex(
            table => new
            {
                table.userId, table.CompanyId
            }).IsUnique(true);
        builder.Entity<SMSSender>().HasIndex(
            table => new
            {
                table.SMSId, table.CompanyId, table.InstanceId
            }).IsUnique(false);

        builder.Entity<WhatsAppConfig>().HasIndex(
            table => new
            {
                table.TwilioAccountId, table.WhatsAppSender
            }).IsUnique(true);

        builder.Entity<WhatsAppConfig>().HasIndex(
            table => new
            {
                table.SID
            }).IsClustered(false);

        builder.Entity<SMSConfig>().HasIndex(
            table => new
            {
                table.TwilioAccountId, table.SMSSender
            }).IsUnique(true);

        builder.Entity<UserProfileDeletionBuffer>().HasIndex(
            table => new
            {
                table.UserProfileId
            }).IsUnique();

        builder.Entity<UserProfileDeletionBuffer>().HasIndex(
            table => new
            {
                table.CompanyId, table.UserProfileId
            });

        builder.Entity<WeChatSender>().Property(e => e.tagid_list).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CompanyMessageTemplate>().Property(e => e.TemplateParams).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CompanyMessageTemplate>().Property(e => e.Conditions).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<Condition>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CompanyMessageTemplate>().Property(e => e.TargetedChannels).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CompanyMessageTemplate>().Property(e => e.TargetedChannelWithIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<TargetedChannelModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CompanyMessageTemplate>()
            .OwnsOne(
                e => e.TargetedChannel,
                navigationBuilder =>
                {
                    navigationBuilder.ToJson();
                });

        builder.Entity<CompanyMessageTemplate>().Property(e => e.StripePaymentRequestOption).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<GenerateStripPaymentRequest>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignChannelMessage>().Property(e => e.TemplateParams).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignChannelMessage>().Property(e => e.TargetedChannels).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<TargetedChannelModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignChannelMessage>()
            .Property(e => e.TargetedChannel)
            .HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<TargetedChannel>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignChannelMessage>().Property(e => e.WhatsApp360DialogExtendedCampaignMessage)
            .HasConversion(
                v => JsonConvert.SerializeObject(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }),
                v => JsonConvert.DeserializeObject<WhatsApp360DialogExtendedCampaignMessage>(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }));

        builder.Entity<CampaignChannelMessage>().Property(e => e.ExtendedMessagePayloadDetail).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<ExtendedMessagePayloadDetail>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CompanyMessageTemplate>().Property(e => e.StatisticsData).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<StatisticsData>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AssignmentRule>().Property(e => e.Conditions).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<Condition>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AssignmentRule>().Property(e => e.TargetedChannels).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AssignmentRule>().Property(e => e.TargetedChannelWithIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<TargetedChannelModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AssignmentRule>().Property(e => e.WebhookVariables).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AssignmentRule>().Property(e => e.RecurringSetting).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<CronJobObject>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationAction>().Property(e => e.MessageParams).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationAction>().Property(e => e.ActionAddedToGroupIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<long>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationAction>().Property(e => e.ActionRemoveFromGroupIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<long>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationAction>().Property(e => e.ActionUpdateCustomFields).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<AddCustomFieldsViewModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationAction>().Property(e => e.ActionAddConversationHashtags).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<ConversationHashtagViewModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationAction>().Property(e => e.ActionAddConversationRemarks).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<RemarkViewModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationAction>().Property(e => e.ChangeConversationStatus).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<StatusViewModel>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationAction>().Property(e => e.TargetedChannelWithIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<TargetedChannelModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationAction>().Property(e => e.AddAdditionalAssigneeIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignAutomationAction>().Property(e => e.MessageParams).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignAutomationAction>().Property(e => e.ActionAddedToGroupIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<long>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignAutomationAction>().Property(e => e.ActionRemoveFromGroupIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<long>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignAutomationAction>().Property(e => e.ActionUpdateCustomFields).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<AddCustomFieldsViewModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignAutomationAction>().Property(e => e.ActionAddConversationHashtags).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<ConversationHashtagViewModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignAutomationAction>().Property(e => e.ActionAddConversationRemarks).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<RemarkViewModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignAutomationAction>().Property(e => e.ChangeConversationStatus).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<StatusViewModel>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignAutomationAction>().Property(e => e.TargetedChannelWithIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<TargetedChannelModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignAutomationAction>().Property(e => e.AddAdditionalAssigneeIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<TeamAssignmentQueue>().Property(e => e.StaffIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<BillRecord>().Property(e => e.metadata).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<Dictionary<string, string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<BillRecord>().Property(e => e.PaymentSplits).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<PaymentSplit>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AssignmentRule>().HasIndex(
            table => new
            {
                table.Status
            }).IsClustered(false);

        builder.Entity<AssignmentRule>().HasIndex(
            table => new
            {
                table.AssignmentId
            }).IsUnique(true);

        builder.Entity<AssignmentRule>().HasIndex(
            table => new
            {
                table.ZaiperSubscriptionId
            }).IsUnique(true);

        builder.Entity<AssignmentQueue>().Property(e => e.StaffIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<BillRecord>().HasIndex(
            table => new
            {
                table.invoice_Id
            }).IsUnique(false);

        builder.Entity<BillRecord>().HasIndex(
            table => new
            {
                table.stripe_subscriptionId
            }).IsUnique(false);

        builder.Entity<BillRecord>().HasIndex(
            table => new
            {
                table.stripeId
            }).IsUnique(false);

        builder.Entity<ImportedUserProfile>().HasIndex(
            table => new
            {
                table.ImportContactHistoryId
            }).IncludeProperties(
            v => new
            {
                v.UserProfileId
            }).IsClustered(false);
        builder.Entity<BroadcastHistory>().HasIndex(
            table => new
            {
                table.BroadcastCampaignId
            }).IncludeProperties(
            v => new
            {
                v.BroadcastSentById, v.ConversationId, v.CreatedAt, v.Status
            });

        builder.Entity<FacebookConfig>().HasIndex(
            table => new
            {
                table.SubscribedFields
            }).IsClustered(false);

        builder.Entity<ImportedUserProfile>().HasIndex(
            table => new
            {
                table.ImportContactHistoryId, table.UserProfileId
            }).IsClustered(false);

        builder.Entity<CompanyAPIKey>().Property(e => e.Permissions).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ZapierPollingRecord>().HasIndex(
            table => new
            {
                table.CompanyId, table.ZaiperTrigger
            }).IsUnique(true);

        builder.Entity<ShareableInvitation>().Property(e => e.TeamIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<long>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<WhatsappRegistrationsFile>().HasIndex(
            table => new
            {
                table.FileId
            }).IsUnique(true);

        builder.Entity<TwilioTopupRecord>().HasIndex(
            table => new
            {
                table.TopupPlanId, table.AccountSID
            }).IsClustered(false);
        builder.Entity<WhatsAppConfig>().HasIndex(
            table => new
            {
                table.IdempotencyToken
            }).IsUnique(true);

        builder.Entity<UploadedFile>().HasIndex(
            table => new
            {
                table.CampaignUploadedFileId
            }).IsClustered(false);
        builder.Entity<UploadedFile>().HasIndex(
            table => new
            {
                table.AssignmentUploadedFileId
            }).IsClustered(false);

        builder.Entity<Promotion>().HasIndex(promote => promote.PromotionCode).IsUnique(true);

        builder.Entity<CompanyTeam>().Property(e => e.DefaultChannels).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<TargetedChannelModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CompanyQuickReplyLingual>().Property(e => e.Params).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CompanyQuickReplyLingual>().Property(e => e.WhatsApp360DialogExtendedMessage).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<WhatsApp360DialogExtendedQuickReplyMessage>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<RolePermission>().Property(e => e.StoredPermission).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<Permission>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<RolePermission>().HasIndex(
            table => new
            {
                table.CompanyId, table.StaffUserRole
            }).IsUnique(true);

        builder.Entity<WhatsAppSender>().HasIndex(
            table => new
            {
                table.InstaneSender
            }).IsClustered(false);

        builder.Entity<AnalyticsRecord>().HasIndex(
            table => new
            {
                table.CompanyId,
                table.TeamId,
                table.StaffId,
                table.IsTeamUnassigned,
                table.DateTime,

                // table.Conditions,
                table.ConditionsHash,
                table.Version
            }).IsUnique(true);

        builder.Entity<AnalyticsRecord>().Property(e => e.Data).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<AnalyticsData>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        // builder.Entity<AnalyticsRecord>().Property(e => e.Conditions).HasConversion(
        //   v => JsonConvert.SerializeObject(v, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }),
        //   v => JsonConvert.DeserializeObject<List<Condition>>(v, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }));
        builder.Entity<TwilioUsageRecord>().HasIndex(
            table => new
            {
                table.CompanyId, table.TwilioAccountId
            }).IsUnique(true);

        builder.Entity<Segment>().Property(e => e.Conditions).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<Condition>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<Company>().Property(e => e.CompanySetting).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<CompanySetting>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<Company>().Property(e => e.UsageLimitOffsetProfile).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<CompanyUsageLimitOffsetProfile>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<Company>().Property(e => e.CommunicationTools).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AdditionalAssignee>().HasIndex(
            table => new
            {
                table.ConversationId, table.AssigneeId
            }).IsUnique(true);

        builder.Entity<InstagramConfig>().HasIndex(
            table => new
            {
                table.PageId, table.InstagramPageId, table.CompanyId
            }).IsUnique(true);

        builder.Entity<TikTokSender>().HasIndex(
            table => new
            {
                table.UserProfileId, table.ChannelIdentityId
            }).IsUnique(false);

        builder.Entity<TikTokSender>().HasIndex(
            table => new
            {
                table.TiktokConversationId
            }).IsUnique(false);

        builder.Entity<RegisteredSession>().HasIndex(
            table => new
            {
                table.Id, table.UUID,
            }).IsUnique(true);

        builder.Entity<CmsSalesPaymentRecord>()
            .HasOne(s => s.BillRecord)
            .WithMany(c => c.CmsSalesPaymentRecords)
            .HasForeignKey(bc => bc.BillRecordId)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<Company>()
            .HasMany(s => s.CmsContactOwnerChangeLogs)
            .WithOne()
            .HasForeignKey(s => s.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<AutomationHistory>()
            .HasIndex(
                table => new
                {
                    table.TargetAssignmentRuleId, table.TargetUserProfileId, table.CompanyId, table.CreatedAt
                }).IsUnique(false);

        builder.Entity<CmsWhatsappApplication>().Property(e => e.Files).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<CmsWhatsappApplicationFile>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<WhatsApp360DialogUsageTransactionLog>().Property(e => e.ConversationPeriodUsage)
            .HasConversion(
                v => JsonConvert.SerializeObject(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }),
                v => JsonConvert.DeserializeObject<Whatsapp360DialogConversationPeriodUsage>(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }));
        builder.Entity<WhatsApp360DialogUsageTransactionLog>().Property(e => e.PhoneNumberPeriodUsage)
            .HasConversion(
                v => JsonConvert.SerializeObject(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }),
                v => JsonConvert.DeserializeObject<Whatsapp360DialogPhoneNumberPeriodUsage>(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }));
        builder.Entity<WhatsApp360DialogUsageTransactionLog>().Property(e => e.NewPhoneNumberFee).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<Whatsapp360DialogNewPhoneNumberFee>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        // builder.Entity<ConversationHashtag>()
        //    .HasIndex(table => new
        //    {
        //        table.ConversationId,
        //        table.HashtagId
        //    }).IsUnique(true);
        builder.Entity<Company>()
            .HasMany(s => s.ShopifyOrderRecords)
            .WithOne()
            .HasForeignKey(s => s.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<UserProfile>()
            .HasMany(s => s.ShopifyOrderRecords)
            .WithOne()
            .HasForeignKey(s => s.UserProfileId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<UserProfile>()
            .HasMany(s => s.CrmHubEntities)
            .WithOne(s => s.UserProfile)
            .HasForeignKey(s => s.UserProfileId)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<Company>()
            .HasMany(s => s.ShopifyAbandonedCarts)
            .WithOne()
            .HasForeignKey(s => s.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<UserProfile>()
            .HasMany(s => s.ShopifyAbandonedCarts)
            .WithOne()
            .HasForeignKey(s => s.UserProfileId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<IcebreakerReplyRule>()
            .HasOne(s => s.FbIgIcebreaker)
            .WithMany(s => s.IcebreakerReplyRules)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<ShopifyOrderRecord>().Property(e => e.LineItems).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<MyShopifyLineItem>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ShopifyOrderRecord>().Property(e => e.Fulfillments).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<ShopifySharp.Fulfillment>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ShopifyAbandonedCart>().Property(e => e.LineItems).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<MyShopifyCheckoutLineItem>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CampaignChannelMessage>().Property(e => e.OfficialTemplateParams).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ShopifyOrderRecord>()
            .HasIndex(
                e => new
                {
                    e.CompanyId, e.UserProfileId, e.OrderName
                }).IsUnique(true);

        builder.Entity<CompanySandbox>().HasIndex(
            e => new
            {
                e.MappingIdentity
            }).IsUnique(true);

        builder.Entity<AutomationActionRecord>().Property(e => e.MessageParams).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationActionRecord>().Property(e => e.ActionAddedToGroupIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<long>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationActionRecord>().Property(e => e.ActionRemoveFromGroupIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<long>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationActionRecord>().Property(e => e.ActionUpdateCustomFields).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<AddCustomFieldsViewModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationActionRecord>().Property(e => e.ActionAddConversationHashtags).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<ConversationHashtagViewModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationActionRecord>().Property(e => e.ActionAddConversationRemarks).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<RemarkViewModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationActionRecord>().Property(e => e.ChangeConversationStatus).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<StatusViewModel>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationActionRecord>().Property(e => e.TargetedChannelWithIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<TargetedChannelModel>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationActionRecord>().Property(e => e.AddAdditionalAssigneeIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ConversationMessage>().Property(e => e.Metadata).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<Dictionary<string, object>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CmsHubSpotCompanyMap>()
            .HasIndex(c => c.CompanyId)
            .IsUnique();

        builder.Entity<CmsHubSpotCompanyMap>()
            .HasIndex(
                c => new
                {
                    c.CompanyId, c.HubSpotCompanyObjectId
                })
            .IsUnique();

        builder.Entity<CmsHubSpotUserContactMap>()
            .HasIndex(c => c.Email)
            .IsUnique();
        builder.Entity<CmsHubSpotUserContactMap>()
            .HasIndex(
                c => new
                {
                    c.Email, HubSpotContactId = c.HubSpotContactObjectId
                })
            .IsUnique();

        builder.Entity<CmsCompanyDataSnapshot>()
            .HasIndex(c => c.SnapshotDate)
            .IsUnique();

        builder.Entity<ShopifyAbandonedCart>()
            .HasIndex(
                c => new
                {
                    c.CartToken, c.CompanyId
                })
            .IsUnique(true);

        builder.Entity<ShopifyAbandonedCart>()
            .HasIndex(
                c => new
                {
                    c.UserProfileId, c.Status
                });

        builder.Entity<ShopifyOrderRecord>()
            .HasIndex(
                c => new
                {
                    c.CompanyId, c.Tags
                });

        builder.Entity<Staff>().Property(e => e.QRCodeChannel).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<TargetedChannelModel>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CompanyTeam>().Property(e => e.QRCodeChannel).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<TargetedChannelModel>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<Staff>().HasIndex(
                e =>
                    new
                    {
                        e.CompanyId, e.QRCodeIdentity
                    })
            .IsUnique(true);

        builder.Entity<CompanyTeam>().HasIndex(
                e =>
                    new
                    {
                        e.CompanyId, e.QRCodeIdentity
                    })
            .IsUnique(true);

        builder.Entity<CoreSandboxTwilioConfig>()
            .HasIndex(
                c => new
                {
                    c.PhoneNumber
                })
            .IsClustered(false);

        builder.Entity<CoreSandboxTwilioConfig>()
            .HasIndex(
                c => new
                {
                    c.TwilioAccountSid, c.TwilioSecret
                })
            .IsClustered(false);

        builder.Entity<TwilioTemplateBookmarkRecord>()
            .HasIndex(
                c => new
                {
                    c.AccountSID, c.TemplateId
                })
            .IsClustered(false);

        builder.Entity<Models.BackgroundTask.BackgroundTask>()
            .HasIndex(
                x => new
                {
                    x.CompanyId, x.StaffId
                })
            .IsClustered(false)
            .IsUnique(false);

        builder.Entity<Models.BackgroundTask.BackgroundTask>()
            .HasIndex(
                x => new
                {
                    x.CompanyId, x.StaffId, x.IsDismissed
                })
            .IsClustered(false)
            .IsUnique(false)
            .IsCreatedOnline();

        builder.Entity<FbIgAutoReply>().Property(e => e.MessageAttachment).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<MessageAttachment>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<FbIgAutoReply>().Property(e => e.QuickReplyButtons).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<QuickReplyButton>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        var longArrayValueConverter = new ValueConverter<long[], string>(
            i => string.Join(",", i),
            s => string.IsNullOrWhiteSpace(s)
                ? new long[0]
                : s.Split(
                    new[]
                    {
                        ','
                    }).Select(v => long.Parse(v)).ToArray());

        builder.Entity<IcebreakerReplyRule>().Property(x => x.AutomationActionIdList)
            .HasConversion(longArrayValueConverter);

        builder.Entity<Whatsapp360DialogExtendedMessagePayload>()
            .Property(e => e.Whatsapp360DialogInteractiveObject).HasConversion(
                v => JsonConvert.SerializeObject(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }),
                v => JsonConvert.DeserializeObject<InteractiveObject>(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }));

        builder.Entity<Whatsapp360DialogExtendedMessagePayload>().Property(e => e.Whatsapp360DialogTemplateMessage)
            .HasConversion(
                v => JsonConvert.SerializeObject(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }),
                v => JsonConvert.DeserializeObject<Whatsapp360DialogTemplateMessageViewModel>(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }));

        builder.Entity<Whatsapp360DialogExtendedMessagePayload>().Property(e => e.ReplyPayload).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<Whatsapp360DialogExtendedMessageReplyPayload>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ConversationMessage>()
            .HasOne(m => m.Whatsapp360DialogExtendedMessagePayload)
            .WithOne(d => d.ConversationMessage)
            .HasForeignKey<Whatsapp360DialogExtendedMessagePayload>(d => d.ConversationMessageId);

        builder.Entity<ConversationMessage>()
            .HasOne(m => m.ExtendedMessagePayload)
            .WithOne(d => d.ConversationMessage)
            .HasForeignKey<ExtendedMessagePayload>(d => d.ConversationMessageId);

        builder.Entity<WhatsApp360DialogConfig>().Property(e => e.OptInConfig).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<WhatsApp360DialogOptInConfig>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationActionRecord>().Property(e => e.WhatsApp360DialogExtendedAutomationMessages)
            .HasConversion(
                v => JsonConvert.SerializeObject(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }),
                v => JsonConvert.DeserializeObject<List<WhatsApp360DialogExtendedAutomationMessage>>(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }));

        builder.Entity<AutomationAction>().Property(e => e.WhatsApp360DialogExtendedAutomationMessages)
            .HasConversion(
                v => JsonConvert.SerializeObject(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }),
                v => JsonConvert.DeserializeObject<List<WhatsApp360DialogExtendedAutomationMessage>>(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }));

        builder.Entity<HubSpotIntegrationCustomFieldMap>().Property(e => e.Options).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<HubSpotIntegrationCustomFieldOption>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<FbIgAutoReplyHistoryRecord>().Property(e => e.ContactReplyMessages).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<ContactReplyMessage>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ShareLinkTrackRecord>().Property(e => e.IpInfo).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<IpInfo>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ShareLinkTrackRecord>().HasIndex(x => x.StaffId).IsClustered(false);
        builder.Entity<ShareLinkTrackRecord>().HasIndex(x => x.UserProfileId).IsClustered(false);

        builder.Entity<WhatsApp360DialogUsageRecord>().Property(e => e.CurrentPhoneNumberPeriodUsage).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<CurrentPhoneNumberPeriodUsage>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<WhatsApp360DialogUsageRecord>().Property(e => e.CurrentConversationPeriodUsage)
            .HasConversion(
                v => JsonConvert.SerializeObject(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }),
                v => JsonConvert.DeserializeObject<Whatsapp360DialogConversationPeriodUsage>(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }));

        builder.Entity<ConversationWhatsappSenderHistory>().HasIndex(
            x => new
            {
                x.ConversationId, x.InstanceId, x.InstanceSender
            }).IsClustered(false);

        builder.Entity<DialogflowServiceAccountConfig>().Property(e => e.DialogflowCredentials).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<DialogflowCredentials>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ResellerStaff>().Property(e => e.TeamNames).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ResellerCompanyProfile>().Property(e => e.ResellerSubscriptionPlanConfig).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<Dictionary<string, string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<StripePaymentConfig>().Property(e => e.AccountInfo).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<Stripe.Account>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<StripePaymentConfig>().Property(e => e.ShippingAllowedCountries).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<StripePaymentConfig>().Property(e => e.ShippingOptions).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<ShippingOption>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<StripePaymentConfig>().Property(e => e.PaymentLinkExpirationOption).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<PaymentLinkExpirationOption>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<StripePaymentConfig>().Property(e => e.SupportedCurrencies).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<StripePaymentRecord>().Property(e => e.LineItems).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<StripePaymentLineItem>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<StripePaymentRecord>().Property(e => e.PaymentIntentPayload).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<Stripe.PaymentIntent>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<StripePaymentRecord>().Property(e => e.Shipping).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<StripeShipping>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<StripePaymentRecord>().Property(e => e.Billing).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<StripeBilling>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<StripePaymentRecord>().HasIndex(
            x => new
            {
                x.CompanyId, x.StripePaymentIntentId
            }).IsClustered(false);
        builder.Entity<StripePaymentRecord>().HasIndex(
            x => new
            {
                x.Status, x.ExpiredAt
            }).IsClustered(false);

        // builder.Entity<ConversationMessage>().HasIndex(x => new {x.SleekPayRecordId}).IsClustered(false);
        builder.Entity<StripePaymentMessageTemplate>().Property(e => e.Params).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ExtendedMessagePayload>().Property(e => e.ExtendedMessagePayloadDetail).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<ExtendedMessagePayloadDetail>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<FacebookOTNTopic>().Property(e => e.HashTagIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings()
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings()
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<DemoConversation>().Property(e => e.DemoUserProfile).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<DemoUserProfile>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<WhatsappTemplateQuickReplyCallback>().Property(e => e.ButtonLayout).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<TemplateButtonComponentLayout>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<WhatsappTemplateQuickReplyCallback>().Property(e => e.CallbackActions).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<WhatsappTemplateButtonCallbackAction>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<WhatsappTemplateQuickReplyCallback>()
            .HasIndex(
                x => new
                {
                    x.CompanyId, x.TemplateName, x.TemplateNamespace
                })
            .IsClustered(false)
            .IsUnique(false)
            .IsCreatedOnline(true);

        builder.Entity<WhatsappTemplateQuickReplyCallback>()
            .HasIndex(
                x => new
                {
                    x.CompanyId
                })
            .IsClustered(false)
            .IsUnique(false)
            .IsCreatedOnline(true);

        builder.Entity<ShopifyProductRecord>().Property(e => e.ProductPayload).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<ShopifyProduct>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ShopifyCollectionRecord>().Property(e => e.CollectionPayload).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<ShopifyCollection>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ShopifyCollectionRecord>().HasIndex(
            x => new
            {
                x.Title,
                x.CompanyId,
                x.CollectionId,
                x.ShopifyId,
                x.SortOrder
            }).IsClustered(false);
        builder.Entity<ShopifyProductRecord>().HasIndex(
            x => new
            {
                x.Title,
                x.CompanyId,
                x.ShopifyId,
                x.ProductId,
                x.Sku
            }).IsClustered(false);
        builder.Entity<ShopifyProductRecord>().HasIndex(
            x => new
            {
                x.CompanyId, x.ShopifyId, x.ProductId
            }).IsUnique(true);

        builder.Entity<ChatHistoryBackupConfig>().Property(e => e.Emails).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<SubscriptionPlan>().Property(e => e.AvailableFunctions).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<SubscriptionSpecificFunction>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<SubscriptionPlan>().Property(e => e.PricingMethod)
            .HasConversion(new EnumToStringConverter<SubscriptionPlanPricingMethod>());

        builder.Entity<SubscriptionPlan>().Property(e => e.TieredPrices)
            .HasConversion(
                x => JsonConvert.SerializeObject(x, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }),
                x => JsonConvert.DeserializeObject<ICollection<SubscriptionPlanTierPrice>>(x, new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }));

        builder.Entity<BlastMessageTemplate>()
            .HasIndex(
                x => new
                {
                    x.CompanyId
                })
            .IsClustered(false)
            .IsUnique(false)
            .IsCreatedOnline();

        builder.Entity<BlastMessageTemplate>().Property(e => e.TargetedChannelWithIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<TargetedChannelModel>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<BlastMessageTemplate>().Property(e => e.BlastMessageContent).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<BlastMessageContent>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<BlastMessageTemplate>().Property(e => e.BlastContactsFile).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<BlastContactsFile>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<BlastMessageTemplate>().Property(e => e.MessageHubRequestProgress).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<MessageHubRequestProgress>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ShareLinkGenerationRecord>().HasIndex(
                x => new
                {
                    x.TrackingId
                })
            .IsUnique(true)
            .IsCreatedOnline();

        builder.Entity<ShopifyConfig>().HasIndex(
            x => new
            {
                x.UsersMyShopifyUrl
            }).IsUnique(true);
        builder.Entity<ShopifyConfig>().Property(e => e.SupportedCountries).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<SupportedCountry>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ShopifyConfig>().Property(e => e.PaymentLinkSetting).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<ShopifyPaymentLinkSetting>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AnalyticsEmailNotificationConfig>().Property(e => e.EmailAddresses).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CmsCompanyAdditionalInfo>().Property(e => e.AllTimeRevenueAnalyticData).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<CmsCompanyAllTimeRevenueAnalyticData>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<ConversationUnreadRecord>()
            .HasIndex(
                x => new
                {
                    x.CompanyId, x.StaffId, x.NotificationDeliveryStatus
                }).IsClustered(false)
            .IsCreatedOnline();

        builder.Entity<ConversationUnreadRecord>()
            .HasIndex(
                x => new
                {
                    x.CompanyId, x.StaffId, x.NotificationDeliveryType, x.NotificationDeliveryStatus
                }).IsClustered(false)
            .IsCreatedOnline();

        builder.Entity<ConversationUnreadRecord>()
            .HasIndex(
                x => new
                {
                    x.CompanyId, x.ConversationId, x.StaffId, x.NotificationDeliveryStatus
                }).IsClustered(false)
            .IsCreatedOnline();

        builder.Entity<ConversationUnreadRecord>()
            .HasIndex(
                x => new
                {
                    x.CompanyId, x.StaffId, x.MessageId, x.NotificationDeliveryStatus
                }).IsClustered(false)
            .IsCreatedOnline();

        builder.Entity<ConversationUnreadRecord>()
            .HasIndex(
                x => new
                {
                    x.CompanyId, x.ConversationId, x.StaffId, x.Id,
                }).IsClustered(false)
            .IsCreatedOnline();

        builder.Entity<ShareLinkGenerationRecord>().HasIndex(
            x => new
            {
                x.CompanyId, x.PlatformCountry
            }).IsClustered(false).IsCreatedOnline();
        builder.Entity<ShareLinkTrackRecord>().HasIndex(
            x => new
            {
                x.CompanyId, x.PlatformCountry
            }).IsClustered(false).IsCreatedOnline();
        builder.Entity<ShopifyOrderRecord>().HasIndex(
            x => new
            {
                x.CompanyId, x.Payment, x.ConversionStatus, x.PlatformCountry
            }).IsClustered(false).IsCreatedOnline();
        builder.Entity<StripePaymentRecord>().HasIndex(
            x => new
            {
                x.CompanyId, x.Status, x.PlatformCountry
            }).IsClustered(false).IsCreatedOnline();

        // Cloud API

        builder.Entity<Conversation>()
            .HasOne(c => c.WhatsappCloudApiUser)
            .WithOne()
            .HasForeignKey<WhatsappCloudApiSender>(d => d.ConversationId)
            .OnDelete(DeleteBehavior.SetNull);

        builder.Entity<UserProfile>()
            .HasOne(c => c.WhatsappCloudApiUser)
            .WithOne()
            .HasForeignKey<WhatsappCloudApiSender>(d => d.UserProfileId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.Entity<WhatsappCloudApiSender>()
            .HasIndex(
                x => new
                {
                    x.CompanyId, x.WhatsappId
                })
            .IsUnique()
            .IsClustered(false)
            .IsCreatedOnline();

        builder.Entity<WhatsappCloudApiConfig>()
            .HasIndex(
                x => new
                {
                    x.CompanyId, x.MessagingHubWabaId, x.MessagingHubWabaPhoneNumberId,
                })
            .IsUnique()
            .IsClustered(false)
            .IsCreatedOnline();

        builder.Entity<ConversationMessage>().Property(e => e.DynamicChannelSender).HasConversion(
                v => JsonConvert.SerializeObject(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }),
                v => JsonConvert.DeserializeObject<ConversationMessageDynamicChannelSender>(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }))
            .HasMaxLength(4000);

        builder.Entity<ConversationMessage>().Property(e => e.AnalyticTags).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<WhatsappCloudApiConfig>().Property(e => e.OptInConfig).HasConversion(
                v => JsonConvert.SerializeObject(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }),
                v => JsonConvert.DeserializeObject<WhatsappCloudApiOptInConfig>(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }))
            .HasMaxLength(4000);

        builder.Entity<WhatsappCloudApiConfig>().Property(e => e.Waba).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<WabaDto>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<WhatsappCloudApiConfig>().Property(e => e.WabaPhoneNumber).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<WabaPhoneNumberDto>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationAction>().Property(e => e.ExtendedAutomationMessage).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<ExtendedAutomationMessage>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<AutomationActionRecord>().Property(e => e.ExtendedAutomationMessage).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<ExtendedAutomationMessage>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CmsHubSpotContactOwnerMap>().Property(e => e.HubspotTeams).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<InternalHubSpotTeamDto>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<WhatsAppCloudApiTemplateBookmark>()
            .HasIndex(
                x => new
                {
                    x.CompanyId, x.MessagingHubWabaId, x.TemplateId
                })
            .IsUnique()
            .IsClustered(false)
            .IsCreatedOnline();

        builder.Entity<ShopifyProductMessageTemplate>().Property(e => e.Params).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<UploadedFile>().Property(e => e.Metadata).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<string>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<WhatsappCloudApiConfig>().Property(e => e.ProductCatalogSetting).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<ProductCatalogSetting>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<Conversation>().Property(e => e.Metadata).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<Dictionary<string, object>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<PiiMaskingConfig>()
            .HasIndex(
                x => new
                {
                    x.CompanyId
                });

        builder.Entity<PiiMaskingConfig>().Property(e => e.RegexPatterns).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<PiiMaskingConfig>().Property(e => e.MaskingCustomObjectSchemaIds).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<UserPreference>().HasIndex(
            table => new
            {
                table.StaffId, table.CompanyId
            }).IsUnique(true);

        builder.Entity<UserPreference>().Property(e => e.Preference).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<UserPreferencesContent>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<UserProfileDuplicationConfig>()
            .HasIndex(
                x => new
                {
                    x.CompanyId
                })
            .IsUnique(true);
        builder.Entity<ShopifyAttribution>()
            .HasNoKey()
            .Metadata
            .SetIsTableExcludedFromMigrations(true);
        builder.Entity<ShopifySalesPerformance>()
            .HasNoKey()
            .Metadata
            .SetIsTableExcludedFromMigrations(true);

        builder.Entity<CmsPartnerStackCustomerMap>()
            .HasIndex(c => c.CompanyId)
            .IsUnique();

        builder.Entity<CmsPartnerStackCustomerMap>()
            .HasIndex(
                c => new
                {
                    c.CompanyId, c.PartnerStackCustomerKey
                })
            .IsUnique();

        builder.Entity<CmsPartnerStackCustomerMap>().Property(c => c.IndividualCommissionConfig).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<IndividualCommissionConfig>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CmsPartnerStackCustomerMap>().Property(c => c.PartnerStackPartnerInformation).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<PartnerStackPartnerInformation>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<CmsPartnerStackCustomerMap>().Property(c => c.PartnerStackCustomerInformation).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<PartnerStackCustomerInformation>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<FacebookLeadAdsNotificationConfig>()
            .HasIndex(
                x => new
                {
                    x.CompanyId, x.FacebookConfigId
                })
            .IsUnique();

        builder.Entity<FacebookLeadAdsNotificationConfig>().Property(e => e.NotificationEmails).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<FacebookLeadAdsNotificationConfig>().Property(e => e.NotificationPhoneNumbers).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<IntegrationAlertConfig>()
            .HasIndex(c => c.CompanyId)
            .IsUnique();

        builder.Entity<IntegrationAlertConfig>().Property(e => e.Emails).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<IntegrationAlertConfig>().Property(e => e.PhoneNumbers).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<BusinessHourConfig>().Property(e => e.WeeklyHours).HasConversion(
                v => JsonConvert.SerializeObject(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }),
                v => JsonConvert.DeserializeObject<WeeklyHours>(
                    v,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    }));

        builder.Entity<Mention>()
            .HasIndex(p => p.CompanyId).IsUnique(false);
        builder.Entity<Mention>()
            .HasIndex(p => p.ConversationId).IsUnique(false);
        builder.Entity<Mention>()
            .HasIndex(
                p => new
                {
                    p.CompanyId, p.ConversationId, p.CreatedAt, p.MentionedStaffId
                }).IsUnique(false);
        builder.Entity<Mention>()
            .HasIndex(
                p => new
                {
                    p.CompanyId, p.CreatedAt, p.MentionedStaffId
                }).IsUnique(false);
        builder.Entity<Mention>()
            .HasIndex(
                p => new
                {
                    p.CompanyId, p.ConversationId, p.CreatedAt
                }).IsUnique(false);

        builder.Entity<SubscriptionTierConfig>().Property(e => e.DefaultFeatureFlags).HasConversion(
            v => JsonConvert.SerializeObject(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }),
            v => JsonConvert.DeserializeObject<List<string>>(
                v,
                new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                }));

        builder.Entity<LiveChatV2Config>()
            .HasIndex(p => p.ChannelIdentityId).IsUnique(false);

        builder.Entity<LiveChatV2Config>()
            .HasIndex(p => p.CompanyId).IsUnique(false);

        builder.Entity<LiveChatV2Config>()
            .HasIndex(p => new
            {
                p.ChannelIdentityId,
                p.CompanyId
            }).IsUnique(false);

        builder.Entity<WebClientSender>()
            .HasIndex(
                p => new
                {
                    p.ConversationId,
                    p.CompanyId
                }).IsUnique(false);

        builder.Entity<WebClientSender>()
            .HasIndex(
                p => new
                {
                    p.CompanyId,
                    p.UserProfileId
                }).IsUnique(false);

        builder.Entity<WebClientSender>()
            .HasIndex(
                p =>
                    p.CompanyId).IsUnique(false);
    }

    public DbSet<Guest> UserRoleGuests { get; set; }

    public DbSet<Admin> UserRoleAdmins { get; set; }

    public DbSet<Staff> UserRoleStaffs { get; set; }

    public DbSet<RegisteredSession> UserRegisteredSessions { get; set; }

    public DbSet<CompanyOperator> UserRoleOperators { get; set; }

    public DbSet<StaffNotificationSetting> UserStaffNotificationSettings { get; set; }

    public DbSet<ProfilePictureFile> UserStaffProfilePictures { get; set; }

    public DbSet<Company> CompanyCompanies { get; set; }

    public DbSet<CompanyCustomField> CompanyCompanyCustomFields { get; set; }

    public DbSet<CompanyHashtag> CompanyDefinedHashtags { get; set; }

    public DbSet<CompanyMessageTemplate> CompanyMessageTemplates { get; set; }

    public DbSet<AssignmentRule> CompanyAssignmentRules { get; set; }

    public DbSet<AssignmentQueue> CompanyAssignmentQueues { get; set; }

    public DbSet<AutomationAction> CompanyAutomationActions { get; set; }

    public DbSet<AutomationHistory> CompanyAutomationHistories { get; set; }

    public DbSet<AutomationActionRecord> CompanyAutomationActionRecords { get; set; }

    public DbSet<AssignmentUploadedFile> CompanyAssignmentUploadedFiles { get; set; }

    public DbSet<CompanySandbox> CompanySandboxes { get; set; }

    public DbSet<CompanyAPIKey> CompanyAPIKeys { get; set; }

    public DbSet<RequestChannel> CompanyRequestChannels { get; set; }

    public DbSet<CompanyIconFile> CompanyIconFiles { get; set; }

    public DbSet<CompanyTeam> CompanyStaffTeams { get; set; }

    public DbSet<TeamMember> CompanyTeamMembers { get; set; }

    public DbSet<TeamAssignmentQueue> CompanyTeamAssignmentQueues { get; set; }

    public DbSet<ShareableInvitation> CompanyShareableInvitations { get; set; }

    public DbSet<ShareableInvitationRecord> CompanyShareableInvitationRecords { get; set; }

    public DbSet<RolePermission> CompanyRolePermissions { get; set; }

    public DbSet<TwilioUsageRecord> CompanyTwilioUsageRecords { get; set; }

    public DbSet<Segment> CompanyAnalyticSegment { get; set; }

    public DbSet<BroadcastHistory> BroadcastCompaignHistories { get; set; }

    public DbSet<CampaignUploadedFile> CampaignUploadedFiles { get; set; }

    public DbSet<CampaignChannelMessage> CampaignChannelMessages { get; set; }

    public DbSet<CampaignAutomationAction> CampaignAutomationActions { get; set; }

    public DbSet<CampaignAutomationUploadedFile> CampaignAutomationUploadedFiles { get; set; }

    public DbSet<CustomUserProfileFieldLingual> CompanyCustomUserProfileFieldLinguals { get; set; }

    public DbSet<CompanyCustomFieldFieldLingual> CompanyCustomFieldFieldLinguals { get; set; }

    public DbSet<CompanyCustomUserProfileField> CompanyCustomUserProfileFields { get; set; }

    public DbSet<CustomUserProfileFieldOption> CompanyCustomUserProfileFieldOptions { get; set; }

    public DbSet<CustomUserProfileFieldOptionLingual> CompanyCustomUserProfileFieldOptionLinguals { get; set; }

    public DbSet<UserDevice> UserUserDevices { get; set; }

    public DbSet<FetchedThread> FacebookFetchedIds { get; set; }

    public DbSet<FacebookSender> SenderFacebookSenders { get; set; }

    public DbSet<WhatsAppSender> SenderWhatsappSenders { get; set; }

    public DbSet<EmailSender> SenderEmailSenders { get; set; }

    public DbSet<WebClientSender> SenderWebClientSenders { get; set; }

    public DbSet<WeChatSender> SenderWeChatSenders { get; set; }

    public DbSet<LineSender> SenderLineSender { get; set; }

    public DbSet<ViberSender> SenderViberSenders { get; set; }

    public DbSet<TelegramSender> SenderTelegramSenders { get; set; }

    public DbSet<IPAddressInfo> SenderWebClientIPAddressInfos { get; set; }

    public DbSet<SMSSender> SenderSMSSenders { get; set; }

    public DbSet<SandboxSender> SenderSandboxSenders { get; set; }

    public DbSet<InstagramSender> SenderInstagramSenders { get; set; }

    public DbSet<TikTokSender> SenderTikTokSenders { get; set; }

    // public DbSet<ChatAPIWhatsAppSender> SenderChatAPIWhatsAppSenders { get; set; }
    public DbSet<FacebookConfig> ConfigFacebookConfigs { get; set; }

    public DbSet<WhatsAppConfig> ConfigWhatsAppConfigs { get; set; }

    public DbSet<EmailConfig> ConfigEmailConfigs { get; set; }

    public DbSet<WeChatConfig> ConfigWeChatConfigs { get; set; }

    public DbSet<LineConfig> ConfigLineConfigs { get; set; }

    public DbSet<ViberConfig> ConfigViberConfigs { get; set; }

    public DbSet<TelegramConfig> ConfigTelegramConfigs { get; set; }

    public DbSet<SMSConfig> ConfigSMSConfigs { get; set; }

    public DbSet<ShoplineConfig> ConfigShoplineConfigs { get; set; }

    public DbSet<ShopifyConfig> ConfigShopifyConfigs { get; set; }

    public DbSet<InstagramConfig> ConfigInstagramConfigs { get; set; }

    public DbSet<TikTokConfig> ConfigTikTokConfigs { get; set; }

    public DbSet<StorageConfig> ConfigStorageConfigs { get; set; }

    public DbSet<DialogflowServiceAccountConfig> ConfigDialogflowServiceAccountConfigs { get; set; }

    public DbSet<UserProfile> UserProfiles { get; set; }

    public DbSet<UserProfilePictureFile> UserProfilePictureFiles { get; set; }

    public DbSet<UserProfileCustomField> UserProfileCustomFields { get; set; }

    // public DbSet<UserProfileActivityLog> UserProfileActivityLogs { get; set; }
    public DbSet<ShopifyOrderRecord> UserProfileShopifyOrders { get; set; }

    public DbSet<ShopifyAbandonedCart> UserProfileShopifyAbandonedCarts { get; set; }

    public DbSet<CrmHubEntity> CrmHubEntities { get; set; }

    public DbSet<Conversation> Conversations { get; set; }

    public DbSet<ConversationWhatsappSenderHistory> ConversationWhatsappHistories { get; set; }

    public DbSet<ConversationMessage> ConversationMessages { get; set; }

    // public DbSet<ConversationTicket> ConversationTickets { get; set; }
    // public DbSet<ConversationProcessedMessage> ConversationProcessedMessages { get; set; }
    public DbSet<AdditionalAssignee> ConversationAdditionalAssignees { get; set; }

    public DbSet<ConversationHashtag> ConversationHashtags { get; set; }

    // public DbSet<ConversationActivityLog> ConversationActivityLogs { get; set; }
    public DbSet<UploadedFile> ConversationMessageUploadedFiles { get; set; }

    // public DbSet<TicketActivityLog> ConversationTicketActivityLogs { get; set; }
    // public DbSet<TicketCustomField> ConversationTicketCustomFields { get; set; }

    // public DbSet<ConversationRemark> ConversationRemarks { get; set; }
    public DbSet<CoreEmailConfig> CoreEmailConfigs { get; set; }

    public DbSet<SubscriptionPlan> CoreSubscriptionPlans { get; set; }

    public DbSet<BillRecord> CompanyBillRecords { get; set; }

    public DbSet<TwilioTopUpPlan> CoreTwilioTopupPlans { get; set; }

    public DbSet<TwilioTopupRecord> CoreTwilioTopupRecords { get; set; }

    public DbSet<EmailNotificationTemplate> CoreEmailNotificationTemplates { get; set; }

    // public DbSet<CoreWhatsappConfig> CoreWhatsappConfigs { get; set; }
    public DbSet<CoreTwilioConfig> CoreTwilioConfigs { get; set; }

    public DbSet<CoreCustomField> CoreCustomFields { get; set; }

    public DbSet<CoreSandboxTwilioConfig> CoreSandboxTwilioConfigs { get; set; }

    public DbSet<CancelSubscriptionRecord> CoreCancelSubscriptionRecords { get; set; }

    // public DbSet<CoreWhatsappChatAPIInstance> CoreWhatsappChatAPIInstances { get; set; }
    public DbSet<WhatsappRegistrationsFile> CoreWhatsappRegistrationFiles { get; set; }

    // public DbSet<BackgroundTask> CoreBackgroundTasks { get; set; }
    public DbSet<NotificationRecord> CompanyNotificationRecords { get; set; }

    public DbSet<ImportContactHistory> CompanyImportContactHistories { get; set; }

    public DbSet<ImportedUserProfile> CompanyImportedUserProfiles { get; set; }

    public DbSet<ImportContactToListRecord> CompanyImportContactToListRecords { get; set; }

    public DbSet<CompanyQuickReply> CompanyQuickReplies { get; set; }

    public DbSet<CompanyQuickReplyLingual> CompanyQuickReplyLinguals { get; set; }

    public DbSet<CompanyQuickReplyFile> CompanyQuickReplyFiles { get; set; }

    public DbSet<QRcodeRecord> QRcodeRecords { get; set; }

    public DbSet<Country> CoreCountries { get; set; }

    public DbSet<NotificationRegistration> NotificationRegistrations { get; set; }

    public DbSet<ZapierPollingRecord> CoreZapierPollingRecord { get; set; }

    public DbSet<RedeemPromotionRecord> CoreRedeemPromotionRecords { get; set; }

    public DbSet<Promotion> CorePromotions { get; set; }

    public DbSet<AnalyticsRecord> AnalyticsRecords { get; set; }

    public DbSet<CmsSalesPaymentRecord> CmsSalesPaymentRecords { get; set; }

    public DbSet<CmsSalesPaymentRecordFile> CmsSalesPaymentRecordFiles { get; set; }

    public DbSet<CmsContactOwnerAssignLog> CmsContactOwnerAssignLogs { get; set; }

    public DbSet<CmsWhatsappApplication> CmsWhatsappApplications { get; set; }

    public DbSet<CmsLoginAsHistory> CmsLoginAsHistories { get; set; }

    public DbSet<CmsHubSpotCompanyMap> CmsHubSpotCompanyMaps { get; set; }

    public DbSet<CmsHubSpotContactOwnerMap> CmsHubSpotContactOwnerMaps { get; set; }

    public DbSet<CmsHubSpotUserContactMap> CmsHubSpotUserContactMaps { get; set; }

    public DbSet<CmsHubSpotCompanySyncHistory> CmsHubSpotCompanySyncHistories { get; set; }

    public DbSet<CmsCompanyDataSnapshot> CmsCompanyDataSnapshots { get; set; }

    public DbSet<CmsCompanyAdditionalInfo> CmsCompanyAdditionalInfos { get; set; }

    public DbSet<SupportTicket> SupportTickets { get; set; }

    public DbSet<SupportTicketFile> SupportTicketFiles { get; set; }

    public DbSet<TwilioTopUpLog> TwilioTopUpLogs { get; set; }

    public DbSet<TwilioTemplateBookmarkRecord> TwilioTemplateBookmarkRecords { get; set; }

    public DbSet<Models.BackgroundTask.BackgroundTask> BackgroundTasks { get; set; }

    public DbSet<CompanyPaymentFailedLog> CompanyPaymentFailedLogs { get; set; }

    public DbSet<FbIgAutoReply> FbIgAutoReplies { get; set; }

    public DbSet<FbIgAutoReplyHistoryRecord> FbIgAutoReplyHistoryRecords { get; set; }

    public DbSet<FbIgAutoReplyFile> FbIgAutoReplyFiles { get; set; }

    public DbSet<FbIgIcebreaker> FbIgIcebreakers { get; set; }

    public DbSet<IcebreakerReplyRule> IcebreakerReplyRules { get; set; }

    public DbSet<IcebreakerHistoryRecord> IcebreakerHistoryRecords { get; set; }

    public DbSet<WhatsApp360DialogSender> SenderWhatsApp360DialogSenders { get; set; }

    public DbSet<WhatsApp360DialogConfig> ConfigWhatsApp360DialogConfigs { get; set; }

    public DbSet<WhatsApp360DialogTemplateBookmark> WhatsApp360DialogTemplateBookmarks { get; set; }

    public DbSet<WhatsApp360DialogUsageRecord> CompanyWhatsApp360DialogUsageRecords { get; set; }

    public DbSet<WhatsApp360DialogUsageTransactionLog> CompanyWhatsApp360DialogUsageTransactionLogs { get; set; }

    public DbSet<CoreWhatsApp360DialogPartnerAuthCredential> CoreWhatsApp360DialogPartnerAuthCredentials
    {
        get;
        set;
    }

    public DbSet<WhatsApp360DialogMediaFile> WhatsApp360DialogMediaFiles { get; set; }

    public DbSet<Whatsapp360DialogExtendedMessagePayload> Whatsapp360DialogExtendedMessagePayload { get; set; }

    public DbSet<ConversationBookmark> ConversationBookmarks { get; set; }

    public DbSet<HubSpotIntegrationContactOwnerMap> HubSpotIntegrationContactOwnerMaps { get; set; }

    public DbSet<HubSpotIntegrationCustomFieldMap> HubSpotIntegrationCustomFieldMaps { get; set; }

    public DbSet<HubSpotIntegrationConfig> HubSpotIntegrationConfigs { get; set; }

    public DbSet<HubSpotIntegrationSyncTask> HubSpotIntegrationSyncTasks { get; set; }

    public DbSet<HubSpotIntegrationSyncTaskErrorRecord> HubSpotIntegrationSyncTaskErrorRecords { get; set; }

    public DbSet<HubSpotIntegrationWebhookErrorRecord> HubSpotIntegrationWebhookErrorRecords { get; set; }

    public DbSet<ShareLinkTrackRecord> CoreShareLinkTrackingRecords { get; set; }

    public DbSet<ShareLinkGenerationRecord> CoreShareLinkGenerationRecords { get; set; }

    public DbSet<CompanyWhatsapp360DialogTopUpConfig> CompanyWhatsapp360DialogTopUpConfigs { get; set; }

    public DbSet<CmsCurrencyExchangeRate> CmsCurrencyExchangeRates { get; set; }

    public DbSet<Cms360DialogItemCost> Cms360DialogItemCosts { get; set; }

    public DbSet<ResellerCompanyProfile> ResellerCompanyProfiles { get; set; }

    public DbSet<ResellerStaff> ResellerStaffs { get; set; }

    public DbSet<ResellerClientCompanyProfile> ResellerClientCompanyProfiles { get; set; }

    public DbSet<ResellerTransactionLog> ResellerTransactionLogs { get; set; }

    public DbSet<ResellerActivityLog> ResellerActivityLogs { get; set; }

    public DbSet<ResellerProfileLogo> ResellerProfileLogos { get; set; }

    public DbSet<ResellerStripeTopUpOption> CoreResellerStripeTopUpOptions { get; set; }

    #region Stripe Payment

    public DbSet<StripePaymentConfig> ConfigStripePaymentConfigs { get; set; }

    public DbSet<StripePaymentRecord> StripePaymentRecords { get; set; }

    public DbSet<StripePaymentMessageTemplate> StripePaymentMessageTemplates { get; set; }

    #endregion

    public DbSet<ImportWhatsappHistoryRecord> ImportWhatsappHistoryRecords { get; set; }

    public DbSet<FacebookOTNTopic> FacebookOtnTopics { get; set; }

    public DbSet<FacebookUserOneTimeToken> FacebookUserOneTimeTokens { get; set; }

    public DbSet<ExtendedMessagePayload> ExtendedMessagePayloads { get; set; }

    public DbSet<ExtendedMessagePayloadFile> ExtendedMessagePayloadFiles { get; set; }

    public DbSet<DemoConversation> DemoConversations { get; set; }

    public DbSet<DemoConversationMessage> DemoConversationMessages { get; set; }

    public DbSet<StripeCompanyLogo> StripeCompanyLogos { get; set; }

    public DbSet<WhatsappTemplateQuickReplyCallback> WhatsappTemplateQuickReplyCallbacks { get; set; }

    public DbSet<ShopifyCollectionProductRecord> ShopifyCollectionProductRecords { get; set; }

    public DbSet<ShopifyProductRecord> ShopifyProductRecords { get; set; }

    public DbSet<ShopifyCollectionRecord> ShopifyCollectionRecords { get; set; }

    public DbSet<ShopifyProductMessageTemplate> ShopifyProductMessageTemplates { get; set; }

    public DbSet<ChatHistoryBackupConfig> ChatHistoryBackupConfig { get; set; }

    public DbSet<BlastMessageConfig> BlastMessageConfigs { get; set; }

    public DbSet<BlastMessageTemplate> BlastMessageTemplates { get; set; }

    public DbSet<AnalyticsEmailNotificationConfig> AnalyticsEmailNotificationConfigs { get; set; }

    public DbSet<CmsSleekPayReportCsv> CmsSleekPayReportCsvs { get; set; }

    public DbSet<CmsSleekPayReportData> CmsSleekPayReportDatas { get; set; }

    public DbSet<WhatsappCloudApiConfig> ConfigWhatsappCloudApiConfigs { get; set; }

    public DbSet<WhatsappCloudApiSender> WhatsappCloudApiSenders { get; set; }

    public DbSet<WhatsappCloudApiWabaConnection> WhatsappCloudApiWabaConnections { get; set; }

    public DbSet<ConversationUnreadRecord> ConversationUnreadRecords { get; set; }

    public DbSet<CmsSleekPayTransactionRateLog> CmsSleekPayTransactionRateLogs { get; set; }

    public DbSet<CustomSubscriptionPlanTranslationMap> CustomSubscriptionPlanTranslationMaps { get; set; }

    public DbSet<CompanyRegionalInfo> CompanyRegionalInfos { get; set; }

    public DbSet<StripePaymentReportExportRecord> StripePaymentReportExportRecords { get; set; }

    public DbSet<WhatsAppCloudApiTemplateBookmark> WhatsAppCloudApiTemplateBookmarks { get; set; }

    public DbSet<PiiMaskingConfig> PiiMaskingConfigs { get; set; }

    public DbSet<UserPreference> UserPreferences { get; set; }

    public DbSet<UserProfileDuplicationConfig> UserProfileDuplicationConfigs { get; set; }

    public DbSet<UserProfileDeletionBuffer> UserProfileDeletionBuffers { get; set; }

    public DbSet<CmsPartnerStackCustomerMap> CmsPartnerStackCustomerMaps { get; set; }

    public DbSet<FacebookLeadAdsNotificationConfig> FacebookLeadAdsNotificationConfigs { get; set; }

    public DbSet<UserProfileDuplicatedLog> UserProfileDuplicatedLogs { get; set; }

    public DbSet<FacebookLeadAdsForm> FacebookLeadAdsForms { get; set; }

    public DbSet<FacebookLeadAdsFormFieldMapping> FacebookLeadAdsFormFieldMappings { get; set; }

    public DbSet<IntegrationAlertConfig> IntegrationAlertConfigs { get; set; }

    public DbSet<BusinessHourConfig> BusinessHourConfigs { get; set; }

    public DbSet<Mention> Mentions { get; set; }

    public DbSet<SubscriptionTierConfig> SubscriptionTierConfigs { get; set; }

    public DbSet<LiveChatV2Config> ConfigLiveChatV2Configs { get; set; }
}
