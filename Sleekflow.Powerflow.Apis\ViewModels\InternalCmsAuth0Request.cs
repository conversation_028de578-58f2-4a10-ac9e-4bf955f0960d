using System.ComponentModel.DataAnnotations;
using Auth0.ManagementApi.Models;
using Auth0.ManagementApi.Paging;
using Newtonsoft.Json;

namespace Sleekflow.Powerflow.Apis.ViewModels;

public class GetAuth0UsersRequest
{
    [JsonProperty("user_id")]
    public string? UserId { get; set; }

    [JsonProperty("username")]
    public string? UserName { get; set; }

    [JsonProperty("email")]
    public string? Email { get; set; }

    [JsonProperty("provider")]
    public string? Provider { get; set; }

    [JsonProperty("is_social")]
    public bool? IsSocial { get; set; }

    [JsonProperty("email_verified")]
    public bool? EmailVerified { get; set; }

    [JsonProperty("is_blocked")]
    public bool? IsBlocked { get; set; }

    [JsonProperty("query")]
    public string? Query { get; set; }

    [JsonProperty("page")]
    public int? Page { get; set; }

    [JsonProperty("item_per_page")]
    public int? ItemPerPage { get; set; }
}

public class GetAuth0UsersResponse
{
    [JsonProperty("user")]
    public List<User>? Users { get; set; }

    [JsonProperty("paging")]
    public PagingInformation? PageInfo { get; set; }
}

public class SetAuth0UserEmailRequest
{
    [Required]
    [JsonProperty("user_id")]
    public string UserId { get; set; }

    [Required]
    [JsonProperty("new_email")]
    public string NewEmail { get; set; }
}

public class SetAuth0UserNameRequest
{
    [Required]
    [JsonProperty("user_id")]
    public string UserId { get; set; }

    [Required]
    [JsonProperty("new_username")]
    public string NewUserName { get; set; }
}

public class GetUserHistoryRequest
{
    [Required]
    [JsonProperty("auth0_user_id")]
    public string Auth0UserId { get; set; }

    [JsonProperty("page")]
    public int Page { get; set; } = 0;

    [JsonProperty("item_per_page")]
    public int PerPage { get; set; } = 100;
}

public class GetUserMfaRequest
{
    [Required]
    [JsonProperty("auth0_user_id")]
    public string Auth0UserId { get; set; }
}

public class ResetUserMfaRequest
{
    [Required]
    [JsonProperty("auth0_user_id")]
    public string Auth0UserId { get; set; }

    [Required]
    [JsonProperty("mfa_id")]
    public string? MfaId { get; set; }
}

public class SetAuth0UserBlockStatusRequest
{
    [Required]
    [JsonProperty("user_id")]
    public string UserId { get; set; }

    [Required]
    [JsonProperty("is_blocked")]
    public bool IsBlocked { get; set; }
}

public class SendVerificationEmailRequest
{
    [Required]
    [JsonProperty("user_id")]
    public string UserId { get; set; }
}

public class SetEmailConfirmedRequest
{
    [Required]
    [JsonProperty("user_id")]
    public string UserId { get; set; }
}

public class UserHaveCompanyRequest
{
    [Required]
    [JsonProperty("user_id")]
    public string UserId { get; set; }
}

public class DeleteUserRequest
{
    [Required]
    [JsonProperty("user_id")]
    public string UserId { get; set; }
}